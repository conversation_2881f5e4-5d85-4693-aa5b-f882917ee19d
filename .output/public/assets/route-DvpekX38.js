import{j as e,L as a,u as o,a as r,O as c}from"./main-RY8ZkTMc.js";import{c as n}from"./createLucideIcon-78bvTjT9.js";import{U as i}from"./users-DptPd1yw.js";import{C as d}from"./calendar-C7hPlekH.js";import{u as m}from"./useMutation-D8585ZL-.js";import{A as h}from"./runtimes-CTOS42-v.js";import{U as x}from"./user-BHfkra8r.js";/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],j=n("house",u);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p=[["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m14.305 16.53.923-.382",key:"1itpsq"}],["path",{d:"m15.228 13.852-.923-.383",key:"eplpkm"}],["path",{d:"m16.852 12.228-.383-.923",key:"13v3q0"}],["path",{d:"m16.852 17.772-.383.924",key:"1i8mnm"}],["path",{d:"m19.148 12.228.383-.923",key:"1q8j1v"}],["path",{d:"m19.53 18.696-.382-.924",key:"vk1qj3"}],["path",{d:"m20.772 13.852.924-.383",key:"n880s0"}],["path",{d:"m20.772 16.148.924.383",key:"1g6xey"}],["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],b=n("user-cog",p);function g(){return e.jsx("div",{className:"h-full w-64 bg-base-300 shadow-lg",children:e.jsxs("div",{className:"p-4",children:[e.jsx("h2",{className:"mb-6 font-bold text-2xl text-primary",children:"Schedhold"}),e.jsxs("ul",{className:"menu menu-lg w-full rounded-box bg-base-200",children:[e.jsx("li",{children:e.jsxs(a,{to:"/admin",className:"flex items-center gap-3 hover:bg-base-300",children:[e.jsx(j,{className:"h-5 w-5"}),"Home"]})}),e.jsx("li",{children:e.jsxs(a,{to:"/admin/clients",className:"flex items-center gap-3 hover:bg-base-300",children:[e.jsx(i,{className:"h-5 w-5"}),"Clientes"]})}),e.jsx("li",{children:e.jsxs(a,{to:"/admin/workers",className:"flex items-center gap-3 hover:bg-base-300",children:[e.jsx(b,{className:"h-5 w-5"}),"Trabajadores"]})}),e.jsx("li",{children:e.jsxs(a,{to:"/admin/schedules",className:"flex items-center gap-3 hover:bg-base-300",children:[e.jsx(d,{className:"h-5 w-5"}),"Horarios"]})})]})]})})}const f=()=>{const{auth:s}=o();return m({mutationKey:["logout"],mutationFn:()=>h.runPromise(s.logout())})};function v(){const{mutate:s}=f(),t=r(),l=()=>{s(void 0,{onSettled:()=>{t({to:"/login"})}})};return e.jsxs("div",{className:"navbar w-full bg-neutral text-neutral-content",children:[e.jsx("div",{className:"flex-1",children:e.jsx("button",{type:"button",className:"btn btn-ghost text-xl",children:"Schedhold"})}),e.jsx("div",{className:"flex-none",children:e.jsxs("details",{className:"dropdown dropdown-end",children:[e.jsx("summary",{className:"btn btn-circle avatar",children:e.jsx(x,{size:26})}),e.jsxs("ul",{className:"menu menu-sm dropdown-content z-[1] mt-3 w-52 rounded-box bg-neutral p-2 shadow",children:[e.jsx("li",{children:e.jsx("span",{children:"Profile"})}),e.jsx("li",{children:e.jsx("span",{children:"Settings"})}),e.jsx("li",{children:e.jsx("button",{type:"button",onClick:l,children:"Logout"})})]})]})})]})}const H=function(){return e.jsxs("div",{className:"flex h-screen w-full ",children:[e.jsx(g,{}),e.jsxs("div",{className:"flex w-[calc(100%-16rem)] min-w-0 flex-col",children:[e.jsx(v,{}),e.jsx("div",{className:"flex-1 overflow-y-auto bg-base-200",children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(c,{})})})]})]})};export{H as component};
