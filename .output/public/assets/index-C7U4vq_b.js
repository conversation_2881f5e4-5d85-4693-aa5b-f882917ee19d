import{c as f}from"./createLucideIcon-78bvTjT9.js";import{o as T,a as $,p,g,n as j,m as M,s as S,h as N}from"./runtimes-CTOS42-v.js";import{j as e,q as D,c as k,r as w}from"./main-RY8ZkTMc.js";import{w as C,E as H}from"./form-BdoD3Q87.js";import{T as L}from"./schedule-options-BL0O7AQ8.js";/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],Q=f("arrow-left",A);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],U=f("clock",E);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],_=f("plus",V),z={name:"",sessionDuration:60,breakDuration:15,turns:[]},F=T({name:p(S("Debe ingresar un nombre para el turno"),M(1,"Debe tener al menos un caracter")),startTime:p(j("Debe ingresar una hora de inicio"),g(0,"La hora debe ser mayor o igual a 0"),N(2359,"La hora debe ser menor o igual a 23:59")),endTime:p(j("Debe ingresar una hora de fin"),g(0,"La hora debe ser mayor o igual a 0"),N(2359,"La hora debe ser menor o igual a 23:59"))}),X=T({name:p(S("Debe ingresar un nombre"),M(1,"Debe tener al menos un caracter")),sessionDuration:p(j("Debe ingresar la duración de la sesión"),g(1,"La duración debe ser mayor a 0")),breakDuration:p(j("Debe ingresar la duración del descanso"),g(0,"La duración debe ser mayor o igual a 0")),turns:$(F)});function Y({turn:t,sessionDuration:r,breakDuration:o}){const c=s=>{const n=Math.floor(s/100),a=s%100;return`${n.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},i=(()=>{const s=Math.floor(t.startTime/100),n=t.startTime%100,a=Math.floor(t.endTime/100),l=t.endTime%100,x=s*60+n,h=a*60+l,y=[];let b=x;for(;b<h;){const v=b+r;if(v>h)break;y.push({start:b,end:v,label:"Sesión"}),b=v+o}return y})(),u=["Domingo","Lunes","Martes","Miércoles","Jueves","Viernes","Sábado"],m=s=>{const n=Math.floor(s/60),a=s%60;return`${n.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`};return e.jsx("div",{className:"card bg-base-100",children:e.jsxs("div",{className:"card-body",children:[e.jsxs("h4",{className:"card-title text-lg",children:[t.name||"Turno sin nombre"," - ",c(t.startTime)," a"," ",c(t.endTime)]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("div",{className:"grid gap-1 text-xs",style:{gridTemplateColumns:`120px repeat(${u.length}, 1fr)`,gridTemplateRows:`auto repeat(${i.length}, minmax(64px, auto))`},children:[e.jsx("div",{className:"rounded bg-base-200 p-2 font-medium",children:"Hora"}),u.map(s=>e.jsx("div",{className:"rounded bg-base-200 p-2 text-center font-medium",children:s},s)),i.map((s,n)=>e.jsxs(D.Fragment,{children:[e.jsxs("div",{className:"flex items-center rounded bg-base-100 p-2 font-mono text-xs",children:[m(s.start)," -"," ",m(s.end)]}),u.map(a=>e.jsx("div",{className:"flex items-center justify-center p-1",children:e.jsx("div",{className:"flex h-16 w-full items-center justify-center rounded bg-primary px-2 py-1 font-medium text-primary-content text-xs",children:s.label})},a))]},`${s}-${n}`))]})}),e.jsxs("div",{className:"mt-4 text-sm",children:[e.jsx("div",{className:"flex gap-4",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-4 w-4 rounded bg-primary"}),e.jsxs("span",{children:["Sesión (",r," min)"]})]})}),e.jsxs("div",{className:"mt-2 text-base-content/70 text-xs",children:["Total de sesiones: ",i.length]})]})]})})}const P=t=>{const r=Math.floor(t/100),o=t%100;return`${r.toString().padStart(2,"0")}:${o.toString().padStart(2,"0")}`},R=t=>{const[r,o]=t.split(":").map(Number);return(r||0)*100+(o||0)},I=t=>{const r=Math.floor(t/100),o=t%100,c=r>=12?"PM":"AM";return`${r===0?12:r>12?r-12:r}:${o.toString().padStart(2,"0")} ${c}`},O=(t,r,o)=>{const c=[],d=r+o,i=Math.floor(t/100),u=t%100;let m=i*60+u+d;for(;m<24*60;){const s=Math.floor(m/60),n=m%60;if(s>=24)break;const a=s*100+n;c.push({value:a,label:I(a)}),m+=d}return c},q=(t,r,o,c)=>{const d=Math.floor(t/100),i=t%100,u=d*60+i,m=Math.floor(r/100),s=r%100,a=m*60+s-u,l=o+c;return Math.floor(a/l)},Z=C({defaultValues:z,props:{},render:({form:t,selectedTurnIndex:r,onTurnSelect:o})=>{const[c,d]=k(t.store,i=>[i.values.sessionDuration,i.values.breakDuration]);return e.jsx("div",{children:e.jsx(t.Field,{name:"turns",mode:"array",children:i=>{const u=i.state.value;return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4",children:[e.jsx("span",{className:"font-semibold",children:"Turnos"}),e.jsx("div",{className:"flex justify-end",children:e.jsxs("button",{type:"button",className:"btn btn-primary btn-sm",onClick:()=>i.pushValue({name:"",startTime:800,endTime:1700}),children:[e.jsx(_,{size:16}),"Agregar Turno"]})})]}),u.length>0&&e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Nombre"}),e.jsx("th",{children:"Hora Inicio"}),e.jsx("th",{children:"Hora Fin"}),e.jsx("th",{children:"Total de sesiones"}),e.jsx("th",{children:"Acciones"})]})}),e.jsx("tbody",{children:u.map((m,s)=>e.jsxs("tr",{className:r===s?"bg-primary/20":"",children:[e.jsx("td",{children:e.jsx(t.Field,{name:`turns[${s}].name`,children:n=>e.jsx("input",{type:"text",className:"input input-sm w-full",value:n.state.value||"",onChange:a=>n.handleChange(a.target.value),placeholder:"Nombre del turno"})})}),e.jsx("td",{children:e.jsx(t.Field,{name:`turns[${s}].startTime`,children:n=>e.jsx("input",{type:"time",className:"input input-sm w-full",value:P(n.state.value||800),onChange:a=>n.handleChange(R(a.target.value))})})}),e.jsx("td",{children:e.jsx(t.Subscribe,{selector:n=>{var a;return(a=n.values.turns[s])==null?void 0:a.startTime},children:n=>{const a=O(n||800,c,d);return w.useEffect(()=>{var l;t.setFieldValue(`turns[${s}].endTime`,((l=a[0])==null?void 0:l.value)||1700)},[n]),e.jsx(t.Field,{name:`turns[${s}].endTime`,children:l=>{var x;return e.jsx("select",{className:"select select-sm w-full",value:l.state.value||((x=a[0])==null?void 0:x.value)||1700,onChange:h=>l.handleChange(Number(h.target.value)),children:a.map(h=>e.jsx("option",{value:h.value,children:h.label},h.value))})}})}})}),e.jsx("td",{children:e.jsx(t.Subscribe,{selector:n=>{var a,l;return[(a=n.values.turns[s])==null?void 0:a.startTime,(l=n.values.turns[s])==null?void 0:l.endTime]},children:([n,a])=>{const l=q(n||800,a||1700,c,d);return e.jsxs("div",{className:"text-base-content/70 text-xs",children:[l," sesiones"]})}})}),e.jsx("td",{children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{type:"button",className:`btn btn-sm ${r===s?"btn-primary":"btn-outline"}`,onClick:()=>o(r===s?null:s),children:e.jsx(H,{size:16})}),e.jsx("button",{type:"button",className:"btn btn-error btn-sm",onClick:()=>{i.removeValue(s),r===s?o(null):r!==null&&r>s&&o(r-1)},children:e.jsx(L,{size:16})})]})})]},`turn-${s}`))})]})}),u.length===0&&e.jsx("div",{className:"card bg-base-200",children:e.jsx("div",{className:"card-body text-center",children:e.jsx("p",{className:"text-base-content/70",children:'No hay turnos agregados. Haz clic en "Agregar Turno" para comenzar.'})})})]})}})})}});export{Q as A,X as C,Y as S,Z as T,U as a,z as d};
