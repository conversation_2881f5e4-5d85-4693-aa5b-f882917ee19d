import{u as f,b as D,y as b,j as e,r as h}from"./main-RY8ZkTMc.js";import{C as T,c as U,u as B,B as $,g as J}from"./BasicTable-RsYQtHbn.js";import{c as v,a as F}from"./classes-CraQI9Rs.js";import{A,W as g,o as X,a as Z,n as I,p as x,b as ee,c as re,d as R,u as oe,m as y,s as u,l as se,e as ae,f as i,D as C}from"./runtimes-CTOS42-v.js";import{u as z}from"./form-BdoD3Q87.js";import{g as j}from"./effectErrors-D8W8e9uM.js";import{u as k}from"./useMutation-D8585ZL-.js";import{q as O}from"./queryOptions-C9woPjwX.js";import{U as m}from"./user-BHfkra8r.js";import{M as Q,a as q,P as w,T as L,b as ne}from"./TextModal-DBwVHEpd.js";import{C as W}from"./calendar-C7hPlekH.js";import{F as G}from"./file-text-DYqz-Dos.js";import{u as K}from"./useQuery-D9VtETml.js";import{c as le}from"./createLucideIcon-78bvTjT9.js";/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const te=[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]],ie=le("pen",te),N=({worker:s})=>O({queryKey:["workers"],queryFn:()=>A.runPromise(s.getAll())}),ce=({worker:s},l)=>O({queryKey:["workers",l],queryFn:()=>A.runPromise(s.getById(l))});function de(){const s=f(),{worker:l}=s,o=D(),a=N(s).queryKey;return k({mutationKey:["create-worker"],mutationFn:r=>A.runPromise(l.create(r)),onMutate:async r=>{await o.cancelQueries({queryKey:a});const n=o.getQueryData(a);return n?o.setQueryData(a,v(n,t=>{t.push(g.make({id:"new",...r,person:{id:"new",...r.person,createdAt:null,updatedAt:null,deletedAt:null},createdAt:null,updatedAt:null,deletedAt:null}))})):o.setQueryData(a,[g.make({id:"new",...r,person:{id:"new",...r.person,createdAt:null,updatedAt:null,deletedAt:null},createdAt:null,updatedAt:null,deletedAt:null})]),{previousWorkers:n}},onError:(r,n,t)=>{o.setQueryData(a,t==null?void 0:t.previousWorkers)},onSettled:()=>{o.invalidateQueries({queryKey:a})}})}const Y=X({name:x(u("Debe ingresar una cuenta"),y(1,"Debe tener al menos un caracter")),fatherLastName:x(u("Debe ingresar un apellido"),y(1,"Debe tener al menos un caracter")),motherLastName:x(u("Debe ingresar un apellido"),y(1,"Debe tener al menos un caracter")),email:oe([x(u("Debe ingresar un email"),ae("Debe ingresar un email valido")),se("")]),address:R(u()),phone:R(u()),birthDate:re(u()),gender:ee(),document:x(u("Debe ingresar un documento"),y(8,"Debe tener al menos 8 caracteres")),documentType:I(),positions:Z(I())}),ue={name:"",fatherLastName:"",motherLastName:"",email:"",address:"",phone:"",birthDate:"",gender:!1,document:"",documentType:0,positions:[]};function pe({setIsOpen:s}){const{mutate:l}=de(),o=z({defaultValues:ue,validators:{onChange:Y},onSubmit:({value:r})=>{const{positions:n,...t}=r;l({person:t,positions:r.positions??[]},{onSuccess:()=>{b.success("Trabajador creado"),a()},onError:c=>{console.log(c);const{error:p}=j(c);b.error(p.message)}})}});function a(){o.reset(),s(!1)}return{form:o,handleClose:a}}function me({isOpen:s,setIsOpen:l}){const{form:o,handleClose:a}=pe({setIsOpen:l});return e.jsx("div",{className:F("modal",s&&"modal-open"),children:e.jsxs("div",{className:"modal-box",children:[e.jsx(T,{onClose:a}),e.jsx("h3",{className:"font-bold text-lg",children:"Crear Trabajador"}),e.jsx("form",{onSubmit:r=>{r.preventDefault(),o.handleSubmit()},children:e.jsxs(o.AppForm,{children:[e.jsxs("fieldset",{className:"fieldset",children:[e.jsx(o.AppField,{name:"positions",children:({FSComboBoxField:r})=>e.jsx(r,{label:"Posición",placeholder:"Posición",isMultiple:!0,options:[{value:i.MANAGER,label:"Gerente"},{value:i.ADMINISTRATOR,label:"Administrador"},{value:i.INTERN,label:"Interno"},{value:i.PSYCHOLOGIST,label:"Psicólogo"},{value:i.THERAPIST,label:"Terapeuta"}]})}),e.jsx(o.AppField,{name:"name",children:({FSTextField:r})=>e.jsx(r,{label:"Nombre",placeholder:"Nombre",prefixComponent:e.jsx(m,{size:16})})}),e.jsx(o.AppField,{name:"fatherLastName",children:({FSTextField:r})=>e.jsx(r,{label:"Apellido Paterno",placeholder:"Apellido Paterno",prefixComponent:e.jsx(m,{size:16})})}),e.jsx(o.AppField,{name:"motherLastName",children:({FSTextField:r})=>e.jsx(r,{label:"Apellido Materno",placeholder:"Apellido Materno",prefixComponent:e.jsx(m,{size:16})})}),e.jsx(o.AppField,{name:"email",children:({FSTextField:r})=>e.jsx(r,{label:"Correo Electrónico",placeholder:"<EMAIL>",prefixComponent:e.jsx(Q,{size:16})})}),e.jsx(o.AppField,{name:"address",children:({FSTextField:r})=>e.jsx(r,{label:"Dirección",placeholder:"Dirección",prefixComponent:e.jsx(q,{size:16})})}),e.jsx(o.AppField,{name:"phone",children:({FSTextField:r})=>e.jsx(r,{label:"Teléfono",placeholder:"Teléfono",prefixComponent:e.jsx(w,{size:16})})}),e.jsx(o.AppField,{name:"birthDate",children:({FSTextField:r})=>e.jsx(r,{label:"Fecha de Nacimiento",placeholder:"YYYY-MM-DD",type:"date",prefixComponent:e.jsx(W,{size:16})})}),e.jsx(o.AppField,{name:"document",children:({FSTextField:r})=>e.jsx(r,{label:"Documento",placeholder:"Número de Documento",prefixComponent:e.jsx(G,{size:16})})}),e.jsx(o.AppField,{name:"documentType",children:({FSSelectField:r})=>e.jsx(r,{label:"Tipo de Documento",placeholder:"Tipo de Documento",isNumber:!0,options:[{value:0,label:"DNI"},{value:1,label:"Pasaporte"},{value:2,label:"RUC"}]})}),e.jsx(o.AppField,{name:"gender",children:({FSToggleField:r})=>e.jsx(r,{label:"Género",trueLabel:"Masculino",falseLabel:"Femenino"})})]}),e.jsx("div",{className:"modal-action",children:e.jsx("button",{type:"submit",className:"btn btn-primary",children:"Crear"})})]})})]})})}function xe(){const s=f(),{worker:l}=s,o=D(),a=N(s).queryKey;return k({mutationKey:["delete-worker"],mutationFn:r=>A.runPromise(l.delete(r)),onMutate:async r=>{await o.cancelQueries({queryKey:a});const n=o.getQueryData(a);return n&&o.setQueryData(a,v(n,t=>{const c=t.findIndex(p=>p.id===r);c!==-1&&t.splice(c,1)})),{previousWorkers:n}},onError:(r,n,t)=>{o.setQueryData(a,t==null?void 0:t.previousWorkers)},onSettled:()=>{o.invalidateQueries({queryKey:a})}})}function be({isOpen:s,setIsOpen:l,id:o}){const{mutate:a}=xe();return e.jsx("div",{className:F("modal",s&&"modal-open"),children:e.jsxs("div",{className:"modal-box",children:[e.jsx(T,{onClose:()=>l(!1)}),e.jsx("h3",{className:"font-bold text-lg",children:"Eliminar trabajador"}),e.jsx("p",{children:"¿Estás seguro de que quieres eliminar este trabajador?"}),e.jsxs("div",{className:"modal-action",children:[e.jsx("button",{type:"button",className:"btn btn-primary",onClick:()=>l(!1),children:"Cancelar"}),e.jsx("button",{type:"button",className:"btn btn-error",onClick:()=>{a(o,{onSuccess:()=>{b.success("Trabajador eliminado"),l(!1)},onError:r=>{console.log(r)}})},children:"Eliminar"})]})]})})}function he(){const s=f(),{worker:l}=s,o=D(),a=N(s).queryKey;return k({mutationKey:["update-worker"],mutationFn:r=>A.runPromise(l.update(r)),onMutate:async r=>{await o.cancelQueries({queryKey:a});const n=o.getQueryData(a);return n&&o.setQueryData(a,v(n,t=>{const c=t.findIndex(p=>p.id===r.id);c!==-1&&(t[c]=g.make({...r,person:{...r.person,createdAt:null,updatedAt:null,deletedAt:null},createdAt:null,updatedAt:null,deletedAt:null}))})),{previousWorkers:n}},onError:(r,n,t)=>{o.setQueryData(a,t==null?void 0:t.previousWorkers)},onSettled:()=>{o.invalidateQueries({queryKey:a})}})}function je({setIsOpen:s,worker:l}){const{mutate:o}=he(),{id:a,createdAt:r,updatedAt:n,deletedAt:t,...c}=l,{id:p,createdAt:ge,updatedAt:De,deletedAt:Te,...V}=c.person,M=z({defaultValues:{...V,positions:c.positions},validators:{onChange:Y},onSubmit:({value:E})=>{const{positions:ve,..._}=E;o({id:a,person:{..._,id:p},positions:E.positions??[]},{onSuccess:()=>{b.success("Trabajador actualizado"),P()},onError:S=>{console.log(S);const{error:H}=j(S);b.error(H.message)}})}});function P(){M.reset(),s(!1)}return{form:M,handleClose:P}}function fe({isOpen:s,setIsOpen:l,worker:o}){const{form:a,handleClose:r}=je({setIsOpen:l,worker:o});return e.jsx("div",{className:F("modal",s&&"modal-open"),children:e.jsxs("div",{className:"modal-box",children:[e.jsx(T,{onClose:r}),e.jsx("h3",{className:"font-bold text-lg",children:"Crear Trabajador"}),e.jsx("form",{onSubmit:n=>{n.preventDefault(),a.handleSubmit()},children:e.jsxs(a.AppForm,{children:[e.jsxs("fieldset",{className:"fieldset",children:[e.jsx(a.AppField,{name:"positions",children:({FSComboBoxField:n})=>e.jsx(n,{label:"Posición",placeholder:"Posición",isMultiple:!0,options:[{value:i.MANAGER,label:"Gerente"},{value:i.ADMINISTRATOR,label:"Administrador"},{value:i.INTERN,label:"Interno"},{value:i.PSYCHOLOGIST,label:"Psicólogo"},{value:i.THERAPIST,label:"Terapeuta"}]})}),e.jsx(a.AppField,{name:"name",children:({FSTextField:n})=>e.jsx(n,{label:"Nombre",placeholder:"Nombre",prefixComponent:e.jsx(m,{size:16})})}),e.jsx(a.AppField,{name:"fatherLastName",children:({FSTextField:n})=>e.jsx(n,{label:"Apellido Paterno",placeholder:"Apellido Paterno",prefixComponent:e.jsx(m,{size:16})})}),e.jsx(a.AppField,{name:"motherLastName",children:({FSTextField:n})=>e.jsx(n,{label:"Apellido Materno",placeholder:"Apellido Materno",prefixComponent:e.jsx(m,{size:16})})}),e.jsx(a.AppField,{name:"email",children:({FSTextField:n})=>e.jsx(n,{label:"Correo Electrónico",placeholder:"<EMAIL>",prefixComponent:e.jsx(Q,{size:16})})}),e.jsx(a.AppField,{name:"address",children:({FSTextField:n})=>e.jsx(n,{label:"Dirección",placeholder:"Dirección",prefixComponent:e.jsx(q,{size:16})})}),e.jsx(a.AppField,{name:"phone",children:({FSTextField:n})=>e.jsx(n,{label:"Teléfono",placeholder:"Teléfono",prefixComponent:e.jsx(w,{size:16})})}),e.jsx(a.AppField,{name:"birthDate",children:({FSTextField:n})=>e.jsx(n,{label:"Fecha de Nacimiento",placeholder:"YYYY-MM-DD",type:"date",prefixComponent:e.jsx(W,{size:16})})}),e.jsx(a.AppField,{name:"document",children:({FSTextField:n})=>e.jsx(n,{label:"Documento",placeholder:"Número de Documento",prefixComponent:e.jsx(G,{size:16})})}),e.jsx(a.AppField,{name:"documentType",children:({FSSelectField:n})=>e.jsx(n,{label:"Tipo de Documento",placeholder:"Tipo de Documento",isNumber:!0,options:[{value:0,label:"DNI"},{value:1,label:"Pasaporte"},{value:2,label:"RUC"}]})}),e.jsx(a.AppField,{name:"gender",children:({FSToggleField:n})=>e.jsx(n,{label:"Género",trueLabel:"Masculino",falseLabel:"Femenino"})})]}),e.jsx("div",{className:"modal-action",children:e.jsx("button",{type:"submit",className:"btn btn-primary",children:"Crear"})})]})})]})})}function Ae({isOpen:s,setIsOpen:l,id:o}){const a=f(),{data:r,isError:n,error:t,isPending:c}=K({...ce(a,o),enabled:s});return h.useEffect(()=>{t&&console.log(t)},[t]),c?e.jsx(L,{text:"Cargando..."}):n?e.jsx(L,{text:"No se pudo cargar el trabajador",title:j(t).error.code.toString()}):e.jsx(fe,{isOpen:s,setIsOpen:l,worker:r})}const d=U(),ye=[d.accessor("person.name",{header:"Name",cell:s=>s.getValue()??"-"}),d.display({header:"Apellidos",cell:({row:s})=>`${s.original.person.fatherLastName} ${s.original.person.motherLastName}`}),d.accessor("person.email",{header:"Email",cell:s=>s.getValue()??"-"}),d.accessor("person.address",{header:"Dirección",cell:s=>s.getValue()??"-"}),d.accessor("person.phone",{header:"Teléfono",cell:s=>s.getValue()??"-"}),d.accessor("person.birthDate",{header:"Fecha de Nacimiento",cell:s=>s.getValue()??"-"}),d.accessor("person.document",{header:"Documento",cell:s=>s.getValue()??"-"}),d.accessor("person.documentType",{header:"Tipo de Documento",cell:s=>({[C.DNI]:"DNI",[C.PASAPORTE]:"Pasaporte",[C.RUC]:"RUC"})[s.getValue()]??"-"}),d.accessor("positions",{header:"Positions",cell:s=>{const l={[i.MANAGER]:"Gerente",[i.ADMINISTRATOR]:"Administrador",[i.INTERN]:"Interno",[i.PSYCHOLOGIST]:"Psicólogo",[i.THERAPIST]:"Terapeuta"};return s.getValue().map(o=>l[o]).join(", ")}}),d.display({header:"Acciones",cell:({row:s})=>{const[l,o]=h.useState(!1),[a,r]=h.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{type:"button",className:"btn btn-circle btn-primary",onClick:()=>o(!0),children:e.jsx(ie,{size:16})}),e.jsx("button",{type:"button",className:"btn btn-circle btn-error",onClick:()=>r(!0),children:e.jsx(ne,{size:16})})]}),e.jsx(Ae,{isOpen:l,setIsOpen:o,id:s.original.id}),e.jsx(be,{isOpen:a,setIsOpen:r,id:s.original.id})]})}})];function Ne({workers:s}){const l=B({data:s,columns:ye,getCoreRowModel:J()});return e.jsx($,{table:l})}function Ce(){const s=f(),{data:l,isError:o,error:a,isPending:r}=K(N(s));return h.useEffect(()=>{a&&console.log(j(a).error)},[a]),o?e.jsxs("div",{children:["Error: ",j(a).error.message]}):r?e.jsx("div",{children:"Loading..."}):e.jsx(Ne,{workers:l})}const We=function(){const[l,o]=h.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"container mx-auto",children:e.jsx("div",{className:"card bg-base-300",children:e.jsxs("div",{className:"card-body",children:[e.jsx("div",{children:e.jsx("button",{type:"button",className:"btn btn-primary",onClick:()=>o(!0),children:"Nuevo trabajador"})}),e.jsx(Ce,{})]})})}),e.jsx(me,{isOpen:l,setIsOpen:o})]})};export{We as component};
