var qu=Object.defineProperty;var Bu=(e,t,n)=>t in e?qu(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var i=(e,t,n)=>Bu(e,typeof t!="symbol"?t+"":t,n);import{B as S,C as Ju,E as At,F as zu,G as nt,H as pr,I as Rs,J as Xr,K as Os,M as Wu,P as Ie,Q as Oe,T as pt,U as ce,V as re,W as xe,X as Ts,Y as Hu,Z as at,_ as Vu,$ as Qu,a0 as Gu,a1 as Yu,a2 as Xu,a3 as Zu,a4 as el,a5 as tl,a6 as Zr,a7 as Na,a8 as nl,a9 as Fa,aa as Zn,ab as rl,ac as we,ad as $t,ae as La,af as Cs,ag as mr,ah as $s,ai as Da,aj as sl,ak as ol,al,am as il,an as cl,ao as ul,ap as ll,aq as dl,ar as hl,as as fl,at as pl,au as ml,av as yl,aw as gl,ax as Sl,ay as _l,az as bl,aA as wl,aB as El,aC as p,aD as Pt,aE as ht,aF as er,aG as kl,aH as Rl,aI as Qe,aJ as Ol,aK as ut,aL as $e,aM as Tl,aN as vn,aO as Cl,aP as Ua,aQ as $l,aR as Pl,aS as Ma,aT as ja,aU as Ge,aV as tr,aW as vl,aX as Al,aY as Il,aZ as Ka,a_ as qa,a$ as po,b0 as xl,b1 as Ba,b2 as Cr,b3 as mo,b4 as Ja,b5 as Nl,b6 as za,b7 as fe,b8 as tn,b9 as rt,ba as I,bb as Fl,bc as yr,bd as Z,be as An,bf as nr,bg as Ll,bh as Dl,bi as Ul,bj as m,bk as Ml,bl as Rt,bm as X,bn as Wa,bo as yo,bp as jl,bq as mt,br as Kl,bs as ql,bt as W,bu as K,bv as Bl,bw as Jl,bx as Ha,by as es,bz as tt,bA as zl,bB as st,bC as Pe,bD as Xe,bE as Wl,bF as ve,bG as Ht,bH as Sn,bI as L,bJ as ts,bK as Me,bL as Ae,bM as yt,bN as U,bO as Hl,bP as Vl,bQ as Ql,bR as Va,bS as Gl,bT as Qa,bU as qt,bV as dn,bW as Yl,bX as Lt,bY as $,bZ as N,b_ as Xl,b$ as ns,c0 as rs,c1 as Zl,c2 as Ga,c3 as ss,c4 as Ya,c5 as ed,c6 as td,c7 as nd,c8 as go,c9 as Rn,ca as rd,cb as Xa,cc as sd,cd as $r,ce as Pr,cf as od,cg as So,ch as ad,ci as Za,cj as os,ck as ei,cl as id,cm as cd,cn as _o,co as ud,cp as ld,cq as ti,cr as ni,cs as dd,ct as Ps,cu as hd,cv as Ot,cw as fd,cx as pd,cy as md,cz as On,cA as _e,cB as yd,cC as gd,cD as ri,cE as Sd,cF as _d,cG as bd,cH as wd,cI as bo,cJ as Ed,cK as kd,cL as Rd,cM as Od,cN as vs,cO as Td,cP as vr,cQ as Cd,cR as Te,cS as Dt,cT as $d,cU as As,cV as Pd,cW as vd,cX as Ad,cY as Ar,cZ as si,c_ as Id,c$ as zn,d0 as xd,d1 as gr,d2 as Nd,d3 as Fd,d4 as Ld,d5 as wo,d6 as Dd,d7 as Eo,d8 as Ud,d9 as Md,da as jd,db as ko,dc as Kd,dd as Ir,de as qd,df as Ro,dg as Bd,dh as Jd,di as zd,dj as Wd,dk as Hd,dl as Vd,dm as Qd,dn as Gd,dp as Yd,dq as Sr,dr as Xd,ds as Zd,dt as eh,du as th,dv as nh,dw as oi,dx as rh,dy as sh,dz as xr,dA as oh,dB as ah,dC as ih,dD as ch,dE as uh,dF as lh,dG as dh,dH as hh,dI as fh,dJ as ph,dK as mh,dL as yh,dM as gh,dN as ai,dO as rr,dP as Ne,dQ as nn,dR as Sh,dS as _h,dT as bh,dU as wh}from"./main-RY8ZkTMc.js";const Eh=S(2,(e,t)=>{const n={...e};for(const r of kh(e))n[r]=t(e[r],r);return n}),kh=e=>Object.keys(e),ii=(e,t)=>{switch(t._tag){case"StringKeyword":case"TemplateLiteral":return Object.keys(e);case"SymbolKeyword":return Object.getOwnPropertySymbols(e);case"Refinement":return ii(e,t.from)}},Gt=e=>Object.keys(e).concat(Object.getOwnPropertySymbols(e)),ci=e=>{let t=!1,n;return()=>(t||(n=e(),t=!0),n)},Rh=e=>{try{return e.toISOString()}catch{return String(e)}},Ke=(e,t=!0)=>{if(Array.isArray(e))return`[${e.map(n=>Ke(n,t)).join(",")}]`;if(Ju(e))return Rh(e);if(At(e,"toString")&&zu(e.toString)&&e.toString!==Object.prototype.toString)return e.toString();if(nt(e))return JSON.stringify(e);if(pr(e)||e==null||Rs(e)||Xr(e))return String(e);if(Os(e))return String(e)+"n";if(Wu(e))return`${e.constructor.name}(${Ke(Array.from(e),t)})`;try{t&&JSON.stringify(e);const n=`{${Gt(e).map(s=>`${nt(s)?JSON.stringify(s):String(s)}:${Ke(e[s],!1)}`).join(",")}}`,r=e.constructor.name;return e.constructor!==Object.prototype.constructor?`${r}(${n})`:n}catch{return"<circular structure>"}},Oh=e=>typeof e=="string"?JSON.stringify(e):String(e),ui=e=>Array.isArray(e),Oo=e=>`[${Oh(e)}]`,Th=e=>ui(e)?e.map(Oo).join(""):Oo(e),It=(e,t,n,r)=>{let s=e;return t!==void 0&&(s+=`
details: ${t}`),r&&(s+=`
schema (${r._tag}): ${r}`),s},Ch=e=>It("Unsupported key schema",void 0,void 0,e),$h=e=>It("Unsupported literal",`literal value: ${Ke(e)}`),To=e=>It("Duplicate index signature",`${e} index signature`),Ph=It("Unsupported index signature parameter","An index signature parameter type must be `string`, `symbol`, a template literal type or a refinement of the previous types"),vh=It("Invalid element","A required element cannot follow an optional element. ts(1257)"),Co=e=>It("Duplicate property signature transformation",`Duplicate key ${Ke(e)}`),Ah=e=>It("Duplicate property signature",`Duplicate key ${Ke(e)}`),Ih=Symbol.for("effect/annotation/Brand"),xh=Symbol.for("effect/annotation/SchemaId"),li=Symbol.for("effect/annotation/Message"),Is=Symbol.for("effect/annotation/MissingMessage"),di=Symbol.for("effect/annotation/Identifier"),it=Symbol.for("effect/annotation/Title"),as=Symbol.for("effect/annotation/AutoTitle"),Dn=Symbol.for("effect/annotation/Description"),hi=Symbol.for("effect/annotation/Examples"),fi=Symbol.for("effect/annotation/Default"),pi=Symbol.for("effect/annotation/JSONSchema"),mi=Symbol.for("effect/annotation/Arbitrary"),yi=Symbol.for("effect/annotation/Pretty"),gi=Symbol.for("effect/annotation/Equivalence"),Nh=Symbol.for("effect/annotation/Documentation"),Si=Symbol.for("effect/annotation/Concurrency"),_i=Symbol.for("effect/annotation/Batching"),bi=Symbol.for("effect/annotation/ParseIssueTitle"),wi=Symbol.for("effect/annotation/ParseOptions"),Ei=Symbol.for("effect/annotation/DecodingFallback"),ki=Symbol.for("effect/annotation/Surrogate"),Fh=Symbol.for("effect/annotation/StableFilter"),ue=S(2,(e,t)=>Object.prototype.hasOwnProperty.call(e.annotations,t)?ce(e.annotations[t]):re()),Lh=ue(Ih),Dh=ue(li),Uh=ue(Is),Ri=ue(it),Oi=ue(as),_r=ue(di),Ti=ue(Dn),Mh=ue(Si),jh=ue(_i),Kh=ue(bi),qh=ue(wi),Bh=ue(Ei),Ci=ue(ki),Jh=ue(Fh),zh=e=>Zu(Jh(e),t=>t===!0),$i=Symbol.for("effect/annotation/JSONIdentifier"),Wh=ue($i),Hh=e=>pt(Wh(e),()=>_r(e));class Pi{constructor(t,n,r,s={}){i(this,"typeParameters");i(this,"decodeUnknown");i(this,"encodeUnknown");i(this,"annotations");i(this,"_tag","Declaration");this.typeParameters=t,this.decodeUnknown=n,this.encodeUnknown=r,this.annotations=s}toString(){return Oe(Je(this),()=>"<declaration schema>")}toJSON(){return{_tag:this._tag,typeParameters:this.typeParameters.map(t=>t.toJSON()),annotations:J(this.annotations)}}}const Un=e=>t=>t._tag===e;class vi{constructor(t,n={}){i(this,"literal");i(this,"annotations");i(this,"_tag","Literal");this.literal=t,this.annotations=n}toString(){return Oe(Je(this),()=>Ke(this.literal))}toJSON(){return{_tag:this._tag,literal:Os(this.literal)?String(this.literal):this.literal,annotations:J(this.annotations)}}}const $o=Un("Literal"),Vh=new vi(null);class Qh{constructor(t,n={}){i(this,"symbol");i(this,"annotations");i(this,"_tag","UniqueSymbol");this.symbol=t,this.annotations=n}toString(){return Oe(Je(this),()=>Ke(this.symbol))}toJSON(){return{_tag:this._tag,symbol:String(this.symbol),annotations:J(this.annotations)}}}class Gh{constructor(t={}){i(this,"annotations");i(this,"_tag","UndefinedKeyword");this.annotations=t}toString(){return xt(this)}toJSON(){return{_tag:this._tag,annotations:J(this.annotations)}}}const is=new Gh({[it]:"undefined"});class Yh{constructor(t={}){i(this,"annotations");i(this,"_tag","NeverKeyword");this.annotations=t}toString(){return xt(this)}toJSON(){return{_tag:this._tag,annotations:J(this.annotations)}}}const xs=new Yh({[it]:"never"});class Xh{constructor(t={}){i(this,"annotations");i(this,"_tag","UnknownKeyword");this.annotations=t}toString(){return xt(this)}toJSON(){return{_tag:this._tag,annotations:J(this.annotations)}}}const Ai=new Xh({[it]:"unknown"});class Zh{constructor(t={}){i(this,"annotations");i(this,"_tag","AnyKeyword");this.annotations=t}toString(){return xt(this)}toJSON(){return{_tag:this._tag,annotations:J(this.annotations)}}}const ef=new Zh({[it]:"any"});class tf{constructor(t={}){i(this,"annotations");i(this,"_tag","StringKeyword");this.annotations=t}toString(){return xt(this)}toJSON(){return{_tag:this._tag,annotations:J(this.annotations)}}}const nf=new tf({[it]:"string",[Dn]:"a string"}),rf=Un("StringKeyword");class sf{constructor(t={}){i(this,"annotations");i(this,"_tag","NumberKeyword");this.annotations=t}toString(){return xt(this)}toJSON(){return{_tag:this._tag,annotations:J(this.annotations)}}}const of=new sf({[it]:"number",[Dn]:"a number"});class af{constructor(t={}){i(this,"annotations");i(this,"_tag","BooleanKeyword");this.annotations=t}toString(){return xt(this)}toJSON(){return{_tag:this._tag,annotations:J(this.annotations)}}}const cf=new af({[it]:"boolean",[Dn]:"a boolean"}),uf=Un("SymbolKeyword");let br=class{constructor(t,n={}){i(this,"type");i(this,"annotations");this.type=t,this.annotations=n}toJSON(){return{type:this.type.toJSON(),annotations:J(this.annotations)}}toString(){return String(this.type)}};class rn extends br{constructor(n,r,s={}){super(n,s);i(this,"isOptional");this.isOptional=r}toJSON(){return{type:this.type.toJSON(),isOptional:this.isOptional,annotations:J(this.annotations)}}toString(){return String(this.type)+(this.isOptional?"?":"")}}const Ii=e=>e.map(t=>t.type);class wr{constructor(t,n,r,s={}){i(this,"elements");i(this,"rest");i(this,"isReadonly");i(this,"annotations");i(this,"_tag","TupleType");this.elements=t,this.rest=n,this.isReadonly=r,this.annotations=s;let o=!1,a=!1;for(const c of t)if(c.isOptional)o=!0;else if(o){a=!0;break}if(a||o&&n.length>1)throw new Error(vh)}toString(){return Oe(Je(this),()=>lf(this))}toJSON(){return{_tag:this._tag,elements:this.elements.map(t=>t.toJSON()),rest:this.rest.map(t=>t.toJSON()),isReadonly:this.isReadonly,annotations:J(this.annotations)}}}const lf=e=>{const t=e.elements.map(String).join(", ");return Xu(e.rest,{onEmpty:()=>`readonly [${t}]`,onNonEmpty:(n,r)=>{const s=String(n),o=s.includes(" | ")?`(${s})`:s;if(r.length>0){const a=r.map(String).join(", ");return e.elements.length>0?`readonly [${t}, ...${o}[], ${a}]`:`readonly [...${o}[], ${a}]`}else return e.elements.length>0?`readonly [${t}, ...${o}[]]`:`ReadonlyArray<${s}>`}})};class G extends rn{constructor(n,r,s,o,a){super(r,s,a);i(this,"name");i(this,"isReadonly");this.name=n,this.isReadonly=o}toString(){return(this.isReadonly?"readonly ":"")+String(this.name)+(this.isOptional?"?":"")+": "+this.type}toJSON(){return{name:String(this.name),type:this.type.toJSON(),isOptional:this.isOptional,isReadonly:this.isReadonly,annotations:J(this.annotations)}}}const xi=e=>{switch(e._tag){case"StringKeyword":case"SymbolKeyword":case"TemplateLiteral":return!0;case"Refinement":return xi(e.from)}return!1};class Mn{constructor(t,n,r){i(this,"type");i(this,"isReadonly");i(this,"parameter");if(this.type=n,this.isReadonly=r,xi(t))this.parameter=t;else throw new Error(Ph)}toString(){return(this.isReadonly?"readonly ":"")+`[x: ${this.parameter}]: ${this.type}`}toJSON(){return{parameter:this.parameter.toJSON(),type:this.type.toJSON(),isReadonly:this.isReadonly}}}class gt{constructor(t,n,r={}){i(this,"annotations");i(this,"_tag","TypeLiteral");i(this,"propertySignatures");i(this,"indexSignatures");this.annotations=r;const s={};for(let a=0;a<t.length;a++){const c=t[a].name;if(Object.prototype.hasOwnProperty.call(s,c))throw new Error(Ah(c));s[c]=null}const o={string:!1,symbol:!1};for(let a=0;a<n.length;a++){const c=Ki(n[a].parameter);if(rf(c)){if(o.string)throw new Error(To("string"));o.string=!0}else if(uf(c)){if(o.symbol)throw new Error(To("symbol"));o.symbol=!0}}this.propertySignatures=t,this.indexSignatures=n}toString(){return Oe(Je(this),()=>df(this))}toJSON(){return{_tag:this._tag,propertySignatures:this.propertySignatures.map(t=>t.toJSON()),indexSignatures:this.indexSignatures.map(t=>t.toJSON()),annotations:J(this.annotations)}}}const Po=e=>e.map(String).join("; "),df=e=>{if(e.propertySignatures.length>0){const t=e.propertySignatures.map(String).join("; ");return e.indexSignatures.length>0?`{ ${t}; ${Po(e.indexSignatures)} }`:`{ ${t} }`}else return e.indexSignatures.length>0?`{ ${Po(e.indexSignatures)} }`:"{}"},hf=Yu(el(tl,e=>{switch(e._tag){case"AnyKeyword":return 0;case"UnknownKeyword":return 1;case"ObjectKeyword":return 2;case"StringKeyword":case"NumberKeyword":case"BooleanKeyword":case"BigIntKeyword":case"SymbolKeyword":return 3}return 4})),ff={string:"StringKeyword",number:"NumberKeyword",boolean:"BooleanKeyword",bigint:"BigIntKeyword"},Ni=e=>Qu(e,t=>Li(t)?Ni(t.types):[t]),pf=e=>{const t=hf(e),n=[],r={},s=[];for(const o of t)switch(o._tag){case"NeverKeyword":break;case"AnyKeyword":return[ef];case"UnknownKeyword":return[Ai];case"ObjectKeyword":case"UndefinedKeyword":case"VoidKeyword":case"StringKeyword":case"NumberKeyword":case"BooleanKeyword":case"BigIntKeyword":case"SymbolKeyword":{r[o._tag]||(r[o._tag]=o,n.push(o));break}case"Literal":{const a=typeof o.literal;switch(a){case"string":case"number":case"bigint":case"boolean":{const c=ff[a];!r[c]&&!s.includes(o.literal)&&(s.push(o.literal),n.push(o));break}case"object":{s.includes(o.literal)||(s.push(o.literal),n.push(o));break}}break}case"UniqueSymbol":{!r.SymbolKeyword&&!s.includes(o.symbol)&&(s.push(o.symbol),n.push(o));break}case"TupleType":{r.ObjectKeyword||n.push(o);break}case"TypeLiteral":{o.propertySignatures.length===0&&o.indexSignatures.length===0?r["{}"]||(r["{}"]=o,n.push(o)):r.ObjectKeyword||n.push(o);break}default:n.push(o)}return n};var Ye;let Ue=(Ye=class{constructor(t,n={}){i(this,"types");i(this,"annotations");i(this,"_tag","Union");this.types=t,this.annotations=n}toString(){return Oe(Je(this),()=>this.types.map(String).join(" | "))}toJSON(){return{_tag:this._tag,types:this.types.map(t=>t.toJSON()),annotations:J(this.annotations)}}},i(Ye,"make",(t,n)=>Fi(t)?new Ye(t,n):t.length===1?t[0]:xs),i(Ye,"unify",(t,n)=>Ye.make(pf(Ni(t)),n)),Ye);const Fi=e=>e.length>1,Li=Un("Union"),Nr=at(Symbol.for("effect/Schema/AST/toJSONMemoMap"),()=>new WeakMap);class Ns{constructor(t,n={}){i(this,"f");i(this,"annotations");i(this,"_tag","Suspend");this.f=t,this.annotations=n,this.f=ci(t)}toString(){return Je(this).pipe(pt(()=>Ts(Hu(this.f)(),t=>Je(t))),Oe(()=>"<suspended schema>"))}toJSON(){const t=this.f();let n=Nr.get(t);return n||(Nr.set(t,{_tag:this._tag}),n={_tag:this._tag,ast:t.toJSON(),annotations:J(this.annotations)},Nr.set(t,n),n)}}let Di=class{constructor(t,n,r={}){i(this,"from");i(this,"filter");i(this,"annotations");i(this,"_tag","Refinement");this.from=t,this.filter=n,this.annotations=r}toString(){return _r(this).pipe(Oe(()=>xe(qi(this),{onNone:()=>`{ ${this.from} | filter }`,onSome:t=>Fs(this.from)?String(this.from)+" & "+t:t})))}toJSON(){return{_tag:this._tag,from:this.from.toJSON(),annotations:J(this.annotations)}}};const Fs=Un("Refinement"),Fr={};let Ls=class{constructor(t,n,r,s={}){i(this,"from");i(this,"to");i(this,"transformation");i(this,"annotations");i(this,"_tag","Transformation");this.from=t,this.to=n,this.transformation=r,this.annotations=s}toString(){return Oe(Je(this),()=>`(${String(this.from)} <-> ${String(this.to)})`)}toJSON(){return{_tag:this._tag,from:this.from.toJSON(),to:this.to.toJSON(),annotations:J(this.annotations)}}};class mf{constructor(t,n){i(this,"decode");i(this,"encode");i(this,"_tag","FinalTransformation");this.decode=t,this.encode=n}}let yf=class{constructor(t,n,r,s){i(this,"from");i(this,"to");i(this,"decode");i(this,"encode");this.from=t,this.to=n,this.decode=r,this.encode=s}};class gf{constructor(t){i(this,"propertySignatureTransformations");i(this,"_tag","TypeLiteralTransformation");this.propertySignatureTransformations=t;const n={},r={};for(const s of t){const o=s.from;if(n[o])throw new Error(Co(o));n[o]=!0;const a=s.to;if(r[a])throw new Error(Co(a));r[a]=!0}}}const Xt=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={...e.annotations,...t},s=Ci(e);return Ie(s)&&(r[ki]=Xt(s.value,t)),n.annotations.value=r,Object.create(Object.getPrototypeOf(e),n)},Sf="[\\s\\S]*",_f="[+-]?\\d*\\.?\\d+(?:[Ee][+-]?\\d+)?",Ui=(e,t)=>{switch(e._tag){case"Literal":return Zr(String(e.literal));case"StringKeyword":return Sf;case"NumberKeyword":return _f;case"TemplateLiteral":return Mi(e);case"Union":return e.types.map(n=>Ui(n)).join("|")}},bf=(e,t,n,r)=>Li(e)?`(${t})`:t,Mi=(e,t,n)=>{let r="";if(e.head!==""){const s=Zr(e.head);r+=s}for(const s of e.spans){const o=Ui(s.type);if(r+=bf(s.type,o),s.literal!==""){const a=Zr(s.literal);r+=a}}return r},wf=e=>new RegExp(`^${Mi(e)}$`),vo=(e,t)=>{const n=[],r=[],s=o=>{switch(o._tag){case"NeverKeyword":break;case"StringKeyword":case"SymbolKeyword":case"TemplateLiteral":case"Refinement":r.push(new Mn(o,t,!0));break;case"Literal":if(nt(o.literal)||pr(o.literal))n.push(new G(o.literal,t,!1,!0));else throw new Error($h(o.literal));break;case"Enums":{for(const[a,c]of o.enums)n.push(new G(c,t,!1,!0));break}case"UniqueSymbol":n.push(new G(o.symbol,t,!1,!0));break;case"Union":o.types.forEach(s);break;default:throw new Error(Ch(o))}};return s(e),{propertySignatures:n,indexSignatures:r}},Bt=e=>{switch(e._tag){case"TupleType":return e.isReadonly===!1?e:new wr(e.elements,e.rest,!1,e.annotations);case"TypeLiteral":{const t=ae(e.propertySignatures,r=>r.isReadonly===!1?r:new G(r.name,r.type,r.isOptional,!1,r.annotations)),n=ae(e.indexSignatures,r=>r.isReadonly===!1?r:new Mn(r.parameter,r.type,!1));return t===e.propertySignatures&&n===e.indexSignatures?e:new gt(t,n,e.annotations)}case"Union":{const t=ae(e.types,Bt);return t===e.types?e:Ue.make(t,e.annotations)}case"Suspend":return new Ns(()=>Bt(e.f()),e.annotations);case"Refinement":{const t=Bt(e.from);return t===e.from?e:new Di(t,e.filter,e.annotations)}case"Transformation":{const t=Bt(e.from),n=Bt(e.to);return t===e.from&&n===e.to?e:new Ls(t,n,e.transformation,e.annotations)}}return e},ji=e=>t=>{let n;for(const r of e)Object.prototype.hasOwnProperty.call(t.annotations,r)&&(n===void 0&&(n={}),n[r]=t.annotations[r]);return n},Ef=ji([hi,fi,pi,mi,yi,gi]),Y=e=>{switch(e._tag){case"Declaration":{const t=ae(e.typeParameters,Y);return t===e.typeParameters?e:new Pi(t,e.decodeUnknown,e.encodeUnknown,e.annotations)}case"TupleType":{const t=ae(e.elements,s=>{const o=Y(s.type);return o===s.type?s:new rn(o,s.isOptional)}),n=Ii(e.rest),r=ae(n,Y);return t===e.elements&&r===n?e:new wr(t,r.map(s=>new br(s)),e.isReadonly,e.annotations)}case"TypeLiteral":{const t=ae(e.propertySignatures,r=>{const s=Y(r.type);return s===r.type?r:new G(r.name,s,r.isOptional,r.isReadonly)}),n=ae(e.indexSignatures,r=>{const s=Y(r.type);return s===r.type?r:new Mn(r.parameter,s,r.isReadonly)});return t===e.propertySignatures&&n===e.indexSignatures?e:new gt(t,n,e.annotations)}case"Union":{const t=ae(e.types,Y);return t===e.types?e:Ue.make(t,e.annotations)}case"Suspend":return new Ns(()=>Y(e.f()),e.annotations);case"Refinement":{const t=Y(e.from);return t===e.from?e:new Di(t,e.filter,e.annotations)}case"Transformation":{const t=Ef(e);return Y(t!==void 0?Xt(e.to,t):e.to)}}return e},Ut=e=>xe(Hh(e),{onNone:()=>{},onSome:t=>({[$i]:t})});function ae(e,t){let n=!1;const r=Vu(e.length);for(let s=0;s<e.length;s++){const o=e[s],a=t(o);a!==o&&(n=!0),r[s]=a}return n?r:e}const Le=(e,t)=>{switch(e._tag){case"Declaration":{const n=ae(e.typeParameters,r=>Le(r));return n===e.typeParameters?e:new Pi(n,e.decodeUnknown,e.encodeUnknown,e.annotations)}case"TupleType":{const n=ae(e.elements,o=>{const a=Le(o.type);return a===o.type?o:new rn(a,o.isOptional)}),r=Ii(e.rest),s=ae(r,o=>Le(o));return n===e.elements&&s===r?e:new wr(n,s.map(o=>new br(o)),e.isReadonly,Ut(e))}case"TypeLiteral":{const n=ae(e.propertySignatures,s=>{const o=Le(s.type);return o===s.type?s:new G(s.name,o,s.isOptional,s.isReadonly)}),r=ae(e.indexSignatures,s=>{const o=Le(s.type);return o===s.type?s:new Mn(s.parameter,o,s.isReadonly)});return n===e.propertySignatures&&r===e.indexSignatures?e:new gt(n,r,Ut(e))}case"Union":{const n=ae(e.types,r=>Le(r));return n===e.types?e:Ue.make(n,Ut(e))}case"Suspend":return new Ns(()=>Le(e.f()),Ut(e));case"Refinement":{const n=Le(e.from),r=Ut(e);return r?Xt(n,r):n}case"Transformation":{const n=Ut(e);return Le(n?Xt(e.from,n):e.from)}}return e},Ao=e=>Le(e),J=e=>{const t={};for(const n of Object.getOwnPropertySymbols(e))t[String(n)]=e[n];return t},Ki=e=>{switch(e._tag){case"StringKeyword":case"SymbolKeyword":case"TemplateLiteral":return e;case"Refinement":return Ki(e.from)}},xt=e=>Oe(Je(e),()=>e._tag);function kf(e){return xe(Lh(e),{onNone:()=>"",onSome:t=>t.map(n=>` & Brand<${Ke(n)}>`).join("")})}const qi=e=>Ri(e).pipe(pt(()=>Ti(e)),pt(()=>Oi(e)),Gu(t=>t+kf(e))),Je=e=>pt(_r(e),()=>qi(e)),Ze=Na,ie=Zn,Rf=rl,cs=nl,et=Fa,Io=Symbol.for("effect/MutableList"),Of={[Io]:Io,[Symbol.iterator](){let e=!1,t=this.head;return{next(){if(e)return this.return();if(t==null)return e=!0,this.return();const n=t.value;return t=t.next,{done:e,value:n}},return(n){return e||(e=!0),{done:!0,value:n}}}},toString(){return Cs(this.toJSON())},toJSON(){return{_id:"MutableList",values:Array.from(this).map(La)}},[$t](){return this.toJSON()},pipe(){return we(this,arguments)}},Tf=e=>({value:e,removed:!1,prev:void 0,next:void 0}),Cf=()=>{const e=Object.create(Of);return e.head=void 0,e.tail=void 0,e._length=0,e},Bi=e=>Ds(e)===0,Ds=e=>e._length,$f=S(2,(e,t)=>{const n=Tf(t);return e.head===void 0&&(e.head=n),e.tail===void 0||(e.tail.next=n,n.prev=e.tail),e.tail=n,e._length+=1,e}),Pf=e=>{const t=e.head;if(t!==void 0)return vf(e,t),t.value},vf=(e,t)=>{t.removed||(t.removed=!0,t.prev!==void 0&&t.next!==void 0?(t.prev.next=t.next,t.next.prev=t.prev):t.prev!==void 0?(e.tail=t.prev,t.prev.next=void 0):t.next!==void 0?(e.head=t.next,t.next.prev=void 0):(e.tail=void 0,e.head=void 0),e._length>0&&(e._length-=1))},xo=Symbol.for("effect/MutableQueue"),he=Symbol.for("effect/mutable/MutableQueue/Empty"),Af={[xo]:xo,[Symbol.iterator](){return Array.from(this.queue)[Symbol.iterator]()},toString(){return Cs(this.toJSON())},toJSON(){return{_id:"MutableQueue",values:Array.from(this).map(La)}},[$t](){return this.toJSON()},pipe(){return we(this,arguments)}},Ji=e=>{const t=Object.create(Af);return t.queue=Cf(),t.capacity=e,t},If=e=>Ji(e),Us=()=>Ji(void 0),Ms=e=>Ds(e.queue),us=e=>Bi(e.queue),xf=e=>e.capacity===void 0?1/0:e.capacity,In=S(2,(e,t)=>{const n=Ds(e.queue);return e.capacity!==void 0&&n===e.capacity?!1:($f(t)(e.queue),!0)}),zi=S(2,(e,t)=>{const n=t[Symbol.iterator]();let r,s=$s(),o=!0;for(;o&&(r=n.next())&&!r.done;)o=In(r.value)(e);for(;r!=null&&!r.done;)s=mr(r.value)(s),r=n.next();return Da(s)}),St=S(2,(e,t)=>Bi(e.queue)?t:Pf(e.queue)),js=S(2,(e,t)=>{let n=$s(),r=0;for(;r<t;){const s=St(he)(e);if(s===he)break;n=mr(s)(n),r+=1}return Da(n)}),Nf=il,ls=ll,Ff=fl,Lf=sl,Df=dl,ds=al,Uf=hl,Mf=ul,jf=pl,Kf=cl,qf=ol,Wi=function(){const e=Symbol.for("effect/Data/Error/plainArgs");return{BaseEffectError:class extends ml{constructor(n){super(n==null?void 0:n.message,n!=null&&n.cause?{cause:n.cause}:void 0),n&&(Object.assign(this,n),Object.defineProperty(this,e,{value:n,enumerable:!1}))}toJSON(){return{...this[e],...this}}}}.BaseEffectError}(),Hi=e=>{const t={BaseEffectError:class extends Wi{constructor(){super(...arguments);i(this,"_tag",e)}}};return t.BaseEffectError.prototype.name=e,t.BaseEffectError},Ks=yl,Bf=Sl,Jf=gl,te=_l,zf=El,Wf=wl,Fe=bl,Hf="effect/QueueEnqueue",Vf=Symbol.for(Hf),Qf="effect/QueueDequeue",Gf=Symbol.for(Qf),Yf="effect/QueueStrategy",Vi=Symbol.for(Yf),Xf="effect/BackingQueue",Zf=Symbol.for(Xf),Qi={_A:e=>e},ep={_A:e=>e},tp={_In:e=>e},np={_Out:e=>e};var ma,ya,ga;class rp extends(ga=Rl,ya=Vf,ma=Gf,ga){constructor(n,r,s,o,a){super();i(this,"queue");i(this,"takers");i(this,"shutdownHook");i(this,"shutdownFlag");i(this,"strategy");i(this,ya,tp);i(this,ma,np);this.queue=n,this.takers=r,this.shutdownHook=s,this.shutdownFlag=o,this.strategy=a}pipe(){return we(this,arguments)}commit(){return this.take}capacity(){return this.queue.capacity()}get size(){return Qe(()=>Ol(this.unsafeSize(),()=>ut))}unsafeSize(){return $e(this.shutdownFlag)?re():ce(this.queue.length()-Ms(this.takers)+this.strategy.surplusSize())}get isEmpty(){return er(this.size,n=>n<=0)}get isFull(){return er(this.size,n=>n>=this.capacity())}get shutdown(){return Tl(vn(n=>(p(this.shutdownFlag,Cl(!0)),p(Ma(Yt(this.takers),r=>ja(r,n.id()),!1,!1),Pl(this.strategy.shutdown),$l(Fa(this.shutdownHook,void 0)),Ua))))}get isShutdown(){return ht(()=>$e(this.shutdownFlag))}get awaitShutdown(){return Zn(this.shutdownHook)}isActive(){return!$e(this.shutdownFlag)}unsafeOffer(n){if($e(this.shutdownFlag))return!1;let r;if(this.queue.length()===0){const o=p(this.takers,St(he));o!==he?(Tt(o,n),r=!0):r=!1}else r=!1;if(r)return!0;const s=this.queue.offer(n);return Vt(this.strategy,this.queue,this.takers),s}offer(n){return Qe(()=>{if($e(this.shutdownFlag))return ut;let r;if(this.queue.length()===0){const o=p(this.takers,St(he));o!==he?(Tt(o,n),r=!0):r=!1}else r=!1;if(r)return Ge(!0);const s=this.queue.offer(n);return Vt(this.strategy,this.queue,this.takers),s?Ge(!0):this.strategy.handleSurplus([n],this.queue,this.takers,this.shutdownFlag)})}offerAll(n){return Qe(()=>{if($e(this.shutdownFlag))return ut;const r=tr(n),s=this.queue.length()===0?tr(yp(this.takers,r.length)):vl,[o,a]=p(r,Al(s.length));for(let d=0;d<s.length;d++){const u=s[d],l=o[d];Tt(u,l)}if(a.length===0)return Ge(!0);const c=this.queue.offerAll(a);return Vt(this.strategy,this.queue,this.takers),Il(c)?Ge(!0):this.strategy.handleSurplus(c,this.queue,this.takers,this.shutdownFlag)})}get take(){return vn(n=>{if($e(this.shutdownFlag))return ut;const r=this.queue.poll(he);if(r!==he)return this.strategy.unsafeOnQueueEmptySpace(this.queue,this.takers),Ge(r);{const s=Ka(n.id());return p(Qe(()=>(p(this.takers,In(s)),Vt(this.strategy,this.queue,this.takers),$e(this.shutdownFlag)?ut:Zn(s))),qa(()=>ht(()=>gp(this.takers,s))))}})}get takeAll(){return Qe(()=>$e(this.shutdownFlag)?ut:ht(()=>{const n=this.queue.pollUpTo(Number.POSITIVE_INFINITY);return this.strategy.unsafeOnQueueEmptySpace(this.queue,this.takers),po(n)}))}takeUpTo(n){return Qe(()=>$e(this.shutdownFlag)?ut:ht(()=>{const r=this.queue.pollUpTo(n);return this.strategy.unsafeOnQueueEmptySpace(this.queue,this.takers),po(r)}))}takeBetween(n,r){return Qe(()=>Gi(this,n,r,$s()))}}const Gi=(e,t,n,r)=>n<t?Ge(r):p(dp(e,n),Pt(s=>{const o=t-s.length;return o===1?p(hs(e),er(a=>p(r,Cr(s),mo(a)))):o>1?p(hs(e),Pt(a=>Gi(e,o-1,n-s.length-1,p(r,Cr(s),mo(a))))):Ge(p(r,Cr(s)))})),sp=e=>p(ht(()=>If(e)),Pt(t=>Yi(Xi(t),hp()))),op=()=>p(ht(()=>Us()),Pt(e=>Yi(Xi(e),fp()))),ap=(e,t,n,r,s)=>new rp(e,t,n,r,s),Yi=(e,t)=>p(Na(),er(n=>ap(e,Us(),n,kl(!1),t)));var Sa;Sa=Zf;class ip{constructor(t){i(this,"mutable");i(this,Sa,ep);this.mutable=t}poll(t){return St(this.mutable,t)}pollUpTo(t){return js(this.mutable,t)}offerAll(t){return zi(this.mutable,t)}offer(t){return In(this.mutable,t)}capacity(){return xf(this.mutable)}length(){return Ms(this.mutable)}}const Xi=e=>new ip(e),cp=e=>e.size,up=e=>e.shutdown,lp=S(2,(e,t)=>e.offer(t)),hs=e=>e.take,dp=S(2,(e,t)=>e.takeUpTo(t)),hp=()=>new pp,fp=()=>new mp;var _a;_a=Vi;class pp{constructor(){i(this,_a,Qi);i(this,"putters",Us())}surplusSize(){return Ms(this.putters)}onCompleteTakersWithEmptyQueue(t){for(;!us(this.putters)&&!us(t);){const n=St(t,void 0),r=St(this.putters,void 0);r[2]&&Tt(r[1],!0),Tt(n,r[0])}}get shutdown(){return p(Nl,Pt(t=>p(ht(()=>Yt(this.putters)),Pt(n=>Ma(n,([r,s,o])=>o?p(ja(s,t),Ua):Ja,!1,!1)))))}handleSurplus(t,n,r,s){return vn(o=>{const a=Ka(o.id());return p(Qe(()=>(this.unsafeOffer(t,a),this.unsafeOnQueueEmptySpace(n,r),Vt(this,n,r),$e(s)?ut:Zn(a))),qa(()=>ht(()=>this.unsafeRemove(a))))})}unsafeOnQueueEmptySpace(t,n){let r=!0;for(;r&&(t.capacity()===Number.POSITIVE_INFINITY||t.length()<t.capacity());){const s=p(this.putters,St(he));if(s===he)r=!1;else{const o=t.offer(s[0]);o&&s[2]?Tt(s[1],!0):o||sr(this.putters,p(Yt(this.putters),mr(s))),Vt(this,t,n)}}}unsafeOffer(t,n){const r=tr(t);for(let s=0;s<r.length;s++){const o=r[s];s===r.length-1?p(this.putters,In([o,n,!0])):p(this.putters,In([o,n,!1]))}}unsafeRemove(t){sr(this.putters,p(Yt(this.putters),Ba(([,n])=>n!==t)))}}var ba;ba=Vi;class mp{constructor(){i(this,ba,Qi)}surplusSize(){return 0}get shutdown(){return Ja}onCompleteTakersWithEmptyQueue(){}handleSurplus(t,n,r,s){return Ge(!1)}unsafeOnQueueEmptySpace(t,n){}}const Tt=(e,t)=>xl(e,Ge(t)),sr=(e,t)=>p(e,zi(t)),Yt=e=>p(e,js(Number.POSITIVE_INFINITY)),yp=(e,t)=>p(e,js(t)),gp=(e,t)=>{sr(e,p(Yt(e),Ba(n=>t!==n)))},Vt=(e,t,n)=>{let r=!0;for(;r&&t.length()!==0;){const s=p(n,St(he));if(s!==he){const o=t.poll(he);o!==he?(Tt(s,o),e.unsafeOnQueueEmptySpace(t,n)):sr(n,p(Yt(n),mr(s))),r=!0}else r=!1}r&&t.length()===0&&!us(n)&&e.onCompleteTakersWithEmptyQueue(n)},Sp=sp,_p=op,bp=cp,No=up,Mt=lp,Fo=hs,Zi="Continue",wp="Close",Ep="Yield",kp="effect/ChannelChildExecutorDecision",Lo=Symbol.for(kp),Rp={[Lo]:Lo},ec=e=>{const t=Object.create(Rp);return t._tag=Zi,t},Vn="ContinuationK",Op="ContinuationFinalizer",tc=Symbol.for("effect/ChannelContinuation"),nc={_Env:e=>e,_InErr:e=>e,_InElem:e=>e,_InDone:e=>e,_OutErr:e=>e,_OutDone:e=>e,_OutErr2:e=>e,_OutElem:e=>e,_OutDone2:e=>e};var wa;wa=tc;class rc{constructor(t,n){i(this,"onSuccess");i(this,"onHalt");i(this,"_tag",Vn);i(this,wa,nc);this.onSuccess=t,this.onHalt=n}onExit(t){return za(t)?this.onHalt(t.cause):this.onSuccess(t.value)}}var Ea;Ea=tc;class Tp{constructor(t){i(this,"finalizer");i(this,"_tag",Op);i(this,Ea,nc);this.finalizer=t}}const sc="PullAfterNext",Cp="PullAfterAllEnqueued",$p="effect/ChannelUpstreamPullStrategy",Pp=Symbol.for($p),vp={_A:e=>e},Ap={[Pp]:vp},oc=e=>{const t=Object.create(Ap);return t._tag=sc,t.emitSeparator=e,t},ac="BracketOut",ic="Bridge",qs="ConcatAll",cc="Emit",uc="Ensuring",lc="Fail",dc="Fold",hc="FromEffect",fc="PipeTo",Ip="Provide",pc="Read",mc="Succeed",yc="SucceedNow",gc="Suspend",xp="effect/Channel",Sc=Symbol.for(xp),Np={_Env:e=>e,_InErr:e=>e,_InElem:e=>e,_InDone:e=>e,_OutErr:e=>e,_OutElem:e=>e,_OutDone:e=>e},ye={[Sc]:Np,pipe(){return we(this,arguments)}},_c=e=>At(e,Sc)||tn(e),Fp=S(2,(e,t)=>{const n=Object.create(ye);return n._tag=ac,n.acquire=()=>e,n.finalizer=t,n}),bc=(e,t,n)=>{const r=Object.create(ye);return r._tag=qs,r.combineInners=t,r.combineAll=n,r.onPull=()=>oc(re()),r.onEmit=()=>ec,r.value=()=>e,r.k=rt,r},Lp=S(4,(e,t,n,r)=>{const s=Object.create(ye);return s._tag=qs,s.combineInners=n,s.combineAll=r,s.onPull=()=>oc(re()),s.onEmit=()=>ec,s.value=()=>e,s.k=t,s}),wc=S(2,(e,t)=>{const n=Object.create(ye);return n._tag=ic,n.input=t,n.channel=e,n}),Ec=S(2,(e,t)=>{const n=Object.create(ye);return n._tag=uc,n.channel=e,n.finalizer=t,n}),Bs=e=>ke(Nf(e)),ke=e=>Dp(()=>e),Dp=e=>{const t=Object.create(ye);return t._tag=lc,t.error=e,t},H=S(2,(e,t)=>{const n=Object.create(ye);return n._tag=dc,n.channel=e,n.k=new rc(t,ke),n}),le=e=>{const t=Object.create(ye);return t._tag=hc,t.effect=()=>e,t},qe=S(2,(e,t)=>{const n=Object.create(ye);return n._tag=fc,n.left=()=>e,n.right=()=>t,n}),kc=e=>Er({onInput:e.onInput,onFailure:t=>fe(Mf(t),{onLeft:e.onFailure,onRight:ke}),onDone:e.onDone}),Er=e=>{const t=Object.create(ye);return t._tag=pc,t.more=e.onInput,t.done=new rc(e.onDone,e.onFailure),t},Rc=e=>Tc(()=>e),xn=e=>{const t=Object.create(ye);return t._tag=yc,t.terminal=e,t},Oc=e=>{const t=Object.create(ye);return t._tag=gc,t.channel=e,t},Tc=e=>{const t=Object.create(ye);return t._tag=mc,t.evaluate=e,t},sn=xn(void 0),pe=e=>{const t=Object.create(ye);return t._tag=cc,t.out=e,t},jn="Done",Kn="Emit",on="FromEffect",qn="Read",Up=Symbol.for("effect/ChannelState"),Mp={_E:e=>e,_R:e=>e},kr={[Up]:Mp},jt=()=>{const e=Object.create(kr);return e._tag=jn,e},Lr=()=>{const e=Object.create(kr);return e._tag=Kn,e},hn=e=>{const t=Object.create(kr);return t._tag=on,t.effect=e,t},Dr=(e,t,n,r)=>{const s=Object.create(kr);return s._tag=qn,s.upstream=e,s.onEffect=t,s.onEmit=n,s.onDone=r,s},or=e=>e._tag===on,jp=e=>or(e)?e.effect:I,Do=e=>or(e)?Fl(e.effect):void 0,Cc="PullFromChild",fs="PullFromUpstream",ps="DrainChildExecutors",$c="Emit";class Wn{constructor(t,n,r){i(this,"childExecutor");i(this,"parentSubexecutor");i(this,"onEmit");i(this,"_tag",Cc);this.childExecutor=t,this.parentSubexecutor=n,this.onEmit=r}close(t){const n=this.childExecutor.close(t),r=this.parentSubexecutor.close(t);return n!==void 0&&r!==void 0?yr(Z(n),Z(r),(s,o)=>p(s,An(o))):n!==void 0?n:r!==void 0?r:void 0}enqueuePullFromChild(t){return this}}class wt{constructor(t,n,r,s,o,a,c,d){i(this,"upstreamExecutor");i(this,"createChild");i(this,"lastDone");i(this,"activeChildExecutors");i(this,"combineChildResults");i(this,"combineWithChildResult");i(this,"onPull");i(this,"onEmit");i(this,"_tag",fs);this.upstreamExecutor=t,this.createChild=n,this.lastDone=r,this.activeChildExecutors=s,this.combineChildResults=o,this.combineWithChildResult=a,this.onPull=c,this.onEmit=d}close(t){const n=this.upstreamExecutor.close(t),s=[...this.activeChildExecutors.map(o=>o!==void 0?o.childExecutor.close(t):void 0),n].reduce((o,a)=>o!==void 0&&a!==void 0?yr(o,Z(a),(c,d)=>An(c,d)):o!==void 0?o:a!==void 0?Z(a):void 0,void 0);return s}enqueuePullFromChild(t){return new wt(this.upstreamExecutor,this.createChild,this.lastDone,[...this.activeChildExecutors,t],this.combineChildResults,this.combineWithChildResult,this.onPull,this.onEmit)}}class Qt{constructor(t,n,r,s,o,a,c){i(this,"upstreamExecutor");i(this,"lastDone");i(this,"activeChildExecutors");i(this,"upstreamDone");i(this,"combineChildResults");i(this,"combineWithChildResult");i(this,"onPull");i(this,"_tag",ps);this.upstreamExecutor=t,this.lastDone=n,this.activeChildExecutors=r,this.upstreamDone=s,this.combineChildResults=o,this.combineWithChildResult=a,this.onPull=c}close(t){const n=this.upstreamExecutor.close(t),s=[...this.activeChildExecutors.map(o=>o!==void 0?o.childExecutor.close(t):void 0),n].reduce((o,a)=>o!==void 0&&a!==void 0?yr(o,Z(a),(c,d)=>An(c,d)):o!==void 0?o:a!==void 0?Z(a):void 0,void 0);return s}enqueuePullFromChild(t){return new Qt(this.upstreamExecutor,this.lastDone,[...this.activeChildExecutors,t],this.upstreamDone,this.combineChildResults,this.combineWithChildResult,this.onPull)}}class Ur{constructor(t,n){i(this,"value");i(this,"next");i(this,"_tag",$c);this.value=t,this.next=n}close(t){const n=this.next.close(t);return n}enqueuePullFromChild(t){return this}}const Kp="Pulled",qp="NoUpstream",Bp="effect/ChannelUpstreamPullRequest",Jp=Symbol.for(Bp),zp={_A:e=>e},Pc={[Jp]:zp},Uo=e=>{const t=Object.create(Pc);return t._tag=Kp,t.value=e,t},Wp=e=>{const t=Object.create(Pc);return t._tag=qp,t.activeDownstreamCount=e,t};class kt{constructor(t,n,r){i(this,"_activeSubexecutor");i(this,"_cancelled");i(this,"_closeLastSubstream");i(this,"_currentChannel");i(this,"_done");i(this,"_doneStack",[]);i(this,"_emitted");i(this,"_executeCloseLastSubstream");i(this,"_input");i(this,"_inProgressFinalizer");i(this,"_providedEnv");this._currentChannel=t,this._executeCloseLastSubstream=r,this._providedEnv=n}run(){let t;for(;t===void 0;)if(this._cancelled!==void 0)t=this.processCancellation();else if(this._activeSubexecutor!==void 0)t=this.runSubexecutor();else try{if(this._currentChannel===void 0)t=jt();else switch(tn(this._currentChannel)&&(this._currentChannel=le(this._currentChannel)),this._currentChannel._tag){case ac:{t=this.runBracketOut(this._currentChannel);break}case ic:{const n=this._currentChannel.input;if(this._currentChannel=this._currentChannel.channel,this._input!==void 0){const r=this._input;this._input=void 0;const s=()=>m(n.awaitRead(),()=>K(()=>{const o=r.run();switch(o._tag){case jn:return Xe(r.getDone(),{onFailure:a=>n.error(a),onSuccess:a=>n.done(a)});case Kn:return m(n.emit(r.getEmit()),()=>s());case on:return Pe(o.effect,{onFailure:a=>n.error(a),onSuccess:()=>s()});case qn:return Js(o,()=>s(),a=>n.error(a))}}));t=hn(m(Wl(ve(s())),o=>W(()=>this.addFinalizer(a=>m(mt(o),()=>K(()=>{const c=this.restorePipe(a,r);return c!==void 0?c:I}))))))}break}case qs:{const n=new kt(this._currentChannel.value(),this._providedEnv,s=>W(()=>{const o=this._closeLastSubstream===void 0?I:this._closeLastSubstream;this._closeLastSubstream=p(o,X(s))}));n._input=this._input;const r=this._currentChannel;this._activeSubexecutor=new wt(n,s=>r.k(s),void 0,[],(s,o)=>r.combineInners(s,o),(s,o)=>r.combineAll(s,o),s=>r.onPull(s),s=>r.onEmit(s)),this._closeLastSubstream=void 0,this._currentChannel=void 0;break}case cc:{this._emitted=this._currentChannel.out,this._currentChannel=this._activeSubexecutor!==void 0?void 0:sn,t=Lr();break}case uc:{this.runEnsuring(this._currentChannel);break}case lc:{t=this.doneHalt(this._currentChannel.error());break}case dc:{this._doneStack.push(this._currentChannel.k),this._currentChannel=this._currentChannel.channel;break}case hc:{const n=this._providedEnv===void 0?this._currentChannel.effect():p(this._currentChannel.effect(),st(this._providedEnv));t=hn(Pe(n,{onFailure:r=>{const s=this.doneHalt(r);return s!==void 0&&or(s)?s.effect:I},onSuccess:r=>{const s=this.doneSucceed(r);return s!==void 0&&or(s)?s.effect:I}}));break}case fc:{const n=this._input,r=new kt(this._currentChannel.left(),this._providedEnv,s=>this._executeCloseLastSubstream(s));r._input=n,this._input=r,this.addFinalizer(s=>{const o=this.restorePipe(s,n);return o!==void 0?o:I}),this._currentChannel=this._currentChannel.right();break}case Ip:{const n=this._providedEnv;this._providedEnv=this._currentChannel.context(),this._currentChannel=this._currentChannel.inner,this.addFinalizer(()=>W(()=>{this._providedEnv=n}));break}case pc:{const n=this._currentChannel;t=Dr(this._input,rt,r=>{try{this._currentChannel=n.more(r)}catch(s){this._currentChannel=n.done.onExit(zl(s))}},r=>{const s=o=>n.done.onExit(o);this._currentChannel=s(r)});break}case mc:{t=this.doneSucceed(this._currentChannel.evaluate());break}case yc:{t=this.doneSucceed(this._currentChannel.terminal);break}case gc:{this._currentChannel=this._currentChannel.channel();break}}}catch(n){this._currentChannel=ke(ls(n))}return t}getDone(){return this._done}getEmit(){return this._emitted}cancelWith(t){this._cancelled=t}clearInProgressFinalizer(){this._inProgressFinalizer=void 0}storeInProgressFinalizer(t){this._inProgressFinalizer=t}popAllFinalizers(t){const n=[];let r=this._doneStack.pop();for(;r;)r._tag==="ContinuationFinalizer"&&n.push(r.finalizer),r=this._doneStack.pop();const s=n.length===0?I:jr(n,t);return this.storeInProgressFinalizer(s),s}popNextFinalizers(){const t=[];for(;this._doneStack.length!==0;){const n=this._doneStack[this._doneStack.length-1];if(n._tag===Vn)return t;t.push(n),this._doneStack.pop()}return t}restorePipe(t,n){const r=this._input;return this._input=n,r!==void 0?r.close(t):I}close(t){let n;const r=this._inProgressFinalizer;r!==void 0&&(n=p(r,Ht(W(()=>this.clearInProgressFinalizer()))));let s;const o=this.popAllFinalizers(t);o!==void 0&&(s=p(o,Ht(W(()=>this.clearInProgressFinalizer()))));const a=this._activeSubexecutor===void 0?void 0:this._activeSubexecutor.close(t);if(!(a===void 0&&n===void 0&&s===void 0))return p(Z(Mr(a)),ts(Z(Mr(n))),ts(Z(Mr(s))),L(([[c,d],u])=>p(c,An(d),An(u))),Sn,m(c=>K(()=>c)))}doneSucceed(t){if(this._doneStack.length===0)return this._done=Me(t),this._currentChannel=void 0,jt();const n=this._doneStack[this._doneStack.length-1];if(n._tag===Vn){this._doneStack.pop(),this._currentChannel=n.onSuccess(t);return}const r=this.popNextFinalizers();if(this._doneStack.length===0)return this._doneStack=r.reverse(),this._done=Me(t),this._currentChannel=void 0,jt();const s=jr(r.map(a=>a.finalizer),Me(t));this.storeInProgressFinalizer(s);const o=p(s,Ht(W(()=>this.clearInProgressFinalizer())),Sn,m(()=>W(()=>this.doneSucceed(t))));return hn(o)}doneHalt(t){if(this._doneStack.length===0)return this._done=Ae(t),this._currentChannel=void 0,jt();const n=this._doneStack[this._doneStack.length-1];if(n._tag===Vn){this._doneStack.pop();try{this._currentChannel=n.onHalt(t)}catch(a){this._currentChannel=ke(ls(a))}return}const r=this.popNextFinalizers();if(this._doneStack.length===0)return this._doneStack=r.reverse(),this._done=Ae(t),this._currentChannel=void 0,jt();const s=jr(r.map(a=>a.finalizer),Ae(t));this.storeInProgressFinalizer(s);const o=p(s,Ht(W(()=>this.clearInProgressFinalizer())),Sn,m(()=>W(()=>this.doneHalt(t))));return hn(o)}processCancellation(){return this._currentChannel=void 0,this._done=this._cancelled,this._cancelled=void 0,jt()}runBracketOut(t){const n=Sn(Pe(this.provide(t.acquire()),{onFailure:r=>W(()=>{this._currentChannel=ke(r)}),onSuccess:r=>W(()=>{this.addFinalizer(s=>this.provide(t.finalizer(r,s))),this._currentChannel=pe(r)})}));return hn(n)}provide(t){return this._providedEnv===void 0?t:p(t,st(this._providedEnv))}runEnsuring(t){this.addFinalizer(t.finalizer),this._currentChannel=t.channel}addFinalizer(t){this._doneStack.push(new Tp(t))}runSubexecutor(){const t=this._activeSubexecutor;switch(t._tag){case Cc:return this.pullFromChild(t.childExecutor,t.parentSubexecutor,t.onEmit,t);case fs:return this.pullFromUpstream(t);case ps:return this.drainChildExecutors(t);case $c:return this._emitted=t.value,this._activeSubexecutor=t.next,Lr()}}replaceSubexecutor(t){this._currentChannel=void 0,this._activeSubexecutor=t}finishWithExit(t){const n=Xe(t,{onFailure:r=>this.doneHalt(r),onSuccess:r=>this.doneSucceed(r)});return this._activeSubexecutor=void 0,n===void 0?I:jp(n)}finishSubexecutorWithCloseEffect(t,...n){this.addFinalizer(()=>p(n,yt(s=>p(W(()=>s(t)),m(o=>o!==void 0?o:I)),{discard:!0})));const r=p(t,Xe({onFailure:s=>this.doneHalt(s),onSuccess:s=>this.doneSucceed(s)}));return this._activeSubexecutor=void 0,r}applyUpstreamPullStrategy(t,n,r){switch(r._tag){case sc:{const s=!t||n.some(o=>o!==void 0);return[r.emitSeparator,s?[void 0,...n]:n]}case Cp:{const s=!t||n.some(o=>o!==void 0);return[r.emitSeparator,s?[...n,void 0]:n]}}}pullFromChild(t,n,r,s){return Dr(t,rt,o=>{const a=r(o);switch(a._tag){case Zi:break;case wp:{this.finishWithDoneValue(t,n,a.value);break}case Ep:{const c=n.enqueuePullFromChild(s);this.replaceSubexecutor(c);break}}this._activeSubexecutor=new Ur(o,this._activeSubexecutor)},Xe({onFailure:o=>{const a=this.handleSubexecutorFailure(t,n,o);return a===void 0?void 0:Do(a)},onSuccess:o=>{this.finishWithDoneValue(t,n,o)}}))}finishWithDoneValue(t,n,r){const s=n;switch(s._tag){case fs:{const o=new wt(s.upstreamExecutor,s.createChild,s.lastDone!==void 0?s.combineChildResults(s.lastDone,r):r,s.activeChildExecutors,s.combineChildResults,s.combineWithChildResult,s.onPull,s.onEmit);this._closeLastSubstream=t.close(Me(r)),this.replaceSubexecutor(o);break}case ps:{const o=new Qt(s.upstreamExecutor,s.lastDone!==void 0?s.combineChildResults(s.lastDone,r):r,s.activeChildExecutors,s.upstreamDone,s.combineChildResults,s.combineWithChildResult,s.onPull);this._closeLastSubstream=t.close(Me(r)),this.replaceSubexecutor(o);break}}}handleSubexecutorFailure(t,n,r){return this.finishSubexecutorWithCloseEffect(Ae(r),s=>n.close(s),s=>t.close(s))}pullFromUpstream(t){if(t.activeChildExecutors.length===0)return this.performPullFromUpstream(t);const n=t.activeChildExecutors[0],r=new wt(t.upstreamExecutor,t.createChild,t.lastDone,t.activeChildExecutors.slice(1),t.combineChildResults,t.combineWithChildResult,t.onPull,t.onEmit);if(n===void 0)return this.performPullFromUpstream(r);this.replaceSubexecutor(new Wn(n.childExecutor,r,n.onEmit))}performPullFromUpstream(t){return Dr(t.upstreamExecutor,n=>{const r=this._closeLastSubstream===void 0?I:this._closeLastSubstream;return this._closeLastSubstream=void 0,p(this._executeCloseLastSubstream(r),X(n))},n=>{if(this._closeLastSubstream!==void 0){const a=this._closeLastSubstream;return this._closeLastSubstream=void 0,p(this._executeCloseLastSubstream(a),L(()=>{const c=new kt(t.createChild(n),this._providedEnv,this._executeCloseLastSubstream);c._input=this._input;const[d,u]=this.applyUpstreamPullStrategy(!1,t.activeChildExecutors,t.onPull(Uo(n)));this._activeSubexecutor=new Wn(c,new wt(t.upstreamExecutor,t.createChild,t.lastDone,u,t.combineChildResults,t.combineWithChildResult,t.onPull,t.onEmit),t.onEmit),Ie(d)&&(this._activeSubexecutor=new Ur(d.value,this._activeSubexecutor))}))}const r=new kt(t.createChild(n),this._providedEnv,this._executeCloseLastSubstream);r._input=this._input;const[s,o]=this.applyUpstreamPullStrategy(!1,t.activeChildExecutors,t.onPull(Uo(n)));this._activeSubexecutor=new Wn(r,new wt(t.upstreamExecutor,t.createChild,t.lastDone,o,t.combineChildResults,t.combineWithChildResult,t.onPull,t.onEmit),t.onEmit),Ie(s)&&(this._activeSubexecutor=new Ur(s.value,this._activeSubexecutor))},n=>{if(t.activeChildExecutors.some(o=>o!==void 0)){const o=new Qt(t.upstreamExecutor,t.lastDone,[void 0,...t.activeChildExecutors],t.upstreamExecutor.getDone(),t.combineChildResults,t.combineWithChildResult,t.onPull);if(this._closeLastSubstream!==void 0){const a=this._closeLastSubstream;return this._closeLastSubstream=void 0,p(this._executeCloseLastSubstream(a),L(()=>this.replaceSubexecutor(o)))}this.replaceSubexecutor(o);return}const r=this._closeLastSubstream,s=this.finishSubexecutorWithCloseEffect(p(n,Gl(o=>t.combineWithChildResult(t.lastDone,o))),()=>r,o=>t.upstreamExecutor.close(o));return s===void 0?void 0:Do(s)})}drainChildExecutors(t){if(t.activeChildExecutors.length===0){const o=this._closeLastSubstream;return o!==void 0&&this.addFinalizer(()=>U(o)),this.finishSubexecutorWithCloseEffect(t.upstreamDone,()=>o,a=>t.upstreamExecutor.close(a))}const n=t.activeChildExecutors[0],r=t.activeChildExecutors.slice(1);if(n===void 0){const[o,a]=this.applyUpstreamPullStrategy(!0,r,t.onPull(Wp(r.reduce((c,d)=>d!==void 0?c+1:c,0))));return this.replaceSubexecutor(new Qt(t.upstreamExecutor,t.lastDone,a,t.upstreamDone,t.combineChildResults,t.combineWithChildResult,t.onPull)),Ie(o)?(this._emitted=o.value,Lr()):void 0}const s=new Qt(t.upstreamExecutor,t.lastDone,r,t.upstreamDone,t.combineChildResults,t.combineWithChildResult,t.onPull);this.replaceSubexecutor(new Wn(n.childExecutor,s,n.onEmit))}}const Mr=e=>e!==void 0?e:I,jr=(e,t)=>p(yt(e,n=>Z(n(t))),L(n=>p(Hl(n),Oe(()=>Vl))),m(n=>K(()=>n))),Js=(e,t,n)=>{const r=[e],s=()=>{const o=r.pop();if(o===void 0||o.upstream===void 0)return Ql("Unexpected end of input for channel execution");const a=o.upstream.run();switch(a._tag){case Kn:{const c=o.onEmit(o.upstream.getEmit());return r.length===0?c===void 0?K(t):p(c,Pe({onFailure:n,onSuccess:t})):c===void 0?K(()=>s()):p(c,Pe({onFailure:n,onSuccess:()=>s()}))}case jn:{const c=o.onDone(o.upstream.getDone());return r.length===0?c===void 0?K(t):p(c,Pe({onFailure:n,onSuccess:t})):c===void 0?K(()=>s()):p(c,Pe({onFailure:n,onSuccess:()=>s()}))}case on:return r.push(o),p(o.onEffect(a.effect),Va(c=>K(()=>{const d=o.onDone(Ae(c));return d===void 0?I:d})),Pe({onFailure:n,onSuccess:()=>s()}));case qn:return r.push(o),r.push(a),K(()=>s())}};return s()},Hp=S(2,(e,t)=>{const n=(r,s,o)=>Ml(W(()=>new kt(e,void 0,rt)),a=>K(()=>Qn(a.run(),a).pipe(Bl(r),X(ie(r)),Jl(ie(s)))),(a,c)=>{const d=a.close(c);return d===void 0?I:Ha(d,u=>es(o,tt(u)))});return nr(r=>Ll([Dl(t,Ul),Ze(),Ze()]).pipe(m(([s,o,a])=>r(n(o,a,s)).pipe(Rt(t),m(c=>t.addFinalizer(d=>{const u=za(d)?Uf(d.cause):void 0;return cs(o).pipe(m(l=>l?et(a,void 0).pipe(X(Wa(c)),X(yo(c))):et(a,void 0).pipe(X(u&&Kl(u)>0?jl(c,ql(u)):mt(c)),X(yo(c)))))}).pipe(X(r(ie(o)))))))))}),Qn=(e,t)=>{const n=e;switch(n._tag){case on:return p(n.effect,m(()=>Qn(t.run(),t)));case Kn:return Qn(t.run(),t);case jn:return K(()=>t.getDone());case qn:return Js(n,()=>Qn(t.run(),t),tt)}},Vp="Done",Qp="Await",Gp="effect/ChannelMergeDecision",Yp=Symbol.for(Gp),Xp={[Yp]:{_R:e=>e,_E0:e=>e,_Z0:e=>e,_E:e=>e,_Z:e=>e}},Mo=e=>{const t=Object.create(Xp);return t._tag=Qp,t.f=e,t},vc="BothRunning",Ac="LeftDone",Ic="RightDone",Zp="effect/ChannelMergeState",jo=Symbol.for(Zp),zs={[jo]:jo},Kr=(e,t)=>{const n=Object.create(zs);return n._tag=vc,n.left=e,n.right=t,n},Ko=e=>{const t=Object.create(zs);return t._tag=Ac,t.f=e,t},qo=e=>{const t=Object.create(zs);return t._tag=Ic,t.f=e,t},xc="BackPressure",Nc="BufferSliding",em="effect/ChannelMergeStrategy",Bo=Symbol.for(em),Fc={[Bo]:Bo},tm=e=>{const t=Object.create(Fc);return t._tag=xc,t},nm=e=>{const t=Object.create(Fc);return t._tag=Nc,t},rm=S(2,(e,{onBackPressure:t,onBufferSliding:n})=>{switch(e._tag){case xc:return t();case Nc:return n()}}),Jt="Empty",_n="Emit",bn="Error",wn="Done",Lc=e=>({_tag:Jt,notifyProducer:e}),qr=e=>({_tag:_n,notifyConsumers:e}),sm=e=>({_tag:bn,cause:e}),om=e=>({_tag:wn,done:e});class am{constructor(t){i(this,"ref");this.ref=t}awaitRead(){return qt(dn(this.ref,t=>t._tag===Jt?[ie(t.notifyProducer),t]:[I,t]))}get close(){return Yl(t=>this.error(Ff(t)))}done(t){return qt(dn(this.ref,n=>{switch(n._tag){case Jt:return[ie(n.notifyProducer),n];case _n:return[yt(n.notifyConsumers,r=>et(r,$(t)),{discard:!0}),om(t)];case bn:return[Lt,n];case wn:return[Lt,n]}}))}emit(t){return m(Ze(),n=>qt(dn(this.ref,r=>{switch(r._tag){case Jt:return[ie(r.notifyProducer),r];case _n:{const s=r.notifyConsumers[0],o=r.notifyConsumers.slice(1);if(s!==void 0)return[et(s,N(t)),o.length===0?Lc(n):qr(o)];throw new Error("Bug: Channel.SingleProducerAsyncInput.emit - Queue was empty! please report an issue at https://github.com/Effect-TS/effect/issues")}case bn:return[Lt,r];case wn:return[Lt,r]}})))}error(t){return qt(dn(this.ref,n=>{switch(n._tag){case Jt:return[ie(n.notifyProducer),n];case _n:return[yt(n.notifyConsumers,r=>Rf(r,t),{discard:!0}),sm(t)];case bn:return[Lt,n];case wn:return[Lt,n]}}))}get take(){return this.takeWith(t=>Ae(jf(t,$)),t=>Me(t),t=>Xl(N(t)))}takeWith(t,n,r){return m(Ze(),s=>qt(dn(this.ref,o=>{switch(o._tag){case Jt:return[X(et(o.notifyProducer,void 0),ns(ie(s),{onFailure:t,onSuccess:fe({onLeft:r,onRight:n})})),qr([s])];case _n:return[ns(ie(s),{onFailure:t,onSuccess:fe({onLeft:r,onRight:n})}),qr([...o.notifyConsumers,s])];case bn:return[U(t(o.cause)),o];case wn:return[U(r(o.done)),o]}})))}}const Dc=()=>p(Ze(),m(e=>Qa(Lc(e))),L(e=>new am(e))),ms=S(2,(e,t)=>Lp(e,t,()=>{},()=>{})),im=e=>{const t=Er({onInput:()=>t,onFailure:ke,onDone:Rc});return qe(e,t)},cm=S(2,(e,t)=>Ec(e,()=>t)),um=e=>H(e,rt),Ws=e=>_t(e.takeWith(ke,t=>H(pe(t),()=>Ws(e)),Rc)),Uc=S(2,(e,t)=>H(e,n=>Tc(()=>t(n)))),lm=S(2,(e,t)=>{const n=kc({onInput:r=>H(pe(t(r)),()=>n),onFailure:Bs,onDone:xn});return qe(e,n)}),dm=e=>t=>hm(e)(t,td),hm=({bufferSize:e=16,concurrency:t,mergeStrategy:n=tm()})=>(r,s)=>Kc(o=>Rn(function*(){const a=t==="unbounded"?Number.MAX_SAFE_INTEGER:t,c=yield*Dc(),d=Ws(c),u=yield*Sp(e);yield*es(o,No(u));const l=yield*_p();yield*es(o,No(l));const h=yield*Qa(re()),g=yield*Ze(),_=(yield*rd(a)).withPermits,E=yield*Tn(qe(d,r),o);function v(y){return y.pipe(m(fe({onLeft:k=>U(ce(k)),onRight:k=>Pr(Mt(u,U(N(k))),re())})),So({until:k=>Ie(k)}),m(k=>ad(h,xe({onNone:()=>ce(k.value),onSome:b=>ce(s(b,k.value))}))),Va(k=>ds(k)?tt(k):Mt(u,tt(k)).pipe(X(et(g,void 0)),Za)))}yield*E.pipe(Pe({onFailure:y=>Mt(u,tt(y)).pipe(X(U(!1))),onSuccess:fe({onLeft:y=>Xa(ve(ie(g)),ve(_(a)(I)),{onSelfDone:(k,b)=>Pr(mt(b),!1),onOtherDone:(k,b)=>X(mt(b),od(h).pipe(m(xe({onNone:()=>Mt(u,U($(y))),onSome:T=>Mt(u,U($(s(T,y))))})),Pr(!1)))}),onRight:y=>rm(n,{onBackPressure:()=>Rn(function*(){const k=yield*Ze(),b=rs(A=>Tn(qe(d,y),A).pipe(m(R=>$r(Z(v(R)),Z(ve(ie(g))))),m(rt)));return yield*et(k,void 0).pipe(X(b),_(1),Rt(o)),yield*ie(k),!(yield*cs(g))}),onBufferSliding:()=>Rn(function*(){const k=yield*Ze(),b=yield*Ze(),T=yield*bp(l);yield*Fo(l).pipe(m(C=>et(C,void 0)),sd(()=>T>=a)),yield*Mt(l,k);const A=rs(C=>Tn(qe(d,y),C).pipe(m(F=>Z(v(F)).pipe($r(Z(ve(ie(g)))),$r(Z(ve(ie(k)))))),m(rt)));return yield*et(b,void 0).pipe(X(A),_(1),Rt(o)),yield*ie(b),!(yield*cs(g))})})})}),So({while:y=>y}),Rt(o));const w=p(Fo(u),qt,ns({onFailure:ke,onSuccess:fe({onLeft:xn,onRight:y=>H(pe(y),()=>w)})}),_t);return wc(w,c)})),Mc=S(3,(e,t,n)=>dm(n)(lm(e,t))),fm=S(2,(e,t)=>{function n(r){return Rn(function*(){const s=yield*Dc(),o=Ws(s),a=yield*Tn(qe(o,e),r),c=yield*Tn(qe(o,t.other),r);function d(l,h,g){return(_,E,v)=>{function w(y){const k=y;return k._tag===Vp?U(le(X(mt(h),k.effect))):L(Wa(h),Xe({onFailure:b=>le(k.f(Ae(b))),onSuccess:fe({onLeft:b=>le(k.f(Me(b))),onRight:b=>Jc(pe(b),u(v(k.f)))})}))}return Xe(l,{onFailure:y=>w(_(Ae(y))),onSuccess:fe({onLeft:y=>w(_(Me(y))),onRight:y=>U(H(pe(y),()=>H(le(Rt(ve(g),r)),k=>u(E(k,h)))))})})}}function u(l){switch(l._tag){case vc:{const h=ve(_o(l.left)),g=ve(_o(l.right));return _t(Xa(h,g,{onSelfDone:(_,E)=>X(mt(E),d(_,l.right,a)(t.onSelfDone,Kr,v=>Ko(v))),onOtherDone:(_,E)=>X(mt(E),d(_,l.left,c)(t.onOtherDone,(v,w)=>Kr(w,v),v=>qo(v)))}))}case Ac:return _t(L(Z(c),Xe({onFailure:h=>le(l.f(Ae(h))),onSuccess:fe({onLeft:h=>le(l.f(Me(h))),onRight:h=>H(pe(h),()=>u(Ko(l.f)))})})));case Ic:return _t(L(Z(a),Xe({onFailure:h=>le(l.f(Ae(h))),onSuccess:fe({onLeft:h=>le(l.f(Me(h))),onRight:h=>H(pe(h),()=>u(qo(l.f)))})})))}}return le(os(l=>{const h=os(E=>(E.transferChildren(l.scope()),I)),g=ve(a).pipe(Ht(h),Rt(r)),_=ve(c).pipe(Ht(h),Rt(r));return yr(g,_,(E,v)=>Kr(E,v))})).pipe(H(u),wc(s))})}return Kc(n)}),pm=S(2,(e,t)=>Oc(()=>{let n;const r=kc({onInput:o=>H(pe(o),()=>r),onFailure:o=>(n=bm(o),ke(ls(n))),onDone:xn}),s=Er({onInput:o=>p(pe(o),H(()=>s)),onFailure:o=>Df(o)&&wm(o.defect)&&Ya(o.defect,n)?Bs(o.defect.error):ke(o),onDone:xn});return qe(qe(qe(e,r),t),s)})),mm=e=>rs(t=>Hp(e,t)),ym=e=>mm(im(e)),jc=e=>_t(nr(t=>L(Zl(),n=>Fp(Ha(t(Ga(e,n)),r=>ss(n,Ae(r))),(r,s)=>ss(n,s))))),gm=e=>Sm(L(nd,t=>H(le(e(t)),pe))),Tn=S(2,(e,t)=>ts(W(()=>new kt(e,void 0,rt)),ei()).pipe(id(([n,r])=>cd(t,s=>{const o=n.close(s);return o!==void 0?st(o,r):I})),Sn,L(([n])=>K(()=>ys(n.run(),n))))),ys=(e,t)=>{const n=e;switch(n._tag){case jn:return Xe(t.getDone(),{onFailure:tt,onSuccess:r=>U($(r))});case Kn:return U(N(t.getEmit()));case on:return p(n.effect,m(()=>ys(t.run(),t)));case qn:return Js(n,()=>ys(t.run(),t),r=>tt(r))}},_t=e=>um(le(e)),Sm=e=>bc(jc(e),(t,n)=>t,(t,n)=>t),Kc=e=>bc(gm(e),(t,n)=>t,(t,n)=>t),qc=e=>Bc(0,e.length,e),Bc=(e,t,n)=>e===t?sn:p(pe(p(n,ed(e))),H(()=>Bc(e+1,t,n))),_m=S(e=>_c(e[1]),(e,t,n)=>n!=null&&n.concurrent?fm(e,{other:t,onSelfDone:r=>Mo(s=>K(()=>go(r,s))),onOtherDone:r=>Mo(s=>K(()=>go(s,r)))}):H(e,r=>Uc(t,s=>[r,s]))),Jc=S(e=>_c(e[1]),(e,t,n)=>n!=null&&n.concurrent?Uc(_m(e,t,{concurrent:!0}),r=>r[1]):H(e,()=>t)),gs=Symbol.for("effect/Channel/ChannelException"),bm=e=>({_tag:"ChannelException",[gs]:gs,error:e}),wm=e=>At(e,gs),Em=Symbol.for("effect/Sink"),km={_A:e=>e,_In:e=>e,_L:e=>e,_E:e=>e,_R:e=>e};var ka;ka=Em;class zc{constructor(t){i(this,"channel");i(this,ka,km);this.channel=t}pipe(){return we(this,arguments)}}const Rm=e=>{const t=Er({onInput:n=>p(le(e(n)),H(()=>t)),onFailure:ke,onDone:()=>sn});return new zc(t)},Om=e=>new zc(le(e)),Wc=e=>tn(e)?Wc(Om(e)):e.channel,Tm=ti,x_=ld,N_=ud,Cm="effect/Stream",Hc=Symbol.for(Cm),$m={_R:e=>e,_E:e=>e,_A:e=>e};var Ra;Ra=Hc;class bt{constructor(t){i(this,"channel");i(this,Ra,$m);this.channel=t}pipe(){return we(this,arguments)}}const Rr=e=>At(e,Hc)||tn(e),Pm=S(2,(e,t)=>new bt(Ec(je(e),t))),vm=e=>Vc(_e(ce(e))),Am=S(e=>Rr(e[0]),(e,t,n)=>{const r=(n==null?void 0:n.bufferSize)??16;return n!=null&&n.switch?Jo(n==null?void 0:n.concurrency,()=>zo(e,1,r,t),s=>zo(e,s,r,t)):Jo(n==null?void 0:n.concurrency,()=>new bt(ms(je(e),s=>p(s,gd(o=>je(t(o))),yd(sn,(o,a)=>p(o,Jc(a)))))),s=>new bt(p(je(e),ms(qc),Mc(o=>je(t(o)),n))))}),Jo=(e,t,n)=>{switch(e){case void 0:return t();case"unbounded":return n(Number.MAX_SAFE_INTEGER);default:return e>1?n(e):t()}},zo=S(4,(e,t,n,r)=>new bt(p(je(e),ms(qc),Mc(s=>je(r(s)),{concurrency:t,mergeStrategy:nm(),bufferSize:n})))),Im=S(e=>Rr(e[0]),(e,t)=>Am(e,rt,t)),je=e=>{if("channel"in e)return e.channel;if(tn(e))return je(xm(e));throw new TypeError("Expected a Stream.")},xm=e=>p(e,ni(ce),Vc),Vc=e=>new bt(_t(dd(e,{onFailure:xe({onNone:()=>sn,onSome:Bs}),onSuccess:t=>pe(Ps(t))}))),Nm=(...e)=>{const t=e.length===1?e[0].evaluate:e[0],n=e.length===1?e[0].onError:e[1],r=e.length===1?e[0].releaseLockOnEnd===!0:!1;return Bm(L(pd(W(()=>t().getReader()),s=>r?W(()=>s.releaseLock()):md(()=>s.cancel())),s=>Lm(m(On({try:()=>s.read(),catch:o=>ce(n(o))}),({done:o,value:a})=>o?_e(re()):U(a)))))},Fm=e=>qm(e,t=>p(L(t,n=>ce([n,t])),ri(xe({onNone:()=>U(re()),onSome:_e})))),Lm=e=>Fm(p(e,L(Ps))),Dm=S(2,(e,t)=>je(e).pipe(pm(Wc(t)),ym)),Um=S(2,(e,t)=>Dm(e,Rm(t))),Mm=e=>new bt(cm(jc(p(e,L(Ps))),I)),Qc=e=>new bt(Oc(()=>je(e()))),jm=S(e=>Rr(e[0]),(e,t)=>L(ei(),n=>Km(e,n,t))),Km=S(e=>Rr(e[0]),(e,t,n)=>{const r=Tm(t);let s,o;const a=hd(!1);return new ReadableStream({start(c){o=r(Um(e,d=>a.whenOpen(W(()=>{a.unsafeClose();for(const u of d)c.enqueue(u);s(),s=void 0})))),o.addObserver(d=>{d._tag==="Failure"?c.error(Kf(d.cause)):c.close()})},pull(){return new Promise(c=>{s=c,Ot(a.open)})},cancel(){if(o)return fd(Za(mt(o)))}},n==null?void 0:n.strategy)}),qm=(e,t)=>Qc(()=>{const n=r=>_t(L(t(r),xe({onNone:()=>sn,onSome:([s,o])=>H(pe(s),()=>n(o))})));return new bt(n(e))}),Bm=e=>Im(Mm(e)),Gc="effect/Redacted",Gn=at("effect/Redacted/redactedRegistry",()=>new WeakMap),Yc=Symbol.for(Gc),Jm={[Yc]:{_A:e=>e},pipe(){return we(this,arguments)},toString(){return"<redacted>"},toJSON(){return"<redacted>"},[$t](){return"<redacted>"},[_d](){return p(bo(Gc),wd(bo(Gn.get(this))),bd(this))},[Sd](e){return zm(e)&&Ya(Gn.get(this),Gn.get(e))}},zm=e=>At(e,Yc),Wm=e=>{const t=Object.create(Jm);return Gn.set(t,e),t};class ne{constructor(t,n,r){i(this,"path");i(this,"actual");i(this,"issue");i(this,"_tag","Pointer");this.path=t,this.actual=n,this.issue=r}}class Wo{constructor(t,n){i(this,"actual");i(this,"message");i(this,"_tag","Unexpected");this.actual=t,this.message=n}}class fn{constructor(t,n){i(this,"ast");i(this,"message");i(this,"_tag","Missing");i(this,"actual");this.ast=t,this.message=n}}class j{constructor(t,n,r,s){i(this,"ast");i(this,"actual");i(this,"issues");i(this,"output");i(this,"_tag","Composite");this.ast=t,this.actual=n,this.issues=r,this.output=s}}class Br{constructor(t,n,r,s){i(this,"ast");i(this,"actual");i(this,"kind");i(this,"issue");i(this,"_tag","Refinement");this.ast=t,this.actual=n,this.kind=r,this.issue=s}}class Jr{constructor(t,n,r,s){i(this,"ast");i(this,"actual");i(this,"kind");i(this,"issue");i(this,"_tag","Transformation");this.ast=t,this.actual=n,this.kind=r,this.issue=s}}class zt{constructor(t,n,r){i(this,"ast");i(this,"actual");i(this,"message");i(this,"_tag","Type");this.ast=t,this.actual=n,this.message=r}}class Ho{constructor(t,n,r){i(this,"ast");i(this,"actual");i(this,"message");i(this,"_tag","Forbidden");this.ast=t,this.actual=n,this.message=r}}const Vo=Symbol.for("effect/Schema/ParseErrorTypeId");var Oa;class Hm extends Hi("ParseError"){constructor(){super(...arguments);i(this,Oa,Vo)}get message(){return this.toString()}toString(){return Xn.formatIssueSync(this.issue)}toJSON(){return{_id:"ParseError",message:this.toString()}}[(Oa=Vo,$t)](){return this.toJSON()}}const Hs=e=>new Hm({issue:e}),Qo=N,de=Rd,ft=S(2,(e,t)=>de(e)?fe(e,{onLeft:$,onRight:t}):m(e,t)),De=S(2,(e,t)=>de(e)?Od(e,t):L(e,t)),Yn=S(2,(e,t)=>de(e)?si(e,t):ni(e,t)),Xc=S(2,(e,t)=>de(e)?fe(e,{onLeft:t,onRight:N}):ri(e,t)),Vs=(e,t)=>t===void 0||pr(t)?e:e===void 0?t:{...e,...t},Zc=(e,t,n)=>{const r=Q(e,t);return(s,o)=>r(s,Vs(n,o))},Qs=(e,t,n)=>{const r=Zc(e,t,n);return(s,o)=>Ed(r(s,o),Hs)},Vm=(e,t,n)=>{const r=Q(e,t);return(s,o)=>r(s,{...Vs(n,o),isEffectAllowed:!0})},ee=(e,t)=>Qs(e.ast,!0,t),Qm=(e,t)=>Zc(e.ast,!0,t),Gm=(e,t)=>Vm(e.ast,!0,t),We=(e,t)=>Qs(e.ast,!1,t),Ym=(e,t)=>Qs(Y(e.ast),!0,t),Xm=at(Symbol.for("effect/ParseResult/decodeMemoMap"),()=>new WeakMap),Zm=at(Symbol.for("effect/ParseResult/encodeMemoMap"),()=>new WeakMap),Q=(e,t)=>{const n=t?Xm:Zm,r=n.get(e);if(r)return r;const s=ey(e,t),o=qh(e),a=Ie(o)?(u,l)=>s(u,Vs(l,o.value)):s,c=Bh(e),d=t&&Ie(c)?(u,l)=>kn(Xc(a(u,l),c.value),e,u,l):a;return n.set(e,d),d},zr=e=>vs(Mh(e)),Wr=e=>vs(jh(e)),ey=(e,t)=>{switch(e._tag){case"Refinement":if(t){const n=Q(e.from,!0);return(r,s)=>{s=s??Fr;const o=(s==null?void 0:s.errors)==="all",a=ft(Xc(n(r,s),c=>{const d=new Br(e,r,"From",c);return o&&zh(e)&&ru(c)?xe(e.filter(r,s,e),{onNone:()=>$(d),onSome:u=>$(new j(e,r,[d,new Br(e,r,"Predicate",u)]))}):$(d)}),c=>xe(e.filter(c,s,e),{onNone:()=>N(c),onSome:d=>$(new Br(e,r,"Predicate",d))}));return kn(a,e,r,s)}}else{const n=Q(Y(e),!0),r=Q(eu(e.from),!1);return(s,o)=>kn(ft(n(s,o),a=>r(a,o)),e,s,o)}case"Transformation":{const n=ry(e.transformation,t),r=t?Q(e.from,!0):Q(e.to,!1),s=t?Q(e.to,!0):Q(e.from,!1);return(o,a)=>kn(ft(Yn(r(o,a),c=>new Jr(e,o,t?"Encoded":"Type",c)),c=>ft(Yn(n(c,a??Fr,e,o),d=>new Jr(e,o,"Transformation",d)),d=>Yn(s(d,a),u=>new Jr(e,o,t?"Type":"Encoded",u)))),e,o,a)}case"Declaration":{const n=t?e.decodeUnknown(...e.typeParameters):e.encodeUnknown(...e.typeParameters);return(r,s)=>kn(n(r,s??Fr,e),e,r,s)}case"Literal":return Se(e,n=>n===e.literal);case"UniqueSymbol":return Se(e,n=>n===e.symbol);case"UndefinedKeyword":return Se(e,Fd);case"NeverKeyword":return Se(e,Nd);case"UnknownKeyword":case"AnyKeyword":case"VoidKeyword":return N;case"StringKeyword":return Se(e,nt);case"NumberKeyword":return Se(e,pr);case"BooleanKeyword":return Se(e,Rs);case"BigIntKeyword":return Se(e,Os);case"SymbolKeyword":return Se(e,Xr);case"ObjectKeyword":return Se(e,gr);case"Enums":return Se(e,n=>e.enums.some(([r,s])=>s===n));case"TemplateLiteral":{const n=wf(e);return Se(e,r=>nt(r)&&n.test(r))}case"TupleType":{const n=e.elements.map(u=>Q(u.type,t)),r=e.rest.map(u=>Q(u.type,t));let s=e.elements.filter(u=>!u.isOptional);e.rest.length>0&&(s=s.concat(e.rest.slice(1)));const o=s.length,a=e.elements.length>0?e.elements.map((u,l)=>l).join(" | "):"never",c=zr(e),d=Wr(e);return(u,l)=>{if(!$d(u))return $(new zt(e,u));const h=(l==null?void 0:l.errors)==="all",g=[];let _=0;const E=[],v=u.length;for(let b=v;b<=o-1;b++){const T=new ne(b,u,new fn(s[b-v]));if(h){g.push([_++,T]);continue}else return $(new j(e,u,T,E))}if(e.rest.length===0)for(let b=e.elements.length;b<=v-1;b++){const T=new ne(b,u,new Wo(u[b],`is unexpected, expected: ${a}`));if(h){g.push([_++,T]);continue}else return $(new j(e,u,T,E))}let w=0,y;for(;w<n.length;w++)if(v<w+1){if(e.elements[w].isOptional)continue}else{const b=n[w],T=b(u[w],l);if(de(T)){if(Te(T)){const A=new ne(w,u,T.left);if(h){g.push([_++,A]);continue}else return $(new j(e,u,A,Ce(E)))}E.push([_++,T.right])}else{const A=_++,R=w;y||(y=[]),y.push(({es:C,output:F})=>m(Dt(T),V=>{if(Te(V)){const M=new ne(R,u,V.left);return h?(C.push([A,M]),I):$(new j(e,u,M,Ce(F)))}return F.push([A,V.right]),I}))}}if(As(r)){const[b,...T]=r;for(;w<v-T.length;w++){const A=b(u[w],l);if(de(A))if(Te(A)){const R=new ne(w,u,A.left);if(h){g.push([_++,R]);continue}else return $(new j(e,u,R,Ce(E)))}else E.push([_++,A.right]);else{const R=_++,C=w;y||(y=[]),y.push(({es:F,output:V})=>m(Dt(A),M=>{if(Te(M)){const D=new ne(C,u,M.left);return h?(F.push([R,D]),I):$(new j(e,u,D,Ce(V)))}else return V.push([R,M.right]),I}))}}for(let A=0;A<T.length;A++)if(w+=A,!(v<w+1)){const R=T[A](u[w],l);if(de(R)){if(Te(R)){const C=new ne(w,u,R.left);if(h){g.push([_++,C]);continue}else return $(new j(e,u,C,Ce(E)))}E.push([_++,R.right])}else{const C=_++,F=w;y||(y=[]),y.push(({es:V,output:M})=>m(Dt(R),D=>{if(Te(D)){const ge=new ne(F,u,D.left);return h?(V.push([C,ge]),I):$(new j(e,u,ge,Ce(M)))}return M.push([C,D.right]),I}))}}}const k=({es:b,output:T})=>Ar(b)?$(new j(e,u,Ce(b),Ce(T))):N(Ce(T));if(y&&y.length>0){const b=y;return K(()=>{const T={es:zn(g),output:zn(E)};return m(yt(b,A=>A(T),{concurrency:c,batching:d,discard:!0}),()=>k(T))})}return k({output:E,es:g})}}case"TypeLiteral":{if(e.propertySignatures.length===0&&e.indexSignatures.length===0)return Se(e,xd);const n=[],r={},s=[];for(const l of e.propertySignatures)n.push([Q(l.type,t),l]),r[l.name]=null,s.push(l.name);const o=e.indexSignatures.map(l=>[Q(l.parameter,t),Q(l.type,t),l.parameter]),a=Ue.make(e.indexSignatures.map(l=>l.parameter).concat(s.map(l=>Xr(l)?new Qh(l):new vi(l)))),c=Q(a,t),d=zr(e),u=Wr(e);return(l,h)=>{if(!Cd(l))return $(new zt(e,l));const g=(h==null?void 0:h.errors)==="all",_=[];let E=0;const v=(h==null?void 0:h.onExcessProperty)==="error",w=(h==null?void 0:h.onExcessProperty)==="preserve",y={};let k;if(v||w){k=Gt(l);for(const R of k){const C=c(R,h);if(de(C)&&Te(C))if(v){const F=new ne(R,l,new Wo(l[R],`is unexpected, expected: ${String(a)}`));if(g){_.push([E++,F]);continue}else return $(new j(e,l,F,y))}else y[R]=l[R]}}let b;const T=(h==null?void 0:h.exact)===!0;for(let R=0;R<n.length;R++){const C=n[R][1],F=C.name,V=Object.prototype.hasOwnProperty.call(l,F);if(!V){if(C.isOptional)continue;if(T){const ge=new ne(F,l,new fn(C));if(g){_.push([E++,ge]);continue}else return $(new j(e,l,ge,y))}}const M=n[R][0],D=M(l[F],h);if(de(D)){if(Te(D)){const ge=new ne(F,l,V?D.left:new fn(C));if(g){_.push([E++,ge]);continue}else return $(new j(e,l,ge,y))}y[F]=D.right}else{const ge=E++,ct=F;b||(b=[]),b.push(({es:Ft,output:Jn})=>m(Dt(D),un=>{if(Te(un)){const ln=new ne(ct,l,V?un.left:new fn(C));return g?(Ft.push([ge,ln]),I):$(new j(e,l,ln,Jn))}return Jn[ct]=un.right,I}))}}for(let R=0;R<o.length;R++){const C=o[R],F=C[0],V=C[1],M=ii(l,C[2]);for(const D of M){const ge=F(D,h);if(de(ge)&&vr(ge)){const ct=V(l[D],h);if(de(ct))if(Te(ct)){const Ft=new ne(D,l,ct.left);if(g){_.push([E++,Ft]);continue}else return $(new j(e,l,Ft,y))}else Object.prototype.hasOwnProperty.call(r,D)||(y[D]=ct.right);else{const Ft=E++,Jn=D;b||(b=[]),b.push(({es:un,output:ln})=>m(Dt(ct),Tr=>{if(Te(Tr)){const fo=new ne(Jn,l,Tr.left);return g?(un.push([Ft,fo]),I):$(new j(e,l,fo,ln))}else return Object.prototype.hasOwnProperty.call(r,D)||(ln[D]=Tr.right),I}))}}}}const A=({es:R,output:C})=>{if(Ar(R))return $(new j(e,l,Ce(R),C));if((h==null?void 0:h.propertyOrder)==="original"){const F=k||Gt(l);for(const M of s)F.indexOf(M)===-1&&F.push(M);const V={};for(const M of F)Object.prototype.hasOwnProperty.call(C,M)&&(V[M]=C[M]);return N(V)}return N(C)};if(b&&b.length>0){const R=b;return K(()=>{const C={es:zn(_),output:Object.assign({},y)};return m(yt(R,F=>F(C),{concurrency:d,batching:u,discard:!0}),()=>A(C))})}return A({es:_,output:y})}}case"Union":{const n=ty(e.types,t),r=Gt(n.keys),s=r.length,o=e.types.length,a=new Map;for(let u=0;u<o;u++)a.set(e.types[u],Q(e.types[u],t));const c=zr(e)??1,d=Wr(e);return(u,l)=>{const h=[];let g=0,_=[];if(s>0)if(Td(u))for(let w=0;w<s;w++){const y=r[w],k=n.keys[y].buckets;if(Object.prototype.hasOwnProperty.call(u,y)){const b=String(u[y]);if(Object.prototype.hasOwnProperty.call(k,b))_=_.concat(k[b]);else{const{candidates:T,literals:A}=n.keys[y],R=Ue.make(A),C=T.length===o?new gt([new G(y,R,!1,!0)],[]):Ue.make(T);h.push([g++,new j(C,u,new ne(y,u,new zt(R,u[y])))])}}else{const{candidates:b,literals:T}=n.keys[y],A=new G(y,Ue.make(T),!1,!0),R=b.length===o?new gt([A],[]):Ue.make(b);h.push([g++,new j(R,u,new ne(y,u,new fn(A)))])}}else{const w=n.candidates.length===o?e:Ue.make(n.candidates);h.push([g++,new zt(w,u)])}n.otherwise.length>0&&(_=_.concat(n.otherwise));let E;for(let w=0;w<_.length;w++){const y=_[w],k=a.get(y)(u,l);if(de(k)&&(!E||E.length===0)){if(vr(k))return k;h.push([g++,k.left])}else{const b=g++;E||(E=[]),E.push(T=>K(()=>"finalResult"in T?I:m(Dt(k),A=>(vr(A)?T.finalResult=A:T.es.push([b,A.left]),I))))}}const v=w=>Ar(w)?w.length===1&&w[0][1]._tag==="Type"?$(w[0][1]):$(new j(e,u,Ce(w))):$(new zt(e,u));if(E&&E.length>0){const w=E;return K(()=>{const y={es:zn(h)};return m(yt(w,k=>k(y),{concurrency:c,batching:d,discard:!0}),()=>"finalResult"in y?y.finalResult:v(y.es))})}return v(h)}}case"Suspend":{const n=ci(()=>Q(Xt(e.f(),e.annotations),t));return(r,s)=>n()(r,s)}}},Se=(e,t)=>n=>t(n)?N(n):$(new zt(e,n)),En=(e,t)=>{switch(e._tag){case"Declaration":{const n=Ci(e);if(Ie(n))return En(n.value,t);break}case"TypeLiteral":{const n=[];for(let r=0;r<e.propertySignatures.length;r++){const s=e.propertySignatures[r],o=t?Ao(s.type):Y(s.type);$o(o)&&!s.isOptional&&n.push([s.name,o])}return n}case"TupleType":{const n=[];for(let r=0;r<e.elements.length;r++){const s=e.elements[r],o=t?Ao(s.type):Y(s.type);$o(o)&&!s.isOptional&&n.push([r,o])}return n}case"Refinement":return En(e.from,t);case"Suspend":return En(e.f(),t);case"Transformation":return En(t?e.from:e.to,t)}return[]},ty=(e,t)=>{const n={},r=[],s=[];for(let o=0;o<e.length;o++){const a=e[o],c=En(a,t);if(c.length>0){s.push(a);for(let d=0;d<c.length;d++){const[u,l]=c[d],h=String(l.literal);n[u]=n[u]||{buckets:{},literals:[],candidates:[]};const g=n[u].buckets;if(Object.prototype.hasOwnProperty.call(g,h)){if(d<c.length-1)continue;g[h].push(a),n[u].literals.push(l),n[u].candidates.push(a)}else{g[h]=[a],n[u].literals.push(l),n[u].candidates.push(a);break}}}else r.push(a)}return{keys:n,otherwise:r,candidates:s}},eu=e=>Fs(e)?eu(e.from):e,kn=(e,t,n,r)=>{if((r==null?void 0:r.isEffectAllowed)===!0||de(e))return e;const s=new vd,o=Pd(e,{scheduler:s});s.flush();const a=o.unsafePoll();if(a){if(Ad(a))return N(a.value);const c=a.cause;return Lf(c)?$(c.error):$(new Ho(t,n,qf(c)))}return $(new Ho(t,n,"cannot be be resolved synchronously, this is caused by using runSync on an effect that performs async work"))},ny=([e],[t])=>e>t?1:e<t?-1:0;function Ce(e){return e.sort(ny).map(t=>t[1])}const ry=(e,t)=>{switch(e._tag){case"FinalTransformation":return t?e.decode:e.encode;case"ComposeTransformation":return N;case"TypeLiteralTransformation":return n=>{let r=N(n);for(const s of e.propertySignatureTransformations){const[o,a]=t?[s.from,s.to]:[s.to,s.from],c=t?s.decode:s.encode;r=De(r,u=>{const l=c(Object.prototype.hasOwnProperty.call(u,o)?ce(u[o]):re());return delete u[o],Ie(l)&&(u[a]=l.value),u})}return r}}},se=(e,t=[])=>({value:e,forest:t}),Xn={formatIssue:e=>De(Wt(e),sy),formatIssueSync:e=>{const t=Xn.formatIssue(e);return de(t)?kd(t):Ot(t)},formatError:e=>Xn.formatIssue(e.issue),formatErrorSync:e=>Xn.formatIssueSync(e.issue)},sy=e=>e.value+tu(`
`,e.forest),tu=(e,t)=>{let n="";const r=t.length;let s;for(let o=0;o<r;o++){s=t[o];const a=o===r-1;n+=e+(a?"└":"├")+"─ "+s.value,n+=tu(e+(r>1&&!a?"│  ":"   "),s.forest)}return n},oy=e=>{switch(e){case"Encoded":return"Encoded side transformation failure";case"Transformation":return"Transformation process failure";case"Type":return"Type side transformation failure"}},ay=e=>{switch(e){case"From":return"From side refinement failure";case"Predicate":return"Predicate refinement failure"}},nu=e=>"ast"in e?ce(e.ast):re(),Ss=N(void 0),iy=e=>nu(e).pipe(Ts(Dh),xe({onNone:()=>Ss,onSome:t=>{const n=t(e);return nt(n)?N({message:n,override:!1}):tn(n)?L(n,r=>({message:r,override:!1})):nt(n.message)?N({message:n.message,override:n.override}):L(n.message,r=>({message:r,override:n.override}))}})),Gs=e=>t=>t._tag===e,ru=Gs("Composite"),Go=Gs("Refinement"),Yo=Gs("Transformation"),Cn=e=>ft(iy(e),t=>t!==void 0?!t.override&&(ru(e)||Go(e)&&e.kind==="From"||Yo(e)&&e.kind!=="Transformation")?Yo(e)||Go(e)?Cn(e.issue):Ss:N(t.message):Ss),su=e=>nu(e).pipe(Ts(Kh),Id(t=>t(e)),vs);function cy(e){return Ti(e).pipe(pt(()=>Ri(e)),pt(()=>Oi(e)),pt(()=>_r(e)),Oe(()=>`{ ${e.from} | filter }`))}function uy(e){return e.message!==void 0?e.message:`Expected ${Fs(e.ast)?cy(e.ast):String(e.ast)}, actual ${Ke(e.actual)}`}const ly=e=>De(Cn(e),t=>t??su(e)??uy(e)),Hn=e=>su(e)??String(e.ast),dy=e=>e.message??"is forbidden",hy=e=>e.message??"is unexpected",fy=e=>{const t=Uh(e.ast);if(Ie(t)){const n=t.value();return nt(n)?N(n):n}return N(e.message??"is missing")},Wt=e=>{switch(e._tag){case"Type":return De(ly(e),se);case"Forbidden":return N(se(Hn(e),[se(dy(e))]));case"Unexpected":return N(se(hy(e)));case"Missing":return De(fy(e),se);case"Transformation":return ft(Cn(e),t=>t!==void 0?N(se(t)):De(Wt(e.issue),n=>se(Hn(e),[se(oy(e.kind),[n])])));case"Refinement":return ft(Cn(e),t=>t!==void 0?N(se(t)):De(Wt(e.issue),n=>se(Hn(e),[se(ay(e.kind),[n])])));case"Pointer":return De(Wt(e.issue),t=>se(Th(e.path),[t]));case"Composite":return ft(Cn(e),t=>{if(t!==void 0)return N(se(t));const n=Hn(e);return ui(e.issues)?De(yt(e.issues,Wt),r=>se(n,r)):De(Wt(e.issues),r=>se(n,[r]))})}};function Kt(e,t){return Pt(e.runtimeEffect,n=>vn(r=>(r.setFiberRefs(n.fiberRefs),r.currentRuntimeFlags=n.runtimeFlags,Yd(t,n.context))))}const py={...Kd,[ko]:ko,pipe(){return we(this,arguments)},commit(){return this.runtimeEffect}},my=(e,t)=>{t=t??Ld();const n=wo(Dd());let r;const s=vn(a=>(r||(r=Eo(Ud(Ga(Md(e,t),n),c=>{o.cachedRuntime=c}),{scope:n,scheduler:a.currentScheduler})),jd(r.await))),o=Object.assign(Object.create(py),{memoMap:t,scope:n,runtimeEffect:s,cachedRuntime:void 0,runtime(){return o.cachedRuntime===void 0?Ir(o.runtimeEffect):Promise.resolve(o.cachedRuntime)},dispose(){return Ir(o.disposeEffect)},disposeEffect:Qe(()=>(o.runtimeEffect=Qd("ManagedRuntime disposed"),o.cachedRuntime=void 0,ss(o.scope,Gd))),runFork(a,c){return o.cachedRuntime===void 0?Eo(Kt(o,a),c):ti(o.cachedRuntime)(a,c)},runSyncExit(a){return o.cachedRuntime===void 0?Hd(Kt(o,a)):Vd(o.cachedRuntime)(a)},runSync(a){return o.cachedRuntime===void 0?wo(Kt(o,a)):Wd(o.cachedRuntime)(a)},runPromiseExit(a,c){return o.cachedRuntime===void 0?Jd(Kt(o,a),c):zd(o.cachedRuntime)(a,c)},runCallback(a,c){return o.cachedRuntime===void 0?Ro(Bd)(Kt(o,a),c):Ro(o.cachedRuntime)(a,c)},runPromise(a,c){return o.cachedRuntime===void 0?Ir(Kt(o,a),c):qd(o.cachedRuntime)(a,c)}});return o},yy=my,Xo=Wm,gy=S(e=>gr(e[0]),(e,...t)=>{const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}),Sy=S(e=>gr(e[0]),(e,...t)=>{const n={...e};for(const r of t)delete n[r];return n}),Nn=Symbol.for("effect/Schema");function Ee(e){var t,n,r;return n=Nn,t=Nn,r=class{constructor(){i(this,n,Zo)}static annotations(o){return Ee(an(this.ast,o))}static pipe(){return we(this,arguments)}static toString(){return String(e)}},i(r,"ast",e),i(r,"Type"),i(r,"Encoded"),i(r,"Context"),i(r,t,Zo),r}const Zo={_A:e=>e,_I:e=>e,_R:e=>e},ea={schemaId:xh,message:li,missingMessage:Is,identifier:di,title:it,description:Dn,examples:hi,default:fi,documentation:Nh,jsonSchema:pi,arbitrary:mi,pretty:yi,equivalence:gi,concurrency:Si,batching:_i,parseIssueTitle:bi,parseOptions:wi,decodingFallback:Ei},Ys=e=>{if(!e)return{};const t={...e};for(const n in ea)if(n in e){const r=ea[n];t[r]=e[n],delete t[n]}return t},an=(e,t)=>Xt(e,Ys(t)),_y=(e,t)=>{const n=Gm(e,t);return(r,s)=>Yn(n(r,s),Hs)},ta=(e,t)=>{const n=Qm(e,t);return(r,s)=>si(n(r,s),Hs)},Zt=e=>At(e,Nn)&&gr(e[Nn]);class ou extends Ee(is){}class au extends Ee(Vh){}class by extends Ee(xs){}class wy extends Ee(Ai){}class f extends Ee(nf){}class x extends Ee(of){}class iu extends Ee(cf){}const Ey=e=>Ue.make(e.map(t=>t.ast));function cu(e,t=Ey(e)){var n;return n=class extends Ee(t){static annotations(s){return cu(this.members,an(this.ast,s))}},i(n,"members",[...e]),n}function Xs(...e){return Fi(e)?cu(e):As(e)?e[0]:by}const O=e=>Xs(e,au),ky=e=>Xs(e,ou),Zs=e=>Xs(e,au,ou),Ry=(e,t)=>new wr(e.map(n=>Zt(n)?new rn(n.ast,!1):n.ast),t.map(n=>Zt(n)?new br(n.ast):n.ast),!0);function uu(e,t,n=Ry(e,t)){var r;return r=class extends Ee(n){static annotations(o){return uu(this.elements,this.rest,an(this.ast,o))}},i(r,"elements",[...e]),i(r,"rest",[...t]),r}function lu(e,t){var n;return n=class extends uu([],[e],t){static annotations(s){return lu(this.value,an(this.ast,s))}},i(n,"value",e),n}const q=e=>lu(e),_s=e=>e?'"?:"':'":"';class du extends rn{constructor(n,r,s,o,a){super(n,r,o);i(this,"isReadonly");i(this,"defaultValue");i(this,"_tag","PropertySignatureDeclaration");this.isReadonly=s,this.defaultValue=a}toString(){const n=_s(this.isOptional),r=String(this.type);return`PropertySignature<${n}, ${r}, never, ${n}, ${r}>`}}class Oy extends rn{constructor(n,r,s,o,a){super(n,r,o);i(this,"isReadonly");i(this,"defaultValue");this.isReadonly=s,this.defaultValue=a}}const Ty=e=>e===void 0?"never":nt(e)?JSON.stringify(e):String(e);class Cy{constructor(t,n,r,s){i(this,"from");i(this,"to");i(this,"decode");i(this,"encode");i(this,"_tag","PropertySignatureTransformation");this.from=t,this.to=n,this.decode=r,this.encode=s}toString(){return`PropertySignature<${_s(this.to.isOptional)}, ${this.to.type}, ${Ty(this.from.fromKey)}, ${_s(this.from.isOptional)}, ${this.from.type}>`}}const hu=(e,t)=>{switch(e._tag){case"PropertySignatureDeclaration":return new du(e.type,e.isOptional,e.isReadonly,{...e.annotations,...t},e.defaultValue);case"PropertySignatureTransformation":return new Cy(e.from,new Oy(e.to.type,e.to.isOptional,e.to.isReadonly,{...e.to.annotations,...t},e.to.defaultValue),e.decode,e.encode)}},fu=Symbol.for("effect/PropertySignature"),pu=e=>At(e,fu);var Ta,Ca;Ca=Nn,Ta=fu;const ho=class ho{constructor(t){i(this,"ast");i(this,Ca);i(this,Ta,null);i(this,"_TypeToken");i(this,"_Key");i(this,"_EncodedToken");i(this,"_HasDefault");this.ast=t}pipe(){return we(this,arguments)}annotations(t){return new ho(hu(this.ast,Ys(t)))}toString(){return String(this.ast)}};let bs=ho;class eo extends bs{constructor(n,r){super(n);i(this,"from");this.from=r}annotations(n){return new eo(hu(this.ast,Ys(n)),this.from)}}const Ct=e=>{const t=e.ast===is||e.ast===xs?is:ky(e).ast;return new eo(new du(t,!0,!0,{},void 0),e)},$y=ji([Is]),Py=(e,t)=>{const n=Gt(e),r=[];if(n.length>0){const o=[],a=[],c=[];for(let d=0;d<n.length;d++){const u=n[d],l=e[u];if(pu(l)){const h=l.ast;switch(h._tag){case"PropertySignatureDeclaration":{const g=h.type,_=h.isOptional,E=h.annotations;o.push(new G(u,g,_,!0,$y(h))),a.push(new G(u,Y(g),_,!0,E)),r.push(new G(u,g,_,!0,E));break}case"PropertySignatureTransformation":{const g=h.from.fromKey??u;o.push(new G(g,h.from.type,h.from.isOptional,!0,h.from.annotations)),a.push(new G(u,h.to.type,h.to.isOptional,!0,h.to.annotations)),c.push(new yf(g,u,h.decode,h.encode));break}}}else o.push(new G(u,l.ast,!1,!0)),a.push(new G(u,Y(l.ast),!1,!0)),r.push(new G(u,l.ast,!1,!0))}if(As(c)){const d=[],u=[];for(const l of t){const{indexSignatures:h,propertySignatures:g}=vo(l.key.ast,l.value.ast);g.forEach(_=>{o.push(_),a.push(new G(_.name,Y(_.type),_.isOptional,_.isReadonly,_.annotations))}),h.forEach(_=>{d.push(_),u.push(new Mn(_.parameter,Y(_.type),_.isReadonly))})}return new Ls(new gt(o,d,{[as]:"Struct (Encoded side)"}),new gt(a,u,{[as]:"Struct (Type side)"}),new gf(c))}}const s=[];for(const o of t){const{indexSignatures:a,propertySignatures:c}=vo(o.key.ast,o.value.ast);c.forEach(d=>r.push(d)),a.forEach(d=>s.push(d))}return new gt(r,s)},vy=(e,t)=>{const n=Gt(e);for(const r of n){const s=e[r];if(t[r]===void 0&&pu(s)){const o=s.ast,a=o._tag==="PropertySignatureDeclaration"?o.defaultValue:o.to.defaultValue;a!==void 0&&(t[r]=a())}}return t};function mu(e,t,n=Py(e,t)){var r;return r=class extends Ee(n){static annotations(o){return mu(this.fields,this.records,an(this.ast,o))}static pick(...o){return P(gy(e,...o))}static omit(...o){return P(Sy(e,...o))}},i(r,"fields",{...e}),i(r,"records",[...t]),i(r,"make",(o,a)=>{const c=vy(e,{...o});return Iy(a)?c:Ym(r)(c)}),r}function P(e,...t){return mu(e,t)}const B=e=>Ee(Bt(e.ast));function yu(e,t,n){var r;return r=class extends Ee(n){static annotations(o){return yu(this.from,this.to,an(this.ast,o))}},i(r,"from",e),i(r,"to",t),r}const Ay=S(e=>Zt(e[0])&&Zt(e[1]),(e,t,n)=>yu(e,t,new Ls(e.ast,t.ast,new mf(n.decode,n.encode)))),z=S(e=>Zt(e[0])&&Zt(e[1]),(e,t,n)=>Ay(e,t,{strict:!0,decode:(r,s,o,a)=>Qo(n.decode(r,a)),encode:(r,s,o,a)=>Qo(n.encode(r,a))}));function Iy(e){return Rs(e)?e:(e==null?void 0:e.disableValidation)??!1}const xy=Pm,Ny=vm,Fy=Nm,Ly=Qc,Dy=jm,ws=P({message:f,details:Ct(wy),code:x}),j_=P({error:ws,correlationId:f}),gu=(e,t)=>{class n extends Wi{constructor(){super(...arguments);i(this,"_tag",t)}}return n.prototype[e]=e,n.prototype.name=t,n},na=Symbol.for("@effect/platform/Cookies"),ra=Symbol.for("@effect/platform/Cookies/Cookie"),Uy={[na]:na,...Sr,toJSON(){return{_id:"@effect/platform/Cookies",cookies:Eh(this.cookies,e=>e.toJSON())}},pipe(){return we(this,arguments)}},My=e=>{const t=Object.create(Uy);return t.cookies=e,t},jy=e=>{const t={};for(const n of e)t[n.name]=n;return My(t)},Ky=e=>{const t=typeof e=="string"?[e]:e,n=[];for(const r of t){const s=qy(r.trim());Ie(s)&&n.push(s.value)}return jy(n)};function qy(e){const t=e.split(";").map(c=>c.trim()).filter(c=>c!=="");if(t.length===0)return re();const n=t[0].indexOf("=");if(n===-1)return re();const r=t[0].slice(0,n);if(!By.test(r))return re();const s=t[0].slice(n+1),o=Jy(s);if(t.length===1)return ce(Object.assign(Object.create(sa),{name:r,value:o,valueEncoded:s}));const a={};for(let c=1;c<t.length;c++){const d=t[c],u=d.indexOf("="),l=u===-1?d:d.slice(0,u).trim(),h=u===-1?void 0:d.slice(u+1).trim();switch(l.toLowerCase()){case"domain":{if(h===void 0)break;const g=h.trim().replace(/^\./,"");g&&(a.domain=g);break}case"expires":{if(h===void 0)break;const g=new Date(h);isNaN(g.getTime())||(a.expires=g);break}case"max-age":{if(h===void 0)break;const g=parseInt(h,10);isNaN(g)||(a.maxAge=Xd(g));break}case"path":{if(h===void 0)break;h[0]==="/"&&(a.path=h);break}case"priority":{if(h===void 0)break;switch(h.toLowerCase()){case"low":a.priority="low";break;case"medium":a.priority="medium";break;case"high":a.priority="high";break}break}case"httponly":{a.httpOnly=!0;break}case"secure":{a.secure=!0;break}case"partitioned":{a.partitioned=!0;break}case"samesite":{if(h===void 0)break;switch(h.toLowerCase()){case"lax":a.sameSite="lax";break;case"strict":a.sameSite="strict";break;case"none":a.sameSite="none";break}break}}}return ce(Object.assign(Object.create(sa),{name:r,value:o,valueEncoded:s,options:Object.keys(a).length>0?a:void 0}))}const By=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,sa={[ra]:ra,...Sr,toJSON(){return{_id:"@effect/platform/Cookies/Cookie",name:this.name,value:this.value,options:this.options}}},Jy=e=>{try{return decodeURIComponent(e)}catch{return e}},oa=Symbol.for("@effect/platform/Headers"),Fn=Object.assign(Object.create(null),{[oa]:oa,[Zd](e){return ks(this,eh(e,_u))}}),Or=e=>Object.assign(Object.create(Fn),e),Su=Object.create(Fn),to=e=>{if(e===void 0)return Su;if(Symbol.iterator in e){const n=Object.create(Fn);for(const[r,s]of e)n[r.toLowerCase()]=s;return n}const t=Object.create(Fn);for(const[n,r]of Object.entries(e))Array.isArray(r)?t[n.toLowerCase()]=r.join(", "):r!==void 0&&(t[n.toLowerCase()]=r);return t},zy=e=>Object.setPrototypeOf(e,Fn),Es=S(3,(e,t,n)=>{const r=Or(e);return r[t.toLowerCase()]=n,r}),Wy=S(2,(e,t)=>Or({...e,...to(t)})),Hy=S(2,(e,t)=>{const n=Or(e);return Object.assign(n,t),n}),Vy=S(2,(e,t)=>{const n=Or(e),r=s=>{if(typeof s=="string"){const o=s.toLowerCase();o in e&&delete n[o]}else for(const o in e)s.test(o)&&delete n[o]};if(Array.isArray(t))for(let s=0;s<t.length;s++)r(t[s]);else r(t);return n}),ks=S(2,(e,t)=>{const n={...e},r=s=>{if(typeof s=="string"){const o=s.toLowerCase();o in e&&(n[o]=Xo(e[o]))}else for(const o in e)s.test(o)&&(n[o]=Xo(e[o]))};if(Array.isArray(t))for(let s=0;s<t.length;s++)r(t[s]);else r(t);return n}),_u=at("@effect/platform/Headers/currentRedactedNames",()=>Ks(["authorization","cookie","set-cookie","x-api-key"])),Qy=Symbol.for("@effect/platform/HttpClientError"),bu=Qy;class wu extends gu(bu,"RequestError"){get methodAndUrl(){return`${this.request.method} ${this.request.url}`}get message(){return this.description?`${this.reason}: ${this.description} (${this.methodAndUrl})`:`${this.reason} error (${this.methodAndUrl})`}}class lt extends gu(bu,"ResponseError"){get methodAndUrl(){return`${this.request.method} ${this.request.url}`}get message(){const t=`${this.response.status} ${this.methodAndUrl}`;return this.description?`${this.reason}: ${this.description} (${t})`:`${this.reason} error (${t})`}}const no=e=>{const t=Eu(e),n=[];for(let r=0;r<t.length;r++)if(Array.isArray(t[r][0])){const[s,o]=t[r];n.push([`${s[0]}[${s.slice(1).join("][")}]`,o])}else n.push(t[r]);return n},Eu=e=>{const t=Symbol.iterator in e?tr(e):Object.entries(e),n=[];for(const[r,s]of t)if(Array.isArray(s))for(let o=0;o<s.length;o++)s[o]!==void 0&&n.push([r,String(s[o])]);else if(typeof s=="object"){const o=Eu(s);for(const[a,c]of o)n.push([[r,...typeof a=="string"?[a]:a],c])}else s!==void 0&&n.push([r,String(s)]);return n},Gy=[],Yy=S(2,(e,t)=>{const n=no(t),r=n.map(([s])=>s);return th(nh(e,([s])=>r.includes(s)),n)}),Xy=(e,t,n)=>{try{const r=new URL(e,Zy());for(let s=0;s<t.length;s++){const[o,a]=t[s];a!==void 0&&r.searchParams.append(o,a)}return n._tag==="Some"&&(r.hash=n.value),N(r)}catch(r){return $(r)}},Zy=()=>{if("location"in globalThis&&globalThis.location!==void 0&&globalThis.location.origin!==void 0&&globalThis.location.pathname!==void 0)return location.origin+location.pathname},$n=Symbol.for("@effect/platform/HttpIncomingMessage"),eg=(e,t)=>{const n=e.headers["content-type"]??"";let r;if(n.includes("application/json"))try{r=Ot(e.json)}catch{}else if(n.includes("text/")||n.includes("urlencoded"))try{r=Ot(e.text)}catch{}const s={...t,headers:oi(e.headers),remoteAddress:e.remoteAddress.toJSON()};return r!==void 0&&(s.body=r),s},tg=e=>zy({b3:`${e.traceId}-${e.spanId}-${e.sampled?"1":"0"}${e.parent._tag==="Some"?`-${e.parent.value.spanId}`:""}`,traceparent:`00-${e.traceId}-${e.spanId}-${e.sampled?"01":"00"}`}),Hr=Symbol.for("@effect/platform/HttpBody");var $a;class ku{constructor(){i(this,$a);this[Hr]=Hr}[($a=Hr,$t)](){return this.toJSON()}toString(){return Cs(this)}}class ng extends ku{constructor(){super(...arguments);i(this,"_tag","Empty")}toJSON(){return{_id:"@effect/platform/HttpBody",_tag:"Empty"}}}const rg=new ng;class sg extends ku{constructor(n,r){super();i(this,"body");i(this,"contentType");i(this,"_tag","Uint8Array");this.body=n,this.contentType=r}get contentLength(){return this.body.length}toJSON(){return{_id:"@effect/platform/HttpBody",_tag:"Uint8Array",body:this.contentType.startsWith("text/")||this.contentType.endsWith("json")?new TextDecoder().decode(this.body):`Uint8Array(${this.body.length})`,contentType:this.contentType,contentLength:this.contentLength}}}const og=(e,t)=>new sg(e,t??"application/octet-stream"),ag=new TextEncoder,ig=(e,t)=>og(ag.encode(e),t??"text/plain"),cg=e=>ig(JSON.stringify(e),"application/json"),aa=Symbol.for("@effect/platform/HttpClientRequest"),ug={[aa]:aa,...Sr,toJSON(){return{_id:"@effect/platform/HttpClientRequest",method:this.method,url:this.url,urlParams:this.urlParams,hash:this.hash,headers:oi(this.headers),body:this.body.toJSON()}},pipe(){return we(this,arguments)}};function ze(e,t,n,r,s,o){const a=Object.create(ug);return a.method=e,a.url=t,a.urlParams=n,a.hash=r,a.headers=s,a.body=o,a}const lg=ze("GET","",Gy,re(),Su,rg),Nt=e=>(t,n)=>Sg(lg,{method:e,url:t,...n??void 0}),dg=Nt("GET"),hg=Nt("POST"),fg=Nt("PUT"),pg=Nt("PATCH"),mg=Nt("DELETE"),yg=Nt("HEAD"),gg=Nt("OPTIONS"),Sg=S(2,(e,t)=>{let n=e;return t.method&&(n=wg(n,t.method)),t.url&&(n=Eg(n,t.url)),t.headers&&(n=Ru(n,t.headers)),t.urlParams&&(n=Rg(n,t.urlParams)),t.hash&&(n=Og(n,t.hash)),t.body&&(n=Tg(n,t.body)),t.accept&&(n=Ou(n,t.accept)),t.acceptJson&&(n=bg(n)),n}),_g=S(3,(e,t,n)=>ze(e.method,e.url,e.urlParams,e.hash,Es(e.headers,t,n),e.body)),Ru=S(2,(e,t)=>ze(e.method,e.url,e.urlParams,e.hash,Wy(e.headers,t),e.body)),Ou=S(2,(e,t)=>_g(e,"Accept",t)),bg=Ou("application/json"),wg=S(2,(e,t)=>ze(t,e.url,e.urlParams,e.hash,e.headers,e.body)),Eg=S(2,(e,t)=>{if(typeof t=="string")return ze(e.method,t,e.urlParams,e.hash,e.headers,e.body);const n=new URL(t.toString()),r=no(n.searchParams),s=n.hash?ce(n.hash.slice(1)):re();return n.search="",n.hash="",ze(e.method,n.toString(),r,s,e.headers,e.body)}),kg=S(2,(e,t)=>ze(e.method,t.endsWith("/")&&e.url.startsWith("/")?t+e.url.slice(1):t+e.url,e.urlParams,e.hash,e.headers,e.body)),Rg=S(2,(e,t)=>ze(e.method,e.url,Yy(e.urlParams,t),e.hash,e.headers,e.body)),Og=S(2,(e,t)=>ze(e.method,e.url,e.urlParams,ce(t),e.headers,e.body)),Tg=S(2,(e,t)=>{let n=e.headers;if(t._tag==="Empty"||t._tag==="FormData")n=Vy(n,["Content-type","Content-length"]);else{const r=t.contentType;r&&(n=Es(n,"content-type",r));const s=t.contentLength;s&&(n=Es(n,"content-length",s.toString()))}return ze(e.method,e.url,e.urlParams,e.hash,n,t)}),Pn=Symbol.for("@effect/platform/HttpClientResponse"),Cg=(e,t)=>new $g(e,t);var Pa,va,Aa;class $g extends(Aa=rh,va=$n,Pa=Pn,Aa){constructor(n,r){super();i(this,"request");i(this,"source");i(this,va);i(this,Pa);i(this,"cachedCookies");i(this,"textBody");i(this,"formDataBody");i(this,"arrayBufferBody");this.request=n,this.source=r,this[$n]=$n,this[Pn]=Pn}toJSON(){return eg(this,{_id:"@effect/platform/HttpClientResponse",request:this.request.toJSON(),status:this.status})}get status(){return this.source.status}get headers(){return to(this.source.headers)}get cookies(){return this.cachedCookies?this.cachedCookies:this.cachedCookies=Ky(this.source.headers.getSetCookie())}get remoteAddress(){return re()}get stream(){return this.source.body?Fy(()=>this.source.body,n=>new lt({request:this.request,response:this,reason:"Decode",cause:n})):Ny(new lt({request:this.request,response:this,reason:"EmptyBody",description:"can not create stream from empty body"}))}get json(){return sh(this.text,{try:n=>n===""?null:JSON.parse(n),catch:n=>new lt({request:this.request,response:this,reason:"Decode",cause:n})})}get text(){return this.textBody??(this.textBody=On({try:()=>this.source.text(),catch:n=>new lt({request:this.request,response:this,reason:"Decode",cause:n})}).pipe(xr,Ot))}get urlParamsBody(){return m(this.text,n=>oh({try:()=>no(new URLSearchParams(n)),catch:r=>new lt({request:this.request,response:this,reason:"Decode",cause:r})}))}get formData(){return this.formDataBody??(this.formDataBody=On({try:()=>this.source.formData(),catch:n=>new lt({request:this.request,response:this,reason:"Decode",cause:n})}).pipe(xr,Ot))}get arrayBuffer(){return this.arrayBufferBody??(this.arrayBufferBody=On({try:()=>this.source.arrayBuffer(),catch:n=>new lt({request:this.request,response:this,reason:"Decode",cause:n})}).pipe(xr,Ot))}}const Pg=e=>e.status>=200&&e.status<300?U(e):_e(new lt({response:e,request:e.request,reason:"StatusCode",description:"non 2xx status code"})),ia=Symbol.for("@effect/platform/HttpClient"),ro=ah("@effect/platform/HttpClient"),vg=at(Symbol.for("@effect/platform/HttpClient/tracerDisabledWhen"),()=>Ks(ph)),Ag=at(Symbol.for("@effect/platform/HttpClient/currentTracerPropagation"),()=>Ks(!0)),Ig=hh()("@effect/platform/HttpClient/SpanNameGenerator",{defaultValue:()=>e=>`http.client ${e.method}`}),xg={[ia]:ia,pipe(){return we(this,arguments)},...Sr,toJSON(){return{_id:"@effect/platform/HttpClient"}},get(e,t){return this.execute(dg(e,t))},head(e,t){return this.execute(yg(e,t))},post(e,t){return this.execute(hg(e,t))},put(e,t){return this.execute(fg(e,t))},patch(e,t){return this.execute(pg(e,t))},del(e,t){return this.execute(mg(e,t))},options(e,t){return this.execute(gg(e,t))}},so=(e,t)=>{const n=Object.create(xg);return n.preprocess=t,n.postprocess=e,n.execute=function(r){return e(t(r))},n},ar=at("@effect/platform/HttpClient/responseRegistry",()=>{if("FinalizationRegistry"in globalThis&&globalThis.FinalizationRegistry){const t=new FinalizationRegistry(n=>{n.abort()});return{register(n,r){t.register(n,r,n)},unregister(n){t.unregister(n)}}}const e=new Map;return{register(t,n){e.set(t,setTimeout(()=>n.abort(),5e3))},unregister(t){const n=e.get(t);n!==void 0&&(clearTimeout(n),e.delete(t))}}}),Ng=at("@effect/platform/HttpClient/scopedRequests",()=>new WeakMap),Fg=e=>so(t=>m(t,n=>os(r=>{const s=Ng.get(n),o=s??new AbortController,a=Xy(n.url,n.urlParams,n.hash);if(a._tag==="Left")return _e(new wu({request:n,reason:"InvalidUrl",cause:a.left}));const c=a.right;if(!r.getFiberRef(Jf)||r.getFiberRef(vg)(n)){const l=e(n,c,o.signal,r);return s?l:nr(h=>Pe(h(l),{onSuccess(g){return ar.register(g,o),U(new ca(g,o))},onFailure(g){return ds(g)&&o.abort(),tt(g)}}))}const u=dh(r.currentContext,Ig);return fh(u(n),{kind:"client",captureStackTrace:!1},l=>{l.attribute("http.request.method",n.method),l.attribute("server.address",c.origin),c.port!==""&&l.attribute("server.port",+c.port),l.attribute("url.full",c.toString()),l.attribute("url.path",c.pathname),l.attribute("url.scheme",c.protocol.slice(0,-1));const h=c.search.slice(1);h!==""&&l.attribute("url.query",h);const g=r.getFiberRef(_u),_=ks(n.headers,g);for(const E in _)l.attribute(`http.request.header.${E}`,String(_[E]));return n=r.getFiberRef(Ag)?Ru(n,tg(l)):n,nr(E=>E(e(n,c,o.signal,r)).pipe(yh(l),Pe({onSuccess:v=>{l.attribute("http.response.status_code",v.status);const w=ks(v.headers,g);for(const y in w)l.attribute(`http.response.header.${y}`,String(w[y]));return s?U(v):(ar.register(v,o),U(new ca(v,o)))},onFailure(v){return!s&&ds(v)&&o.abort(),tt(v)}})))})})),U);var Ia,xa;class ca{constructor(t,n){i(this,"original");i(this,"controller");i(this,xa,Pn);i(this,Ia,$n);this.original=t,this.controller=n}applyInterrupt(t){return K(()=>(ar.unregister(this.original),mh(t,()=>W(()=>{this.controller.abort()}))))}get request(){return this.original.request}get status(){return this.original.status}get headers(){return this.original.headers}get cookies(){return this.original.cookies}get remoteAddress(){return this.original.remoteAddress}get formData(){return this.applyInterrupt(this.original.formData)}get text(){return this.applyInterrupt(this.original.text)}get json(){return this.applyInterrupt(this.original.json)}get urlParamsBody(){return this.applyInterrupt(this.original.urlParamsBody)}get arrayBuffer(){return this.applyInterrupt(this.original.arrayBuffer)}get stream(){return Ly(()=>(ar.unregister(this.original),xy(this.original.stream,t=>(gh(t)&&this.controller.abort(),I))))}toJSON(){return this.original.toJSON()}[(xa=Pn,Ia=$n,$t)](){return this.original[$t]()}}const{del:K_,execute:q_,get:B_,head:J_,options:z_,patch:W_,post:H_,put:V_}=lh(ro),Lg=e=>oo(e,m(Pg)),oo=S(2,(e,t)=>{const n=e;return so(r=>t(n.postprocess(r)),n.preprocess)}),Dg=S(2,(e,t)=>{const n=e;return so(n.postprocess,r=>L(n.preprocess(r),t))}),Ug=e=>te(ro,m(ih(),t=>L(e,n=>oo(n,ch(r=>uh(t,r)))))),Mg="@effect/platform/FetchHttpClient/Fetch",Tu="@effect/platform/FetchHttpClient/FetchOptions",jg=Fg((e,t,n,r)=>{const s=r.getFiberRef(Bf),o=s.unsafeMap.get(Mg)??globalThis.fetch,a=s.unsafeMap.get(Tu)??{},c=a.headers?Hy(to(a.headers),e.headers):e.headers,d=u=>L(On({try:()=>o(t,{...a,method:e.method,headers:c,body:u,duplex:e.body._tag==="Stream"?"half":void 0,signal:n}),catch:l=>new wu({request:e,reason:"Transport",cause:l})}),l=>Cg(e,l));switch(e.body._tag){case"Raw":case"Uint8Array":return d(e.body.body);case"FormData":return d(e.body.formData);case"Stream":return m(Dy(e.body.stream),d)}return d(void 0)}),Kg=Ug(U(jg));class qg extends ai(Tu)(){}const Bg=Kg,Jg=ro,zg=Lg,ua=oo,Wg=Dg,Hg=kg,Be=cg;var Ve;function Cu(e){return{lang:(e==null?void 0:e.lang)??(Ve==null?void 0:Ve.lang),message:e==null?void 0:e.message,abortEarly:(e==null?void 0:e.abortEarly)??(Ve==null?void 0:Ve.abortEarly),abortPipeEarly:(e==null?void 0:e.abortPipeEarly)??(Ve==null?void 0:Ve.abortPipeEarly)}}var Vr;function Vg(e){return Vr==null?void 0:Vr.get(e)}var Qr;function Qg(e){return Qr==null?void 0:Qr.get(e)}var Gr;function Gg(e,t){var n;return(n=Gr==null?void 0:Gr.get(e))==null?void 0:n.get(t)}function en(e){var n,r;const t=typeof e;return t==="string"?`"${e}"`:t==="number"||t==="bigint"||t==="boolean"?`${e}`:t==="object"||t==="function"?(e&&((r=(n=Object.getPrototypeOf(e))==null?void 0:n.constructor)==null?void 0:r.name))??"null":t}function be(e,t,n,r,s){const o=s&&"input"in s?s.input:n.value,a=(s==null?void 0:s.expected)??e.expects??null,c=(s==null?void 0:s.received)??en(o),d={kind:e.kind,type:e.type,input:o,expected:a,received:c,message:`Invalid ${t}: ${a?`Expected ${a} but r`:"R"}eceived ${c}`,requirement:e.requirement,path:s==null?void 0:s.path,issues:s==null?void 0:s.issues,lang:r.lang,abortEarly:r.abortEarly,abortPipeEarly:r.abortPipeEarly},u=e.kind==="schema",l=(s==null?void 0:s.message)??e.message??Gg(e.reference,d.lang)??(u?Qg(d.lang):null)??r.message??Vg(d.lang);l!==void 0&&(d.message=typeof l=="function"?l(d):l),u&&(n.typed=!1),n.issues?n.issues.push(d):n.issues=[d]}function He(e){return{version:1,vendor:"valibot",validate(t){return e["~run"]({value:t},Cu())}}}function Yg(e,t){const n=[...new Set(e)];return n.length>1?`(${n.join(` ${t} `)})`:n[0]??"never"}var Xg=class extends Error{constructor(e){super(e[0].message),this.name="ValiError",this.issues=e}},Zg=/^[\w+-]+(?:\.[\w+-]+)*@[\da-z]+(?:[.-][\da-z]+)*\.[a-z]{2,}$/iu;function eS(e){return{kind:"validation",type:"email",reference:eS,expects:null,async:!1,requirement:Zg,message:e,"~run"(t,n){return t.typed&&!this.requirement.test(t.value)&&be(this,"email",t,n),t}}}function tS(e,t){return{kind:"validation",type:"max_value",reference:tS,async:!1,expects:`<=${e instanceof Date?e.toJSON():en(e)}`,requirement:e,message:t,"~run"(n,r){return n.typed&&!(n.value<=this.requirement)&&be(this,"value",n,r,{received:n.value instanceof Date?n.value.toJSON():en(n.value)}),n}}}function nS(e,t){return{kind:"validation",type:"min_length",reference:nS,async:!1,expects:`>=${e}`,requirement:e,message:t,"~run"(n,r){return n.typed&&n.value.length<this.requirement&&be(this,"length",n,r,{received:`${n.value.length}`}),n}}}function rS(e,t){return{kind:"validation",type:"min_value",reference:rS,async:!1,expects:`>=${e instanceof Date?e.toJSON():en(e)}`,requirement:e,message:t,"~run"(n,r){return n.typed&&!(n.value>=this.requirement)&&be(this,"value",n,r,{received:n.value instanceof Date?n.value.toJSON():en(n.value)}),n}}}function sS(e,t,n){return typeof e.fallback=="function"?e.fallback(t,n):e.fallback}function ao(e,t,n){return typeof e.default=="function"?e.default(t,n):e.default}function oS(e,t){return{kind:"schema",type:"array",reference:oS,expects:"Array",async:!1,item:e,message:t,get"~standard"(){return He(this)},"~run"(n,r){var o;const s=n.value;if(Array.isArray(s)){n.typed=!0,n.value=[];for(let a=0;a<s.length;a++){const c=s[a],d=this.item["~run"]({value:c},r);if(d.issues){const u={type:"array",origin:"value",input:s,key:a,value:c};for(const l of d.issues)l.path?l.path.unshift(u):l.path=[u],(o=n.issues)==null||o.push(l);if(n.issues||(n.issues=d.issues),r.abortEarly){n.typed=!1;break}}d.typed||(n.typed=!1),n.value.push(d.value)}}else be(this,"type",n,r);return n}}}function aS(e){return{kind:"schema",type:"boolean",reference:aS,expects:"boolean",async:!1,message:e,get"~standard"(){return He(this)},"~run"(t,n){return typeof t.value=="boolean"?t.typed=!0:be(this,"type",t,n),t}}}function iS(e,t){return{kind:"schema",type:"literal",reference:iS,expects:en(e),async:!1,literal:e,message:t,get"~standard"(){return He(this)},"~run"(n,r){return n.value===this.literal?n.typed=!0:be(this,"type",n,r),n}}}function cS(e,t){return{kind:"schema",type:"nullable",reference:cS,expects:`(${e.expects} | null)`,async:!1,wrapped:e,default:t,get"~standard"(){return He(this)},"~run"(n,r){return n.value===null&&(this.default!==void 0&&(n.value=ao(this,n,r)),n.value===null)?(n.typed=!0,n):this.wrapped["~run"](n,r)}}}function uS(e){return{kind:"schema",type:"number",reference:uS,expects:"number",async:!1,message:e,get"~standard"(){return He(this)},"~run"(t,n){return typeof t.value=="number"&&!isNaN(t.value)?t.typed=!0:be(this,"type",t,n),t}}}function $u(e,t){return{kind:"schema",type:"object",reference:$u,expects:"Object",async:!1,entries:e,message:t,get"~standard"(){return He(this)},"~run"(n,r){var o;const s=n.value;if(s&&typeof s=="object"){n.typed=!0,n.value={};for(const a in this.entries){const c=this.entries[a];if(a in s||(c.type==="exact_optional"||c.type==="optional"||c.type==="nullish")&&c.default!==void 0){const d=a in s?s[a]:ao(c),u=c["~run"]({value:d},r);if(u.issues){const l={type:"object",origin:"value",input:s,key:a,value:d};for(const h of u.issues)h.path?h.path.unshift(l):h.path=[l],(o=n.issues)==null||o.push(h);if(n.issues||(n.issues=u.issues),r.abortEarly){n.typed=!1;break}}u.typed||(n.typed=!1),n.value[a]=u.value}else if(c.fallback!==void 0)n.value[a]=sS(c);else if(c.type!=="exact_optional"&&c.type!=="optional"&&c.type!=="nullish"&&(be(this,"key",n,r,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:s,key:a,value:s[a]}]}),r.abortEarly))break}}else be(this,"type",n,r);return n}}}function lS(e,t){return{kind:"schema",type:"optional",reference:lS,expects:`(${e.expects} | undefined)`,async:!1,wrapped:e,default:t,get"~standard"(){return He(this)},"~run"(n,r){return n.value===void 0&&(this.default!==void 0&&(n.value=ao(this,n,r)),n.value===void 0)?(n.typed=!0,n):this.wrapped["~run"](n,r)}}}function Pu(e){return{kind:"schema",type:"string",reference:Pu,expects:"string",async:!1,message:e,get"~standard"(){return He(this)},"~run"(t,n){return typeof t.value=="string"?t.typed=!0:be(this,"type",t,n),t}}}function la(e){let t;if(e)for(const n of e)t?t.push(...n.issues):t=n.issues;return t}function dS(e,t){return{kind:"schema",type:"union",reference:dS,expects:Yg(e.map(n=>n.expects),"|"),async:!1,options:e,message:t,get"~standard"(){return He(this)},"~run"(n,r){let s,o,a;for(const c of this.options){const d=c["~run"]({value:n.value},r);if(d.typed)if(d.issues)o?o.push(d):o=[d];else{s=d;break}else a?a.push(d):a=[d]}if(s)return s;if(o){if(o.length===1)return o[0];be(this,"type",n,r,{issues:la(o)}),n.typed=!0}else{if((a==null?void 0:a.length)===1)return a[0];be(this,"type",n,r,{issues:la(a)})}return n}}}function hS(e,t,n){const r=e["~run"]({value:t},Cu(n));if(r.issues)throw new Xg(r.issues);return r.value}function Q_(...e){return{...e[0],pipe:e,get"~standard"(){return He(this)},"~run"(t,n){for(const r of e)if(r.kind!=="metadata"){if(t.issues&&(r.kind==="schema"||r.kind==="transformation")){t.typed=!1;break}(!t.issues||!n.abortEarly&&!n.abortPipeEarly)&&(t=r["~run"](t,n))}return t}}}const fS={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,TSS_APP_BASE:"/",TSS_CLIENT_ENTRY:"/~start/default-client-entry",TSS_OUTPUT_PUBLIC_DIR:"/home/<USER>/Work/schedhold/schedhold-frontend/.output/public",TSS_SERVER_FN_BASE:"/_serverFn",VITE_API_URL:"http://localhost:8000"},pS=$u({VITE_API_URL:Pu()}),mS=hS(pS,fS),Et={API_URL:mS.VITE_API_URL,CORRELATION_ID_HEADER:"x-correlation-id"};class dt extends Hi("AppError"){}var oe=(e=>(e[e.ClientParseError=-3]="ClientParseError",e[e.RequestError=-2]="RequestError",e[e.ResponseError=-1]="ResponseError",e[e.InternalErrorCode=0]="InternalErrorCode",e[e.MultiErrorCode=1]="MultiErrorCode",e[e.ParseErrorCode=2]="ParseErrorCode",e[e.BadRequestCode=3]="BadRequestCode",e[e.UnauthorizedCode=4]="UnauthorizedCode",e[e.NotFoundCode=5]="NotFoundCode",e[e.ConflictCode=6]="ConflictCode",e))(oe||{});const yS=Bg.pipe(Fe(Wf(qg,{credentials:"include"}))),gS=Rn(function*(){return{httpClient:(yield*Jg).pipe(Wg(Hg(`${Et.API_URL}/api`)),zg,ua(rr({RequestError:t=>_e(new dt({correlationId:oe.RequestError.toString(),error:{code:oe.RequestError,details:t,message:"Request error"}})),ResponseError:t=>t.response.json.pipe(m(n=>ta(ws)(n).pipe(fe({onLeft:r=>_e(new dt({correlationId:t.response.headers[Et.CORRELATION_ID_HEADER]??oe.ResponseError.toString(),error:{code:oe.ResponseError,details:r,message:"Response error"}})),onRight:r=>_e(new dt({correlationId:t.response.headers[Et.CORRELATION_ID_HEADER]??oe.ResponseError.toString(),error:r}))}))))})),ua(rr({ResponseError:t=>t.response.json.pipe(ta(ws),fe({onLeft:n=>_e(new dt({correlationId:t.response.headers[Et.CORRELATION_ID_HEADER]??oe.ResponseError.toString(),error:{code:oe.ClientParseError,details:n,message:"Response error"}})),onRight:n=>_e(new dt({correlationId:t.response.headers[Et.CORRELATION_ID_HEADER]??oe.ResponseError.toString(),error:n}))}))})))}}).pipe(st(yS)),fr=class fr extends ai("ApiHttpClient")(){};i(fr,"Live",te(fr,gS));let Re=fr;const me=e=>t=>t.json.pipe(m(_y(e)),rr({ParseError:n=>_e(new dt({correlationId:oe.ClientParseError.toString(),error:{code:oe.ClientParseError,details:n,message:"Parse error"}})),ResponseError:n=>_e(new dt({correlationId:t.headers[Et.CORRELATION_ID_HEADER]??oe.ResponseError.toString(),error:{code:oe.ResponseError,details:n,message:"Response error"}}))})),ot=e=>e.json.pipe(rr({ResponseError:t=>_e(new dt({correlationId:e.headers[Et.CORRELATION_ID_HEADER]??oe.ResponseError.toString(),error:{code:oe.ResponseError,details:t,message:"Response error"}}))}),Ne(U(I)));class ir extends nn("AuthRepository")(){}const vu=P({id:f,name:f,email:f,createdAt:O(f),updatedAt:O(f),deletedAt:O(f)}),SS=P({id:f,name:f,email:f,created_at:O(f),updated_at:O(f),deleted_at:O(f)}),_S=z(SS,vu,{strict:!0,decode:e=>({...e,createdAt:e.created_at,updatedAt:e.updated_at,deletedAt:e.deleted_at}),encode:e=>({...e,created_at:e.createdAt,updated_at:e.updatedAt,deleted_at:e.deletedAt})}),bS=P({username:f,password:f});P({user:vu});const da=P({user:_S}),wS=bS,Yr="/v1/auth",ES=Re.pipe(Ne(({httpClient:e})=>e),Ne(e=>({login:t=>e.post(`${Yr}/login`,{body:Be(ee(wS)(t))}).pipe(m(me(da))),logout:()=>e.post(`${Yr}/logout`).pipe(m(ot)),getSession:()=>e.get(`${Yr}/is_logged_in`).pipe(m(me(da)))})),st(Re.Live)),kS=te(ir,ES),RS=te(ir,ir),OS=te(Sh,ir);class cr extends nn("ClientRepository")(){}var TS=(e=>(e[e.DNI=0]="DNI",e[e.PASAPORTE=1]="PASAPORTE",e[e.RUC=2]="RUC",e))(TS||{});const cn=P({id:f,name:f,fatherLastName:f,motherLastName:f,email:Ct(f),address:Ct(f),phone:Ct(f),birthDate:O(f),gender:iu,document:f,documentType:x,createdAt:O(f),updatedAt:O(f),deletedAt:O(f)}),io=cn.omit("id","createdAt","updatedAt","deletedAt"),co=cn.omit("createdAt","updatedAt","deletedAt"),Bn=P({id:f,name:f,father_last_name:f,mother_last_name:f,email:Ct(f),address:Ct(f),phone:Ct(f),birth_date:O(f),gender:iu,document:f,document_type:x,created_at:O(f),updated_at:O(f),deleted_at:O(f)}),vt=z(Bn,cn,{strict:!0,decode:e=>({...e,fatherLastName:e.father_last_name,motherLastName:e.mother_last_name,birthDate:e.birth_date,documentType:e.document_type,createdAt:e.created_at,updatedAt:e.updated_at,deletedAt:e.deleted_at}),encode:e=>({...e,father_last_name:e.fatherLastName,mother_last_name:e.motherLastName,birth_date:e.birthDate,document_type:e.documentType,created_at:e.createdAt,updated_at:e.updatedAt,deleted_at:e.deletedAt})}),CS=z(B(Zs(q(vt))),B(q(cn)),{strict:!0,decode:e=>e||[],encode:e=>e}),uo=Bn.omit("id","created_at","updated_at","deleted_at"),Ln=z(io,uo,{strict:!0,decode:e=>({...e,father_last_name:e.fatherLastName,mother_last_name:e.motherLastName,birth_date:e.birthDate,document_type:e.documentType}),encode:e=>({...e,fatherLastName:e.father_last_name,motherLastName:e.mother_last_name,birthDate:e.birth_date,documentType:e.document_type})}),$S=f,lo=Bn.omit("created_at","updated_at","deleted_at"),ur=z(co,lo,{strict:!0,decode:e=>({...e,father_last_name:e.fatherLastName,mother_last_name:e.motherLastName,birth_date:e.birthDate,document_type:e.documentType}),encode:e=>({...e,fatherLastName:e.father_last_name,motherLastName:e.mother_last_name,birthDate:e.birth_date,documentType:e.document_type})}),Au=P({id:f,person:cn,createdAt:O(f),updatedAt:O(f),deletedAt:O(f)}),PS=P({person:io}),vS=P({id:f,person:co}),AS=P({id:f,person:Bn,created_at:O(f),updated_at:O(f),deleted_at:O(f)}),Iu=z(AS,Au,{strict:!0,decode:e=>({...e,person:ee(vt)(e.person),createdAt:e.created_at,updatedAt:e.updated_at,deletedAt:e.deleted_at}),encode:e=>({...e,person:We(vt)(e.person),created_at:e.createdAt,updated_at:e.updatedAt,deleted_at:e.deletedAt})}),IS=z(B(Zs(q(Iu))),B(q(Au)),{strict:!0,decode:e=>e||[],encode:e=>e}),xS=P({person:uo}),NS=z(PS,xS,{strict:!0,encode:e=>({...e,person:We(Ln)(e.person)}),decode:e=>({...e,person:ee(Ln)(e.person)})}),FS=f,LS=P({id:f,person:lo}),DS=z(vS,LS,{strict:!0,encode:e=>({...e,person:We(ur)(e.person)}),decode:e=>({...e,person:ee(ur)(e.person)})}),pn="/v1/clients",US=Re.pipe(Ne(({httpClient:e})=>e),Ne(e=>({getAll:()=>e.get(pn).pipe(m(me(IS))),getById:t=>e.get(`${pn}/${t}`).pipe(m(me(Iu))),create:t=>e.post(pn,{body:Be(ee(NS)(t))}).pipe(m(me(FS))),update:t=>e.put(`${pn}/${t.id}`,{body:Be(ee(DS)(t))}).pipe(m(ot)),delete:t=>e.del(`${pn}/${t}`).pipe(m(ot))})),st(Re.Live)),MS=te(cr,US),jS=te(cr,cr),KS=te(_h,cr);class lr extends nn("PersonRepository")(){}const mn="/v1/person",qS=Re.pipe(Ne(({httpClient:e})=>e),Ne(e=>({getAll:()=>e.get(mn).pipe(m(me(CS))),getById:t=>e.get(`${mn}/${t}`).pipe(m(me(vt))),create:t=>e.post(mn,{body:Be(ee(Ln)(t))}).pipe(m(me($S))),update:t=>e.post(mn,{body:Be(We(vt)(t))}).pipe(m(ot)),delete:t=>e.post(mn,{body:Be({id:t})}).pipe(m(ot))})),st(Re.Live)),BS=te(lr,qS),JS=te(lr,lr);class zS extends nn("PersonUsecase")(){}const WS=te(zS,lr);class dr extends nn("ScheduleRepository")(){}const xu=P({id:f,name:f,startTime:x,endTime:x,createdAt:O(f),updatedAt:O(f),deletedAt:O(f)}),HS=P({id:f,name:f,sessionDuration:x,breakDuration:x,turns:B(q(xu)),createdAt:O(f),updatedAt:O(f),deletedAt:O(f)}),Nu=P({name:f,startTime:x,endTime:x}),Fu=P({id:f,name:f,startTime:x,endTime:x}),VS=P({name:f,sessionDuration:x,breakDuration:x,turns:B(q(Nu))}),QS=P({id:f,name:f,sessionDuration:x,breakDuration:x,turns:B(q(Fu))}),Lu=P({id:f,name:f,start_time:x,end_time:x,created_at:O(f),updated_at:O(f),deleted_at:O(f)}),ha=z(Lu,xu,{strict:!0,encode:e=>({id:e.id,name:e.name,start_time:e.startTime,end_time:e.endTime,created_at:e.createdAt,updated_at:e.updatedAt,deleted_at:e.deletedAt}),decode:e=>({id:e.id,name:e.name,startTime:e.start_time,endTime:e.end_time,createdAt:e.created_at,updatedAt:e.updated_at,deletedAt:e.deleted_at})}),GS=P({id:f,name:f,session_duration:x,break_duration:x,turns:B(q(Lu)),created_at:O(f),updated_at:O(f),deleted_at:O(f)}),Du=z(GS,HS,{strict:!0,encode:e=>({id:e.id,name:e.name,session_duration:e.sessionDuration,break_duration:e.breakDuration,turns:e.turns.map(t=>We(ha)(t)),created_at:e.createdAt,updated_at:e.updatedAt,deleted_at:e.deletedAt}),decode:e=>({id:e.id,name:e.name,sessionDuration:e.session_duration,breakDuration:e.break_duration,turns:e.turns.map(t=>ee(ha)(t)),createdAt:e.created_at,updatedAt:e.updated_at,deletedAt:e.deleted_at})}),YS=B(q(Du)),Uu=P({name:f,start_time:x,end_time:x}),fa=z(Nu,Uu,{strict:!0,decode:e=>({name:e.name,start_time:e.startTime,end_time:e.endTime}),encode:e=>({name:e.name,startTime:e.start_time,endTime:e.end_time})}),XS=P({name:f,session_duration:x,break_duration:x,turns:B(q(Uu))}),ZS=z(VS,XS,{strict:!0,decode:e=>({name:e.name,session_duration:e.sessionDuration,break_duration:e.breakDuration,turns:e.turns.map(t=>ee(fa)(t))}),encode:e=>({name:e.name,sessionDuration:e.session_duration,breakDuration:e.break_duration,turns:e.turns.map(t=>We(fa)(t))})}),e_=f,Mu=P({id:f,name:f,start_time:x,end_time:x}),pa=z(Fu,Mu,{strict:!0,decode:e=>({id:e.id,name:e.name,start_time:e.startTime,end_time:e.endTime}),encode:e=>({id:e.id,name:e.name,startTime:e.start_time,endTime:e.end_time})}),t_=P({id:f,name:f,session_duration:x,break_duration:x,turns:B(q(Mu))}),n_=z(QS,t_,{strict:!0,decode:e=>({id:e.id,name:e.name,session_duration:e.sessionDuration,break_duration:e.breakDuration,turns:e.turns.map(t=>ee(pa)(t))}),encode:e=>({id:e.id,name:e.name,sessionDuration:e.session_duration,breakDuration:e.break_duration,turns:e.turns.map(t=>We(pa)(t))})}),yn="/v1/schedules",r_=Re.pipe(Ne(({httpClient:e})=>e),Ne(e=>({getAll:()=>e.get(yn).pipe(m(me(YS))),getById:t=>e.get(`${yn}/${t}`).pipe(m(me(Du))),create:t=>e.post(yn,{body:Be(ee(ZS)(t))}).pipe(m(me(e_))),update:t=>e.put(`${yn}/${t.id}`,{body:Be(ee(n_)(t))}).pipe(m(ot)),delete:t=>e.del(`${yn}/${t}`).pipe(m(ot))})),st(Re.Live)),s_=te(dr,r_),o_=te(dr,dr),a_=te(bh,dr);class hr extends nn("WorkerRepository")(){}var i_=(e=>(e[e.MANAGER=1]="MANAGER",e[e.ADMINISTRATOR=2]="ADMINISTRATOR",e[e.INTERN=3]="INTERN",e[e.PSYCHOLOGIST=4]="PSYCHOLOGIST",e[e.THERAPIST=5]="THERAPIST",e))(i_||{});const ju=P({id:f,person:cn,positions:B(q(x)),createdAt:O(f),updatedAt:O(f),deletedAt:O(f)}),c_=P({person:io,positions:B(q(x))}),u_=P({id:f,person:co,positions:B(q(x))}),l_=P({id:f,person:Bn,positions:B(q(x)),created_at:O(f),updated_at:O(f),deleted_at:O(f)}),Ku=z(l_,ju,{strict:!0,decode:e=>({...e,person:ee(vt)(e.person),createdAt:e.created_at,updatedAt:e.updated_at,deletedAt:e.deleted_at}),encode:e=>({...e,person:We(vt)(e.person),created_at:e.createdAt,updated_at:e.updatedAt,deleted_at:e.deletedAt})}),d_=z(B(Zs(q(Ku))),B(q(ju)),{strict:!0,decode:e=>e||[],encode:e=>e}),h_=P({person:uo,positions:B(q(x))}),f_=z(c_,h_,{strict:!0,encode:e=>({...e,person:We(Ln)(e.person)}),decode:e=>({...e,person:ee(Ln)(e.person)})}),p_=f,m_=P({id:f,person:lo,positions:B(q(x))}),y_=z(u_,m_,{strict:!0,encode:e=>({...e,person:We(ur)(e.person)}),decode:e=>({...e,person:ee(ur)(e.person)})}),gn="/v1/workers",g_=Re.pipe(Ne(({httpClient:e})=>e),Ne(e=>({getAll:()=>e.get(gn).pipe(m(me(d_))),getById:t=>e.get(`${gn}/${t}`).pipe(m(me(Ku))),create:t=>e.post(gn,{body:Be(ee(f_)(t))}).pipe(m(me(p_))),update:t=>e.put(gn,{body:Be(ee(y_)(t))}).pipe(m(ot)),delete:t=>e.del(`${gn}/${t}`).pipe(m(ot))})),st(Re.Live)),S_=te(hr,g_),__=te(hr,hr),b_=te(wh,hr),w_=OS.pipe(Fe(RS),Fe(kS)),E_=KS.pipe(Fe(jS),Fe(MS)),k_=WS.pipe(Fe(JS),Fe(BS)),R_=b_.pipe(Fe(__),Fe(S_)),O_=a_.pipe(Fe(o_),Fe(s_)),T_=zf(w_,E_,k_,R_,O_),G_=yy(T_);export{G_ as A,TS as D,j_ as E,x_ as F,ju as W,oS as a,aS as b,cS as c,lS as d,eS as e,i_ as f,rS as g,tS as h,N_ as i,iS as l,nS as m,uS as n,$u as o,Q_ as p,Pu as s,dS as u};
