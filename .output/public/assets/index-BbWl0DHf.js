import{j as e,L as s}from"./main-RY8ZkTMc.js";import{U as a}from"./users-DptPd1yw.js";import{c as t}from"./createLucideIcon-78bvTjT9.js";import{C as r}from"./calendar-C7hPlekH.js";/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l=[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]],o=t("briefcase",l),m=function(){return e.jsx("div",{className:"container mx-auto p-8",children:e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:[e.jsxs(s,{to:"/admin/workers",className:"group hover:-translate-y-1 relative overflow-hidden rounded-lg bg-base-100 p-6 shadow-lg transition-all hover:shadow-xl",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"rounded-full bg-primary/10 p-3",children:e.jsx(a,{className:"h-6 w-6 text-primary"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"font-semibold text-base-content text-xl",children:"Workers"}),e.jsx("p",{className:"text-base-content/70",children:"Manage your workforce"})]})]}),e.jsx("div",{className:"absolute bottom-0 left-0 h-1 w-full scale-x-0 transform bg-primary transition-transform duration-300 group-hover:scale-x-100"})]}),e.jsxs(s,{to:"/admin/clients",className:"group hover:-translate-y-1 relative overflow-hidden rounded-lg bg-base-100 p-6 shadow-lg transition-all hover:shadow-xl",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"rounded-full bg-secondary/10 p-3",children:e.jsx(o,{className:"h-6 w-6 text-secondary"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"font-semibold text-base-content text-xl",children:"Clients"}),e.jsx("p",{className:"text-base-content/70",children:"Manage your clients"})]})]}),e.jsx("div",{className:"absolute bottom-0 left-0 h-1 w-full scale-x-0 transform bg-secondary transition-transform duration-300 group-hover:scale-x-100"})]}),e.jsxs(s,{to:"/admin/schedules",className:"group hover:-translate-y-1 relative overflow-hidden rounded-lg bg-base-100 p-6 shadow-lg transition-all hover:shadow-xl",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"rounded-full bg-accent/10 p-3",children:e.jsx(r,{className:"h-6 w-6 text-accent"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"font-semibold text-base-content text-xl",children:"Schedules"}),e.jsx("p",{className:"text-base-content/70",children:"Manage work schedules"})]})]}),e.jsx("div",{className:"absolute bottom-0 left-0 h-1 w-full scale-x-0 transform bg-accent transition-transform duration-300 group-hover:scale-x-100"})]})]})})};export{m as component};
