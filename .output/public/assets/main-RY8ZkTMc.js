const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/login-Ckz1Puxy.js","assets/form-BdoD3Q87.js","assets/classes-CraQI9Rs.js","assets/createLucideIcon-78bvTjT9.js","assets/effectErrors-D8W8e9uM.js","assets/runtimes-CTOS42-v.js","assets/useMutation-D8585ZL-.js","assets/user-BHfkra8r.js","assets/route-DP-pnEp9.js","assets/useQuery-D9VtETml.js","assets/queryOptions-C9woPjwX.js","assets/route-DvpekX38.js","assets/users-DptPd1yw.js","assets/calendar-C7hPlekH.js","assets/index-BbWl0DHf.js","assets/index-Q8CEboXZ.js","assets/BasicTable-RsYQtHbn.js","assets/TextModal-DBwVHEpd.js","assets/file-text-DYqz-Dos.js","assets/index-DmGivCOT.js","assets/schedule-options-BL0O7AQ8.js","assets/square-pen-KUfj7VU3.js","assets/index-BZf7DOmf.js","assets/create-Cw-MR3Bi.js","assets/index-C7U4vq_b.js","assets/_id-2ptn8Wpq.js"])))=>i.map(i=>d[i]);
var yC=Object.defineProperty;var ZS=t=>{throw TypeError(t)};var pC=(t,n,r)=>n in t?yC(t,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[n]=r;var E=(t,n,r)=>pC(t,typeof n!="symbol"?n+"":n,r),Py=(t,n,r)=>n.has(t)||ZS("Cannot "+r);var G=(t,n,r)=>(Py(t,n,"read from private field"),r?r.call(t):n.get(t)),Ht=(t,n,r)=>n.has(t)?ZS("Cannot add the same private member more than once"):n instanceof WeakSet?n.add(t):n.set(t,r),wt=(t,n,r,s)=>(Py(t,n,"write to private field"),s?s.call(t,r):n.set(t,r),r),Xe=(t,n,r)=>(Py(t,n,"access private method"),r);var Ef=(t,n,r,s)=>({set _(l){wt(t,n,l,r)},get _(){return G(t,n,s)}});function gC(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var Vy={exports:{}},Kl={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var JS;function vC(){if(JS)return Kl;JS=1;var t=Symbol.for("react.transitional.element"),n=Symbol.for("react.fragment");function r(s,l,c){var f=null;if(c!==void 0&&(f=""+c),l.key!==void 0&&(f=""+l.key),"key"in l){c={};for(var h in l)h!=="key"&&(c[h]=l[h])}else c=l;return l=c.ref,{$$typeof:t,type:s,key:f,ref:l!==void 0?l:null,props:c}}return Kl.Fragment=n,Kl.jsx=r,Kl.jsxs=r,Kl}var WS;function _C(){return WS||(WS=1,Vy.exports=vC()),Vy.exports}var tt=_C(),Gy={exports:{}},xt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var t1;function bC(){if(t1)return xt;t1=1;var t=Symbol.for("react.transitional.element"),n=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),f=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),g=Symbol.iterator;function _(M){return M===null||typeof M!="object"?null:(M=g&&M[g]||M["@@iterator"],typeof M=="function"?M:null)}var S={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},T=Object.assign,A={};function x(M,H,st){this.props=M,this.context=H,this.refs=A,this.updater=st||S}x.prototype.isReactComponent={},x.prototype.setState=function(M,H){if(typeof M!="object"&&typeof M!="function"&&M!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,M,H,"setState")},x.prototype.forceUpdate=function(M){this.updater.enqueueForceUpdate(this,M,"forceUpdate")};function L(){}L.prototype=x.prototype;function j(M,H,st){this.props=M,this.context=H,this.refs=A,this.updater=st||S}var q=j.prototype=new L;q.constructor=j,T(q,x.prototype),q.isPureReactComponent=!0;var K=Array.isArray,Z={H:null,A:null,T:null,S:null,V:null},I=Object.prototype.hasOwnProperty;function Q(M,H,st,at,it,ft){return st=ft.ref,{$$typeof:t,type:M,key:H,ref:st!==void 0?st:null,props:ft}}function $(M,H){return Q(M.type,H,void 0,void 0,void 0,M.props)}function F(M){return typeof M=="object"&&M!==null&&M.$$typeof===t}function W(M){var H={"=":"=0",":":"=2"};return"$"+M.replace(/[=:]/g,function(st){return H[st]})}var rt=/\/+/g;function nt(M,H){return typeof M=="object"&&M!==null&&M.key!=null?W(""+M.key):H.toString(36)}function pt(){}function St(M){switch(M.status){case"fulfilled":return M.value;case"rejected":throw M.reason;default:switch(typeof M.status=="string"?M.then(pt,pt):(M.status="pending",M.then(function(H){M.status==="pending"&&(M.status="fulfilled",M.value=H)},function(H){M.status==="pending"&&(M.status="rejected",M.reason=H)})),M.status){case"fulfilled":return M.value;case"rejected":throw M.reason}}throw M}function vt(M,H,st,at,it){var ft=typeof M;(ft==="undefined"||ft==="boolean")&&(M=null);var ct=!1;if(M===null)ct=!0;else switch(ft){case"bigint":case"string":case"number":ct=!0;break;case"object":switch(M.$$typeof){case t:case n:ct=!0;break;case p:return ct=M._init,vt(ct(M._payload),H,st,at,it)}}if(ct)return it=it(M),ct=at===""?"."+nt(M,0):at,K(it)?(st="",ct!=null&&(st=ct.replace(rt,"$&/")+"/"),vt(it,H,st,"",function(zt){return zt})):it!=null&&(F(it)&&(it=$(it,st+(it.key==null||M&&M.key===it.key?"":(""+it.key).replace(rt,"$&/")+"/")+ct)),H.push(it)),1;ct=0;var Nt=at===""?".":at+":";if(K(M))for(var dt=0;dt<M.length;dt++)at=M[dt],ft=Nt+nt(at,dt),ct+=vt(at,H,st,ft,it);else if(dt=_(M),typeof dt=="function")for(M=dt.call(M),dt=0;!(at=M.next()).done;)at=at.value,ft=Nt+nt(at,dt++),ct+=vt(at,H,st,ft,it);else if(ft==="object"){if(typeof M.then=="function")return vt(St(M),H,st,at,it);throw H=String(M),Error("Objects are not valid as a React child (found: "+(H==="[object Object]"?"object with keys {"+Object.keys(M).join(", ")+"}":H)+"). If you meant to render a collection of children, use an array instead.")}return ct}function k(M,H,st){if(M==null)return M;var at=[],it=0;return vt(M,at,"","",function(ft){return H.call(st,ft,it++)}),at}function X(M){if(M._status===-1){var H=M._result;H=H(),H.then(function(st){(M._status===0||M._status===-1)&&(M._status=1,M._result=st)},function(st){(M._status===0||M._status===-1)&&(M._status=2,M._result=st)}),M._status===-1&&(M._status=0,M._result=H)}if(M._status===1)return M._result.default;throw M._result}var lt=typeof reportError=="function"?reportError:function(M){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var H=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof M=="object"&&M!==null&&typeof M.message=="string"?String(M.message):String(M),error:M});if(!window.dispatchEvent(H))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",M);return}console.error(M)};function At(){}return xt.Children={map:k,forEach:function(M,H,st){k(M,function(){H.apply(this,arguments)},st)},count:function(M){var H=0;return k(M,function(){H++}),H},toArray:function(M){return k(M,function(H){return H})||[]},only:function(M){if(!F(M))throw Error("React.Children.only expected to receive a single React element child.");return M}},xt.Component=x,xt.Fragment=r,xt.Profiler=l,xt.PureComponent=j,xt.StrictMode=s,xt.Suspense=m,xt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Z,xt.__COMPILER_RUNTIME={__proto__:null,c:function(M){return Z.H.useMemoCache(M)}},xt.cache=function(M){return function(){return M.apply(null,arguments)}},xt.cloneElement=function(M,H,st){if(M==null)throw Error("The argument must be a React element, but you passed "+M+".");var at=T({},M.props),it=M.key,ft=void 0;if(H!=null)for(ct in H.ref!==void 0&&(ft=void 0),H.key!==void 0&&(it=""+H.key),H)!I.call(H,ct)||ct==="key"||ct==="__self"||ct==="__source"||ct==="ref"&&H.ref===void 0||(at[ct]=H[ct]);var ct=arguments.length-2;if(ct===1)at.children=st;else if(1<ct){for(var Nt=Array(ct),dt=0;dt<ct;dt++)Nt[dt]=arguments[dt+2];at.children=Nt}return Q(M.type,it,void 0,void 0,ft,at)},xt.createContext=function(M){return M={$$typeof:f,_currentValue:M,_currentValue2:M,_threadCount:0,Provider:null,Consumer:null},M.Provider=M,M.Consumer={$$typeof:c,_context:M},M},xt.createElement=function(M,H,st){var at,it={},ft=null;if(H!=null)for(at in H.key!==void 0&&(ft=""+H.key),H)I.call(H,at)&&at!=="key"&&at!=="__self"&&at!=="__source"&&(it[at]=H[at]);var ct=arguments.length-2;if(ct===1)it.children=st;else if(1<ct){for(var Nt=Array(ct),dt=0;dt<ct;dt++)Nt[dt]=arguments[dt+2];it.children=Nt}if(M&&M.defaultProps)for(at in ct=M.defaultProps,ct)it[at]===void 0&&(it[at]=ct[at]);return Q(M,ft,void 0,void 0,null,it)},xt.createRef=function(){return{current:null}},xt.forwardRef=function(M){return{$$typeof:h,render:M}},xt.isValidElement=F,xt.lazy=function(M){return{$$typeof:p,_payload:{_status:-1,_result:M},_init:X}},xt.memo=function(M,H){return{$$typeof:y,type:M,compare:H===void 0?null:H}},xt.startTransition=function(M){var H=Z.T,st={};Z.T=st;try{var at=M(),it=Z.S;it!==null&&it(st,at),typeof at=="object"&&at!==null&&typeof at.then=="function"&&at.then(At,lt)}catch(ft){lt(ft)}finally{Z.T=H}},xt.unstable_useCacheRefresh=function(){return Z.H.useCacheRefresh()},xt.use=function(M){return Z.H.use(M)},xt.useActionState=function(M,H,st){return Z.H.useActionState(M,H,st)},xt.useCallback=function(M,H){return Z.H.useCallback(M,H)},xt.useContext=function(M){return Z.H.useContext(M)},xt.useDebugValue=function(){},xt.useDeferredValue=function(M,H){return Z.H.useDeferredValue(M,H)},xt.useEffect=function(M,H,st){var at=Z.H;if(typeof st=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return at.useEffect(M,H)},xt.useId=function(){return Z.H.useId()},xt.useImperativeHandle=function(M,H,st){return Z.H.useImperativeHandle(M,H,st)},xt.useInsertionEffect=function(M,H){return Z.H.useInsertionEffect(M,H)},xt.useLayoutEffect=function(M,H){return Z.H.useLayoutEffect(M,H)},xt.useMemo=function(M,H){return Z.H.useMemo(M,H)},xt.useOptimistic=function(M,H){return Z.H.useOptimistic(M,H)},xt.useReducer=function(M,H,st){return Z.H.useReducer(M,H,st)},xt.useRef=function(M){return Z.H.useRef(M)},xt.useState=function(M){return Z.H.useState(M)},xt.useSyncExternalStore=function(M,H,st){return Z.H.useSyncExternalStore(M,H,st)},xt.useTransition=function(){return Z.H.useTransition()},xt.version="19.1.0",xt}var e1;function jc(){return e1||(e1=1,Gy.exports=bC()),Gy.exports}var et=jc();const Gt=gC(et);var Ky={exports:{}},Ql={},Qy={exports:{}},Yy={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n1;function SC(){return n1||(n1=1,function(t){function n(k,X){var lt=k.length;k.push(X);t:for(;0<lt;){var At=lt-1>>>1,M=k[At];if(0<l(M,X))k[At]=X,k[lt]=M,lt=At;else break t}}function r(k){return k.length===0?null:k[0]}function s(k){if(k.length===0)return null;var X=k[0],lt=k.pop();if(lt!==X){k[0]=lt;t:for(var At=0,M=k.length,H=M>>>1;At<H;){var st=2*(At+1)-1,at=k[st],it=st+1,ft=k[it];if(0>l(at,lt))it<M&&0>l(ft,at)?(k[At]=ft,k[it]=lt,At=it):(k[At]=at,k[st]=lt,At=st);else if(it<M&&0>l(ft,lt))k[At]=ft,k[it]=lt,At=it;else break t}}return X}function l(k,X){var lt=k.sortIndex-X.sortIndex;return lt!==0?lt:k.id-X.id}if(t.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var c=performance;t.unstable_now=function(){return c.now()}}else{var f=Date,h=f.now();t.unstable_now=function(){return f.now()-h}}var m=[],y=[],p=1,g=null,_=3,S=!1,T=!1,A=!1,x=!1,L=typeof setTimeout=="function"?setTimeout:null,j=typeof clearTimeout=="function"?clearTimeout:null,q=typeof setImmediate<"u"?setImmediate:null;function K(k){for(var X=r(y);X!==null;){if(X.callback===null)s(y);else if(X.startTime<=k)s(y),X.sortIndex=X.expirationTime,n(m,X);else break;X=r(y)}}function Z(k){if(A=!1,K(k),!T)if(r(m)!==null)T=!0,I||(I=!0,nt());else{var X=r(y);X!==null&&vt(Z,X.startTime-k)}}var I=!1,Q=-1,$=5,F=-1;function W(){return x?!0:!(t.unstable_now()-F<$)}function rt(){if(x=!1,I){var k=t.unstable_now();F=k;var X=!0;try{t:{T=!1,A&&(A=!1,j(Q),Q=-1),S=!0;var lt=_;try{e:{for(K(k),g=r(m);g!==null&&!(g.expirationTime>k&&W());){var At=g.callback;if(typeof At=="function"){g.callback=null,_=g.priorityLevel;var M=At(g.expirationTime<=k);if(k=t.unstable_now(),typeof M=="function"){g.callback=M,K(k),X=!0;break e}g===r(m)&&s(m),K(k)}else s(m);g=r(m)}if(g!==null)X=!0;else{var H=r(y);H!==null&&vt(Z,H.startTime-k),X=!1}}break t}finally{g=null,_=lt,S=!1}X=void 0}}finally{X?nt():I=!1}}}var nt;if(typeof q=="function")nt=function(){q(rt)};else if(typeof MessageChannel<"u"){var pt=new MessageChannel,St=pt.port2;pt.port1.onmessage=rt,nt=function(){St.postMessage(null)}}else nt=function(){L(rt,0)};function vt(k,X){Q=L(function(){k(t.unstable_now())},X)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(k){k.callback=null},t.unstable_forceFrameRate=function(k){0>k||125<k?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<k?Math.floor(1e3/k):5},t.unstable_getCurrentPriorityLevel=function(){return _},t.unstable_next=function(k){switch(_){case 1:case 2:case 3:var X=3;break;default:X=_}var lt=_;_=X;try{return k()}finally{_=lt}},t.unstable_requestPaint=function(){x=!0},t.unstable_runWithPriority=function(k,X){switch(k){case 1:case 2:case 3:case 4:case 5:break;default:k=3}var lt=_;_=k;try{return X()}finally{_=lt}},t.unstable_scheduleCallback=function(k,X,lt){var At=t.unstable_now();switch(typeof lt=="object"&&lt!==null?(lt=lt.delay,lt=typeof lt=="number"&&0<lt?At+lt:At):lt=At,k){case 1:var M=-1;break;case 2:M=250;break;case 5:M=1073741823;break;case 4:M=1e4;break;default:M=5e3}return M=lt+M,k={id:p++,callback:X,priorityLevel:k,startTime:lt,expirationTime:M,sortIndex:-1},lt>At?(k.sortIndex=lt,n(y,k),r(m)===null&&k===r(y)&&(A?(j(Q),Q=-1):A=!0,vt(Z,lt-At))):(k.sortIndex=M,n(m,k),T||S||(T=!0,I||(I=!0,nt()))),k},t.unstable_shouldYield=W,t.unstable_wrapCallback=function(k){var X=_;return function(){var lt=_;_=X;try{return k.apply(this,arguments)}finally{_=lt}}}}(Yy)),Yy}var a1;function EC(){return a1||(a1=1,Qy.exports=SC()),Qy.exports}var Xy={exports:{}},Ze={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r1;function TC(){if(r1)return Ze;r1=1;var t=jc();function n(m){var y="https://react.dev/errors/"+m;if(1<arguments.length){y+="?args[]="+encodeURIComponent(arguments[1]);for(var p=2;p<arguments.length;p++)y+="&args[]="+encodeURIComponent(arguments[p])}return"Minified React error #"+m+"; visit "+y+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var s={d:{f:r,r:function(){throw Error(n(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},l=Symbol.for("react.portal");function c(m,y,p){var g=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:l,key:g==null?null:""+g,children:m,containerInfo:y,implementation:p}}var f=t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(m,y){if(m==="font")return"";if(typeof y=="string")return y==="use-credentials"?y:""}return Ze.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,Ze.createPortal=function(m,y){var p=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!y||y.nodeType!==1&&y.nodeType!==9&&y.nodeType!==11)throw Error(n(299));return c(m,y,null,p)},Ze.flushSync=function(m){var y=f.T,p=s.p;try{if(f.T=null,s.p=2,m)return m()}finally{f.T=y,s.p=p,s.d.f()}},Ze.preconnect=function(m,y){typeof m=="string"&&(y?(y=y.crossOrigin,y=typeof y=="string"?y==="use-credentials"?y:"":void 0):y=null,s.d.C(m,y))},Ze.prefetchDNS=function(m){typeof m=="string"&&s.d.D(m)},Ze.preinit=function(m,y){if(typeof m=="string"&&y&&typeof y.as=="string"){var p=y.as,g=h(p,y.crossOrigin),_=typeof y.integrity=="string"?y.integrity:void 0,S=typeof y.fetchPriority=="string"?y.fetchPriority:void 0;p==="style"?s.d.S(m,typeof y.precedence=="string"?y.precedence:void 0,{crossOrigin:g,integrity:_,fetchPriority:S}):p==="script"&&s.d.X(m,{crossOrigin:g,integrity:_,fetchPriority:S,nonce:typeof y.nonce=="string"?y.nonce:void 0})}},Ze.preinitModule=function(m,y){if(typeof m=="string")if(typeof y=="object"&&y!==null){if(y.as==null||y.as==="script"){var p=h(y.as,y.crossOrigin);s.d.M(m,{crossOrigin:p,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0})}}else y==null&&s.d.M(m)},Ze.preload=function(m,y){if(typeof m=="string"&&typeof y=="object"&&y!==null&&typeof y.as=="string"){var p=y.as,g=h(p,y.crossOrigin);s.d.L(m,p,{crossOrigin:g,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0,type:typeof y.type=="string"?y.type:void 0,fetchPriority:typeof y.fetchPriority=="string"?y.fetchPriority:void 0,referrerPolicy:typeof y.referrerPolicy=="string"?y.referrerPolicy:void 0,imageSrcSet:typeof y.imageSrcSet=="string"?y.imageSrcSet:void 0,imageSizes:typeof y.imageSizes=="string"?y.imageSizes:void 0,media:typeof y.media=="string"?y.media:void 0})}},Ze.preloadModule=function(m,y){if(typeof m=="string")if(y){var p=h(y.as,y.crossOrigin);s.d.m(m,{as:typeof y.as=="string"&&y.as!=="script"?y.as:void 0,crossOrigin:p,integrity:typeof y.integrity=="string"?y.integrity:void 0})}else s.d.m(m)},Ze.requestFormReset=function(m){s.d.r(m)},Ze.unstable_batchedUpdates=function(m,y){return m(y)},Ze.useFormState=function(m,y,p){return f.H.useFormState(m,y,p)},Ze.useFormStatus=function(){return f.H.useHostTransitionStatus()},Ze.version="19.1.0",Ze}var s1;function l2(){if(s1)return Xy.exports;s1=1;function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(n){console.error(n)}}return t(),Xy.exports=TC(),Xy.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var i1;function RC(){if(i1)return Ql;i1=1;var t=EC(),n=jc(),r=l2();function s(e){var a="https://react.dev/errors/"+e;if(1<arguments.length){a+="?args[]="+encodeURIComponent(arguments[1]);for(var i=2;i<arguments.length;i++)a+="&args[]="+encodeURIComponent(arguments[i])}return"Minified React error #"+e+"; visit "+a+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function l(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function c(e){var a=e,i=e;if(e.alternate)for(;a.return;)a=a.return;else{e=a;do a=e,(a.flags&4098)!==0&&(i=a.return),e=a.return;while(e)}return a.tag===3?i:null}function f(e){if(e.tag===13){var a=e.memoizedState;if(a===null&&(e=e.alternate,e!==null&&(a=e.memoizedState)),a!==null)return a.dehydrated}return null}function h(e){if(c(e)!==e)throw Error(s(188))}function m(e){var a=e.alternate;if(!a){if(a=c(e),a===null)throw Error(s(188));return a!==e?null:e}for(var i=e,o=a;;){var u=i.return;if(u===null)break;var d=u.alternate;if(d===null){if(o=u.return,o!==null){i=o;continue}break}if(u.child===d.child){for(d=u.child;d;){if(d===i)return h(u),e;if(d===o)return h(u),a;d=d.sibling}throw Error(s(188))}if(i.return!==o.return)i=u,o=d;else{for(var v=!1,b=u.child;b;){if(b===i){v=!0,i=u,o=d;break}if(b===o){v=!0,o=u,i=d;break}b=b.sibling}if(!v){for(b=d.child;b;){if(b===i){v=!0,i=d,o=u;break}if(b===o){v=!0,o=d,i=u;break}b=b.sibling}if(!v)throw Error(s(189))}}if(i.alternate!==o)throw Error(s(190))}if(i.tag!==3)throw Error(s(188));return i.stateNode.current===i?e:a}function y(e){var a=e.tag;if(a===5||a===26||a===27||a===6)return e;for(e=e.child;e!==null;){if(a=y(e),a!==null)return a;e=e.sibling}return null}var p=Object.assign,g=Symbol.for("react.element"),_=Symbol.for("react.transitional.element"),S=Symbol.for("react.portal"),T=Symbol.for("react.fragment"),A=Symbol.for("react.strict_mode"),x=Symbol.for("react.profiler"),L=Symbol.for("react.provider"),j=Symbol.for("react.consumer"),q=Symbol.for("react.context"),K=Symbol.for("react.forward_ref"),Z=Symbol.for("react.suspense"),I=Symbol.for("react.suspense_list"),Q=Symbol.for("react.memo"),$=Symbol.for("react.lazy"),F=Symbol.for("react.activity"),W=Symbol.for("react.memo_cache_sentinel"),rt=Symbol.iterator;function nt(e){return e===null||typeof e!="object"?null:(e=rt&&e[rt]||e["@@iterator"],typeof e=="function"?e:null)}var pt=Symbol.for("react.client.reference");function St(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===pt?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case T:return"Fragment";case x:return"Profiler";case A:return"StrictMode";case Z:return"Suspense";case I:return"SuspenseList";case F:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case S:return"Portal";case q:return(e.displayName||"Context")+".Provider";case j:return(e._context.displayName||"Context")+".Consumer";case K:var a=e.render;return e=e.displayName,e||(e=a.displayName||a.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Q:return a=e.displayName||null,a!==null?a:St(e.type)||"Memo";case $:a=e._payload,e=e._init;try{return St(e(a))}catch{}}return null}var vt=Array.isArray,k=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,X=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,lt={pending:!1,data:null,method:null,action:null},At=[],M=-1;function H(e){return{current:e}}function st(e){0>M||(e.current=At[M],At[M]=null,M--)}function at(e,a){M++,At[M]=e.current,e.current=a}var it=H(null),ft=H(null),ct=H(null),Nt=H(null);function dt(e,a){switch(at(ct,a),at(ft,e),at(it,null),a.nodeType){case 9:case 11:e=(e=a.documentElement)&&(e=e.namespaceURI)?OS(e):0;break;default:if(e=a.tagName,a=a.namespaceURI)a=OS(a),e=MS(a,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}st(it),at(it,e)}function zt(){st(it),st(ft),st(ct)}function ee(e){e.memoizedState!==null&&at(Nt,e);var a=it.current,i=MS(a,e.type);a!==i&&(at(ft,e),at(it,i))}function me(e){ft.current===e&&(st(it),st(ft)),Nt.current===e&&(st(Nt),Hl._currentValue=lt)}var ue=Object.prototype.hasOwnProperty,fn=t.unstable_scheduleCallback,Zn=t.unstable_cancelCallback,or=t.unstable_shouldYield,kn=t.unstable_requestPaint,$e=t.unstable_now,Ws=t.unstable_getCurrentPriorityLevel,Yo=t.unstable_ImmediatePriority,Xo=t.unstable_UserBlockingPriority,Vt=t.unstable_NormalPriority,Me=t.unstable_LowPriority,ti=t.unstable_IdlePriority,ou=t.log,kh=t.unstable_setDisableYieldValue,Yr=null,bn=null;function lr(e){if(typeof ou=="function"&&kh(e),bn&&typeof bn.setStrictMode=="function")try{bn.setStrictMode(Yr,e)}catch{}}var Sn=Math.clz32?Math.clz32:eA,Ww=Math.log,tA=Math.LN2;function eA(e){return e>>>=0,e===0?32:31-(Ww(e)/tA|0)|0}var lu=256,cu=4194304;function Xr(e){var a=e&42;if(a!==0)return a;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function uu(e,a,i){var o=e.pendingLanes;if(o===0)return 0;var u=0,d=e.suspendedLanes,v=e.pingedLanes;e=e.warmLanes;var b=o&134217727;return b!==0?(o=b&~d,o!==0?u=Xr(o):(v&=b,v!==0?u=Xr(v):i||(i=b&~e,i!==0&&(u=Xr(i))))):(b=o&~d,b!==0?u=Xr(b):v!==0?u=Xr(v):i||(i=o&~e,i!==0&&(u=Xr(i)))),u===0?0:a!==0&&a!==u&&(a&d)===0&&(d=u&-u,i=a&-a,d>=i||d===32&&(i&4194048)!==0)?a:u}function Zo(e,a){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&a)===0}function nA(e,a){switch(e){case 1:case 2:case 4:case 8:case 64:return a+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return a+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function lv(){var e=lu;return lu<<=1,(lu&4194048)===0&&(lu=256),e}function cv(){var e=cu;return cu<<=1,(cu&62914560)===0&&(cu=4194304),e}function Nh(e){for(var a=[],i=0;31>i;i++)a.push(e);return a}function Jo(e,a){e.pendingLanes|=a,a!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function aA(e,a,i,o,u,d){var v=e.pendingLanes;e.pendingLanes=i,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=i,e.entangledLanes&=i,e.errorRecoveryDisabledLanes&=i,e.shellSuspendCounter=0;var b=e.entanglements,R=e.expirationTimes,z=e.hiddenUpdates;for(i=v&~i;0<i;){var P=31-Sn(i),Y=1<<P;b[P]=0,R[P]=-1;var U=z[P];if(U!==null)for(z[P]=null,P=0;P<U.length;P++){var B=U[P];B!==null&&(B.lane&=-536870913)}i&=~Y}o!==0&&uv(e,o,0),d!==0&&u===0&&e.tag!==0&&(e.suspendedLanes|=d&~(v&~a))}function uv(e,a,i){e.pendingLanes|=a,e.suspendedLanes&=~a;var o=31-Sn(a);e.entangledLanes|=a,e.entanglements[o]=e.entanglements[o]|1073741824|i&4194090}function fv(e,a){var i=e.entangledLanes|=a;for(e=e.entanglements;i;){var o=31-Sn(i),u=1<<o;u&a|e[o]&a&&(e[o]|=a),i&=~u}}function zh(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Lh(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function dv(){var e=X.p;return e!==0?e:(e=window.event,e===void 0?32:VS(e.type))}function rA(e,a){var i=X.p;try{return X.p=e,a()}finally{X.p=i}}var cr=Math.random().toString(36).slice(2),Qe="__reactFiber$"+cr,dn="__reactProps$"+cr,ei="__reactContainer$"+cr,$h="__reactEvents$"+cr,sA="__reactListeners$"+cr,iA="__reactHandles$"+cr,hv="__reactResources$"+cr,Wo="__reactMarker$"+cr;function Fh(e){delete e[Qe],delete e[dn],delete e[$h],delete e[sA],delete e[iA]}function ni(e){var a=e[Qe];if(a)return a;for(var i=e.parentNode;i;){if(a=i[ei]||i[Qe]){if(i=a.alternate,a.child!==null||i!==null&&i.child!==null)for(e=CS(e);e!==null;){if(i=e[Qe])return i;e=CS(e)}return a}e=i,i=e.parentNode}return null}function ai(e){if(e=e[Qe]||e[ei]){var a=e.tag;if(a===5||a===6||a===13||a===26||a===27||a===3)return e}return null}function tl(e){var a=e.tag;if(a===5||a===26||a===27||a===6)return e.stateNode;throw Error(s(33))}function ri(e){var a=e[hv];return a||(a=e[hv]={hoistableStyles:new Map,hoistableScripts:new Map}),a}function Fe(e){e[Wo]=!0}var mv=new Set,yv={};function Zr(e,a){si(e,a),si(e+"Capture",a)}function si(e,a){for(yv[e]=a,e=0;e<a.length;e++)mv.add(a[e])}var oA=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),pv={},gv={};function lA(e){return ue.call(gv,e)?!0:ue.call(pv,e)?!1:oA.test(e)?gv[e]=!0:(pv[e]=!0,!1)}function fu(e,a,i){if(lA(a))if(i===null)e.removeAttribute(a);else{switch(typeof i){case"undefined":case"function":case"symbol":e.removeAttribute(a);return;case"boolean":var o=a.toLowerCase().slice(0,5);if(o!=="data-"&&o!=="aria-"){e.removeAttribute(a);return}}e.setAttribute(a,""+i)}}function du(e,a,i){if(i===null)e.removeAttribute(a);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttribute(a,""+i)}}function Na(e,a,i,o){if(o===null)e.removeAttribute(i);else{switch(typeof o){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(i);return}e.setAttributeNS(a,i,""+o)}}var jh,vv;function ii(e){if(jh===void 0)try{throw Error()}catch(i){var a=i.stack.trim().match(/\n( *(at )?)/);jh=a&&a[1]||"",vv=-1<i.stack.indexOf(`
    at`)?" (<anonymous>)":-1<i.stack.indexOf("@")?"@unknown:0:0":""}return`
`+jh+e+vv}var Uh=!1;function Bh(e,a){if(!e||Uh)return"";Uh=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var o={DetermineComponentFrameRoot:function(){try{if(a){var Y=function(){throw Error()};if(Object.defineProperty(Y.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Y,[])}catch(B){var U=B}Reflect.construct(e,[],Y)}else{try{Y.call()}catch(B){U=B}e.call(Y.prototype)}}else{try{throw Error()}catch(B){U=B}(Y=e())&&typeof Y.catch=="function"&&Y.catch(function(){})}}catch(B){if(B&&U&&typeof B.stack=="string")return[B.stack,U.stack]}return[null,null]}};o.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(o.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(o.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var d=o.DetermineComponentFrameRoot(),v=d[0],b=d[1];if(v&&b){var R=v.split(`
`),z=b.split(`
`);for(u=o=0;o<R.length&&!R[o].includes("DetermineComponentFrameRoot");)o++;for(;u<z.length&&!z[u].includes("DetermineComponentFrameRoot");)u++;if(o===R.length||u===z.length)for(o=R.length-1,u=z.length-1;1<=o&&0<=u&&R[o]!==z[u];)u--;for(;1<=o&&0<=u;o--,u--)if(R[o]!==z[u]){if(o!==1||u!==1)do if(o--,u--,0>u||R[o]!==z[u]){var P=`
`+R[o].replace(" at new "," at ");return e.displayName&&P.includes("<anonymous>")&&(P=P.replace("<anonymous>",e.displayName)),P}while(1<=o&&0<=u);break}}}finally{Uh=!1,Error.prepareStackTrace=i}return(i=e?e.displayName||e.name:"")?ii(i):""}function cA(e){switch(e.tag){case 26:case 27:case 5:return ii(e.type);case 16:return ii("Lazy");case 13:return ii("Suspense");case 19:return ii("SuspenseList");case 0:case 15:return Bh(e.type,!1);case 11:return Bh(e.type.render,!1);case 1:return Bh(e.type,!0);case 31:return ii("Activity");default:return""}}function _v(e){try{var a="";do a+=cA(e),e=e.return;while(e);return a}catch(i){return`
Error generating stack: `+i.message+`
`+i.stack}}function Nn(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function bv(e){var a=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(a==="checkbox"||a==="radio")}function uA(e){var a=bv(e)?"checked":"value",i=Object.getOwnPropertyDescriptor(e.constructor.prototype,a),o=""+e[a];if(!e.hasOwnProperty(a)&&typeof i<"u"&&typeof i.get=="function"&&typeof i.set=="function"){var u=i.get,d=i.set;return Object.defineProperty(e,a,{configurable:!0,get:function(){return u.call(this)},set:function(v){o=""+v,d.call(this,v)}}),Object.defineProperty(e,a,{enumerable:i.enumerable}),{getValue:function(){return o},setValue:function(v){o=""+v},stopTracking:function(){e._valueTracker=null,delete e[a]}}}}function hu(e){e._valueTracker||(e._valueTracker=uA(e))}function Sv(e){if(!e)return!1;var a=e._valueTracker;if(!a)return!0;var i=a.getValue(),o="";return e&&(o=bv(e)?e.checked?"true":"false":e.value),e=o,e!==i?(a.setValue(e),!0):!1}function mu(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var fA=/[\n"\\]/g;function zn(e){return e.replace(fA,function(a){return"\\"+a.charCodeAt(0).toString(16)+" "})}function qh(e,a,i,o,u,d,v,b){e.name="",v!=null&&typeof v!="function"&&typeof v!="symbol"&&typeof v!="boolean"?e.type=v:e.removeAttribute("type"),a!=null?v==="number"?(a===0&&e.value===""||e.value!=a)&&(e.value=""+Nn(a)):e.value!==""+Nn(a)&&(e.value=""+Nn(a)):v!=="submit"&&v!=="reset"||e.removeAttribute("value"),a!=null?Hh(e,v,Nn(a)):i!=null?Hh(e,v,Nn(i)):o!=null&&e.removeAttribute("value"),u==null&&d!=null&&(e.defaultChecked=!!d),u!=null&&(e.checked=u&&typeof u!="function"&&typeof u!="symbol"),b!=null&&typeof b!="function"&&typeof b!="symbol"&&typeof b!="boolean"?e.name=""+Nn(b):e.removeAttribute("name")}function Ev(e,a,i,o,u,d,v,b){if(d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(e.type=d),a!=null||i!=null){if(!(d!=="submit"&&d!=="reset"||a!=null))return;i=i!=null?""+Nn(i):"",a=a!=null?""+Nn(a):i,b||a===e.value||(e.value=a),e.defaultValue=a}o=o??u,o=typeof o!="function"&&typeof o!="symbol"&&!!o,e.checked=b?e.checked:!!o,e.defaultChecked=!!o,v!=null&&typeof v!="function"&&typeof v!="symbol"&&typeof v!="boolean"&&(e.name=v)}function Hh(e,a,i){a==="number"&&mu(e.ownerDocument)===e||e.defaultValue===""+i||(e.defaultValue=""+i)}function oi(e,a,i,o){if(e=e.options,a){a={};for(var u=0;u<i.length;u++)a["$"+i[u]]=!0;for(i=0;i<e.length;i++)u=a.hasOwnProperty("$"+e[i].value),e[i].selected!==u&&(e[i].selected=u),u&&o&&(e[i].defaultSelected=!0)}else{for(i=""+Nn(i),a=null,u=0;u<e.length;u++){if(e[u].value===i){e[u].selected=!0,o&&(e[u].defaultSelected=!0);return}a!==null||e[u].disabled||(a=e[u])}a!==null&&(a.selected=!0)}}function Tv(e,a,i){if(a!=null&&(a=""+Nn(a),a!==e.value&&(e.value=a),i==null)){e.defaultValue!==a&&(e.defaultValue=a);return}e.defaultValue=i!=null?""+Nn(i):""}function Rv(e,a,i,o){if(a==null){if(o!=null){if(i!=null)throw Error(s(92));if(vt(o)){if(1<o.length)throw Error(s(93));o=o[0]}i=o}i==null&&(i=""),a=i}i=Nn(a),e.defaultValue=i,o=e.textContent,o===i&&o!==""&&o!==null&&(e.value=o)}function li(e,a){if(a){var i=e.firstChild;if(i&&i===e.lastChild&&i.nodeType===3){i.nodeValue=a;return}}e.textContent=a}var dA=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Ov(e,a,i){var o=a.indexOf("--")===0;i==null||typeof i=="boolean"||i===""?o?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="":o?e.setProperty(a,i):typeof i!="number"||i===0||dA.has(a)?a==="float"?e.cssFloat=i:e[a]=(""+i).trim():e[a]=i+"px"}function Mv(e,a,i){if(a!=null&&typeof a!="object")throw Error(s(62));if(e=e.style,i!=null){for(var o in i)!i.hasOwnProperty(o)||a!=null&&a.hasOwnProperty(o)||(o.indexOf("--")===0?e.setProperty(o,""):o==="float"?e.cssFloat="":e[o]="");for(var u in a)o=a[u],a.hasOwnProperty(u)&&i[u]!==o&&Ov(e,u,o)}else for(var d in a)a.hasOwnProperty(d)&&Ov(e,d,a[d])}function Ih(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var hA=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),mA=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function yu(e){return mA.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Ph=null;function Vh(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ci=null,ui=null;function wv(e){var a=ai(e);if(a&&(e=a.stateNode)){var i=e[dn]||null;t:switch(e=a.stateNode,a.type){case"input":if(qh(e,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name),a=i.name,i.type==="radio"&&a!=null){for(i=e;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll('input[name="'+zn(""+a)+'"][type="radio"]'),a=0;a<i.length;a++){var o=i[a];if(o!==e&&o.form===e.form){var u=o[dn]||null;if(!u)throw Error(s(90));qh(o,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(a=0;a<i.length;a++)o=i[a],o.form===e.form&&Sv(o)}break t;case"textarea":Tv(e,i.value,i.defaultValue);break t;case"select":a=i.value,a!=null&&oi(e,!!i.multiple,a,!1)}}}var Gh=!1;function Av(e,a,i){if(Gh)return e(a,i);Gh=!0;try{var o=e(a);return o}finally{if(Gh=!1,(ci!==null||ui!==null)&&(tf(),ci&&(a=ci,e=ui,ui=ci=null,wv(a),e)))for(a=0;a<e.length;a++)wv(e[a])}}function el(e,a){var i=e.stateNode;if(i===null)return null;var o=i[dn]||null;if(o===null)return null;i=o[a];t:switch(a){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(o=!o.disabled)||(e=e.type,o=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!o;break t;default:e=!1}if(e)return null;if(i&&typeof i!="function")throw Error(s(231,a,typeof i));return i}var za=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Kh=!1;if(za)try{var nl={};Object.defineProperty(nl,"passive",{get:function(){Kh=!0}}),window.addEventListener("test",nl,nl),window.removeEventListener("test",nl,nl)}catch{Kh=!1}var ur=null,Qh=null,pu=null;function xv(){if(pu)return pu;var e,a=Qh,i=a.length,o,u="value"in ur?ur.value:ur.textContent,d=u.length;for(e=0;e<i&&a[e]===u[e];e++);var v=i-e;for(o=1;o<=v&&a[i-o]===u[d-o];o++);return pu=u.slice(e,1<o?1-o:void 0)}function gu(e){var a=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&a===13&&(e=13)):e=a,e===10&&(e=13),32<=e||e===13?e:0}function vu(){return!0}function Cv(){return!1}function hn(e){function a(i,o,u,d,v){this._reactName=i,this._targetInst=u,this.type=o,this.nativeEvent=d,this.target=v,this.currentTarget=null;for(var b in e)e.hasOwnProperty(b)&&(i=e[b],this[b]=i?i(d):d[b]);return this.isDefaultPrevented=(d.defaultPrevented!=null?d.defaultPrevented:d.returnValue===!1)?vu:Cv,this.isPropagationStopped=Cv,this}return p(a.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=vu)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=vu)},persist:function(){},isPersistent:vu}),a}var Jr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},_u=hn(Jr),al=p({},Jr,{view:0,detail:0}),yA=hn(al),Yh,Xh,rl,bu=p({},al,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Jh,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==rl&&(rl&&e.type==="mousemove"?(Yh=e.screenX-rl.screenX,Xh=e.screenY-rl.screenY):Xh=Yh=0,rl=e),Yh)},movementY:function(e){return"movementY"in e?e.movementY:Xh}}),Dv=hn(bu),pA=p({},bu,{dataTransfer:0}),gA=hn(pA),vA=p({},al,{relatedTarget:0}),Zh=hn(vA),_A=p({},Jr,{animationName:0,elapsedTime:0,pseudoElement:0}),bA=hn(_A),SA=p({},Jr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),EA=hn(SA),TA=p({},Jr,{data:0}),kv=hn(TA),RA={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},OA={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},MA={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function wA(e){var a=this.nativeEvent;return a.getModifierState?a.getModifierState(e):(e=MA[e])?!!a[e]:!1}function Jh(){return wA}var AA=p({},al,{key:function(e){if(e.key){var a=RA[e.key]||e.key;if(a!=="Unidentified")return a}return e.type==="keypress"?(e=gu(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?OA[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Jh,charCode:function(e){return e.type==="keypress"?gu(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?gu(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),xA=hn(AA),CA=p({},bu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Nv=hn(CA),DA=p({},al,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Jh}),kA=hn(DA),NA=p({},Jr,{propertyName:0,elapsedTime:0,pseudoElement:0}),zA=hn(NA),LA=p({},bu,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),$A=hn(LA),FA=p({},Jr,{newState:0,oldState:0}),jA=hn(FA),UA=[9,13,27,32],Wh=za&&"CompositionEvent"in window,sl=null;za&&"documentMode"in document&&(sl=document.documentMode);var BA=za&&"TextEvent"in window&&!sl,zv=za&&(!Wh||sl&&8<sl&&11>=sl),Lv=" ",$v=!1;function Fv(e,a){switch(e){case"keyup":return UA.indexOf(a.keyCode)!==-1;case"keydown":return a.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function jv(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var fi=!1;function qA(e,a){switch(e){case"compositionend":return jv(a);case"keypress":return a.which!==32?null:($v=!0,Lv);case"textInput":return e=a.data,e===Lv&&$v?null:e;default:return null}}function HA(e,a){if(fi)return e==="compositionend"||!Wh&&Fv(e,a)?(e=xv(),pu=Qh=ur=null,fi=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(a.ctrlKey||a.altKey||a.metaKey)||a.ctrlKey&&a.altKey){if(a.char&&1<a.char.length)return a.char;if(a.which)return String.fromCharCode(a.which)}return null;case"compositionend":return zv&&a.locale!=="ko"?null:a.data;default:return null}}var IA={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Uv(e){var a=e&&e.nodeName&&e.nodeName.toLowerCase();return a==="input"?!!IA[e.type]:a==="textarea"}function Bv(e,a,i,o){ci?ui?ui.push(o):ui=[o]:ci=o,a=of(a,"onChange"),0<a.length&&(i=new _u("onChange","change",null,i,o),e.push({event:i,listeners:a}))}var il=null,ol=null;function PA(e){bS(e,0)}function Su(e){var a=tl(e);if(Sv(a))return e}function qv(e,a){if(e==="change")return a}var Hv=!1;if(za){var tm;if(za){var em="oninput"in document;if(!em){var Iv=document.createElement("div");Iv.setAttribute("oninput","return;"),em=typeof Iv.oninput=="function"}tm=em}else tm=!1;Hv=tm&&(!document.documentMode||9<document.documentMode)}function Pv(){il&&(il.detachEvent("onpropertychange",Vv),ol=il=null)}function Vv(e){if(e.propertyName==="value"&&Su(ol)){var a=[];Bv(a,ol,e,Vh(e)),Av(PA,a)}}function VA(e,a,i){e==="focusin"?(Pv(),il=a,ol=i,il.attachEvent("onpropertychange",Vv)):e==="focusout"&&Pv()}function GA(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Su(ol)}function KA(e,a){if(e==="click")return Su(a)}function QA(e,a){if(e==="input"||e==="change")return Su(a)}function YA(e,a){return e===a&&(e!==0||1/e===1/a)||e!==e&&a!==a}var En=typeof Object.is=="function"?Object.is:YA;function ll(e,a){if(En(e,a))return!0;if(typeof e!="object"||e===null||typeof a!="object"||a===null)return!1;var i=Object.keys(e),o=Object.keys(a);if(i.length!==o.length)return!1;for(o=0;o<i.length;o++){var u=i[o];if(!ue.call(a,u)||!En(e[u],a[u]))return!1}return!0}function Gv(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Kv(e,a){var i=Gv(e);e=0;for(var o;i;){if(i.nodeType===3){if(o=e+i.textContent.length,e<=a&&o>=a)return{node:i,offset:a-e};e=o}t:{for(;i;){if(i.nextSibling){i=i.nextSibling;break t}i=i.parentNode}i=void 0}i=Gv(i)}}function Qv(e,a){return e&&a?e===a?!0:e&&e.nodeType===3?!1:a&&a.nodeType===3?Qv(e,a.parentNode):"contains"in e?e.contains(a):e.compareDocumentPosition?!!(e.compareDocumentPosition(a)&16):!1:!1}function Yv(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var a=mu(e.document);a instanceof e.HTMLIFrameElement;){try{var i=typeof a.contentWindow.location.href=="string"}catch{i=!1}if(i)e=a.contentWindow;else break;a=mu(e.document)}return a}function nm(e){var a=e&&e.nodeName&&e.nodeName.toLowerCase();return a&&(a==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||a==="textarea"||e.contentEditable==="true")}var XA=za&&"documentMode"in document&&11>=document.documentMode,di=null,am=null,cl=null,rm=!1;function Xv(e,a,i){var o=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;rm||di==null||di!==mu(o)||(o=di,"selectionStart"in o&&nm(o)?o={start:o.selectionStart,end:o.selectionEnd}:(o=(o.ownerDocument&&o.ownerDocument.defaultView||window).getSelection(),o={anchorNode:o.anchorNode,anchorOffset:o.anchorOffset,focusNode:o.focusNode,focusOffset:o.focusOffset}),cl&&ll(cl,o)||(cl=o,o=of(am,"onSelect"),0<o.length&&(a=new _u("onSelect","select",null,a,i),e.push({event:a,listeners:o}),a.target=di)))}function Wr(e,a){var i={};return i[e.toLowerCase()]=a.toLowerCase(),i["Webkit"+e]="webkit"+a,i["Moz"+e]="moz"+a,i}var hi={animationend:Wr("Animation","AnimationEnd"),animationiteration:Wr("Animation","AnimationIteration"),animationstart:Wr("Animation","AnimationStart"),transitionrun:Wr("Transition","TransitionRun"),transitionstart:Wr("Transition","TransitionStart"),transitioncancel:Wr("Transition","TransitionCancel"),transitionend:Wr("Transition","TransitionEnd")},sm={},Zv={};za&&(Zv=document.createElement("div").style,"AnimationEvent"in window||(delete hi.animationend.animation,delete hi.animationiteration.animation,delete hi.animationstart.animation),"TransitionEvent"in window||delete hi.transitionend.transition);function ts(e){if(sm[e])return sm[e];if(!hi[e])return e;var a=hi[e],i;for(i in a)if(a.hasOwnProperty(i)&&i in Zv)return sm[e]=a[i];return e}var Jv=ts("animationend"),Wv=ts("animationiteration"),t_=ts("animationstart"),ZA=ts("transitionrun"),JA=ts("transitionstart"),WA=ts("transitioncancel"),e_=ts("transitionend"),n_=new Map,im="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");im.push("scrollEnd");function Jn(e,a){n_.set(e,a),Zr(a,[e])}var a_=new WeakMap;function Ln(e,a){if(typeof e=="object"&&e!==null){var i=a_.get(e);return i!==void 0?i:(a={value:e,source:a,stack:_v(a)},a_.set(e,a),a)}return{value:e,source:a,stack:_v(a)}}var $n=[],mi=0,om=0;function Eu(){for(var e=mi,a=om=mi=0;a<e;){var i=$n[a];$n[a++]=null;var o=$n[a];$n[a++]=null;var u=$n[a];$n[a++]=null;var d=$n[a];if($n[a++]=null,o!==null&&u!==null){var v=o.pending;v===null?u.next=u:(u.next=v.next,v.next=u),o.pending=u}d!==0&&r_(i,u,d)}}function Tu(e,a,i,o){$n[mi++]=e,$n[mi++]=a,$n[mi++]=i,$n[mi++]=o,om|=o,e.lanes|=o,e=e.alternate,e!==null&&(e.lanes|=o)}function lm(e,a,i,o){return Tu(e,a,i,o),Ru(e)}function yi(e,a){return Tu(e,null,null,a),Ru(e)}function r_(e,a,i){e.lanes|=i;var o=e.alternate;o!==null&&(o.lanes|=i);for(var u=!1,d=e.return;d!==null;)d.childLanes|=i,o=d.alternate,o!==null&&(o.childLanes|=i),d.tag===22&&(e=d.stateNode,e===null||e._visibility&1||(u=!0)),e=d,d=d.return;return e.tag===3?(d=e.stateNode,u&&a!==null&&(u=31-Sn(i),e=d.hiddenUpdates,o=e[u],o===null?e[u]=[a]:o.push(a),a.lane=i|536870912),d):null}function Ru(e){if(50<zl)throw zl=0,my=null,Error(s(185));for(var a=e.return;a!==null;)e=a,a=e.return;return e.tag===3?e.stateNode:null}var pi={};function tx(e,a,i,o){this.tag=e,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=a,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=o,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Tn(e,a,i,o){return new tx(e,a,i,o)}function cm(e){return e=e.prototype,!(!e||!e.isReactComponent)}function La(e,a){var i=e.alternate;return i===null?(i=Tn(e.tag,a,e.key,e.mode),i.elementType=e.elementType,i.type=e.type,i.stateNode=e.stateNode,i.alternate=e,e.alternate=i):(i.pendingProps=a,i.type=e.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=e.flags&65011712,i.childLanes=e.childLanes,i.lanes=e.lanes,i.child=e.child,i.memoizedProps=e.memoizedProps,i.memoizedState=e.memoizedState,i.updateQueue=e.updateQueue,a=e.dependencies,i.dependencies=a===null?null:{lanes:a.lanes,firstContext:a.firstContext},i.sibling=e.sibling,i.index=e.index,i.ref=e.ref,i.refCleanup=e.refCleanup,i}function s_(e,a){e.flags&=65011714;var i=e.alternate;return i===null?(e.childLanes=0,e.lanes=a,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=i.childLanes,e.lanes=i.lanes,e.child=i.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=i.memoizedProps,e.memoizedState=i.memoizedState,e.updateQueue=i.updateQueue,e.type=i.type,a=i.dependencies,e.dependencies=a===null?null:{lanes:a.lanes,firstContext:a.firstContext}),e}function Ou(e,a,i,o,u,d){var v=0;if(o=e,typeof e=="function")cm(e)&&(v=1);else if(typeof e=="string")v=nC(e,i,it.current)?26:e==="html"||e==="head"||e==="body"?27:5;else t:switch(e){case F:return e=Tn(31,i,a,u),e.elementType=F,e.lanes=d,e;case T:return es(i.children,u,d,a);case A:v=8,u|=24;break;case x:return e=Tn(12,i,a,u|2),e.elementType=x,e.lanes=d,e;case Z:return e=Tn(13,i,a,u),e.elementType=Z,e.lanes=d,e;case I:return e=Tn(19,i,a,u),e.elementType=I,e.lanes=d,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case L:case q:v=10;break t;case j:v=9;break t;case K:v=11;break t;case Q:v=14;break t;case $:v=16,o=null;break t}v=29,i=Error(s(130,e===null?"null":typeof e,"")),o=null}return a=Tn(v,i,a,u),a.elementType=e,a.type=o,a.lanes=d,a}function es(e,a,i,o){return e=Tn(7,e,o,a),e.lanes=i,e}function um(e,a,i){return e=Tn(6,e,null,a),e.lanes=i,e}function fm(e,a,i){return a=Tn(4,e.children!==null?e.children:[],e.key,a),a.lanes=i,a.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},a}var gi=[],vi=0,Mu=null,wu=0,Fn=[],jn=0,ns=null,$a=1,Fa="";function as(e,a){gi[vi++]=wu,gi[vi++]=Mu,Mu=e,wu=a}function i_(e,a,i){Fn[jn++]=$a,Fn[jn++]=Fa,Fn[jn++]=ns,ns=e;var o=$a;e=Fa;var u=32-Sn(o)-1;o&=~(1<<u),i+=1;var d=32-Sn(a)+u;if(30<d){var v=u-u%5;d=(o&(1<<v)-1).toString(32),o>>=v,u-=v,$a=1<<32-Sn(a)+u|i<<u|o,Fa=d+e}else $a=1<<d|i<<u|o,Fa=e}function dm(e){e.return!==null&&(as(e,1),i_(e,1,0))}function hm(e){for(;e===Mu;)Mu=gi[--vi],gi[vi]=null,wu=gi[--vi],gi[vi]=null;for(;e===ns;)ns=Fn[--jn],Fn[jn]=null,Fa=Fn[--jn],Fn[jn]=null,$a=Fn[--jn],Fn[jn]=null}var tn=null,ye=null,Pt=!1,rs=null,ha=!1,mm=Error(s(519));function ss(e){var a=Error(s(418,""));throw dl(Ln(a,e)),mm}function o_(e){var a=e.stateNode,i=e.type,o=e.memoizedProps;switch(a[Qe]=e,a[dn]=o,i){case"dialog":Ft("cancel",a),Ft("close",a);break;case"iframe":case"object":case"embed":Ft("load",a);break;case"video":case"audio":for(i=0;i<$l.length;i++)Ft($l[i],a);break;case"source":Ft("error",a);break;case"img":case"image":case"link":Ft("error",a),Ft("load",a);break;case"details":Ft("toggle",a);break;case"input":Ft("invalid",a),Ev(a,o.value,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name,!0),hu(a);break;case"select":Ft("invalid",a);break;case"textarea":Ft("invalid",a),Rv(a,o.value,o.defaultValue,o.children),hu(a)}i=o.children,typeof i!="string"&&typeof i!="number"&&typeof i!="bigint"||a.textContent===""+i||o.suppressHydrationWarning===!0||RS(a.textContent,i)?(o.popover!=null&&(Ft("beforetoggle",a),Ft("toggle",a)),o.onScroll!=null&&Ft("scroll",a),o.onScrollEnd!=null&&Ft("scrollend",a),o.onClick!=null&&(a.onclick=lf),a=!0):a=!1,a||ss(e)}function l_(e){for(tn=e.return;tn;)switch(tn.tag){case 5:case 13:ha=!1;return;case 27:case 3:ha=!0;return;default:tn=tn.return}}function ul(e){if(e!==tn)return!1;if(!Pt)return l_(e),Pt=!0,!1;var a=e.tag,i;if((i=a!==3&&a!==27)&&((i=a===5)&&(i=e.type,i=!(i!=="form"&&i!=="button")||Cy(e.type,e.memoizedProps)),i=!i),i&&ye&&ss(e),l_(e),a===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));t:{for(e=e.nextSibling,a=0;e;){if(e.nodeType===8)if(i=e.data,i==="/$"){if(a===0){ye=ta(e.nextSibling);break t}a--}else i!=="$"&&i!=="$!"&&i!=="$?"||a++;e=e.nextSibling}ye=null}}else a===27?(a=ye,Mr(e.type)?(e=zy,zy=null,ye=e):ye=a):ye=tn?ta(e.stateNode.nextSibling):null;return!0}function fl(){ye=tn=null,Pt=!1}function c_(){var e=rs;return e!==null&&(pn===null?pn=e:pn.push.apply(pn,e),rs=null),e}function dl(e){rs===null?rs=[e]:rs.push(e)}var ym=H(null),is=null,ja=null;function fr(e,a,i){at(ym,a._currentValue),a._currentValue=i}function Ua(e){e._currentValue=ym.current,st(ym)}function pm(e,a,i){for(;e!==null;){var o=e.alternate;if((e.childLanes&a)!==a?(e.childLanes|=a,o!==null&&(o.childLanes|=a)):o!==null&&(o.childLanes&a)!==a&&(o.childLanes|=a),e===i)break;e=e.return}}function gm(e,a,i,o){var u=e.child;for(u!==null&&(u.return=e);u!==null;){var d=u.dependencies;if(d!==null){var v=u.child;d=d.firstContext;t:for(;d!==null;){var b=d;d=u;for(var R=0;R<a.length;R++)if(b.context===a[R]){d.lanes|=i,b=d.alternate,b!==null&&(b.lanes|=i),pm(d.return,i,e),o||(v=null);break t}d=b.next}}else if(u.tag===18){if(v=u.return,v===null)throw Error(s(341));v.lanes|=i,d=v.alternate,d!==null&&(d.lanes|=i),pm(v,i,e),v=null}else v=u.child;if(v!==null)v.return=u;else for(v=u;v!==null;){if(v===e){v=null;break}if(u=v.sibling,u!==null){u.return=v.return,v=u;break}v=v.return}u=v}}function hl(e,a,i,o){e=null;for(var u=a,d=!1;u!==null;){if(!d){if((u.flags&524288)!==0)d=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var v=u.alternate;if(v===null)throw Error(s(387));if(v=v.memoizedProps,v!==null){var b=u.type;En(u.pendingProps.value,v.value)||(e!==null?e.push(b):e=[b])}}else if(u===Nt.current){if(v=u.alternate,v===null)throw Error(s(387));v.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(e!==null?e.push(Hl):e=[Hl])}u=u.return}e!==null&&gm(a,e,i,o),a.flags|=262144}function Au(e){for(e=e.firstContext;e!==null;){if(!En(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function os(e){is=e,ja=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Ye(e){return u_(is,e)}function xu(e,a){return is===null&&os(e),u_(e,a)}function u_(e,a){var i=a._currentValue;if(a={context:a,memoizedValue:i,next:null},ja===null){if(e===null)throw Error(s(308));ja=a,e.dependencies={lanes:0,firstContext:a},e.flags|=524288}else ja=ja.next=a;return i}var ex=typeof AbortController<"u"?AbortController:function(){var e=[],a=this.signal={aborted:!1,addEventListener:function(i,o){e.push(o)}};this.abort=function(){a.aborted=!0,e.forEach(function(i){return i()})}},nx=t.unstable_scheduleCallback,ax=t.unstable_NormalPriority,Ce={$$typeof:q,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function vm(){return{controller:new ex,data:new Map,refCount:0}}function ml(e){e.refCount--,e.refCount===0&&nx(ax,function(){e.controller.abort()})}var yl=null,_m=0,_i=0,bi=null;function rx(e,a){if(yl===null){var i=yl=[];_m=0,_i=Sy(),bi={status:"pending",value:void 0,then:function(o){i.push(o)}}}return _m++,a.then(f_,f_),a}function f_(){if(--_m===0&&yl!==null){bi!==null&&(bi.status="fulfilled");var e=yl;yl=null,_i=0,bi=null;for(var a=0;a<e.length;a++)(0,e[a])()}}function sx(e,a){var i=[],o={status:"pending",value:null,reason:null,then:function(u){i.push(u)}};return e.then(function(){o.status="fulfilled",o.value=a;for(var u=0;u<i.length;u++)(0,i[u])(a)},function(u){for(o.status="rejected",o.reason=u,u=0;u<i.length;u++)(0,i[u])(void 0)}),o}var d_=k.S;k.S=function(e,a){typeof a=="object"&&a!==null&&typeof a.then=="function"&&rx(e,a),d_!==null&&d_(e,a)};var ls=H(null);function bm(){var e=ls.current;return e!==null?e:se.pooledCache}function Cu(e,a){a===null?at(ls,ls.current):at(ls,a.pool)}function h_(){var e=bm();return e===null?null:{parent:Ce._currentValue,pool:e}}var pl=Error(s(460)),m_=Error(s(474)),Du=Error(s(542)),Sm={then:function(){}};function y_(e){return e=e.status,e==="fulfilled"||e==="rejected"}function ku(){}function p_(e,a,i){switch(i=e[i],i===void 0?e.push(a):i!==a&&(a.then(ku,ku),a=i),a.status){case"fulfilled":return a.value;case"rejected":throw e=a.reason,v_(e),e;default:if(typeof a.status=="string")a.then(ku,ku);else{if(e=se,e!==null&&100<e.shellSuspendCounter)throw Error(s(482));e=a,e.status="pending",e.then(function(o){if(a.status==="pending"){var u=a;u.status="fulfilled",u.value=o}},function(o){if(a.status==="pending"){var u=a;u.status="rejected",u.reason=o}})}switch(a.status){case"fulfilled":return a.value;case"rejected":throw e=a.reason,v_(e),e}throw gl=a,pl}}var gl=null;function g_(){if(gl===null)throw Error(s(459));var e=gl;return gl=null,e}function v_(e){if(e===pl||e===Du)throw Error(s(483))}var dr=!1;function Em(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Tm(e,a){e=e.updateQueue,a.updateQueue===e&&(a.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function hr(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function mr(e,a,i){var o=e.updateQueue;if(o===null)return null;if(o=o.shared,(Kt&2)!==0){var u=o.pending;return u===null?a.next=a:(a.next=u.next,u.next=a),o.pending=a,a=Ru(e),r_(e,null,i),a}return Tu(e,o,a,i),Ru(e)}function vl(e,a,i){if(a=a.updateQueue,a!==null&&(a=a.shared,(i&4194048)!==0)){var o=a.lanes;o&=e.pendingLanes,i|=o,a.lanes=i,fv(e,i)}}function Rm(e,a){var i=e.updateQueue,o=e.alternate;if(o!==null&&(o=o.updateQueue,i===o)){var u=null,d=null;if(i=i.firstBaseUpdate,i!==null){do{var v={lane:i.lane,tag:i.tag,payload:i.payload,callback:null,next:null};d===null?u=d=v:d=d.next=v,i=i.next}while(i!==null);d===null?u=d=a:d=d.next=a}else u=d=a;i={baseState:o.baseState,firstBaseUpdate:u,lastBaseUpdate:d,shared:o.shared,callbacks:o.callbacks},e.updateQueue=i;return}e=i.lastBaseUpdate,e===null?i.firstBaseUpdate=a:e.next=a,i.lastBaseUpdate=a}var Om=!1;function _l(){if(Om){var e=bi;if(e!==null)throw e}}function bl(e,a,i,o){Om=!1;var u=e.updateQueue;dr=!1;var d=u.firstBaseUpdate,v=u.lastBaseUpdate,b=u.shared.pending;if(b!==null){u.shared.pending=null;var R=b,z=R.next;R.next=null,v===null?d=z:v.next=z,v=R;var P=e.alternate;P!==null&&(P=P.updateQueue,b=P.lastBaseUpdate,b!==v&&(b===null?P.firstBaseUpdate=z:b.next=z,P.lastBaseUpdate=R))}if(d!==null){var Y=u.baseState;v=0,P=z=R=null,b=d;do{var U=b.lane&-536870913,B=U!==b.lane;if(B?(Bt&U)===U:(o&U)===U){U!==0&&U===_i&&(Om=!0),P!==null&&(P=P.next={lane:0,tag:b.tag,payload:b.payload,callback:null,next:null});t:{var Et=e,_t=b;U=a;var Wt=i;switch(_t.tag){case 1:if(Et=_t.payload,typeof Et=="function"){Y=Et.call(Wt,Y,U);break t}Y=Et;break t;case 3:Et.flags=Et.flags&-65537|128;case 0:if(Et=_t.payload,U=typeof Et=="function"?Et.call(Wt,Y,U):Et,U==null)break t;Y=p({},Y,U);break t;case 2:dr=!0}}U=b.callback,U!==null&&(e.flags|=64,B&&(e.flags|=8192),B=u.callbacks,B===null?u.callbacks=[U]:B.push(U))}else B={lane:U,tag:b.tag,payload:b.payload,callback:b.callback,next:null},P===null?(z=P=B,R=Y):P=P.next=B,v|=U;if(b=b.next,b===null){if(b=u.shared.pending,b===null)break;B=b,b=B.next,B.next=null,u.lastBaseUpdate=B,u.shared.pending=null}}while(!0);P===null&&(R=Y),u.baseState=R,u.firstBaseUpdate=z,u.lastBaseUpdate=P,d===null&&(u.shared.lanes=0),Er|=v,e.lanes=v,e.memoizedState=Y}}function __(e,a){if(typeof e!="function")throw Error(s(191,e));e.call(a)}function b_(e,a){var i=e.callbacks;if(i!==null)for(e.callbacks=null,e=0;e<i.length;e++)__(i[e],a)}var Si=H(null),Nu=H(0);function S_(e,a){e=Ga,at(Nu,e),at(Si,a),Ga=e|a.baseLanes}function Mm(){at(Nu,Ga),at(Si,Si.current)}function wm(){Ga=Nu.current,st(Si),st(Nu)}var yr=0,kt=null,Zt=null,we=null,zu=!1,Ei=!1,cs=!1,Lu=0,Sl=0,Ti=null,ix=0;function be(){throw Error(s(321))}function Am(e,a){if(a===null)return!1;for(var i=0;i<a.length&&i<e.length;i++)if(!En(e[i],a[i]))return!1;return!0}function xm(e,a,i,o,u,d){return yr=d,kt=a,a.memoizedState=null,a.updateQueue=null,a.lanes=0,k.H=e===null||e.memoizedState===null?rb:sb,cs=!1,d=i(o,u),cs=!1,Ei&&(d=T_(a,i,o,u)),E_(e),d}function E_(e){k.H=qu;var a=Zt!==null&&Zt.next!==null;if(yr=0,we=Zt=kt=null,zu=!1,Sl=0,Ti=null,a)throw Error(s(300));e===null||je||(e=e.dependencies,e!==null&&Au(e)&&(je=!0))}function T_(e,a,i,o){kt=e;var u=0;do{if(Ei&&(Ti=null),Sl=0,Ei=!1,25<=u)throw Error(s(301));if(u+=1,we=Zt=null,e.updateQueue!=null){var d=e.updateQueue;d.lastEffect=null,d.events=null,d.stores=null,d.memoCache!=null&&(d.memoCache.index=0)}k.H=hx,d=a(i,o)}while(Ei);return d}function ox(){var e=k.H,a=e.useState()[0];return a=typeof a.then=="function"?El(a):a,e=e.useState()[0],(Zt!==null?Zt.memoizedState:null)!==e&&(kt.flags|=1024),a}function Cm(){var e=Lu!==0;return Lu=0,e}function Dm(e,a,i){a.updateQueue=e.updateQueue,a.flags&=-2053,e.lanes&=~i}function km(e){if(zu){for(e=e.memoizedState;e!==null;){var a=e.queue;a!==null&&(a.pending=null),e=e.next}zu=!1}yr=0,we=Zt=kt=null,Ei=!1,Sl=Lu=0,Ti=null}function mn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return we===null?kt.memoizedState=we=e:we=we.next=e,we}function Ae(){if(Zt===null){var e=kt.alternate;e=e!==null?e.memoizedState:null}else e=Zt.next;var a=we===null?kt.memoizedState:we.next;if(a!==null)we=a,Zt=e;else{if(e===null)throw kt.alternate===null?Error(s(467)):Error(s(310));Zt=e,e={memoizedState:Zt.memoizedState,baseState:Zt.baseState,baseQueue:Zt.baseQueue,queue:Zt.queue,next:null},we===null?kt.memoizedState=we=e:we=we.next=e}return we}function Nm(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function El(e){var a=Sl;return Sl+=1,Ti===null&&(Ti=[]),e=p_(Ti,e,a),a=kt,(we===null?a.memoizedState:we.next)===null&&(a=a.alternate,k.H=a===null||a.memoizedState===null?rb:sb),e}function $u(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return El(e);if(e.$$typeof===q)return Ye(e)}throw Error(s(438,String(e)))}function zm(e){var a=null,i=kt.updateQueue;if(i!==null&&(a=i.memoCache),a==null){var o=kt.alternate;o!==null&&(o=o.updateQueue,o!==null&&(o=o.memoCache,o!=null&&(a={data:o.data.map(function(u){return u.slice()}),index:0})))}if(a==null&&(a={data:[],index:0}),i===null&&(i=Nm(),kt.updateQueue=i),i.memoCache=a,i=a.data[a.index],i===void 0)for(i=a.data[a.index]=Array(e),o=0;o<e;o++)i[o]=W;return a.index++,i}function Ba(e,a){return typeof a=="function"?a(e):a}function Fu(e){var a=Ae();return Lm(a,Zt,e)}function Lm(e,a,i){var o=e.queue;if(o===null)throw Error(s(311));o.lastRenderedReducer=i;var u=e.baseQueue,d=o.pending;if(d!==null){if(u!==null){var v=u.next;u.next=d.next,d.next=v}a.baseQueue=u=d,o.pending=null}if(d=e.baseState,u===null)e.memoizedState=d;else{a=u.next;var b=v=null,R=null,z=a,P=!1;do{var Y=z.lane&-536870913;if(Y!==z.lane?(Bt&Y)===Y:(yr&Y)===Y){var U=z.revertLane;if(U===0)R!==null&&(R=R.next={lane:0,revertLane:0,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null}),Y===_i&&(P=!0);else if((yr&U)===U){z=z.next,U===_i&&(P=!0);continue}else Y={lane:0,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},R===null?(b=R=Y,v=d):R=R.next=Y,kt.lanes|=U,Er|=U;Y=z.action,cs&&i(d,Y),d=z.hasEagerState?z.eagerState:i(d,Y)}else U={lane:Y,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},R===null?(b=R=U,v=d):R=R.next=U,kt.lanes|=Y,Er|=Y;z=z.next}while(z!==null&&z!==a);if(R===null?v=d:R.next=b,!En(d,e.memoizedState)&&(je=!0,P&&(i=bi,i!==null)))throw i;e.memoizedState=d,e.baseState=v,e.baseQueue=R,o.lastRenderedState=d}return u===null&&(o.lanes=0),[e.memoizedState,o.dispatch]}function $m(e){var a=Ae(),i=a.queue;if(i===null)throw Error(s(311));i.lastRenderedReducer=e;var o=i.dispatch,u=i.pending,d=a.memoizedState;if(u!==null){i.pending=null;var v=u=u.next;do d=e(d,v.action),v=v.next;while(v!==u);En(d,a.memoizedState)||(je=!0),a.memoizedState=d,a.baseQueue===null&&(a.baseState=d),i.lastRenderedState=d}return[d,o]}function R_(e,a,i){var o=kt,u=Ae(),d=Pt;if(d){if(i===void 0)throw Error(s(407));i=i()}else i=a();var v=!En((Zt||u).memoizedState,i);v&&(u.memoizedState=i,je=!0),u=u.queue;var b=w_.bind(null,o,u,e);if(Tl(2048,8,b,[e]),u.getSnapshot!==a||v||we!==null&&we.memoizedState.tag&1){if(o.flags|=2048,Ri(9,ju(),M_.bind(null,o,u,i,a),null),se===null)throw Error(s(349));d||(yr&124)!==0||O_(o,a,i)}return i}function O_(e,a,i){e.flags|=16384,e={getSnapshot:a,value:i},a=kt.updateQueue,a===null?(a=Nm(),kt.updateQueue=a,a.stores=[e]):(i=a.stores,i===null?a.stores=[e]:i.push(e))}function M_(e,a,i,o){a.value=i,a.getSnapshot=o,A_(a)&&x_(e)}function w_(e,a,i){return i(function(){A_(a)&&x_(e)})}function A_(e){var a=e.getSnapshot;e=e.value;try{var i=a();return!En(e,i)}catch{return!0}}function x_(e){var a=yi(e,2);a!==null&&An(a,e,2)}function Fm(e){var a=mn();if(typeof e=="function"){var i=e;if(e=i(),cs){lr(!0);try{i()}finally{lr(!1)}}}return a.memoizedState=a.baseState=e,a.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ba,lastRenderedState:e},a}function C_(e,a,i,o){return e.baseState=i,Lm(e,Zt,typeof o=="function"?o:Ba)}function lx(e,a,i,o,u){if(Bu(e))throw Error(s(485));if(e=a.action,e!==null){var d={payload:u,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(v){d.listeners.push(v)}};k.T!==null?i(!0):d.isTransition=!1,o(d),i=a.pending,i===null?(d.next=a.pending=d,D_(a,d)):(d.next=i.next,a.pending=i.next=d)}}function D_(e,a){var i=a.action,o=a.payload,u=e.state;if(a.isTransition){var d=k.T,v={};k.T=v;try{var b=i(u,o),R=k.S;R!==null&&R(v,b),k_(e,a,b)}catch(z){jm(e,a,z)}finally{k.T=d}}else try{d=i(u,o),k_(e,a,d)}catch(z){jm(e,a,z)}}function k_(e,a,i){i!==null&&typeof i=="object"&&typeof i.then=="function"?i.then(function(o){N_(e,a,o)},function(o){return jm(e,a,o)}):N_(e,a,i)}function N_(e,a,i){a.status="fulfilled",a.value=i,z_(a),e.state=i,a=e.pending,a!==null&&(i=a.next,i===a?e.pending=null:(i=i.next,a.next=i,D_(e,i)))}function jm(e,a,i){var o=e.pending;if(e.pending=null,o!==null){o=o.next;do a.status="rejected",a.reason=i,z_(a),a=a.next;while(a!==o)}e.action=null}function z_(e){e=e.listeners;for(var a=0;a<e.length;a++)(0,e[a])()}function L_(e,a){return a}function $_(e,a){if(Pt){var i=se.formState;if(i!==null){t:{var o=kt;if(Pt){if(ye){e:{for(var u=ye,d=ha;u.nodeType!==8;){if(!d){u=null;break e}if(u=ta(u.nextSibling),u===null){u=null;break e}}d=u.data,u=d==="F!"||d==="F"?u:null}if(u){ye=ta(u.nextSibling),o=u.data==="F!";break t}}ss(o)}o=!1}o&&(a=i[0])}}return i=mn(),i.memoizedState=i.baseState=a,o={pending:null,lanes:0,dispatch:null,lastRenderedReducer:L_,lastRenderedState:a},i.queue=o,i=eb.bind(null,kt,o),o.dispatch=i,o=Fm(!1),d=Im.bind(null,kt,!1,o.queue),o=mn(),u={state:a,dispatch:null,action:e,pending:null},o.queue=u,i=lx.bind(null,kt,u,d,i),u.dispatch=i,o.memoizedState=e,[a,i,!1]}function F_(e){var a=Ae();return j_(a,Zt,e)}function j_(e,a,i){if(a=Lm(e,a,L_)[0],e=Fu(Ba)[0],typeof a=="object"&&a!==null&&typeof a.then=="function")try{var o=El(a)}catch(v){throw v===pl?Du:v}else o=a;a=Ae();var u=a.queue,d=u.dispatch;return i!==a.memoizedState&&(kt.flags|=2048,Ri(9,ju(),cx.bind(null,u,i),null)),[o,d,e]}function cx(e,a){e.action=a}function U_(e){var a=Ae(),i=Zt;if(i!==null)return j_(a,i,e);Ae(),a=a.memoizedState,i=Ae();var o=i.queue.dispatch;return i.memoizedState=e,[a,o,!1]}function Ri(e,a,i,o){return e={tag:e,create:i,deps:o,inst:a,next:null},a=kt.updateQueue,a===null&&(a=Nm(),kt.updateQueue=a),i=a.lastEffect,i===null?a.lastEffect=e.next=e:(o=i.next,i.next=e,e.next=o,a.lastEffect=e),e}function ju(){return{destroy:void 0,resource:void 0}}function B_(){return Ae().memoizedState}function Uu(e,a,i,o){var u=mn();o=o===void 0?null:o,kt.flags|=e,u.memoizedState=Ri(1|a,ju(),i,o)}function Tl(e,a,i,o){var u=Ae();o=o===void 0?null:o;var d=u.memoizedState.inst;Zt!==null&&o!==null&&Am(o,Zt.memoizedState.deps)?u.memoizedState=Ri(a,d,i,o):(kt.flags|=e,u.memoizedState=Ri(1|a,d,i,o))}function q_(e,a){Uu(8390656,8,e,a)}function H_(e,a){Tl(2048,8,e,a)}function I_(e,a){return Tl(4,2,e,a)}function P_(e,a){return Tl(4,4,e,a)}function V_(e,a){if(typeof a=="function"){e=e();var i=a(e);return function(){typeof i=="function"?i():a(null)}}if(a!=null)return e=e(),a.current=e,function(){a.current=null}}function G_(e,a,i){i=i!=null?i.concat([e]):null,Tl(4,4,V_.bind(null,a,e),i)}function Um(){}function K_(e,a){var i=Ae();a=a===void 0?null:a;var o=i.memoizedState;return a!==null&&Am(a,o[1])?o[0]:(i.memoizedState=[e,a],e)}function Q_(e,a){var i=Ae();a=a===void 0?null:a;var o=i.memoizedState;if(a!==null&&Am(a,o[1]))return o[0];if(o=e(),cs){lr(!0);try{e()}finally{lr(!1)}}return i.memoizedState=[o,a],o}function Bm(e,a,i){return i===void 0||(yr&1073741824)!==0?e.memoizedState=a:(e.memoizedState=i,e=Zb(),kt.lanes|=e,Er|=e,i)}function Y_(e,a,i,o){return En(i,a)?i:Si.current!==null?(e=Bm(e,i,o),En(e,a)||(je=!0),e):(yr&42)===0?(je=!0,e.memoizedState=i):(e=Zb(),kt.lanes|=e,Er|=e,a)}function X_(e,a,i,o,u){var d=X.p;X.p=d!==0&&8>d?d:8;var v=k.T,b={};k.T=b,Im(e,!1,a,i);try{var R=u(),z=k.S;if(z!==null&&z(b,R),R!==null&&typeof R=="object"&&typeof R.then=="function"){var P=sx(R,o);Rl(e,a,P,wn(e))}else Rl(e,a,o,wn(e))}catch(Y){Rl(e,a,{then:function(){},status:"rejected",reason:Y},wn())}finally{X.p=d,k.T=v}}function ux(){}function qm(e,a,i,o){if(e.tag!==5)throw Error(s(476));var u=Z_(e).queue;X_(e,u,a,lt,i===null?ux:function(){return J_(e),i(o)})}function Z_(e){var a=e.memoizedState;if(a!==null)return a;a={memoizedState:lt,baseState:lt,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ba,lastRenderedState:lt},next:null};var i={};return a.next={memoizedState:i,baseState:i,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ba,lastRenderedState:i},next:null},e.memoizedState=a,e=e.alternate,e!==null&&(e.memoizedState=a),a}function J_(e){var a=Z_(e).next.queue;Rl(e,a,{},wn())}function Hm(){return Ye(Hl)}function W_(){return Ae().memoizedState}function tb(){return Ae().memoizedState}function fx(e){for(var a=e.return;a!==null;){switch(a.tag){case 24:case 3:var i=wn();e=hr(i);var o=mr(a,e,i);o!==null&&(An(o,a,i),vl(o,a,i)),a={cache:vm()},e.payload=a;return}a=a.return}}function dx(e,a,i){var o=wn();i={lane:o,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null},Bu(e)?nb(a,i):(i=lm(e,a,i,o),i!==null&&(An(i,e,o),ab(i,a,o)))}function eb(e,a,i){var o=wn();Rl(e,a,i,o)}function Rl(e,a,i,o){var u={lane:o,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null};if(Bu(e))nb(a,u);else{var d=e.alternate;if(e.lanes===0&&(d===null||d.lanes===0)&&(d=a.lastRenderedReducer,d!==null))try{var v=a.lastRenderedState,b=d(v,i);if(u.hasEagerState=!0,u.eagerState=b,En(b,v))return Tu(e,a,u,0),se===null&&Eu(),!1}catch{}finally{}if(i=lm(e,a,u,o),i!==null)return An(i,e,o),ab(i,a,o),!0}return!1}function Im(e,a,i,o){if(o={lane:2,revertLane:Sy(),action:o,hasEagerState:!1,eagerState:null,next:null},Bu(e)){if(a)throw Error(s(479))}else a=lm(e,i,o,2),a!==null&&An(a,e,2)}function Bu(e){var a=e.alternate;return e===kt||a!==null&&a===kt}function nb(e,a){Ei=zu=!0;var i=e.pending;i===null?a.next=a:(a.next=i.next,i.next=a),e.pending=a}function ab(e,a,i){if((i&4194048)!==0){var o=a.lanes;o&=e.pendingLanes,i|=o,a.lanes=i,fv(e,i)}}var qu={readContext:Ye,use:$u,useCallback:be,useContext:be,useEffect:be,useImperativeHandle:be,useLayoutEffect:be,useInsertionEffect:be,useMemo:be,useReducer:be,useRef:be,useState:be,useDebugValue:be,useDeferredValue:be,useTransition:be,useSyncExternalStore:be,useId:be,useHostTransitionStatus:be,useFormState:be,useActionState:be,useOptimistic:be,useMemoCache:be,useCacheRefresh:be},rb={readContext:Ye,use:$u,useCallback:function(e,a){return mn().memoizedState=[e,a===void 0?null:a],e},useContext:Ye,useEffect:q_,useImperativeHandle:function(e,a,i){i=i!=null?i.concat([e]):null,Uu(4194308,4,V_.bind(null,a,e),i)},useLayoutEffect:function(e,a){return Uu(4194308,4,e,a)},useInsertionEffect:function(e,a){Uu(4,2,e,a)},useMemo:function(e,a){var i=mn();a=a===void 0?null:a;var o=e();if(cs){lr(!0);try{e()}finally{lr(!1)}}return i.memoizedState=[o,a],o},useReducer:function(e,a,i){var o=mn();if(i!==void 0){var u=i(a);if(cs){lr(!0);try{i(a)}finally{lr(!1)}}}else u=a;return o.memoizedState=o.baseState=u,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:u},o.queue=e,e=e.dispatch=dx.bind(null,kt,e),[o.memoizedState,e]},useRef:function(e){var a=mn();return e={current:e},a.memoizedState=e},useState:function(e){e=Fm(e);var a=e.queue,i=eb.bind(null,kt,a);return a.dispatch=i,[e.memoizedState,i]},useDebugValue:Um,useDeferredValue:function(e,a){var i=mn();return Bm(i,e,a)},useTransition:function(){var e=Fm(!1);return e=X_.bind(null,kt,e.queue,!0,!1),mn().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,a,i){var o=kt,u=mn();if(Pt){if(i===void 0)throw Error(s(407));i=i()}else{if(i=a(),se===null)throw Error(s(349));(Bt&124)!==0||O_(o,a,i)}u.memoizedState=i;var d={value:i,getSnapshot:a};return u.queue=d,q_(w_.bind(null,o,d,e),[e]),o.flags|=2048,Ri(9,ju(),M_.bind(null,o,d,i,a),null),i},useId:function(){var e=mn(),a=se.identifierPrefix;if(Pt){var i=Fa,o=$a;i=(o&~(1<<32-Sn(o)-1)).toString(32)+i,a="«"+a+"R"+i,i=Lu++,0<i&&(a+="H"+i.toString(32)),a+="»"}else i=ix++,a="«"+a+"r"+i.toString(32)+"»";return e.memoizedState=a},useHostTransitionStatus:Hm,useFormState:$_,useActionState:$_,useOptimistic:function(e){var a=mn();a.memoizedState=a.baseState=e;var i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return a.queue=i,a=Im.bind(null,kt,!0,i),i.dispatch=a,[e,a]},useMemoCache:zm,useCacheRefresh:function(){return mn().memoizedState=fx.bind(null,kt)}},sb={readContext:Ye,use:$u,useCallback:K_,useContext:Ye,useEffect:H_,useImperativeHandle:G_,useInsertionEffect:I_,useLayoutEffect:P_,useMemo:Q_,useReducer:Fu,useRef:B_,useState:function(){return Fu(Ba)},useDebugValue:Um,useDeferredValue:function(e,a){var i=Ae();return Y_(i,Zt.memoizedState,e,a)},useTransition:function(){var e=Fu(Ba)[0],a=Ae().memoizedState;return[typeof e=="boolean"?e:El(e),a]},useSyncExternalStore:R_,useId:W_,useHostTransitionStatus:Hm,useFormState:F_,useActionState:F_,useOptimistic:function(e,a){var i=Ae();return C_(i,Zt,e,a)},useMemoCache:zm,useCacheRefresh:tb},hx={readContext:Ye,use:$u,useCallback:K_,useContext:Ye,useEffect:H_,useImperativeHandle:G_,useInsertionEffect:I_,useLayoutEffect:P_,useMemo:Q_,useReducer:$m,useRef:B_,useState:function(){return $m(Ba)},useDebugValue:Um,useDeferredValue:function(e,a){var i=Ae();return Zt===null?Bm(i,e,a):Y_(i,Zt.memoizedState,e,a)},useTransition:function(){var e=$m(Ba)[0],a=Ae().memoizedState;return[typeof e=="boolean"?e:El(e),a]},useSyncExternalStore:R_,useId:W_,useHostTransitionStatus:Hm,useFormState:U_,useActionState:U_,useOptimistic:function(e,a){var i=Ae();return Zt!==null?C_(i,Zt,e,a):(i.baseState=e,[e,i.queue.dispatch])},useMemoCache:zm,useCacheRefresh:tb},Oi=null,Ol=0;function Hu(e){var a=Ol;return Ol+=1,Oi===null&&(Oi=[]),p_(Oi,e,a)}function Ml(e,a){a=a.props.ref,e.ref=a!==void 0?a:null}function Iu(e,a){throw a.$$typeof===g?Error(s(525)):(e=Object.prototype.toString.call(a),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(a).join(", ")+"}":e)))}function ib(e){var a=e._init;return a(e._payload)}function ob(e){function a(C,w){if(e){var N=C.deletions;N===null?(C.deletions=[w],C.flags|=16):N.push(w)}}function i(C,w){if(!e)return null;for(;w!==null;)a(C,w),w=w.sibling;return null}function o(C){for(var w=new Map;C!==null;)C.key!==null?w.set(C.key,C):w.set(C.index,C),C=C.sibling;return w}function u(C,w){return C=La(C,w),C.index=0,C.sibling=null,C}function d(C,w,N){return C.index=N,e?(N=C.alternate,N!==null?(N=N.index,N<w?(C.flags|=67108866,w):N):(C.flags|=67108866,w)):(C.flags|=1048576,w)}function v(C){return e&&C.alternate===null&&(C.flags|=67108866),C}function b(C,w,N,V){return w===null||w.tag!==6?(w=um(N,C.mode,V),w.return=C,w):(w=u(w,N),w.return=C,w)}function R(C,w,N,V){var ut=N.type;return ut===T?P(C,w,N.props.children,V,N.key):w!==null&&(w.elementType===ut||typeof ut=="object"&&ut!==null&&ut.$$typeof===$&&ib(ut)===w.type)?(w=u(w,N.props),Ml(w,N),w.return=C,w):(w=Ou(N.type,N.key,N.props,null,C.mode,V),Ml(w,N),w.return=C,w)}function z(C,w,N,V){return w===null||w.tag!==4||w.stateNode.containerInfo!==N.containerInfo||w.stateNode.implementation!==N.implementation?(w=fm(N,C.mode,V),w.return=C,w):(w=u(w,N.children||[]),w.return=C,w)}function P(C,w,N,V,ut){return w===null||w.tag!==7?(w=es(N,C.mode,V,ut),w.return=C,w):(w=u(w,N),w.return=C,w)}function Y(C,w,N){if(typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint")return w=um(""+w,C.mode,N),w.return=C,w;if(typeof w=="object"&&w!==null){switch(w.$$typeof){case _:return N=Ou(w.type,w.key,w.props,null,C.mode,N),Ml(N,w),N.return=C,N;case S:return w=fm(w,C.mode,N),w.return=C,w;case $:var V=w._init;return w=V(w._payload),Y(C,w,N)}if(vt(w)||nt(w))return w=es(w,C.mode,N,null),w.return=C,w;if(typeof w.then=="function")return Y(C,Hu(w),N);if(w.$$typeof===q)return Y(C,xu(C,w),N);Iu(C,w)}return null}function U(C,w,N,V){var ut=w!==null?w.key:null;if(typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint")return ut!==null?null:b(C,w,""+N,V);if(typeof N=="object"&&N!==null){switch(N.$$typeof){case _:return N.key===ut?R(C,w,N,V):null;case S:return N.key===ut?z(C,w,N,V):null;case $:return ut=N._init,N=ut(N._payload),U(C,w,N,V)}if(vt(N)||nt(N))return ut!==null?null:P(C,w,N,V,null);if(typeof N.then=="function")return U(C,w,Hu(N),V);if(N.$$typeof===q)return U(C,w,xu(C,N),V);Iu(C,N)}return null}function B(C,w,N,V,ut){if(typeof V=="string"&&V!==""||typeof V=="number"||typeof V=="bigint")return C=C.get(N)||null,b(w,C,""+V,ut);if(typeof V=="object"&&V!==null){switch(V.$$typeof){case _:return C=C.get(V.key===null?N:V.key)||null,R(w,C,V,ut);case S:return C=C.get(V.key===null?N:V.key)||null,z(w,C,V,ut);case $:var Lt=V._init;return V=Lt(V._payload),B(C,w,N,V,ut)}if(vt(V)||nt(V))return C=C.get(N)||null,P(w,C,V,ut,null);if(typeof V.then=="function")return B(C,w,N,Hu(V),ut);if(V.$$typeof===q)return B(C,w,N,xu(w,V),ut);Iu(w,V)}return null}function Et(C,w,N,V){for(var ut=null,Lt=null,ht=w,bt=w=0,Be=null;ht!==null&&bt<N.length;bt++){ht.index>bt?(Be=ht,ht=null):Be=ht.sibling;var It=U(C,ht,N[bt],V);if(It===null){ht===null&&(ht=Be);break}e&&ht&&It.alternate===null&&a(C,ht),w=d(It,w,bt),Lt===null?ut=It:Lt.sibling=It,Lt=It,ht=Be}if(bt===N.length)return i(C,ht),Pt&&as(C,bt),ut;if(ht===null){for(;bt<N.length;bt++)ht=Y(C,N[bt],V),ht!==null&&(w=d(ht,w,bt),Lt===null?ut=ht:Lt.sibling=ht,Lt=ht);return Pt&&as(C,bt),ut}for(ht=o(ht);bt<N.length;bt++)Be=B(ht,C,bt,N[bt],V),Be!==null&&(e&&Be.alternate!==null&&ht.delete(Be.key===null?bt:Be.key),w=d(Be,w,bt),Lt===null?ut=Be:Lt.sibling=Be,Lt=Be);return e&&ht.forEach(function(Dr){return a(C,Dr)}),Pt&&as(C,bt),ut}function _t(C,w,N,V){if(N==null)throw Error(s(151));for(var ut=null,Lt=null,ht=w,bt=w=0,Be=null,It=N.next();ht!==null&&!It.done;bt++,It=N.next()){ht.index>bt?(Be=ht,ht=null):Be=ht.sibling;var Dr=U(C,ht,It.value,V);if(Dr===null){ht===null&&(ht=Be);break}e&&ht&&Dr.alternate===null&&a(C,ht),w=d(Dr,w,bt),Lt===null?ut=Dr:Lt.sibling=Dr,Lt=Dr,ht=Be}if(It.done)return i(C,ht),Pt&&as(C,bt),ut;if(ht===null){for(;!It.done;bt++,It=N.next())It=Y(C,It.value,V),It!==null&&(w=d(It,w,bt),Lt===null?ut=It:Lt.sibling=It,Lt=It);return Pt&&as(C,bt),ut}for(ht=o(ht);!It.done;bt++,It=N.next())It=B(ht,C,bt,It.value,V),It!==null&&(e&&It.alternate!==null&&ht.delete(It.key===null?bt:It.key),w=d(It,w,bt),Lt===null?ut=It:Lt.sibling=It,Lt=It);return e&&ht.forEach(function(mC){return a(C,mC)}),Pt&&as(C,bt),ut}function Wt(C,w,N,V){if(typeof N=="object"&&N!==null&&N.type===T&&N.key===null&&(N=N.props.children),typeof N=="object"&&N!==null){switch(N.$$typeof){case _:t:{for(var ut=N.key;w!==null;){if(w.key===ut){if(ut=N.type,ut===T){if(w.tag===7){i(C,w.sibling),V=u(w,N.props.children),V.return=C,C=V;break t}}else if(w.elementType===ut||typeof ut=="object"&&ut!==null&&ut.$$typeof===$&&ib(ut)===w.type){i(C,w.sibling),V=u(w,N.props),Ml(V,N),V.return=C,C=V;break t}i(C,w);break}else a(C,w);w=w.sibling}N.type===T?(V=es(N.props.children,C.mode,V,N.key),V.return=C,C=V):(V=Ou(N.type,N.key,N.props,null,C.mode,V),Ml(V,N),V.return=C,C=V)}return v(C);case S:t:{for(ut=N.key;w!==null;){if(w.key===ut)if(w.tag===4&&w.stateNode.containerInfo===N.containerInfo&&w.stateNode.implementation===N.implementation){i(C,w.sibling),V=u(w,N.children||[]),V.return=C,C=V;break t}else{i(C,w);break}else a(C,w);w=w.sibling}V=fm(N,C.mode,V),V.return=C,C=V}return v(C);case $:return ut=N._init,N=ut(N._payload),Wt(C,w,N,V)}if(vt(N))return Et(C,w,N,V);if(nt(N)){if(ut=nt(N),typeof ut!="function")throw Error(s(150));return N=ut.call(N),_t(C,w,N,V)}if(typeof N.then=="function")return Wt(C,w,Hu(N),V);if(N.$$typeof===q)return Wt(C,w,xu(C,N),V);Iu(C,N)}return typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint"?(N=""+N,w!==null&&w.tag===6?(i(C,w.sibling),V=u(w,N),V.return=C,C=V):(i(C,w),V=um(N,C.mode,V),V.return=C,C=V),v(C)):i(C,w)}return function(C,w,N,V){try{Ol=0;var ut=Wt(C,w,N,V);return Oi=null,ut}catch(ht){if(ht===pl||ht===Du)throw ht;var Lt=Tn(29,ht,null,C.mode);return Lt.lanes=V,Lt.return=C,Lt}finally{}}}var Mi=ob(!0),lb=ob(!1),Un=H(null),ma=null;function pr(e){var a=e.alternate;at(De,De.current&1),at(Un,e),ma===null&&(a===null||Si.current!==null||a.memoizedState!==null)&&(ma=e)}function cb(e){if(e.tag===22){if(at(De,De.current),at(Un,e),ma===null){var a=e.alternate;a!==null&&a.memoizedState!==null&&(ma=e)}}else gr()}function gr(){at(De,De.current),at(Un,Un.current)}function qa(e){st(Un),ma===e&&(ma=null),st(De)}var De=H(0);function Pu(e){for(var a=e;a!==null;){if(a.tag===13){var i=a.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||Ny(i)))return a}else if(a.tag===19&&a.memoizedProps.revealOrder!==void 0){if((a.flags&128)!==0)return a}else if(a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)break;for(;a.sibling===null;){if(a.return===null||a.return===e)return null;a=a.return}a.sibling.return=a.return,a=a.sibling}return null}function Pm(e,a,i,o){a=e.memoizedState,i=i(o,a),i=i==null?a:p({},a,i),e.memoizedState=i,e.lanes===0&&(e.updateQueue.baseState=i)}var Vm={enqueueSetState:function(e,a,i){e=e._reactInternals;var o=wn(),u=hr(o);u.payload=a,i!=null&&(u.callback=i),a=mr(e,u,o),a!==null&&(An(a,e,o),vl(a,e,o))},enqueueReplaceState:function(e,a,i){e=e._reactInternals;var o=wn(),u=hr(o);u.tag=1,u.payload=a,i!=null&&(u.callback=i),a=mr(e,u,o),a!==null&&(An(a,e,o),vl(a,e,o))},enqueueForceUpdate:function(e,a){e=e._reactInternals;var i=wn(),o=hr(i);o.tag=2,a!=null&&(o.callback=a),a=mr(e,o,i),a!==null&&(An(a,e,i),vl(a,e,i))}};function ub(e,a,i,o,u,d,v){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(o,d,v):a.prototype&&a.prototype.isPureReactComponent?!ll(i,o)||!ll(u,d):!0}function fb(e,a,i,o){e=a.state,typeof a.componentWillReceiveProps=="function"&&a.componentWillReceiveProps(i,o),typeof a.UNSAFE_componentWillReceiveProps=="function"&&a.UNSAFE_componentWillReceiveProps(i,o),a.state!==e&&Vm.enqueueReplaceState(a,a.state,null)}function us(e,a){var i=a;if("ref"in a){i={};for(var o in a)o!=="ref"&&(i[o]=a[o])}if(e=e.defaultProps){i===a&&(i=p({},i));for(var u in e)i[u]===void 0&&(i[u]=e[u])}return i}var Vu=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var a=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(a))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function db(e){Vu(e)}function hb(e){console.error(e)}function mb(e){Vu(e)}function Gu(e,a){try{var i=e.onUncaughtError;i(a.value,{componentStack:a.stack})}catch(o){setTimeout(function(){throw o})}}function yb(e,a,i){try{var o=e.onCaughtError;o(i.value,{componentStack:i.stack,errorBoundary:a.tag===1?a.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function Gm(e,a,i){return i=hr(i),i.tag=3,i.payload={element:null},i.callback=function(){Gu(e,a)},i}function pb(e){return e=hr(e),e.tag=3,e}function gb(e,a,i,o){var u=i.type.getDerivedStateFromError;if(typeof u=="function"){var d=o.value;e.payload=function(){return u(d)},e.callback=function(){yb(a,i,o)}}var v=i.stateNode;v!==null&&typeof v.componentDidCatch=="function"&&(e.callback=function(){yb(a,i,o),typeof u!="function"&&(Tr===null?Tr=new Set([this]):Tr.add(this));var b=o.stack;this.componentDidCatch(o.value,{componentStack:b!==null?b:""})})}function mx(e,a,i,o,u){if(i.flags|=32768,o!==null&&typeof o=="object"&&typeof o.then=="function"){if(a=i.alternate,a!==null&&hl(a,i,u,!0),i=Un.current,i!==null){switch(i.tag){case 13:return ma===null?py():i.alternate===null&&pe===0&&(pe=3),i.flags&=-257,i.flags|=65536,i.lanes=u,o===Sm?i.flags|=16384:(a=i.updateQueue,a===null?i.updateQueue=new Set([o]):a.add(o),vy(e,o,u)),!1;case 22:return i.flags|=65536,o===Sm?i.flags|=16384:(a=i.updateQueue,a===null?(a={transitions:null,markerInstances:null,retryQueue:new Set([o])},i.updateQueue=a):(i=a.retryQueue,i===null?a.retryQueue=new Set([o]):i.add(o)),vy(e,o,u)),!1}throw Error(s(435,i.tag))}return vy(e,o,u),py(),!1}if(Pt)return a=Un.current,a!==null?((a.flags&65536)===0&&(a.flags|=256),a.flags|=65536,a.lanes=u,o!==mm&&(e=Error(s(422),{cause:o}),dl(Ln(e,i)))):(o!==mm&&(a=Error(s(423),{cause:o}),dl(Ln(a,i))),e=e.current.alternate,e.flags|=65536,u&=-u,e.lanes|=u,o=Ln(o,i),u=Gm(e.stateNode,o,u),Rm(e,u),pe!==4&&(pe=2)),!1;var d=Error(s(520),{cause:o});if(d=Ln(d,i),Nl===null?Nl=[d]:Nl.push(d),pe!==4&&(pe=2),a===null)return!0;o=Ln(o,i),i=a;do{switch(i.tag){case 3:return i.flags|=65536,e=u&-u,i.lanes|=e,e=Gm(i.stateNode,o,e),Rm(i,e),!1;case 1:if(a=i.type,d=i.stateNode,(i.flags&128)===0&&(typeof a.getDerivedStateFromError=="function"||d!==null&&typeof d.componentDidCatch=="function"&&(Tr===null||!Tr.has(d))))return i.flags|=65536,u&=-u,i.lanes|=u,u=pb(u),gb(u,e,i,o),Rm(i,u),!1}i=i.return}while(i!==null);return!1}var vb=Error(s(461)),je=!1;function Ie(e,a,i,o){a.child=e===null?lb(a,null,i,o):Mi(a,e.child,i,o)}function _b(e,a,i,o,u){i=i.render;var d=a.ref;if("ref"in o){var v={};for(var b in o)b!=="ref"&&(v[b]=o[b])}else v=o;return os(a),o=xm(e,a,i,v,d,u),b=Cm(),e!==null&&!je?(Dm(e,a,u),Ha(e,a,u)):(Pt&&b&&dm(a),a.flags|=1,Ie(e,a,o,u),a.child)}function bb(e,a,i,o,u){if(e===null){var d=i.type;return typeof d=="function"&&!cm(d)&&d.defaultProps===void 0&&i.compare===null?(a.tag=15,a.type=d,Sb(e,a,d,o,u)):(e=Ou(i.type,null,o,a,a.mode,u),e.ref=a.ref,e.return=a,a.child=e)}if(d=e.child,!ty(e,u)){var v=d.memoizedProps;if(i=i.compare,i=i!==null?i:ll,i(v,o)&&e.ref===a.ref)return Ha(e,a,u)}return a.flags|=1,e=La(d,o),e.ref=a.ref,e.return=a,a.child=e}function Sb(e,a,i,o,u){if(e!==null){var d=e.memoizedProps;if(ll(d,o)&&e.ref===a.ref)if(je=!1,a.pendingProps=o=d,ty(e,u))(e.flags&131072)!==0&&(je=!0);else return a.lanes=e.lanes,Ha(e,a,u)}return Km(e,a,i,o,u)}function Eb(e,a,i){var o=a.pendingProps,u=o.children,d=e!==null?e.memoizedState:null;if(o.mode==="hidden"){if((a.flags&128)!==0){if(o=d!==null?d.baseLanes|i:i,e!==null){for(u=a.child=e.child,d=0;u!==null;)d=d|u.lanes|u.childLanes,u=u.sibling;a.childLanes=d&~o}else a.childLanes=0,a.child=null;return Tb(e,a,o,i)}if((i&536870912)!==0)a.memoizedState={baseLanes:0,cachePool:null},e!==null&&Cu(a,d!==null?d.cachePool:null),d!==null?S_(a,d):Mm(),cb(a);else return a.lanes=a.childLanes=536870912,Tb(e,a,d!==null?d.baseLanes|i:i,i)}else d!==null?(Cu(a,d.cachePool),S_(a,d),gr(),a.memoizedState=null):(e!==null&&Cu(a,null),Mm(),gr());return Ie(e,a,u,i),a.child}function Tb(e,a,i,o){var u=bm();return u=u===null?null:{parent:Ce._currentValue,pool:u},a.memoizedState={baseLanes:i,cachePool:u},e!==null&&Cu(a,null),Mm(),cb(a),e!==null&&hl(e,a,o,!0),null}function Ku(e,a){var i=a.ref;if(i===null)e!==null&&e.ref!==null&&(a.flags|=4194816);else{if(typeof i!="function"&&typeof i!="object")throw Error(s(284));(e===null||e.ref!==i)&&(a.flags|=4194816)}}function Km(e,a,i,o,u){return os(a),i=xm(e,a,i,o,void 0,u),o=Cm(),e!==null&&!je?(Dm(e,a,u),Ha(e,a,u)):(Pt&&o&&dm(a),a.flags|=1,Ie(e,a,i,u),a.child)}function Rb(e,a,i,o,u,d){return os(a),a.updateQueue=null,i=T_(a,o,i,u),E_(e),o=Cm(),e!==null&&!je?(Dm(e,a,d),Ha(e,a,d)):(Pt&&o&&dm(a),a.flags|=1,Ie(e,a,i,d),a.child)}function Ob(e,a,i,o,u){if(os(a),a.stateNode===null){var d=pi,v=i.contextType;typeof v=="object"&&v!==null&&(d=Ye(v)),d=new i(o,d),a.memoizedState=d.state!==null&&d.state!==void 0?d.state:null,d.updater=Vm,a.stateNode=d,d._reactInternals=a,d=a.stateNode,d.props=o,d.state=a.memoizedState,d.refs={},Em(a),v=i.contextType,d.context=typeof v=="object"&&v!==null?Ye(v):pi,d.state=a.memoizedState,v=i.getDerivedStateFromProps,typeof v=="function"&&(Pm(a,i,v,o),d.state=a.memoizedState),typeof i.getDerivedStateFromProps=="function"||typeof d.getSnapshotBeforeUpdate=="function"||typeof d.UNSAFE_componentWillMount!="function"&&typeof d.componentWillMount!="function"||(v=d.state,typeof d.componentWillMount=="function"&&d.componentWillMount(),typeof d.UNSAFE_componentWillMount=="function"&&d.UNSAFE_componentWillMount(),v!==d.state&&Vm.enqueueReplaceState(d,d.state,null),bl(a,o,d,u),_l(),d.state=a.memoizedState),typeof d.componentDidMount=="function"&&(a.flags|=4194308),o=!0}else if(e===null){d=a.stateNode;var b=a.memoizedProps,R=us(i,b);d.props=R;var z=d.context,P=i.contextType;v=pi,typeof P=="object"&&P!==null&&(v=Ye(P));var Y=i.getDerivedStateFromProps;P=typeof Y=="function"||typeof d.getSnapshotBeforeUpdate=="function",b=a.pendingProps!==b,P||typeof d.UNSAFE_componentWillReceiveProps!="function"&&typeof d.componentWillReceiveProps!="function"||(b||z!==v)&&fb(a,d,o,v),dr=!1;var U=a.memoizedState;d.state=U,bl(a,o,d,u),_l(),z=a.memoizedState,b||U!==z||dr?(typeof Y=="function"&&(Pm(a,i,Y,o),z=a.memoizedState),(R=dr||ub(a,i,R,o,U,z,v))?(P||typeof d.UNSAFE_componentWillMount!="function"&&typeof d.componentWillMount!="function"||(typeof d.componentWillMount=="function"&&d.componentWillMount(),typeof d.UNSAFE_componentWillMount=="function"&&d.UNSAFE_componentWillMount()),typeof d.componentDidMount=="function"&&(a.flags|=4194308)):(typeof d.componentDidMount=="function"&&(a.flags|=4194308),a.memoizedProps=o,a.memoizedState=z),d.props=o,d.state=z,d.context=v,o=R):(typeof d.componentDidMount=="function"&&(a.flags|=4194308),o=!1)}else{d=a.stateNode,Tm(e,a),v=a.memoizedProps,P=us(i,v),d.props=P,Y=a.pendingProps,U=d.context,z=i.contextType,R=pi,typeof z=="object"&&z!==null&&(R=Ye(z)),b=i.getDerivedStateFromProps,(z=typeof b=="function"||typeof d.getSnapshotBeforeUpdate=="function")||typeof d.UNSAFE_componentWillReceiveProps!="function"&&typeof d.componentWillReceiveProps!="function"||(v!==Y||U!==R)&&fb(a,d,o,R),dr=!1,U=a.memoizedState,d.state=U,bl(a,o,d,u),_l();var B=a.memoizedState;v!==Y||U!==B||dr||e!==null&&e.dependencies!==null&&Au(e.dependencies)?(typeof b=="function"&&(Pm(a,i,b,o),B=a.memoizedState),(P=dr||ub(a,i,P,o,U,B,R)||e!==null&&e.dependencies!==null&&Au(e.dependencies))?(z||typeof d.UNSAFE_componentWillUpdate!="function"&&typeof d.componentWillUpdate!="function"||(typeof d.componentWillUpdate=="function"&&d.componentWillUpdate(o,B,R),typeof d.UNSAFE_componentWillUpdate=="function"&&d.UNSAFE_componentWillUpdate(o,B,R)),typeof d.componentDidUpdate=="function"&&(a.flags|=4),typeof d.getSnapshotBeforeUpdate=="function"&&(a.flags|=1024)):(typeof d.componentDidUpdate!="function"||v===e.memoizedProps&&U===e.memoizedState||(a.flags|=4),typeof d.getSnapshotBeforeUpdate!="function"||v===e.memoizedProps&&U===e.memoizedState||(a.flags|=1024),a.memoizedProps=o,a.memoizedState=B),d.props=o,d.state=B,d.context=R,o=P):(typeof d.componentDidUpdate!="function"||v===e.memoizedProps&&U===e.memoizedState||(a.flags|=4),typeof d.getSnapshotBeforeUpdate!="function"||v===e.memoizedProps&&U===e.memoizedState||(a.flags|=1024),o=!1)}return d=o,Ku(e,a),o=(a.flags&128)!==0,d||o?(d=a.stateNode,i=o&&typeof i.getDerivedStateFromError!="function"?null:d.render(),a.flags|=1,e!==null&&o?(a.child=Mi(a,e.child,null,u),a.child=Mi(a,null,i,u)):Ie(e,a,i,u),a.memoizedState=d.state,e=a.child):e=Ha(e,a,u),e}function Mb(e,a,i,o){return fl(),a.flags|=256,Ie(e,a,i,o),a.child}var Qm={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ym(e){return{baseLanes:e,cachePool:h_()}}function Xm(e,a,i){return e=e!==null?e.childLanes&~i:0,a&&(e|=Bn),e}function wb(e,a,i){var o=a.pendingProps,u=!1,d=(a.flags&128)!==0,v;if((v=d)||(v=e!==null&&e.memoizedState===null?!1:(De.current&2)!==0),v&&(u=!0,a.flags&=-129),v=(a.flags&32)!==0,a.flags&=-33,e===null){if(Pt){if(u?pr(a):gr(),Pt){var b=ye,R;if(R=b){t:{for(R=b,b=ha;R.nodeType!==8;){if(!b){b=null;break t}if(R=ta(R.nextSibling),R===null){b=null;break t}}b=R}b!==null?(a.memoizedState={dehydrated:b,treeContext:ns!==null?{id:$a,overflow:Fa}:null,retryLane:536870912,hydrationErrors:null},R=Tn(18,null,null,0),R.stateNode=b,R.return=a,a.child=R,tn=a,ye=null,R=!0):R=!1}R||ss(a)}if(b=a.memoizedState,b!==null&&(b=b.dehydrated,b!==null))return Ny(b)?a.lanes=32:a.lanes=536870912,null;qa(a)}return b=o.children,o=o.fallback,u?(gr(),u=a.mode,b=Qu({mode:"hidden",children:b},u),o=es(o,u,i,null),b.return=a,o.return=a,b.sibling=o,a.child=b,u=a.child,u.memoizedState=Ym(i),u.childLanes=Xm(e,v,i),a.memoizedState=Qm,o):(pr(a),Zm(a,b))}if(R=e.memoizedState,R!==null&&(b=R.dehydrated,b!==null)){if(d)a.flags&256?(pr(a),a.flags&=-257,a=Jm(e,a,i)):a.memoizedState!==null?(gr(),a.child=e.child,a.flags|=128,a=null):(gr(),u=o.fallback,b=a.mode,o=Qu({mode:"visible",children:o.children},b),u=es(u,b,i,null),u.flags|=2,o.return=a,u.return=a,o.sibling=u,a.child=o,Mi(a,e.child,null,i),o=a.child,o.memoizedState=Ym(i),o.childLanes=Xm(e,v,i),a.memoizedState=Qm,a=u);else if(pr(a),Ny(b)){if(v=b.nextSibling&&b.nextSibling.dataset,v)var z=v.dgst;v=z,o=Error(s(419)),o.stack="",o.digest=v,dl({value:o,source:null,stack:null}),a=Jm(e,a,i)}else if(je||hl(e,a,i,!1),v=(i&e.childLanes)!==0,je||v){if(v=se,v!==null&&(o=i&-i,o=(o&42)!==0?1:zh(o),o=(o&(v.suspendedLanes|i))!==0?0:o,o!==0&&o!==R.retryLane))throw R.retryLane=o,yi(e,o),An(v,e,o),vb;b.data==="$?"||py(),a=Jm(e,a,i)}else b.data==="$?"?(a.flags|=192,a.child=e.child,a=null):(e=R.treeContext,ye=ta(b.nextSibling),tn=a,Pt=!0,rs=null,ha=!1,e!==null&&(Fn[jn++]=$a,Fn[jn++]=Fa,Fn[jn++]=ns,$a=e.id,Fa=e.overflow,ns=a),a=Zm(a,o.children),a.flags|=4096);return a}return u?(gr(),u=o.fallback,b=a.mode,R=e.child,z=R.sibling,o=La(R,{mode:"hidden",children:o.children}),o.subtreeFlags=R.subtreeFlags&65011712,z!==null?u=La(z,u):(u=es(u,b,i,null),u.flags|=2),u.return=a,o.return=a,o.sibling=u,a.child=o,o=u,u=a.child,b=e.child.memoizedState,b===null?b=Ym(i):(R=b.cachePool,R!==null?(z=Ce._currentValue,R=R.parent!==z?{parent:z,pool:z}:R):R=h_(),b={baseLanes:b.baseLanes|i,cachePool:R}),u.memoizedState=b,u.childLanes=Xm(e,v,i),a.memoizedState=Qm,o):(pr(a),i=e.child,e=i.sibling,i=La(i,{mode:"visible",children:o.children}),i.return=a,i.sibling=null,e!==null&&(v=a.deletions,v===null?(a.deletions=[e],a.flags|=16):v.push(e)),a.child=i,a.memoizedState=null,i)}function Zm(e,a){return a=Qu({mode:"visible",children:a},e.mode),a.return=e,e.child=a}function Qu(e,a){return e=Tn(22,e,null,a),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Jm(e,a,i){return Mi(a,e.child,null,i),e=Zm(a,a.pendingProps.children),e.flags|=2,a.memoizedState=null,e}function Ab(e,a,i){e.lanes|=a;var o=e.alternate;o!==null&&(o.lanes|=a),pm(e.return,a,i)}function Wm(e,a,i,o,u){var d=e.memoizedState;d===null?e.memoizedState={isBackwards:a,rendering:null,renderingStartTime:0,last:o,tail:i,tailMode:u}:(d.isBackwards=a,d.rendering=null,d.renderingStartTime=0,d.last=o,d.tail=i,d.tailMode=u)}function xb(e,a,i){var o=a.pendingProps,u=o.revealOrder,d=o.tail;if(Ie(e,a,o.children,i),o=De.current,(o&2)!==0)o=o&1|2,a.flags|=128;else{if(e!==null&&(e.flags&128)!==0)t:for(e=a.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ab(e,i,a);else if(e.tag===19)Ab(e,i,a);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===a)break t;for(;e.sibling===null;){if(e.return===null||e.return===a)break t;e=e.return}e.sibling.return=e.return,e=e.sibling}o&=1}switch(at(De,o),u){case"forwards":for(i=a.child,u=null;i!==null;)e=i.alternate,e!==null&&Pu(e)===null&&(u=i),i=i.sibling;i=u,i===null?(u=a.child,a.child=null):(u=i.sibling,i.sibling=null),Wm(a,!1,u,i,d);break;case"backwards":for(i=null,u=a.child,a.child=null;u!==null;){if(e=u.alternate,e!==null&&Pu(e)===null){a.child=u;break}e=u.sibling,u.sibling=i,i=u,u=e}Wm(a,!0,i,null,d);break;case"together":Wm(a,!1,null,null,void 0);break;default:a.memoizedState=null}return a.child}function Ha(e,a,i){if(e!==null&&(a.dependencies=e.dependencies),Er|=a.lanes,(i&a.childLanes)===0)if(e!==null){if(hl(e,a,i,!1),(i&a.childLanes)===0)return null}else return null;if(e!==null&&a.child!==e.child)throw Error(s(153));if(a.child!==null){for(e=a.child,i=La(e,e.pendingProps),a.child=i,i.return=a;e.sibling!==null;)e=e.sibling,i=i.sibling=La(e,e.pendingProps),i.return=a;i.sibling=null}return a.child}function ty(e,a){return(e.lanes&a)!==0?!0:(e=e.dependencies,!!(e!==null&&Au(e)))}function yx(e,a,i){switch(a.tag){case 3:dt(a,a.stateNode.containerInfo),fr(a,Ce,e.memoizedState.cache),fl();break;case 27:case 5:ee(a);break;case 4:dt(a,a.stateNode.containerInfo);break;case 10:fr(a,a.type,a.memoizedProps.value);break;case 13:var o=a.memoizedState;if(o!==null)return o.dehydrated!==null?(pr(a),a.flags|=128,null):(i&a.child.childLanes)!==0?wb(e,a,i):(pr(a),e=Ha(e,a,i),e!==null?e.sibling:null);pr(a);break;case 19:var u=(e.flags&128)!==0;if(o=(i&a.childLanes)!==0,o||(hl(e,a,i,!1),o=(i&a.childLanes)!==0),u){if(o)return xb(e,a,i);a.flags|=128}if(u=a.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),at(De,De.current),o)break;return null;case 22:case 23:return a.lanes=0,Eb(e,a,i);case 24:fr(a,Ce,e.memoizedState.cache)}return Ha(e,a,i)}function Cb(e,a,i){if(e!==null)if(e.memoizedProps!==a.pendingProps)je=!0;else{if(!ty(e,i)&&(a.flags&128)===0)return je=!1,yx(e,a,i);je=(e.flags&131072)!==0}else je=!1,Pt&&(a.flags&1048576)!==0&&i_(a,wu,a.index);switch(a.lanes=0,a.tag){case 16:t:{e=a.pendingProps;var o=a.elementType,u=o._init;if(o=u(o._payload),a.type=o,typeof o=="function")cm(o)?(e=us(o,e),a.tag=1,a=Ob(null,a,o,e,i)):(a.tag=0,a=Km(null,a,o,e,i));else{if(o!=null){if(u=o.$$typeof,u===K){a.tag=11,a=_b(null,a,o,e,i);break t}else if(u===Q){a.tag=14,a=bb(null,a,o,e,i);break t}}throw a=St(o)||o,Error(s(306,a,""))}}return a;case 0:return Km(e,a,a.type,a.pendingProps,i);case 1:return o=a.type,u=us(o,a.pendingProps),Ob(e,a,o,u,i);case 3:t:{if(dt(a,a.stateNode.containerInfo),e===null)throw Error(s(387));o=a.pendingProps;var d=a.memoizedState;u=d.element,Tm(e,a),bl(a,o,null,i);var v=a.memoizedState;if(o=v.cache,fr(a,Ce,o),o!==d.cache&&gm(a,[Ce],i,!0),_l(),o=v.element,d.isDehydrated)if(d={element:o,isDehydrated:!1,cache:v.cache},a.updateQueue.baseState=d,a.memoizedState=d,a.flags&256){a=Mb(e,a,o,i);break t}else if(o!==u){u=Ln(Error(s(424)),a),dl(u),a=Mb(e,a,o,i);break t}else{switch(e=a.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(ye=ta(e.firstChild),tn=a,Pt=!0,rs=null,ha=!0,i=lb(a,null,o,i),a.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling}else{if(fl(),o===u){a=Ha(e,a,i);break t}Ie(e,a,o,i)}a=a.child}return a;case 26:return Ku(e,a),e===null?(i=zS(a.type,null,a.pendingProps,null))?a.memoizedState=i:Pt||(i=a.type,e=a.pendingProps,o=cf(ct.current).createElement(i),o[Qe]=a,o[dn]=e,Ve(o,i,e),Fe(o),a.stateNode=o):a.memoizedState=zS(a.type,e.memoizedProps,a.pendingProps,e.memoizedState),null;case 27:return ee(a),e===null&&Pt&&(o=a.stateNode=DS(a.type,a.pendingProps,ct.current),tn=a,ha=!0,u=ye,Mr(a.type)?(zy=u,ye=ta(o.firstChild)):ye=u),Ie(e,a,a.pendingProps.children,i),Ku(e,a),e===null&&(a.flags|=4194304),a.child;case 5:return e===null&&Pt&&((u=o=ye)&&(o=Ix(o,a.type,a.pendingProps,ha),o!==null?(a.stateNode=o,tn=a,ye=ta(o.firstChild),ha=!1,u=!0):u=!1),u||ss(a)),ee(a),u=a.type,d=a.pendingProps,v=e!==null?e.memoizedProps:null,o=d.children,Cy(u,d)?o=null:v!==null&&Cy(u,v)&&(a.flags|=32),a.memoizedState!==null&&(u=xm(e,a,ox,null,null,i),Hl._currentValue=u),Ku(e,a),Ie(e,a,o,i),a.child;case 6:return e===null&&Pt&&((e=i=ye)&&(i=Px(i,a.pendingProps,ha),i!==null?(a.stateNode=i,tn=a,ye=null,e=!0):e=!1),e||ss(a)),null;case 13:return wb(e,a,i);case 4:return dt(a,a.stateNode.containerInfo),o=a.pendingProps,e===null?a.child=Mi(a,null,o,i):Ie(e,a,o,i),a.child;case 11:return _b(e,a,a.type,a.pendingProps,i);case 7:return Ie(e,a,a.pendingProps,i),a.child;case 8:return Ie(e,a,a.pendingProps.children,i),a.child;case 12:return Ie(e,a,a.pendingProps.children,i),a.child;case 10:return o=a.pendingProps,fr(a,a.type,o.value),Ie(e,a,o.children,i),a.child;case 9:return u=a.type._context,o=a.pendingProps.children,os(a),u=Ye(u),o=o(u),a.flags|=1,Ie(e,a,o,i),a.child;case 14:return bb(e,a,a.type,a.pendingProps,i);case 15:return Sb(e,a,a.type,a.pendingProps,i);case 19:return xb(e,a,i);case 31:return o=a.pendingProps,i=a.mode,o={mode:o.mode,children:o.children},e===null?(i=Qu(o,i),i.ref=a.ref,a.child=i,i.return=a,a=i):(i=La(e.child,o),i.ref=a.ref,a.child=i,i.return=a,a=i),a;case 22:return Eb(e,a,i);case 24:return os(a),o=Ye(Ce),e===null?(u=bm(),u===null&&(u=se,d=vm(),u.pooledCache=d,d.refCount++,d!==null&&(u.pooledCacheLanes|=i),u=d),a.memoizedState={parent:o,cache:u},Em(a),fr(a,Ce,u)):((e.lanes&i)!==0&&(Tm(e,a),bl(a,null,null,i),_l()),u=e.memoizedState,d=a.memoizedState,u.parent!==o?(u={parent:o,cache:o},a.memoizedState=u,a.lanes===0&&(a.memoizedState=a.updateQueue.baseState=u),fr(a,Ce,o)):(o=d.cache,fr(a,Ce,o),o!==u.cache&&gm(a,[Ce],i,!0))),Ie(e,a,a.pendingProps.children,i),a.child;case 29:throw a.pendingProps}throw Error(s(156,a.tag))}function Ia(e){e.flags|=4}function Db(e,a){if(a.type!=="stylesheet"||(a.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!US(a)){if(a=Un.current,a!==null&&((Bt&4194048)===Bt?ma!==null:(Bt&62914560)!==Bt&&(Bt&536870912)===0||a!==ma))throw gl=Sm,m_;e.flags|=8192}}function Yu(e,a){a!==null&&(e.flags|=4),e.flags&16384&&(a=e.tag!==22?cv():536870912,e.lanes|=a,Ci|=a)}function wl(e,a){if(!Pt)switch(e.tailMode){case"hidden":a=e.tail;for(var i=null;a!==null;)a.alternate!==null&&(i=a),a=a.sibling;i===null?e.tail=null:i.sibling=null;break;case"collapsed":i=e.tail;for(var o=null;i!==null;)i.alternate!==null&&(o=i),i=i.sibling;o===null?a||e.tail===null?e.tail=null:e.tail.sibling=null:o.sibling=null}}function fe(e){var a=e.alternate!==null&&e.alternate.child===e.child,i=0,o=0;if(a)for(var u=e.child;u!==null;)i|=u.lanes|u.childLanes,o|=u.subtreeFlags&65011712,o|=u.flags&65011712,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)i|=u.lanes|u.childLanes,o|=u.subtreeFlags,o|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=o,e.childLanes=i,a}function px(e,a,i){var o=a.pendingProps;switch(hm(a),a.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return fe(a),null;case 1:return fe(a),null;case 3:return i=a.stateNode,o=null,e!==null&&(o=e.memoizedState.cache),a.memoizedState.cache!==o&&(a.flags|=2048),Ua(Ce),zt(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(e===null||e.child===null)&&(ul(a)?Ia(a):e===null||e.memoizedState.isDehydrated&&(a.flags&256)===0||(a.flags|=1024,c_())),fe(a),null;case 26:return i=a.memoizedState,e===null?(Ia(a),i!==null?(fe(a),Db(a,i)):(fe(a),a.flags&=-16777217)):i?i!==e.memoizedState?(Ia(a),fe(a),Db(a,i)):(fe(a),a.flags&=-16777217):(e.memoizedProps!==o&&Ia(a),fe(a),a.flags&=-16777217),null;case 27:me(a),i=ct.current;var u=a.type;if(e!==null&&a.stateNode!=null)e.memoizedProps!==o&&Ia(a);else{if(!o){if(a.stateNode===null)throw Error(s(166));return fe(a),null}e=it.current,ul(a)?o_(a):(e=DS(u,o,i),a.stateNode=e,Ia(a))}return fe(a),null;case 5:if(me(a),i=a.type,e!==null&&a.stateNode!=null)e.memoizedProps!==o&&Ia(a);else{if(!o){if(a.stateNode===null)throw Error(s(166));return fe(a),null}if(e=it.current,ul(a))o_(a);else{switch(u=cf(ct.current),e){case 1:e=u.createElementNS("http://www.w3.org/2000/svg",i);break;case 2:e=u.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;default:switch(i){case"svg":e=u.createElementNS("http://www.w3.org/2000/svg",i);break;case"math":e=u.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;case"script":e=u.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof o.is=="string"?u.createElement("select",{is:o.is}):u.createElement("select"),o.multiple?e.multiple=!0:o.size&&(e.size=o.size);break;default:e=typeof o.is=="string"?u.createElement(i,{is:o.is}):u.createElement(i)}}e[Qe]=a,e[dn]=o;t:for(u=a.child;u!==null;){if(u.tag===5||u.tag===6)e.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===a)break t;for(;u.sibling===null;){if(u.return===null||u.return===a)break t;u=u.return}u.sibling.return=u.return,u=u.sibling}a.stateNode=e;t:switch(Ve(e,i,o),i){case"button":case"input":case"select":case"textarea":e=!!o.autoFocus;break t;case"img":e=!0;break t;default:e=!1}e&&Ia(a)}}return fe(a),a.flags&=-16777217,null;case 6:if(e&&a.stateNode!=null)e.memoizedProps!==o&&Ia(a);else{if(typeof o!="string"&&a.stateNode===null)throw Error(s(166));if(e=ct.current,ul(a)){if(e=a.stateNode,i=a.memoizedProps,o=null,u=tn,u!==null)switch(u.tag){case 27:case 5:o=u.memoizedProps}e[Qe]=a,e=!!(e.nodeValue===i||o!==null&&o.suppressHydrationWarning===!0||RS(e.nodeValue,i)),e||ss(a)}else e=cf(e).createTextNode(o),e[Qe]=a,a.stateNode=e}return fe(a),null;case 13:if(o=a.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(u=ul(a),o!==null&&o.dehydrated!==null){if(e===null){if(!u)throw Error(s(318));if(u=a.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(s(317));u[Qe]=a}else fl(),(a.flags&128)===0&&(a.memoizedState=null),a.flags|=4;fe(a),u=!1}else u=c_(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=u),u=!0;if(!u)return a.flags&256?(qa(a),a):(qa(a),null)}if(qa(a),(a.flags&128)!==0)return a.lanes=i,a;if(i=o!==null,e=e!==null&&e.memoizedState!==null,i){o=a.child,u=null,o.alternate!==null&&o.alternate.memoizedState!==null&&o.alternate.memoizedState.cachePool!==null&&(u=o.alternate.memoizedState.cachePool.pool);var d=null;o.memoizedState!==null&&o.memoizedState.cachePool!==null&&(d=o.memoizedState.cachePool.pool),d!==u&&(o.flags|=2048)}return i!==e&&i&&(a.child.flags|=8192),Yu(a,a.updateQueue),fe(a),null;case 4:return zt(),e===null&&Oy(a.stateNode.containerInfo),fe(a),null;case 10:return Ua(a.type),fe(a),null;case 19:if(st(De),u=a.memoizedState,u===null)return fe(a),null;if(o=(a.flags&128)!==0,d=u.rendering,d===null)if(o)wl(u,!1);else{if(pe!==0||e!==null&&(e.flags&128)!==0)for(e=a.child;e!==null;){if(d=Pu(e),d!==null){for(a.flags|=128,wl(u,!1),e=d.updateQueue,a.updateQueue=e,Yu(a,e),a.subtreeFlags=0,e=i,i=a.child;i!==null;)s_(i,e),i=i.sibling;return at(De,De.current&1|2),a.child}e=e.sibling}u.tail!==null&&$e()>Ju&&(a.flags|=128,o=!0,wl(u,!1),a.lanes=4194304)}else{if(!o)if(e=Pu(d),e!==null){if(a.flags|=128,o=!0,e=e.updateQueue,a.updateQueue=e,Yu(a,e),wl(u,!0),u.tail===null&&u.tailMode==="hidden"&&!d.alternate&&!Pt)return fe(a),null}else 2*$e()-u.renderingStartTime>Ju&&i!==536870912&&(a.flags|=128,o=!0,wl(u,!1),a.lanes=4194304);u.isBackwards?(d.sibling=a.child,a.child=d):(e=u.last,e!==null?e.sibling=d:a.child=d,u.last=d)}return u.tail!==null?(a=u.tail,u.rendering=a,u.tail=a.sibling,u.renderingStartTime=$e(),a.sibling=null,e=De.current,at(De,o?e&1|2:e&1),a):(fe(a),null);case 22:case 23:return qa(a),wm(),o=a.memoizedState!==null,e!==null?e.memoizedState!==null!==o&&(a.flags|=8192):o&&(a.flags|=8192),o?(i&536870912)!==0&&(a.flags&128)===0&&(fe(a),a.subtreeFlags&6&&(a.flags|=8192)):fe(a),i=a.updateQueue,i!==null&&Yu(a,i.retryQueue),i=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(i=e.memoizedState.cachePool.pool),o=null,a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(o=a.memoizedState.cachePool.pool),o!==i&&(a.flags|=2048),e!==null&&st(ls),null;case 24:return i=null,e!==null&&(i=e.memoizedState.cache),a.memoizedState.cache!==i&&(a.flags|=2048),Ua(Ce),fe(a),null;case 25:return null;case 30:return null}throw Error(s(156,a.tag))}function gx(e,a){switch(hm(a),a.tag){case 1:return e=a.flags,e&65536?(a.flags=e&-65537|128,a):null;case 3:return Ua(Ce),zt(),e=a.flags,(e&65536)!==0&&(e&128)===0?(a.flags=e&-65537|128,a):null;case 26:case 27:case 5:return me(a),null;case 13:if(qa(a),e=a.memoizedState,e!==null&&e.dehydrated!==null){if(a.alternate===null)throw Error(s(340));fl()}return e=a.flags,e&65536?(a.flags=e&-65537|128,a):null;case 19:return st(De),null;case 4:return zt(),null;case 10:return Ua(a.type),null;case 22:case 23:return qa(a),wm(),e!==null&&st(ls),e=a.flags,e&65536?(a.flags=e&-65537|128,a):null;case 24:return Ua(Ce),null;case 25:return null;default:return null}}function kb(e,a){switch(hm(a),a.tag){case 3:Ua(Ce),zt();break;case 26:case 27:case 5:me(a);break;case 4:zt();break;case 13:qa(a);break;case 19:st(De);break;case 10:Ua(a.type);break;case 22:case 23:qa(a),wm(),e!==null&&st(ls);break;case 24:Ua(Ce)}}function Al(e,a){try{var i=a.updateQueue,o=i!==null?i.lastEffect:null;if(o!==null){var u=o.next;i=u;do{if((i.tag&e)===e){o=void 0;var d=i.create,v=i.inst;o=d(),v.destroy=o}i=i.next}while(i!==u)}}catch(b){ne(a,a.return,b)}}function vr(e,a,i){try{var o=a.updateQueue,u=o!==null?o.lastEffect:null;if(u!==null){var d=u.next;o=d;do{if((o.tag&e)===e){var v=o.inst,b=v.destroy;if(b!==void 0){v.destroy=void 0,u=a;var R=i,z=b;try{z()}catch(P){ne(u,R,P)}}}o=o.next}while(o!==d)}}catch(P){ne(a,a.return,P)}}function Nb(e){var a=e.updateQueue;if(a!==null){var i=e.stateNode;try{b_(a,i)}catch(o){ne(e,e.return,o)}}}function zb(e,a,i){i.props=us(e.type,e.memoizedProps),i.state=e.memoizedState;try{i.componentWillUnmount()}catch(o){ne(e,a,o)}}function xl(e,a){try{var i=e.ref;if(i!==null){switch(e.tag){case 26:case 27:case 5:var o=e.stateNode;break;case 30:o=e.stateNode;break;default:o=e.stateNode}typeof i=="function"?e.refCleanup=i(o):i.current=o}}catch(u){ne(e,a,u)}}function ya(e,a){var i=e.ref,o=e.refCleanup;if(i!==null)if(typeof o=="function")try{o()}catch(u){ne(e,a,u)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof i=="function")try{i(null)}catch(u){ne(e,a,u)}else i.current=null}function Lb(e){var a=e.type,i=e.memoizedProps,o=e.stateNode;try{t:switch(a){case"button":case"input":case"select":case"textarea":i.autoFocus&&o.focus();break t;case"img":i.src?o.src=i.src:i.srcSet&&(o.srcset=i.srcSet)}}catch(u){ne(e,e.return,u)}}function ey(e,a,i){try{var o=e.stateNode;jx(o,e.type,i,a),o[dn]=a}catch(u){ne(e,e.return,u)}}function $b(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Mr(e.type)||e.tag===4}function ny(e){t:for(;;){for(;e.sibling===null;){if(e.return===null||$b(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Mr(e.type)||e.flags&2||e.child===null||e.tag===4)continue t;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ay(e,a,i){var o=e.tag;if(o===5||o===6)e=e.stateNode,a?(i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i).insertBefore(e,a):(a=i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i,a.appendChild(e),i=i._reactRootContainer,i!=null||a.onclick!==null||(a.onclick=lf));else if(o!==4&&(o===27&&Mr(e.type)&&(i=e.stateNode,a=null),e=e.child,e!==null))for(ay(e,a,i),e=e.sibling;e!==null;)ay(e,a,i),e=e.sibling}function Xu(e,a,i){var o=e.tag;if(o===5||o===6)e=e.stateNode,a?i.insertBefore(e,a):i.appendChild(e);else if(o!==4&&(o===27&&Mr(e.type)&&(i=e.stateNode),e=e.child,e!==null))for(Xu(e,a,i),e=e.sibling;e!==null;)Xu(e,a,i),e=e.sibling}function Fb(e){var a=e.stateNode,i=e.memoizedProps;try{for(var o=e.type,u=a.attributes;u.length;)a.removeAttributeNode(u[0]);Ve(a,o,i),a[Qe]=e,a[dn]=i}catch(d){ne(e,e.return,d)}}var Pa=!1,Se=!1,ry=!1,jb=typeof WeakSet=="function"?WeakSet:Set,Ue=null;function vx(e,a){if(e=e.containerInfo,Ay=yf,e=Yv(e),nm(e)){if("selectionStart"in e)var i={start:e.selectionStart,end:e.selectionEnd};else t:{i=(i=e.ownerDocument)&&i.defaultView||window;var o=i.getSelection&&i.getSelection();if(o&&o.rangeCount!==0){i=o.anchorNode;var u=o.anchorOffset,d=o.focusNode;o=o.focusOffset;try{i.nodeType,d.nodeType}catch{i=null;break t}var v=0,b=-1,R=-1,z=0,P=0,Y=e,U=null;e:for(;;){for(var B;Y!==i||u!==0&&Y.nodeType!==3||(b=v+u),Y!==d||o!==0&&Y.nodeType!==3||(R=v+o),Y.nodeType===3&&(v+=Y.nodeValue.length),(B=Y.firstChild)!==null;)U=Y,Y=B;for(;;){if(Y===e)break e;if(U===i&&++z===u&&(b=v),U===d&&++P===o&&(R=v),(B=Y.nextSibling)!==null)break;Y=U,U=Y.parentNode}Y=B}i=b===-1||R===-1?null:{start:b,end:R}}else i=null}i=i||{start:0,end:0}}else i=null;for(xy={focusedElem:e,selectionRange:i},yf=!1,Ue=a;Ue!==null;)if(a=Ue,e=a.child,(a.subtreeFlags&1024)!==0&&e!==null)e.return=a,Ue=e;else for(;Ue!==null;){switch(a=Ue,d=a.alternate,e=a.flags,a.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&d!==null){e=void 0,i=a,u=d.memoizedProps,d=d.memoizedState,o=i.stateNode;try{var Et=us(i.type,u,i.elementType===i.type);e=o.getSnapshotBeforeUpdate(Et,d),o.__reactInternalSnapshotBeforeUpdate=e}catch(_t){ne(i,i.return,_t)}}break;case 3:if((e&1024)!==0){if(e=a.stateNode.containerInfo,i=e.nodeType,i===9)ky(e);else if(i===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":ky(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(s(163))}if(e=a.sibling,e!==null){e.return=a.return,Ue=e;break}Ue=a.return}}function Ub(e,a,i){var o=i.flags;switch(i.tag){case 0:case 11:case 15:_r(e,i),o&4&&Al(5,i);break;case 1:if(_r(e,i),o&4)if(e=i.stateNode,a===null)try{e.componentDidMount()}catch(v){ne(i,i.return,v)}else{var u=us(i.type,a.memoizedProps);a=a.memoizedState;try{e.componentDidUpdate(u,a,e.__reactInternalSnapshotBeforeUpdate)}catch(v){ne(i,i.return,v)}}o&64&&Nb(i),o&512&&xl(i,i.return);break;case 3:if(_r(e,i),o&64&&(e=i.updateQueue,e!==null)){if(a=null,i.child!==null)switch(i.child.tag){case 27:case 5:a=i.child.stateNode;break;case 1:a=i.child.stateNode}try{b_(e,a)}catch(v){ne(i,i.return,v)}}break;case 27:a===null&&o&4&&Fb(i);case 26:case 5:_r(e,i),a===null&&o&4&&Lb(i),o&512&&xl(i,i.return);break;case 12:_r(e,i);break;case 13:_r(e,i),o&4&&Hb(e,i),o&64&&(e=i.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(i=wx.bind(null,i),Vx(e,i))));break;case 22:if(o=i.memoizedState!==null||Pa,!o){a=a!==null&&a.memoizedState!==null||Se,u=Pa;var d=Se;Pa=o,(Se=a)&&!d?br(e,i,(i.subtreeFlags&8772)!==0):_r(e,i),Pa=u,Se=d}break;case 30:break;default:_r(e,i)}}function Bb(e){var a=e.alternate;a!==null&&(e.alternate=null,Bb(a)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(a=e.stateNode,a!==null&&Fh(a)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var ce=null,yn=!1;function Va(e,a,i){for(i=i.child;i!==null;)qb(e,a,i),i=i.sibling}function qb(e,a,i){if(bn&&typeof bn.onCommitFiberUnmount=="function")try{bn.onCommitFiberUnmount(Yr,i)}catch{}switch(i.tag){case 26:Se||ya(i,a),Va(e,a,i),i.memoizedState?i.memoizedState.count--:i.stateNode&&(i=i.stateNode,i.parentNode.removeChild(i));break;case 27:Se||ya(i,a);var o=ce,u=yn;Mr(i.type)&&(ce=i.stateNode,yn=!1),Va(e,a,i),jl(i.stateNode),ce=o,yn=u;break;case 5:Se||ya(i,a);case 6:if(o=ce,u=yn,ce=null,Va(e,a,i),ce=o,yn=u,ce!==null)if(yn)try{(ce.nodeType===9?ce.body:ce.nodeName==="HTML"?ce.ownerDocument.body:ce).removeChild(i.stateNode)}catch(d){ne(i,a,d)}else try{ce.removeChild(i.stateNode)}catch(d){ne(i,a,d)}break;case 18:ce!==null&&(yn?(e=ce,xS(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,i.stateNode),Gl(e)):xS(ce,i.stateNode));break;case 4:o=ce,u=yn,ce=i.stateNode.containerInfo,yn=!0,Va(e,a,i),ce=o,yn=u;break;case 0:case 11:case 14:case 15:Se||vr(2,i,a),Se||vr(4,i,a),Va(e,a,i);break;case 1:Se||(ya(i,a),o=i.stateNode,typeof o.componentWillUnmount=="function"&&zb(i,a,o)),Va(e,a,i);break;case 21:Va(e,a,i);break;case 22:Se=(o=Se)||i.memoizedState!==null,Va(e,a,i),Se=o;break;default:Va(e,a,i)}}function Hb(e,a){if(a.memoizedState===null&&(e=a.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Gl(e)}catch(i){ne(a,a.return,i)}}function _x(e){switch(e.tag){case 13:case 19:var a=e.stateNode;return a===null&&(a=e.stateNode=new jb),a;case 22:return e=e.stateNode,a=e._retryCache,a===null&&(a=e._retryCache=new jb),a;default:throw Error(s(435,e.tag))}}function sy(e,a){var i=_x(e);a.forEach(function(o){var u=Ax.bind(null,e,o);i.has(o)||(i.add(o),o.then(u,u))})}function Rn(e,a){var i=a.deletions;if(i!==null)for(var o=0;o<i.length;o++){var u=i[o],d=e,v=a,b=v;t:for(;b!==null;){switch(b.tag){case 27:if(Mr(b.type)){ce=b.stateNode,yn=!1;break t}break;case 5:ce=b.stateNode,yn=!1;break t;case 3:case 4:ce=b.stateNode.containerInfo,yn=!0;break t}b=b.return}if(ce===null)throw Error(s(160));qb(d,v,u),ce=null,yn=!1,d=u.alternate,d!==null&&(d.return=null),u.return=null}if(a.subtreeFlags&13878)for(a=a.child;a!==null;)Ib(a,e),a=a.sibling}var Wn=null;function Ib(e,a){var i=e.alternate,o=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Rn(a,e),On(e),o&4&&(vr(3,e,e.return),Al(3,e),vr(5,e,e.return));break;case 1:Rn(a,e),On(e),o&512&&(Se||i===null||ya(i,i.return)),o&64&&Pa&&(e=e.updateQueue,e!==null&&(o=e.callbacks,o!==null&&(i=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=i===null?o:i.concat(o))));break;case 26:var u=Wn;if(Rn(a,e),On(e),o&512&&(Se||i===null||ya(i,i.return)),o&4){var d=i!==null?i.memoizedState:null;if(o=e.memoizedState,i===null)if(o===null)if(e.stateNode===null){t:{o=e.type,i=e.memoizedProps,u=u.ownerDocument||u;e:switch(o){case"title":d=u.getElementsByTagName("title")[0],(!d||d[Wo]||d[Qe]||d.namespaceURI==="http://www.w3.org/2000/svg"||d.hasAttribute("itemprop"))&&(d=u.createElement(o),u.head.insertBefore(d,u.querySelector("head > title"))),Ve(d,o,i),d[Qe]=e,Fe(d),o=d;break t;case"link":var v=FS("link","href",u).get(o+(i.href||""));if(v){for(var b=0;b<v.length;b++)if(d=v[b],d.getAttribute("href")===(i.href==null||i.href===""?null:i.href)&&d.getAttribute("rel")===(i.rel==null?null:i.rel)&&d.getAttribute("title")===(i.title==null?null:i.title)&&d.getAttribute("crossorigin")===(i.crossOrigin==null?null:i.crossOrigin)){v.splice(b,1);break e}}d=u.createElement(o),Ve(d,o,i),u.head.appendChild(d);break;case"meta":if(v=FS("meta","content",u).get(o+(i.content||""))){for(b=0;b<v.length;b++)if(d=v[b],d.getAttribute("content")===(i.content==null?null:""+i.content)&&d.getAttribute("name")===(i.name==null?null:i.name)&&d.getAttribute("property")===(i.property==null?null:i.property)&&d.getAttribute("http-equiv")===(i.httpEquiv==null?null:i.httpEquiv)&&d.getAttribute("charset")===(i.charSet==null?null:i.charSet)){v.splice(b,1);break e}}d=u.createElement(o),Ve(d,o,i),u.head.appendChild(d);break;default:throw Error(s(468,o))}d[Qe]=e,Fe(d),o=d}e.stateNode=o}else jS(u,e.type,e.stateNode);else e.stateNode=$S(u,o,e.memoizedProps);else d!==o?(d===null?i.stateNode!==null&&(i=i.stateNode,i.parentNode.removeChild(i)):d.count--,o===null?jS(u,e.type,e.stateNode):$S(u,o,e.memoizedProps)):o===null&&e.stateNode!==null&&ey(e,e.memoizedProps,i.memoizedProps)}break;case 27:Rn(a,e),On(e),o&512&&(Se||i===null||ya(i,i.return)),i!==null&&o&4&&ey(e,e.memoizedProps,i.memoizedProps);break;case 5:if(Rn(a,e),On(e),o&512&&(Se||i===null||ya(i,i.return)),e.flags&32){u=e.stateNode;try{li(u,"")}catch(B){ne(e,e.return,B)}}o&4&&e.stateNode!=null&&(u=e.memoizedProps,ey(e,u,i!==null?i.memoizedProps:u)),o&1024&&(ry=!0);break;case 6:if(Rn(a,e),On(e),o&4){if(e.stateNode===null)throw Error(s(162));o=e.memoizedProps,i=e.stateNode;try{i.nodeValue=o}catch(B){ne(e,e.return,B)}}break;case 3:if(df=null,u=Wn,Wn=uf(a.containerInfo),Rn(a,e),Wn=u,On(e),o&4&&i!==null&&i.memoizedState.isDehydrated)try{Gl(a.containerInfo)}catch(B){ne(e,e.return,B)}ry&&(ry=!1,Pb(e));break;case 4:o=Wn,Wn=uf(e.stateNode.containerInfo),Rn(a,e),On(e),Wn=o;break;case 12:Rn(a,e),On(e);break;case 13:Rn(a,e),On(e),e.child.flags&8192&&e.memoizedState!==null!=(i!==null&&i.memoizedState!==null)&&(fy=$e()),o&4&&(o=e.updateQueue,o!==null&&(e.updateQueue=null,sy(e,o)));break;case 22:u=e.memoizedState!==null;var R=i!==null&&i.memoizedState!==null,z=Pa,P=Se;if(Pa=z||u,Se=P||R,Rn(a,e),Se=P,Pa=z,On(e),o&8192)t:for(a=e.stateNode,a._visibility=u?a._visibility&-2:a._visibility|1,u&&(i===null||R||Pa||Se||fs(e)),i=null,a=e;;){if(a.tag===5||a.tag===26){if(i===null){R=i=a;try{if(d=R.stateNode,u)v=d.style,typeof v.setProperty=="function"?v.setProperty("display","none","important"):v.display="none";else{b=R.stateNode;var Y=R.memoizedProps.style,U=Y!=null&&Y.hasOwnProperty("display")?Y.display:null;b.style.display=U==null||typeof U=="boolean"?"":(""+U).trim()}}catch(B){ne(R,R.return,B)}}}else if(a.tag===6){if(i===null){R=a;try{R.stateNode.nodeValue=u?"":R.memoizedProps}catch(B){ne(R,R.return,B)}}}else if((a.tag!==22&&a.tag!==23||a.memoizedState===null||a===e)&&a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)break t;for(;a.sibling===null;){if(a.return===null||a.return===e)break t;i===a&&(i=null),a=a.return}i===a&&(i=null),a.sibling.return=a.return,a=a.sibling}o&4&&(o=e.updateQueue,o!==null&&(i=o.retryQueue,i!==null&&(o.retryQueue=null,sy(e,i))));break;case 19:Rn(a,e),On(e),o&4&&(o=e.updateQueue,o!==null&&(e.updateQueue=null,sy(e,o)));break;case 30:break;case 21:break;default:Rn(a,e),On(e)}}function On(e){var a=e.flags;if(a&2){try{for(var i,o=e.return;o!==null;){if($b(o)){i=o;break}o=o.return}if(i==null)throw Error(s(160));switch(i.tag){case 27:var u=i.stateNode,d=ny(e);Xu(e,d,u);break;case 5:var v=i.stateNode;i.flags&32&&(li(v,""),i.flags&=-33);var b=ny(e);Xu(e,b,v);break;case 3:case 4:var R=i.stateNode.containerInfo,z=ny(e);ay(e,z,R);break;default:throw Error(s(161))}}catch(P){ne(e,e.return,P)}e.flags&=-3}a&4096&&(e.flags&=-4097)}function Pb(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var a=e;Pb(a),a.tag===5&&a.flags&1024&&a.stateNode.reset(),e=e.sibling}}function _r(e,a){if(a.subtreeFlags&8772)for(a=a.child;a!==null;)Ub(e,a.alternate,a),a=a.sibling}function fs(e){for(e=e.child;e!==null;){var a=e;switch(a.tag){case 0:case 11:case 14:case 15:vr(4,a,a.return),fs(a);break;case 1:ya(a,a.return);var i=a.stateNode;typeof i.componentWillUnmount=="function"&&zb(a,a.return,i),fs(a);break;case 27:jl(a.stateNode);case 26:case 5:ya(a,a.return),fs(a);break;case 22:a.memoizedState===null&&fs(a);break;case 30:fs(a);break;default:fs(a)}e=e.sibling}}function br(e,a,i){for(i=i&&(a.subtreeFlags&8772)!==0,a=a.child;a!==null;){var o=a.alternate,u=e,d=a,v=d.flags;switch(d.tag){case 0:case 11:case 15:br(u,d,i),Al(4,d);break;case 1:if(br(u,d,i),o=d,u=o.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(z){ne(o,o.return,z)}if(o=d,u=o.updateQueue,u!==null){var b=o.stateNode;try{var R=u.shared.hiddenCallbacks;if(R!==null)for(u.shared.hiddenCallbacks=null,u=0;u<R.length;u++)__(R[u],b)}catch(z){ne(o,o.return,z)}}i&&v&64&&Nb(d),xl(d,d.return);break;case 27:Fb(d);case 26:case 5:br(u,d,i),i&&o===null&&v&4&&Lb(d),xl(d,d.return);break;case 12:br(u,d,i);break;case 13:br(u,d,i),i&&v&4&&Hb(u,d);break;case 22:d.memoizedState===null&&br(u,d,i),xl(d,d.return);break;case 30:break;default:br(u,d,i)}a=a.sibling}}function iy(e,a){var i=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(i=e.memoizedState.cachePool.pool),e=null,a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(e=a.memoizedState.cachePool.pool),e!==i&&(e!=null&&e.refCount++,i!=null&&ml(i))}function oy(e,a){e=null,a.alternate!==null&&(e=a.alternate.memoizedState.cache),a=a.memoizedState.cache,a!==e&&(a.refCount++,e!=null&&ml(e))}function pa(e,a,i,o){if(a.subtreeFlags&10256)for(a=a.child;a!==null;)Vb(e,a,i,o),a=a.sibling}function Vb(e,a,i,o){var u=a.flags;switch(a.tag){case 0:case 11:case 15:pa(e,a,i,o),u&2048&&Al(9,a);break;case 1:pa(e,a,i,o);break;case 3:pa(e,a,i,o),u&2048&&(e=null,a.alternate!==null&&(e=a.alternate.memoizedState.cache),a=a.memoizedState.cache,a!==e&&(a.refCount++,e!=null&&ml(e)));break;case 12:if(u&2048){pa(e,a,i,o),e=a.stateNode;try{var d=a.memoizedProps,v=d.id,b=d.onPostCommit;typeof b=="function"&&b(v,a.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(R){ne(a,a.return,R)}}else pa(e,a,i,o);break;case 13:pa(e,a,i,o);break;case 23:break;case 22:d=a.stateNode,v=a.alternate,a.memoizedState!==null?d._visibility&2?pa(e,a,i,o):Cl(e,a):d._visibility&2?pa(e,a,i,o):(d._visibility|=2,wi(e,a,i,o,(a.subtreeFlags&10256)!==0)),u&2048&&iy(v,a);break;case 24:pa(e,a,i,o),u&2048&&oy(a.alternate,a);break;default:pa(e,a,i,o)}}function wi(e,a,i,o,u){for(u=u&&(a.subtreeFlags&10256)!==0,a=a.child;a!==null;){var d=e,v=a,b=i,R=o,z=v.flags;switch(v.tag){case 0:case 11:case 15:wi(d,v,b,R,u),Al(8,v);break;case 23:break;case 22:var P=v.stateNode;v.memoizedState!==null?P._visibility&2?wi(d,v,b,R,u):Cl(d,v):(P._visibility|=2,wi(d,v,b,R,u)),u&&z&2048&&iy(v.alternate,v);break;case 24:wi(d,v,b,R,u),u&&z&2048&&oy(v.alternate,v);break;default:wi(d,v,b,R,u)}a=a.sibling}}function Cl(e,a){if(a.subtreeFlags&10256)for(a=a.child;a!==null;){var i=e,o=a,u=o.flags;switch(o.tag){case 22:Cl(i,o),u&2048&&iy(o.alternate,o);break;case 24:Cl(i,o),u&2048&&oy(o.alternate,o);break;default:Cl(i,o)}a=a.sibling}}var Dl=8192;function Ai(e){if(e.subtreeFlags&Dl)for(e=e.child;e!==null;)Gb(e),e=e.sibling}function Gb(e){switch(e.tag){case 26:Ai(e),e.flags&Dl&&e.memoizedState!==null&&rC(Wn,e.memoizedState,e.memoizedProps);break;case 5:Ai(e);break;case 3:case 4:var a=Wn;Wn=uf(e.stateNode.containerInfo),Ai(e),Wn=a;break;case 22:e.memoizedState===null&&(a=e.alternate,a!==null&&a.memoizedState!==null?(a=Dl,Dl=16777216,Ai(e),Dl=a):Ai(e));break;default:Ai(e)}}function Kb(e){var a=e.alternate;if(a!==null&&(e=a.child,e!==null)){a.child=null;do a=e.sibling,e.sibling=null,e=a;while(e!==null)}}function kl(e){var a=e.deletions;if((e.flags&16)!==0){if(a!==null)for(var i=0;i<a.length;i++){var o=a[i];Ue=o,Yb(o,e)}Kb(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Qb(e),e=e.sibling}function Qb(e){switch(e.tag){case 0:case 11:case 15:kl(e),e.flags&2048&&vr(9,e,e.return);break;case 3:kl(e);break;case 12:kl(e);break;case 22:var a=e.stateNode;e.memoizedState!==null&&a._visibility&2&&(e.return===null||e.return.tag!==13)?(a._visibility&=-3,Zu(e)):kl(e);break;default:kl(e)}}function Zu(e){var a=e.deletions;if((e.flags&16)!==0){if(a!==null)for(var i=0;i<a.length;i++){var o=a[i];Ue=o,Yb(o,e)}Kb(e)}for(e=e.child;e!==null;){switch(a=e,a.tag){case 0:case 11:case 15:vr(8,a,a.return),Zu(a);break;case 22:i=a.stateNode,i._visibility&2&&(i._visibility&=-3,Zu(a));break;default:Zu(a)}e=e.sibling}}function Yb(e,a){for(;Ue!==null;){var i=Ue;switch(i.tag){case 0:case 11:case 15:vr(8,i,a);break;case 23:case 22:if(i.memoizedState!==null&&i.memoizedState.cachePool!==null){var o=i.memoizedState.cachePool.pool;o!=null&&o.refCount++}break;case 24:ml(i.memoizedState.cache)}if(o=i.child,o!==null)o.return=i,Ue=o;else t:for(i=e;Ue!==null;){o=Ue;var u=o.sibling,d=o.return;if(Bb(o),o===i){Ue=null;break t}if(u!==null){u.return=d,Ue=u;break t}Ue=d}}}var bx={getCacheForType:function(e){var a=Ye(Ce),i=a.data.get(e);return i===void 0&&(i=e(),a.data.set(e,i)),i}},Sx=typeof WeakMap=="function"?WeakMap:Map,Kt=0,se=null,$t=null,Bt=0,Qt=0,Mn=null,Sr=!1,xi=!1,ly=!1,Ga=0,pe=0,Er=0,ds=0,cy=0,Bn=0,Ci=0,Nl=null,pn=null,uy=!1,fy=0,Ju=1/0,Wu=null,Tr=null,Pe=0,Rr=null,Di=null,ki=0,dy=0,hy=null,Xb=null,zl=0,my=null;function wn(){if((Kt&2)!==0&&Bt!==0)return Bt&-Bt;if(k.T!==null){var e=_i;return e!==0?e:Sy()}return dv()}function Zb(){Bn===0&&(Bn=(Bt&536870912)===0||Pt?lv():536870912);var e=Un.current;return e!==null&&(e.flags|=32),Bn}function An(e,a,i){(e===se&&(Qt===2||Qt===9)||e.cancelPendingCommit!==null)&&(Ni(e,0),Or(e,Bt,Bn,!1)),Jo(e,i),((Kt&2)===0||e!==se)&&(e===se&&((Kt&2)===0&&(ds|=i),pe===4&&Or(e,Bt,Bn,!1)),ga(e))}function Jb(e,a,i){if((Kt&6)!==0)throw Error(s(327));var o=!i&&(a&124)===0&&(a&e.expiredLanes)===0||Zo(e,a),u=o?Rx(e,a):gy(e,a,!0),d=o;do{if(u===0){xi&&!o&&Or(e,a,0,!1);break}else{if(i=e.current.alternate,d&&!Ex(i)){u=gy(e,a,!1),d=!1;continue}if(u===2){if(d=a,e.errorRecoveryDisabledLanes&d)var v=0;else v=e.pendingLanes&-536870913,v=v!==0?v:v&536870912?536870912:0;if(v!==0){a=v;t:{var b=e;u=Nl;var R=b.current.memoizedState.isDehydrated;if(R&&(Ni(b,v).flags|=256),v=gy(b,v,!1),v!==2){if(ly&&!R){b.errorRecoveryDisabledLanes|=d,ds|=d,u=4;break t}d=pn,pn=u,d!==null&&(pn===null?pn=d:pn.push.apply(pn,d))}u=v}if(d=!1,u!==2)continue}}if(u===1){Ni(e,0),Or(e,a,0,!0);break}t:{switch(o=e,d=u,d){case 0:case 1:throw Error(s(345));case 4:if((a&4194048)!==a)break;case 6:Or(o,a,Bn,!Sr);break t;case 2:pn=null;break;case 3:case 5:break;default:throw Error(s(329))}if((a&62914560)===a&&(u=fy+300-$e(),10<u)){if(Or(o,a,Bn,!Sr),uu(o,0,!0)!==0)break t;o.timeoutHandle=wS(Wb.bind(null,o,i,pn,Wu,uy,a,Bn,ds,Ci,Sr,d,2,-0,0),u);break t}Wb(o,i,pn,Wu,uy,a,Bn,ds,Ci,Sr,d,0,-0,0)}}break}while(!0);ga(e)}function Wb(e,a,i,o,u,d,v,b,R,z,P,Y,U,B){if(e.timeoutHandle=-1,Y=a.subtreeFlags,(Y&8192||(Y&16785408)===16785408)&&(ql={stylesheets:null,count:0,unsuspend:aC},Gb(a),Y=sC(),Y!==null)){e.cancelPendingCommit=Y(iS.bind(null,e,a,d,i,o,u,v,b,R,P,1,U,B)),Or(e,d,v,!z);return}iS(e,a,d,i,o,u,v,b,R)}function Ex(e){for(var a=e;;){var i=a.tag;if((i===0||i===11||i===15)&&a.flags&16384&&(i=a.updateQueue,i!==null&&(i=i.stores,i!==null)))for(var o=0;o<i.length;o++){var u=i[o],d=u.getSnapshot;u=u.value;try{if(!En(d(),u))return!1}catch{return!1}}if(i=a.child,a.subtreeFlags&16384&&i!==null)i.return=a,a=i;else{if(a===e)break;for(;a.sibling===null;){if(a.return===null||a.return===e)return!0;a=a.return}a.sibling.return=a.return,a=a.sibling}}return!0}function Or(e,a,i,o){a&=~cy,a&=~ds,e.suspendedLanes|=a,e.pingedLanes&=~a,o&&(e.warmLanes|=a),o=e.expirationTimes;for(var u=a;0<u;){var d=31-Sn(u),v=1<<d;o[d]=-1,u&=~v}i!==0&&uv(e,i,a)}function tf(){return(Kt&6)===0?(Ll(0),!1):!0}function yy(){if($t!==null){if(Qt===0)var e=$t.return;else e=$t,ja=is=null,km(e),Oi=null,Ol=0,e=$t;for(;e!==null;)kb(e.alternate,e),e=e.return;$t=null}}function Ni(e,a){var i=e.timeoutHandle;i!==-1&&(e.timeoutHandle=-1,Bx(i)),i=e.cancelPendingCommit,i!==null&&(e.cancelPendingCommit=null,i()),yy(),se=e,$t=i=La(e.current,null),Bt=a,Qt=0,Mn=null,Sr=!1,xi=Zo(e,a),ly=!1,Ci=Bn=cy=ds=Er=pe=0,pn=Nl=null,uy=!1,(a&8)!==0&&(a|=a&32);var o=e.entangledLanes;if(o!==0)for(e=e.entanglements,o&=a;0<o;){var u=31-Sn(o),d=1<<u;a|=e[u],o&=~d}return Ga=a,Eu(),i}function tS(e,a){kt=null,k.H=qu,a===pl||a===Du?(a=g_(),Qt=3):a===m_?(a=g_(),Qt=4):Qt=a===vb?8:a!==null&&typeof a=="object"&&typeof a.then=="function"?6:1,Mn=a,$t===null&&(pe=1,Gu(e,Ln(a,e.current)))}function eS(){var e=k.H;return k.H=qu,e===null?qu:e}function nS(){var e=k.A;return k.A=bx,e}function py(){pe=4,Sr||(Bt&4194048)!==Bt&&Un.current!==null||(xi=!0),(Er&134217727)===0&&(ds&134217727)===0||se===null||Or(se,Bt,Bn,!1)}function gy(e,a,i){var o=Kt;Kt|=2;var u=eS(),d=nS();(se!==e||Bt!==a)&&(Wu=null,Ni(e,a)),a=!1;var v=pe;t:do try{if(Qt!==0&&$t!==null){var b=$t,R=Mn;switch(Qt){case 8:yy(),v=6;break t;case 3:case 2:case 9:case 6:Un.current===null&&(a=!0);var z=Qt;if(Qt=0,Mn=null,zi(e,b,R,z),i&&xi){v=0;break t}break;default:z=Qt,Qt=0,Mn=null,zi(e,b,R,z)}}Tx(),v=pe;break}catch(P){tS(e,P)}while(!0);return a&&e.shellSuspendCounter++,ja=is=null,Kt=o,k.H=u,k.A=d,$t===null&&(se=null,Bt=0,Eu()),v}function Tx(){for(;$t!==null;)aS($t)}function Rx(e,a){var i=Kt;Kt|=2;var o=eS(),u=nS();se!==e||Bt!==a?(Wu=null,Ju=$e()+500,Ni(e,a)):xi=Zo(e,a);t:do try{if(Qt!==0&&$t!==null){a=$t;var d=Mn;e:switch(Qt){case 1:Qt=0,Mn=null,zi(e,a,d,1);break;case 2:case 9:if(y_(d)){Qt=0,Mn=null,rS(a);break}a=function(){Qt!==2&&Qt!==9||se!==e||(Qt=7),ga(e)},d.then(a,a);break t;case 3:Qt=7;break t;case 4:Qt=5;break t;case 7:y_(d)?(Qt=0,Mn=null,rS(a)):(Qt=0,Mn=null,zi(e,a,d,7));break;case 5:var v=null;switch($t.tag){case 26:v=$t.memoizedState;case 5:case 27:var b=$t;if(!v||US(v)){Qt=0,Mn=null;var R=b.sibling;if(R!==null)$t=R;else{var z=b.return;z!==null?($t=z,ef(z)):$t=null}break e}}Qt=0,Mn=null,zi(e,a,d,5);break;case 6:Qt=0,Mn=null,zi(e,a,d,6);break;case 8:yy(),pe=6;break t;default:throw Error(s(462))}}Ox();break}catch(P){tS(e,P)}while(!0);return ja=is=null,k.H=o,k.A=u,Kt=i,$t!==null?0:(se=null,Bt=0,Eu(),pe)}function Ox(){for(;$t!==null&&!or();)aS($t)}function aS(e){var a=Cb(e.alternate,e,Ga);e.memoizedProps=e.pendingProps,a===null?ef(e):$t=a}function rS(e){var a=e,i=a.alternate;switch(a.tag){case 15:case 0:a=Rb(i,a,a.pendingProps,a.type,void 0,Bt);break;case 11:a=Rb(i,a,a.pendingProps,a.type.render,a.ref,Bt);break;case 5:km(a);default:kb(i,a),a=$t=s_(a,Ga),a=Cb(i,a,Ga)}e.memoizedProps=e.pendingProps,a===null?ef(e):$t=a}function zi(e,a,i,o){ja=is=null,km(a),Oi=null,Ol=0;var u=a.return;try{if(mx(e,u,a,i,Bt)){pe=1,Gu(e,Ln(i,e.current)),$t=null;return}}catch(d){if(u!==null)throw $t=u,d;pe=1,Gu(e,Ln(i,e.current)),$t=null;return}a.flags&32768?(Pt||o===1?e=!0:xi||(Bt&536870912)!==0?e=!1:(Sr=e=!0,(o===2||o===9||o===3||o===6)&&(o=Un.current,o!==null&&o.tag===13&&(o.flags|=16384))),sS(a,e)):ef(a)}function ef(e){var a=e;do{if((a.flags&32768)!==0){sS(a,Sr);return}e=a.return;var i=px(a.alternate,a,Ga);if(i!==null){$t=i;return}if(a=a.sibling,a!==null){$t=a;return}$t=a=e}while(a!==null);pe===0&&(pe=5)}function sS(e,a){do{var i=gx(e.alternate,e);if(i!==null){i.flags&=32767,$t=i;return}if(i=e.return,i!==null&&(i.flags|=32768,i.subtreeFlags=0,i.deletions=null),!a&&(e=e.sibling,e!==null)){$t=e;return}$t=e=i}while(e!==null);pe=6,$t=null}function iS(e,a,i,o,u,d,v,b,R){e.cancelPendingCommit=null;do nf();while(Pe!==0);if((Kt&6)!==0)throw Error(s(327));if(a!==null){if(a===e.current)throw Error(s(177));if(d=a.lanes|a.childLanes,d|=om,aA(e,i,d,v,b,R),e===se&&($t=se=null,Bt=0),Di=a,Rr=e,ki=i,dy=d,hy=u,Xb=o,(a.subtreeFlags&10256)!==0||(a.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,xx(Vt,function(){return fS(),null})):(e.callbackNode=null,e.callbackPriority=0),o=(a.flags&13878)!==0,(a.subtreeFlags&13878)!==0||o){o=k.T,k.T=null,u=X.p,X.p=2,v=Kt,Kt|=4;try{vx(e,a,i)}finally{Kt=v,X.p=u,k.T=o}}Pe=1,oS(),lS(),cS()}}function oS(){if(Pe===1){Pe=0;var e=Rr,a=Di,i=(a.flags&13878)!==0;if((a.subtreeFlags&13878)!==0||i){i=k.T,k.T=null;var o=X.p;X.p=2;var u=Kt;Kt|=4;try{Ib(a,e);var d=xy,v=Yv(e.containerInfo),b=d.focusedElem,R=d.selectionRange;if(v!==b&&b&&b.ownerDocument&&Qv(b.ownerDocument.documentElement,b)){if(R!==null&&nm(b)){var z=R.start,P=R.end;if(P===void 0&&(P=z),"selectionStart"in b)b.selectionStart=z,b.selectionEnd=Math.min(P,b.value.length);else{var Y=b.ownerDocument||document,U=Y&&Y.defaultView||window;if(U.getSelection){var B=U.getSelection(),Et=b.textContent.length,_t=Math.min(R.start,Et),Wt=R.end===void 0?_t:Math.min(R.end,Et);!B.extend&&_t>Wt&&(v=Wt,Wt=_t,_t=v);var C=Kv(b,_t),w=Kv(b,Wt);if(C&&w&&(B.rangeCount!==1||B.anchorNode!==C.node||B.anchorOffset!==C.offset||B.focusNode!==w.node||B.focusOffset!==w.offset)){var N=Y.createRange();N.setStart(C.node,C.offset),B.removeAllRanges(),_t>Wt?(B.addRange(N),B.extend(w.node,w.offset)):(N.setEnd(w.node,w.offset),B.addRange(N))}}}}for(Y=[],B=b;B=B.parentNode;)B.nodeType===1&&Y.push({element:B,left:B.scrollLeft,top:B.scrollTop});for(typeof b.focus=="function"&&b.focus(),b=0;b<Y.length;b++){var V=Y[b];V.element.scrollLeft=V.left,V.element.scrollTop=V.top}}yf=!!Ay,xy=Ay=null}finally{Kt=u,X.p=o,k.T=i}}e.current=a,Pe=2}}function lS(){if(Pe===2){Pe=0;var e=Rr,a=Di,i=(a.flags&8772)!==0;if((a.subtreeFlags&8772)!==0||i){i=k.T,k.T=null;var o=X.p;X.p=2;var u=Kt;Kt|=4;try{Ub(e,a.alternate,a)}finally{Kt=u,X.p=o,k.T=i}}Pe=3}}function cS(){if(Pe===4||Pe===3){Pe=0,kn();var e=Rr,a=Di,i=ki,o=Xb;(a.subtreeFlags&10256)!==0||(a.flags&10256)!==0?Pe=5:(Pe=0,Di=Rr=null,uS(e,e.pendingLanes));var u=e.pendingLanes;if(u===0&&(Tr=null),Lh(i),a=a.stateNode,bn&&typeof bn.onCommitFiberRoot=="function")try{bn.onCommitFiberRoot(Yr,a,void 0,(a.current.flags&128)===128)}catch{}if(o!==null){a=k.T,u=X.p,X.p=2,k.T=null;try{for(var d=e.onRecoverableError,v=0;v<o.length;v++){var b=o[v];d(b.value,{componentStack:b.stack})}}finally{k.T=a,X.p=u}}(ki&3)!==0&&nf(),ga(e),u=e.pendingLanes,(i&4194090)!==0&&(u&42)!==0?e===my?zl++:(zl=0,my=e):zl=0,Ll(0)}}function uS(e,a){(e.pooledCacheLanes&=a)===0&&(a=e.pooledCache,a!=null&&(e.pooledCache=null,ml(a)))}function nf(e){return oS(),lS(),cS(),fS()}function fS(){if(Pe!==5)return!1;var e=Rr,a=dy;dy=0;var i=Lh(ki),o=k.T,u=X.p;try{X.p=32>i?32:i,k.T=null,i=hy,hy=null;var d=Rr,v=ki;if(Pe=0,Di=Rr=null,ki=0,(Kt&6)!==0)throw Error(s(331));var b=Kt;if(Kt|=4,Qb(d.current),Vb(d,d.current,v,i),Kt=b,Ll(0,!1),bn&&typeof bn.onPostCommitFiberRoot=="function")try{bn.onPostCommitFiberRoot(Yr,d)}catch{}return!0}finally{X.p=u,k.T=o,uS(e,a)}}function dS(e,a,i){a=Ln(i,a),a=Gm(e.stateNode,a,2),e=mr(e,a,2),e!==null&&(Jo(e,2),ga(e))}function ne(e,a,i){if(e.tag===3)dS(e,e,i);else for(;a!==null;){if(a.tag===3){dS(a,e,i);break}else if(a.tag===1){var o=a.stateNode;if(typeof a.type.getDerivedStateFromError=="function"||typeof o.componentDidCatch=="function"&&(Tr===null||!Tr.has(o))){e=Ln(i,e),i=pb(2),o=mr(a,i,2),o!==null&&(gb(i,o,a,e),Jo(o,2),ga(o));break}}a=a.return}}function vy(e,a,i){var o=e.pingCache;if(o===null){o=e.pingCache=new Sx;var u=new Set;o.set(a,u)}else u=o.get(a),u===void 0&&(u=new Set,o.set(a,u));u.has(i)||(ly=!0,u.add(i),e=Mx.bind(null,e,a,i),a.then(e,e))}function Mx(e,a,i){var o=e.pingCache;o!==null&&o.delete(a),e.pingedLanes|=e.suspendedLanes&i,e.warmLanes&=~i,se===e&&(Bt&i)===i&&(pe===4||pe===3&&(Bt&62914560)===Bt&&300>$e()-fy?(Kt&2)===0&&Ni(e,0):cy|=i,Ci===Bt&&(Ci=0)),ga(e)}function hS(e,a){a===0&&(a=cv()),e=yi(e,a),e!==null&&(Jo(e,a),ga(e))}function wx(e){var a=e.memoizedState,i=0;a!==null&&(i=a.retryLane),hS(e,i)}function Ax(e,a){var i=0;switch(e.tag){case 13:var o=e.stateNode,u=e.memoizedState;u!==null&&(i=u.retryLane);break;case 19:o=e.stateNode;break;case 22:o=e.stateNode._retryCache;break;default:throw Error(s(314))}o!==null&&o.delete(a),hS(e,i)}function xx(e,a){return fn(e,a)}var af=null,Li=null,_y=!1,rf=!1,by=!1,hs=0;function ga(e){e!==Li&&e.next===null&&(Li===null?af=Li=e:Li=Li.next=e),rf=!0,_y||(_y=!0,Dx())}function Ll(e,a){if(!by&&rf){by=!0;do for(var i=!1,o=af;o!==null;){if(e!==0){var u=o.pendingLanes;if(u===0)var d=0;else{var v=o.suspendedLanes,b=o.pingedLanes;d=(1<<31-Sn(42|e)+1)-1,d&=u&~(v&~b),d=d&201326741?d&201326741|1:d?d|2:0}d!==0&&(i=!0,gS(o,d))}else d=Bt,d=uu(o,o===se?d:0,o.cancelPendingCommit!==null||o.timeoutHandle!==-1),(d&3)===0||Zo(o,d)||(i=!0,gS(o,d));o=o.next}while(i);by=!1}}function Cx(){mS()}function mS(){rf=_y=!1;var e=0;hs!==0&&(Ux()&&(e=hs),hs=0);for(var a=$e(),i=null,o=af;o!==null;){var u=o.next,d=yS(o,a);d===0?(o.next=null,i===null?af=u:i.next=u,u===null&&(Li=i)):(i=o,(e!==0||(d&3)!==0)&&(rf=!0)),o=u}Ll(e)}function yS(e,a){for(var i=e.suspendedLanes,o=e.pingedLanes,u=e.expirationTimes,d=e.pendingLanes&-62914561;0<d;){var v=31-Sn(d),b=1<<v,R=u[v];R===-1?((b&i)===0||(b&o)!==0)&&(u[v]=nA(b,a)):R<=a&&(e.expiredLanes|=b),d&=~b}if(a=se,i=Bt,i=uu(e,e===a?i:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),o=e.callbackNode,i===0||e===a&&(Qt===2||Qt===9)||e.cancelPendingCommit!==null)return o!==null&&o!==null&&Zn(o),e.callbackNode=null,e.callbackPriority=0;if((i&3)===0||Zo(e,i)){if(a=i&-i,a===e.callbackPriority)return a;switch(o!==null&&Zn(o),Lh(i)){case 2:case 8:i=Xo;break;case 32:i=Vt;break;case 268435456:i=ti;break;default:i=Vt}return o=pS.bind(null,e),i=fn(i,o),e.callbackPriority=a,e.callbackNode=i,a}return o!==null&&o!==null&&Zn(o),e.callbackPriority=2,e.callbackNode=null,2}function pS(e,a){if(Pe!==0&&Pe!==5)return e.callbackNode=null,e.callbackPriority=0,null;var i=e.callbackNode;if(nf()&&e.callbackNode!==i)return null;var o=Bt;return o=uu(e,e===se?o:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),o===0?null:(Jb(e,o,a),yS(e,$e()),e.callbackNode!=null&&e.callbackNode===i?pS.bind(null,e):null)}function gS(e,a){if(nf())return null;Jb(e,a,!0)}function Dx(){qx(function(){(Kt&6)!==0?fn(Yo,Cx):mS()})}function Sy(){return hs===0&&(hs=lv()),hs}function vS(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:yu(""+e)}function _S(e,a){var i=a.ownerDocument.createElement("input");return i.name=a.name,i.value=a.value,e.id&&i.setAttribute("form",e.id),a.parentNode.insertBefore(i,a),e=new FormData(e),i.parentNode.removeChild(i),e}function kx(e,a,i,o,u){if(a==="submit"&&i&&i.stateNode===u){var d=vS((u[dn]||null).action),v=o.submitter;v&&(a=(a=v[dn]||null)?vS(a.formAction):v.getAttribute("formAction"),a!==null&&(d=a,v=null));var b=new _u("action","action",null,o,u);e.push({event:b,listeners:[{instance:null,listener:function(){if(o.defaultPrevented){if(hs!==0){var R=v?_S(u,v):new FormData(u);qm(i,{pending:!0,data:R,method:u.method,action:d},null,R)}}else typeof d=="function"&&(b.preventDefault(),R=v?_S(u,v):new FormData(u),qm(i,{pending:!0,data:R,method:u.method,action:d},d,R))},currentTarget:u}]})}}for(var Ey=0;Ey<im.length;Ey++){var Ty=im[Ey],Nx=Ty.toLowerCase(),zx=Ty[0].toUpperCase()+Ty.slice(1);Jn(Nx,"on"+zx)}Jn(Jv,"onAnimationEnd"),Jn(Wv,"onAnimationIteration"),Jn(t_,"onAnimationStart"),Jn("dblclick","onDoubleClick"),Jn("focusin","onFocus"),Jn("focusout","onBlur"),Jn(ZA,"onTransitionRun"),Jn(JA,"onTransitionStart"),Jn(WA,"onTransitionCancel"),Jn(e_,"onTransitionEnd"),si("onMouseEnter",["mouseout","mouseover"]),si("onMouseLeave",["mouseout","mouseover"]),si("onPointerEnter",["pointerout","pointerover"]),si("onPointerLeave",["pointerout","pointerover"]),Zr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Zr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Zr("onBeforeInput",["compositionend","keypress","textInput","paste"]),Zr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Zr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Zr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var $l="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Lx=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat($l));function bS(e,a){a=(a&4)!==0;for(var i=0;i<e.length;i++){var o=e[i],u=o.event;o=o.listeners;t:{var d=void 0;if(a)for(var v=o.length-1;0<=v;v--){var b=o[v],R=b.instance,z=b.currentTarget;if(b=b.listener,R!==d&&u.isPropagationStopped())break t;d=b,u.currentTarget=z;try{d(u)}catch(P){Vu(P)}u.currentTarget=null,d=R}else for(v=0;v<o.length;v++){if(b=o[v],R=b.instance,z=b.currentTarget,b=b.listener,R!==d&&u.isPropagationStopped())break t;d=b,u.currentTarget=z;try{d(u)}catch(P){Vu(P)}u.currentTarget=null,d=R}}}}function Ft(e,a){var i=a[$h];i===void 0&&(i=a[$h]=new Set);var o=e+"__bubble";i.has(o)||(SS(a,e,2,!1),i.add(o))}function Ry(e,a,i){var o=0;a&&(o|=4),SS(i,e,o,a)}var sf="_reactListening"+Math.random().toString(36).slice(2);function Oy(e){if(!e[sf]){e[sf]=!0,mv.forEach(function(i){i!=="selectionchange"&&(Lx.has(i)||Ry(i,!1,e),Ry(i,!0,e))});var a=e.nodeType===9?e:e.ownerDocument;a===null||a[sf]||(a[sf]=!0,Ry("selectionchange",!1,a))}}function SS(e,a,i,o){switch(VS(a)){case 2:var u=lC;break;case 8:u=cC;break;default:u=Uy}i=u.bind(null,a,i,e),u=void 0,!Kh||a!=="touchstart"&&a!=="touchmove"&&a!=="wheel"||(u=!0),o?u!==void 0?e.addEventListener(a,i,{capture:!0,passive:u}):e.addEventListener(a,i,!0):u!==void 0?e.addEventListener(a,i,{passive:u}):e.addEventListener(a,i,!1)}function My(e,a,i,o,u){var d=o;if((a&1)===0&&(a&2)===0&&o!==null)t:for(;;){if(o===null)return;var v=o.tag;if(v===3||v===4){var b=o.stateNode.containerInfo;if(b===u)break;if(v===4)for(v=o.return;v!==null;){var R=v.tag;if((R===3||R===4)&&v.stateNode.containerInfo===u)return;v=v.return}for(;b!==null;){if(v=ni(b),v===null)return;if(R=v.tag,R===5||R===6||R===26||R===27){o=d=v;continue t}b=b.parentNode}}o=o.return}Av(function(){var z=d,P=Vh(i),Y=[];t:{var U=n_.get(e);if(U!==void 0){var B=_u,Et=e;switch(e){case"keypress":if(gu(i)===0)break t;case"keydown":case"keyup":B=xA;break;case"focusin":Et="focus",B=Zh;break;case"focusout":Et="blur",B=Zh;break;case"beforeblur":case"afterblur":B=Zh;break;case"click":if(i.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":B=Dv;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":B=gA;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":B=kA;break;case Jv:case Wv:case t_:B=bA;break;case e_:B=zA;break;case"scroll":case"scrollend":B=yA;break;case"wheel":B=$A;break;case"copy":case"cut":case"paste":B=EA;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":B=Nv;break;case"toggle":case"beforetoggle":B=jA}var _t=(a&4)!==0,Wt=!_t&&(e==="scroll"||e==="scrollend"),C=_t?U!==null?U+"Capture":null:U;_t=[];for(var w=z,N;w!==null;){var V=w;if(N=V.stateNode,V=V.tag,V!==5&&V!==26&&V!==27||N===null||C===null||(V=el(w,C),V!=null&&_t.push(Fl(w,V,N))),Wt)break;w=w.return}0<_t.length&&(U=new B(U,Et,null,i,P),Y.push({event:U,listeners:_t}))}}if((a&7)===0){t:{if(U=e==="mouseover"||e==="pointerover",B=e==="mouseout"||e==="pointerout",U&&i!==Ph&&(Et=i.relatedTarget||i.fromElement)&&(ni(Et)||Et[ei]))break t;if((B||U)&&(U=P.window===P?P:(U=P.ownerDocument)?U.defaultView||U.parentWindow:window,B?(Et=i.relatedTarget||i.toElement,B=z,Et=Et?ni(Et):null,Et!==null&&(Wt=c(Et),_t=Et.tag,Et!==Wt||_t!==5&&_t!==27&&_t!==6)&&(Et=null)):(B=null,Et=z),B!==Et)){if(_t=Dv,V="onMouseLeave",C="onMouseEnter",w="mouse",(e==="pointerout"||e==="pointerover")&&(_t=Nv,V="onPointerLeave",C="onPointerEnter",w="pointer"),Wt=B==null?U:tl(B),N=Et==null?U:tl(Et),U=new _t(V,w+"leave",B,i,P),U.target=Wt,U.relatedTarget=N,V=null,ni(P)===z&&(_t=new _t(C,w+"enter",Et,i,P),_t.target=N,_t.relatedTarget=Wt,V=_t),Wt=V,B&&Et)e:{for(_t=B,C=Et,w=0,N=_t;N;N=$i(N))w++;for(N=0,V=C;V;V=$i(V))N++;for(;0<w-N;)_t=$i(_t),w--;for(;0<N-w;)C=$i(C),N--;for(;w--;){if(_t===C||C!==null&&_t===C.alternate)break e;_t=$i(_t),C=$i(C)}_t=null}else _t=null;B!==null&&ES(Y,U,B,_t,!1),Et!==null&&Wt!==null&&ES(Y,Wt,Et,_t,!0)}}t:{if(U=z?tl(z):window,B=U.nodeName&&U.nodeName.toLowerCase(),B==="select"||B==="input"&&U.type==="file")var ut=qv;else if(Uv(U))if(Hv)ut=QA;else{ut=GA;var Lt=VA}else B=U.nodeName,!B||B.toLowerCase()!=="input"||U.type!=="checkbox"&&U.type!=="radio"?z&&Ih(z.elementType)&&(ut=qv):ut=KA;if(ut&&(ut=ut(e,z))){Bv(Y,ut,i,P);break t}Lt&&Lt(e,U,z),e==="focusout"&&z&&U.type==="number"&&z.memoizedProps.value!=null&&Hh(U,"number",U.value)}switch(Lt=z?tl(z):window,e){case"focusin":(Uv(Lt)||Lt.contentEditable==="true")&&(di=Lt,am=z,cl=null);break;case"focusout":cl=am=di=null;break;case"mousedown":rm=!0;break;case"contextmenu":case"mouseup":case"dragend":rm=!1,Xv(Y,i,P);break;case"selectionchange":if(XA)break;case"keydown":case"keyup":Xv(Y,i,P)}var ht;if(Wh)t:{switch(e){case"compositionstart":var bt="onCompositionStart";break t;case"compositionend":bt="onCompositionEnd";break t;case"compositionupdate":bt="onCompositionUpdate";break t}bt=void 0}else fi?Fv(e,i)&&(bt="onCompositionEnd"):e==="keydown"&&i.keyCode===229&&(bt="onCompositionStart");bt&&(zv&&i.locale!=="ko"&&(fi||bt!=="onCompositionStart"?bt==="onCompositionEnd"&&fi&&(ht=xv()):(ur=P,Qh="value"in ur?ur.value:ur.textContent,fi=!0)),Lt=of(z,bt),0<Lt.length&&(bt=new kv(bt,e,null,i,P),Y.push({event:bt,listeners:Lt}),ht?bt.data=ht:(ht=jv(i),ht!==null&&(bt.data=ht)))),(ht=BA?qA(e,i):HA(e,i))&&(bt=of(z,"onBeforeInput"),0<bt.length&&(Lt=new kv("onBeforeInput","beforeinput",null,i,P),Y.push({event:Lt,listeners:bt}),Lt.data=ht)),kx(Y,e,z,i,P)}bS(Y,a)})}function Fl(e,a,i){return{instance:e,listener:a,currentTarget:i}}function of(e,a){for(var i=a+"Capture",o=[];e!==null;){var u=e,d=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||d===null||(u=el(e,i),u!=null&&o.unshift(Fl(e,u,d)),u=el(e,a),u!=null&&o.push(Fl(e,u,d))),e.tag===3)return o;e=e.return}return[]}function $i(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function ES(e,a,i,o,u){for(var d=a._reactName,v=[];i!==null&&i!==o;){var b=i,R=b.alternate,z=b.stateNode;if(b=b.tag,R!==null&&R===o)break;b!==5&&b!==26&&b!==27||z===null||(R=z,u?(z=el(i,d),z!=null&&v.unshift(Fl(i,z,R))):u||(z=el(i,d),z!=null&&v.push(Fl(i,z,R)))),i=i.return}v.length!==0&&e.push({event:a,listeners:v})}var $x=/\r\n?/g,Fx=/\u0000|\uFFFD/g;function TS(e){return(typeof e=="string"?e:""+e).replace($x,`
`).replace(Fx,"")}function RS(e,a){return a=TS(a),TS(e)===a}function lf(){}function Jt(e,a,i,o,u,d){switch(i){case"children":typeof o=="string"?a==="body"||a==="textarea"&&o===""||li(e,o):(typeof o=="number"||typeof o=="bigint")&&a!=="body"&&li(e,""+o);break;case"className":du(e,"class",o);break;case"tabIndex":du(e,"tabindex",o);break;case"dir":case"role":case"viewBox":case"width":case"height":du(e,i,o);break;case"style":Mv(e,o,d);break;case"data":if(a!=="object"){du(e,"data",o);break}case"src":case"href":if(o===""&&(a!=="a"||i!=="href")){e.removeAttribute(i);break}if(o==null||typeof o=="function"||typeof o=="symbol"||typeof o=="boolean"){e.removeAttribute(i);break}o=yu(""+o),e.setAttribute(i,o);break;case"action":case"formAction":if(typeof o=="function"){e.setAttribute(i,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof d=="function"&&(i==="formAction"?(a!=="input"&&Jt(e,a,"name",u.name,u,null),Jt(e,a,"formEncType",u.formEncType,u,null),Jt(e,a,"formMethod",u.formMethod,u,null),Jt(e,a,"formTarget",u.formTarget,u,null)):(Jt(e,a,"encType",u.encType,u,null),Jt(e,a,"method",u.method,u,null),Jt(e,a,"target",u.target,u,null)));if(o==null||typeof o=="symbol"||typeof o=="boolean"){e.removeAttribute(i);break}o=yu(""+o),e.setAttribute(i,o);break;case"onClick":o!=null&&(e.onclick=lf);break;case"onScroll":o!=null&&Ft("scroll",e);break;case"onScrollEnd":o!=null&&Ft("scrollend",e);break;case"dangerouslySetInnerHTML":if(o!=null){if(typeof o!="object"||!("__html"in o))throw Error(s(61));if(i=o.__html,i!=null){if(u.children!=null)throw Error(s(60));e.innerHTML=i}}break;case"multiple":e.multiple=o&&typeof o!="function"&&typeof o!="symbol";break;case"muted":e.muted=o&&typeof o!="function"&&typeof o!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(o==null||typeof o=="function"||typeof o=="boolean"||typeof o=="symbol"){e.removeAttribute("xlink:href");break}i=yu(""+o),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",i);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":o!=null&&typeof o!="function"&&typeof o!="symbol"?e.setAttribute(i,""+o):e.removeAttribute(i);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":o&&typeof o!="function"&&typeof o!="symbol"?e.setAttribute(i,""):e.removeAttribute(i);break;case"capture":case"download":o===!0?e.setAttribute(i,""):o!==!1&&o!=null&&typeof o!="function"&&typeof o!="symbol"?e.setAttribute(i,o):e.removeAttribute(i);break;case"cols":case"rows":case"size":case"span":o!=null&&typeof o!="function"&&typeof o!="symbol"&&!isNaN(o)&&1<=o?e.setAttribute(i,o):e.removeAttribute(i);break;case"rowSpan":case"start":o==null||typeof o=="function"||typeof o=="symbol"||isNaN(o)?e.removeAttribute(i):e.setAttribute(i,o);break;case"popover":Ft("beforetoggle",e),Ft("toggle",e),fu(e,"popover",o);break;case"xlinkActuate":Na(e,"http://www.w3.org/1999/xlink","xlink:actuate",o);break;case"xlinkArcrole":Na(e,"http://www.w3.org/1999/xlink","xlink:arcrole",o);break;case"xlinkRole":Na(e,"http://www.w3.org/1999/xlink","xlink:role",o);break;case"xlinkShow":Na(e,"http://www.w3.org/1999/xlink","xlink:show",o);break;case"xlinkTitle":Na(e,"http://www.w3.org/1999/xlink","xlink:title",o);break;case"xlinkType":Na(e,"http://www.w3.org/1999/xlink","xlink:type",o);break;case"xmlBase":Na(e,"http://www.w3.org/XML/1998/namespace","xml:base",o);break;case"xmlLang":Na(e,"http://www.w3.org/XML/1998/namespace","xml:lang",o);break;case"xmlSpace":Na(e,"http://www.w3.org/XML/1998/namespace","xml:space",o);break;case"is":fu(e,"is",o);break;case"innerText":case"textContent":break;default:(!(2<i.length)||i[0]!=="o"&&i[0]!=="O"||i[1]!=="n"&&i[1]!=="N")&&(i=hA.get(i)||i,fu(e,i,o))}}function wy(e,a,i,o,u,d){switch(i){case"style":Mv(e,o,d);break;case"dangerouslySetInnerHTML":if(o!=null){if(typeof o!="object"||!("__html"in o))throw Error(s(61));if(i=o.__html,i!=null){if(u.children!=null)throw Error(s(60));e.innerHTML=i}}break;case"children":typeof o=="string"?li(e,o):(typeof o=="number"||typeof o=="bigint")&&li(e,""+o);break;case"onScroll":o!=null&&Ft("scroll",e);break;case"onScrollEnd":o!=null&&Ft("scrollend",e);break;case"onClick":o!=null&&(e.onclick=lf);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!yv.hasOwnProperty(i))t:{if(i[0]==="o"&&i[1]==="n"&&(u=i.endsWith("Capture"),a=i.slice(2,u?i.length-7:void 0),d=e[dn]||null,d=d!=null?d[i]:null,typeof d=="function"&&e.removeEventListener(a,d,u),typeof o=="function")){typeof d!="function"&&d!==null&&(i in e?e[i]=null:e.hasAttribute(i)&&e.removeAttribute(i)),e.addEventListener(a,o,u);break t}i in e?e[i]=o:o===!0?e.setAttribute(i,""):fu(e,i,o)}}}function Ve(e,a,i){switch(a){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Ft("error",e),Ft("load",e);var o=!1,u=!1,d;for(d in i)if(i.hasOwnProperty(d)){var v=i[d];if(v!=null)switch(d){case"src":o=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,a));default:Jt(e,a,d,v,i,null)}}u&&Jt(e,a,"srcSet",i.srcSet,i,null),o&&Jt(e,a,"src",i.src,i,null);return;case"input":Ft("invalid",e);var b=d=v=u=null,R=null,z=null;for(o in i)if(i.hasOwnProperty(o)){var P=i[o];if(P!=null)switch(o){case"name":u=P;break;case"type":v=P;break;case"checked":R=P;break;case"defaultChecked":z=P;break;case"value":d=P;break;case"defaultValue":b=P;break;case"children":case"dangerouslySetInnerHTML":if(P!=null)throw Error(s(137,a));break;default:Jt(e,a,o,P,i,null)}}Ev(e,d,b,R,z,v,u,!1),hu(e);return;case"select":Ft("invalid",e),o=v=d=null;for(u in i)if(i.hasOwnProperty(u)&&(b=i[u],b!=null))switch(u){case"value":d=b;break;case"defaultValue":v=b;break;case"multiple":o=b;default:Jt(e,a,u,b,i,null)}a=d,i=v,e.multiple=!!o,a!=null?oi(e,!!o,a,!1):i!=null&&oi(e,!!o,i,!0);return;case"textarea":Ft("invalid",e),d=u=o=null;for(v in i)if(i.hasOwnProperty(v)&&(b=i[v],b!=null))switch(v){case"value":o=b;break;case"defaultValue":u=b;break;case"children":d=b;break;case"dangerouslySetInnerHTML":if(b!=null)throw Error(s(91));break;default:Jt(e,a,v,b,i,null)}Rv(e,o,u,d),hu(e);return;case"option":for(R in i)if(i.hasOwnProperty(R)&&(o=i[R],o!=null))switch(R){case"selected":e.selected=o&&typeof o!="function"&&typeof o!="symbol";break;default:Jt(e,a,R,o,i,null)}return;case"dialog":Ft("beforetoggle",e),Ft("toggle",e),Ft("cancel",e),Ft("close",e);break;case"iframe":case"object":Ft("load",e);break;case"video":case"audio":for(o=0;o<$l.length;o++)Ft($l[o],e);break;case"image":Ft("error",e),Ft("load",e);break;case"details":Ft("toggle",e);break;case"embed":case"source":case"link":Ft("error",e),Ft("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(z in i)if(i.hasOwnProperty(z)&&(o=i[z],o!=null))switch(z){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,a));default:Jt(e,a,z,o,i,null)}return;default:if(Ih(a)){for(P in i)i.hasOwnProperty(P)&&(o=i[P],o!==void 0&&wy(e,a,P,o,i,void 0));return}}for(b in i)i.hasOwnProperty(b)&&(o=i[b],o!=null&&Jt(e,a,b,o,i,null))}function jx(e,a,i,o){switch(a){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,d=null,v=null,b=null,R=null,z=null,P=null;for(B in i){var Y=i[B];if(i.hasOwnProperty(B)&&Y!=null)switch(B){case"checked":break;case"value":break;case"defaultValue":R=Y;default:o.hasOwnProperty(B)||Jt(e,a,B,null,o,Y)}}for(var U in o){var B=o[U];if(Y=i[U],o.hasOwnProperty(U)&&(B!=null||Y!=null))switch(U){case"type":d=B;break;case"name":u=B;break;case"checked":z=B;break;case"defaultChecked":P=B;break;case"value":v=B;break;case"defaultValue":b=B;break;case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(s(137,a));break;default:B!==Y&&Jt(e,a,U,B,o,Y)}}qh(e,v,b,R,z,P,d,u);return;case"select":B=v=b=U=null;for(d in i)if(R=i[d],i.hasOwnProperty(d)&&R!=null)switch(d){case"value":break;case"multiple":B=R;default:o.hasOwnProperty(d)||Jt(e,a,d,null,o,R)}for(u in o)if(d=o[u],R=i[u],o.hasOwnProperty(u)&&(d!=null||R!=null))switch(u){case"value":U=d;break;case"defaultValue":b=d;break;case"multiple":v=d;default:d!==R&&Jt(e,a,u,d,o,R)}a=b,i=v,o=B,U!=null?oi(e,!!i,U,!1):!!o!=!!i&&(a!=null?oi(e,!!i,a,!0):oi(e,!!i,i?[]:"",!1));return;case"textarea":B=U=null;for(b in i)if(u=i[b],i.hasOwnProperty(b)&&u!=null&&!o.hasOwnProperty(b))switch(b){case"value":break;case"children":break;default:Jt(e,a,b,null,o,u)}for(v in o)if(u=o[v],d=i[v],o.hasOwnProperty(v)&&(u!=null||d!=null))switch(v){case"value":U=u;break;case"defaultValue":B=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(s(91));break;default:u!==d&&Jt(e,a,v,u,o,d)}Tv(e,U,B);return;case"option":for(var Et in i)if(U=i[Et],i.hasOwnProperty(Et)&&U!=null&&!o.hasOwnProperty(Et))switch(Et){case"selected":e.selected=!1;break;default:Jt(e,a,Et,null,o,U)}for(R in o)if(U=o[R],B=i[R],o.hasOwnProperty(R)&&U!==B&&(U!=null||B!=null))switch(R){case"selected":e.selected=U&&typeof U!="function"&&typeof U!="symbol";break;default:Jt(e,a,R,U,o,B)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var _t in i)U=i[_t],i.hasOwnProperty(_t)&&U!=null&&!o.hasOwnProperty(_t)&&Jt(e,a,_t,null,o,U);for(z in o)if(U=o[z],B=i[z],o.hasOwnProperty(z)&&U!==B&&(U!=null||B!=null))switch(z){case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(s(137,a));break;default:Jt(e,a,z,U,o,B)}return;default:if(Ih(a)){for(var Wt in i)U=i[Wt],i.hasOwnProperty(Wt)&&U!==void 0&&!o.hasOwnProperty(Wt)&&wy(e,a,Wt,void 0,o,U);for(P in o)U=o[P],B=i[P],!o.hasOwnProperty(P)||U===B||U===void 0&&B===void 0||wy(e,a,P,U,o,B);return}}for(var C in i)U=i[C],i.hasOwnProperty(C)&&U!=null&&!o.hasOwnProperty(C)&&Jt(e,a,C,null,o,U);for(Y in o)U=o[Y],B=i[Y],!o.hasOwnProperty(Y)||U===B||U==null&&B==null||Jt(e,a,Y,U,o,B)}var Ay=null,xy=null;function cf(e){return e.nodeType===9?e:e.ownerDocument}function OS(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function MS(e,a){if(e===0)switch(a){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&a==="foreignObject"?0:e}function Cy(e,a){return e==="textarea"||e==="noscript"||typeof a.children=="string"||typeof a.children=="number"||typeof a.children=="bigint"||typeof a.dangerouslySetInnerHTML=="object"&&a.dangerouslySetInnerHTML!==null&&a.dangerouslySetInnerHTML.__html!=null}var Dy=null;function Ux(){var e=window.event;return e&&e.type==="popstate"?e===Dy?!1:(Dy=e,!0):(Dy=null,!1)}var wS=typeof setTimeout=="function"?setTimeout:void 0,Bx=typeof clearTimeout=="function"?clearTimeout:void 0,AS=typeof Promise=="function"?Promise:void 0,qx=typeof queueMicrotask=="function"?queueMicrotask:typeof AS<"u"?function(e){return AS.resolve(null).then(e).catch(Hx)}:wS;function Hx(e){setTimeout(function(){throw e})}function Mr(e){return e==="head"}function xS(e,a){var i=a,o=0,u=0;do{var d=i.nextSibling;if(e.removeChild(i),d&&d.nodeType===8)if(i=d.data,i==="/$"){if(0<o&&8>o){i=o;var v=e.ownerDocument;if(i&1&&jl(v.documentElement),i&2&&jl(v.body),i&4)for(i=v.head,jl(i),v=i.firstChild;v;){var b=v.nextSibling,R=v.nodeName;v[Wo]||R==="SCRIPT"||R==="STYLE"||R==="LINK"&&v.rel.toLowerCase()==="stylesheet"||i.removeChild(v),v=b}}if(u===0){e.removeChild(d),Gl(a);return}u--}else i==="$"||i==="$?"||i==="$!"?u++:o=i.charCodeAt(0)-48;else o=0;i=d}while(i);Gl(a)}function ky(e){var a=e.firstChild;for(a&&a.nodeType===10&&(a=a.nextSibling);a;){var i=a;switch(a=a.nextSibling,i.nodeName){case"HTML":case"HEAD":case"BODY":ky(i),Fh(i);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(i.rel.toLowerCase()==="stylesheet")continue}e.removeChild(i)}}function Ix(e,a,i,o){for(;e.nodeType===1;){var u=i;if(e.nodeName.toLowerCase()!==a.toLowerCase()){if(!o&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(o){if(!e[Wo])switch(a){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(d=e.getAttribute("rel"),d==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(d!==u.rel||e.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||e.getAttribute("title")!==(u.title==null?null:u.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(d=e.getAttribute("src"),(d!==(u.src==null?null:u.src)||e.getAttribute("type")!==(u.type==null?null:u.type)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&d&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(a==="input"&&e.type==="hidden"){var d=u.name==null?null:""+u.name;if(u.type==="hidden"&&e.getAttribute("name")===d)return e}else return e;if(e=ta(e.nextSibling),e===null)break}return null}function Px(e,a,i){if(a==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!i||(e=ta(e.nextSibling),e===null))return null;return e}function Ny(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Vx(e,a){var i=e.ownerDocument;if(e.data!=="$?"||i.readyState==="complete")a();else{var o=function(){a(),i.removeEventListener("DOMContentLoaded",o)};i.addEventListener("DOMContentLoaded",o),e._reactRetry=o}}function ta(e){for(;e!=null;e=e.nextSibling){var a=e.nodeType;if(a===1||a===3)break;if(a===8){if(a=e.data,a==="$"||a==="$!"||a==="$?"||a==="F!"||a==="F")break;if(a==="/$")return null}}return e}var zy=null;function CS(e){e=e.previousSibling;for(var a=0;e;){if(e.nodeType===8){var i=e.data;if(i==="$"||i==="$!"||i==="$?"){if(a===0)return e;a--}else i==="/$"&&a++}e=e.previousSibling}return null}function DS(e,a,i){switch(a=cf(i),e){case"html":if(e=a.documentElement,!e)throw Error(s(452));return e;case"head":if(e=a.head,!e)throw Error(s(453));return e;case"body":if(e=a.body,!e)throw Error(s(454));return e;default:throw Error(s(451))}}function jl(e){for(var a=e.attributes;a.length;)e.removeAttributeNode(a[0]);Fh(e)}var qn=new Map,kS=new Set;function uf(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Ka=X.d;X.d={f:Gx,r:Kx,D:Qx,C:Yx,L:Xx,m:Zx,X:Wx,S:Jx,M:tC};function Gx(){var e=Ka.f(),a=tf();return e||a}function Kx(e){var a=ai(e);a!==null&&a.tag===5&&a.type==="form"?J_(a):Ka.r(e)}var Fi=typeof document>"u"?null:document;function NS(e,a,i){var o=Fi;if(o&&typeof a=="string"&&a){var u=zn(a);u='link[rel="'+e+'"][href="'+u+'"]',typeof i=="string"&&(u+='[crossorigin="'+i+'"]'),kS.has(u)||(kS.add(u),e={rel:e,crossOrigin:i,href:a},o.querySelector(u)===null&&(a=o.createElement("link"),Ve(a,"link",e),Fe(a),o.head.appendChild(a)))}}function Qx(e){Ka.D(e),NS("dns-prefetch",e,null)}function Yx(e,a){Ka.C(e,a),NS("preconnect",e,a)}function Xx(e,a,i){Ka.L(e,a,i);var o=Fi;if(o&&e&&a){var u='link[rel="preload"][as="'+zn(a)+'"]';a==="image"&&i&&i.imageSrcSet?(u+='[imagesrcset="'+zn(i.imageSrcSet)+'"]',typeof i.imageSizes=="string"&&(u+='[imagesizes="'+zn(i.imageSizes)+'"]')):u+='[href="'+zn(e)+'"]';var d=u;switch(a){case"style":d=ji(e);break;case"script":d=Ui(e)}qn.has(d)||(e=p({rel:"preload",href:a==="image"&&i&&i.imageSrcSet?void 0:e,as:a},i),qn.set(d,e),o.querySelector(u)!==null||a==="style"&&o.querySelector(Ul(d))||a==="script"&&o.querySelector(Bl(d))||(a=o.createElement("link"),Ve(a,"link",e),Fe(a),o.head.appendChild(a)))}}function Zx(e,a){Ka.m(e,a);var i=Fi;if(i&&e){var o=a&&typeof a.as=="string"?a.as:"script",u='link[rel="modulepreload"][as="'+zn(o)+'"][href="'+zn(e)+'"]',d=u;switch(o){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":d=Ui(e)}if(!qn.has(d)&&(e=p({rel:"modulepreload",href:e},a),qn.set(d,e),i.querySelector(u)===null)){switch(o){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(i.querySelector(Bl(d)))return}o=i.createElement("link"),Ve(o,"link",e),Fe(o),i.head.appendChild(o)}}}function Jx(e,a,i){Ka.S(e,a,i);var o=Fi;if(o&&e){var u=ri(o).hoistableStyles,d=ji(e);a=a||"default";var v=u.get(d);if(!v){var b={loading:0,preload:null};if(v=o.querySelector(Ul(d)))b.loading=5;else{e=p({rel:"stylesheet",href:e,"data-precedence":a},i),(i=qn.get(d))&&Ly(e,i);var R=v=o.createElement("link");Fe(R),Ve(R,"link",e),R._p=new Promise(function(z,P){R.onload=z,R.onerror=P}),R.addEventListener("load",function(){b.loading|=1}),R.addEventListener("error",function(){b.loading|=2}),b.loading|=4,ff(v,a,o)}v={type:"stylesheet",instance:v,count:1,state:b},u.set(d,v)}}}function Wx(e,a){Ka.X(e,a);var i=Fi;if(i&&e){var o=ri(i).hoistableScripts,u=Ui(e),d=o.get(u);d||(d=i.querySelector(Bl(u)),d||(e=p({src:e,async:!0},a),(a=qn.get(u))&&$y(e,a),d=i.createElement("script"),Fe(d),Ve(d,"link",e),i.head.appendChild(d)),d={type:"script",instance:d,count:1,state:null},o.set(u,d))}}function tC(e,a){Ka.M(e,a);var i=Fi;if(i&&e){var o=ri(i).hoistableScripts,u=Ui(e),d=o.get(u);d||(d=i.querySelector(Bl(u)),d||(e=p({src:e,async:!0,type:"module"},a),(a=qn.get(u))&&$y(e,a),d=i.createElement("script"),Fe(d),Ve(d,"link",e),i.head.appendChild(d)),d={type:"script",instance:d,count:1,state:null},o.set(u,d))}}function zS(e,a,i,o){var u=(u=ct.current)?uf(u):null;if(!u)throw Error(s(446));switch(e){case"meta":case"title":return null;case"style":return typeof i.precedence=="string"&&typeof i.href=="string"?(a=ji(i.href),i=ri(u).hoistableStyles,o=i.get(a),o||(o={type:"style",instance:null,count:0,state:null},i.set(a,o)),o):{type:"void",instance:null,count:0,state:null};case"link":if(i.rel==="stylesheet"&&typeof i.href=="string"&&typeof i.precedence=="string"){e=ji(i.href);var d=ri(u).hoistableStyles,v=d.get(e);if(v||(u=u.ownerDocument||u,v={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},d.set(e,v),(d=u.querySelector(Ul(e)))&&!d._p&&(v.instance=d,v.state.loading=5),qn.has(e)||(i={rel:"preload",as:"style",href:i.href,crossOrigin:i.crossOrigin,integrity:i.integrity,media:i.media,hrefLang:i.hrefLang,referrerPolicy:i.referrerPolicy},qn.set(e,i),d||eC(u,e,i,v.state))),a&&o===null)throw Error(s(528,""));return v}if(a&&o!==null)throw Error(s(529,""));return null;case"script":return a=i.async,i=i.src,typeof i=="string"&&a&&typeof a!="function"&&typeof a!="symbol"?(a=Ui(i),i=ri(u).hoistableScripts,o=i.get(a),o||(o={type:"script",instance:null,count:0,state:null},i.set(a,o)),o):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,e))}}function ji(e){return'href="'+zn(e)+'"'}function Ul(e){return'link[rel="stylesheet"]['+e+"]"}function LS(e){return p({},e,{"data-precedence":e.precedence,precedence:null})}function eC(e,a,i,o){e.querySelector('link[rel="preload"][as="style"]['+a+"]")?o.loading=1:(a=e.createElement("link"),o.preload=a,a.addEventListener("load",function(){return o.loading|=1}),a.addEventListener("error",function(){return o.loading|=2}),Ve(a,"link",i),Fe(a),e.head.appendChild(a))}function Ui(e){return'[src="'+zn(e)+'"]'}function Bl(e){return"script[async]"+e}function $S(e,a,i){if(a.count++,a.instance===null)switch(a.type){case"style":var o=e.querySelector('style[data-href~="'+zn(i.href)+'"]');if(o)return a.instance=o,Fe(o),o;var u=p({},i,{"data-href":i.href,"data-precedence":i.precedence,href:null,precedence:null});return o=(e.ownerDocument||e).createElement("style"),Fe(o),Ve(o,"style",u),ff(o,i.precedence,e),a.instance=o;case"stylesheet":u=ji(i.href);var d=e.querySelector(Ul(u));if(d)return a.state.loading|=4,a.instance=d,Fe(d),d;o=LS(i),(u=qn.get(u))&&Ly(o,u),d=(e.ownerDocument||e).createElement("link"),Fe(d);var v=d;return v._p=new Promise(function(b,R){v.onload=b,v.onerror=R}),Ve(d,"link",o),a.state.loading|=4,ff(d,i.precedence,e),a.instance=d;case"script":return d=Ui(i.src),(u=e.querySelector(Bl(d)))?(a.instance=u,Fe(u),u):(o=i,(u=qn.get(d))&&(o=p({},i),$y(o,u)),e=e.ownerDocument||e,u=e.createElement("script"),Fe(u),Ve(u,"link",o),e.head.appendChild(u),a.instance=u);case"void":return null;default:throw Error(s(443,a.type))}else a.type==="stylesheet"&&(a.state.loading&4)===0&&(o=a.instance,a.state.loading|=4,ff(o,i.precedence,e));return a.instance}function ff(e,a,i){for(var o=i.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=o.length?o[o.length-1]:null,d=u,v=0;v<o.length;v++){var b=o[v];if(b.dataset.precedence===a)d=b;else if(d!==u)break}d?d.parentNode.insertBefore(e,d.nextSibling):(a=i.nodeType===9?i.head:i,a.insertBefore(e,a.firstChild))}function Ly(e,a){e.crossOrigin==null&&(e.crossOrigin=a.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=a.referrerPolicy),e.title==null&&(e.title=a.title)}function $y(e,a){e.crossOrigin==null&&(e.crossOrigin=a.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=a.referrerPolicy),e.integrity==null&&(e.integrity=a.integrity)}var df=null;function FS(e,a,i){if(df===null){var o=new Map,u=df=new Map;u.set(i,o)}else u=df,o=u.get(i),o||(o=new Map,u.set(i,o));if(o.has(e))return o;for(o.set(e,null),i=i.getElementsByTagName(e),u=0;u<i.length;u++){var d=i[u];if(!(d[Wo]||d[Qe]||e==="link"&&d.getAttribute("rel")==="stylesheet")&&d.namespaceURI!=="http://www.w3.org/2000/svg"){var v=d.getAttribute(a)||"";v=e+v;var b=o.get(v);b?b.push(d):o.set(v,[d])}}return o}function jS(e,a,i){e=e.ownerDocument||e,e.head.insertBefore(i,a==="title"?e.querySelector("head > title"):null)}function nC(e,a,i){if(i===1||a.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof a.precedence!="string"||typeof a.href!="string"||a.href==="")break;return!0;case"link":if(typeof a.rel!="string"||typeof a.href!="string"||a.href===""||a.onLoad||a.onError)break;switch(a.rel){case"stylesheet":return e=a.disabled,typeof a.precedence=="string"&&e==null;default:return!0}case"script":if(a.async&&typeof a.async!="function"&&typeof a.async!="symbol"&&!a.onLoad&&!a.onError&&a.src&&typeof a.src=="string")return!0}return!1}function US(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var ql=null;function aC(){}function rC(e,a,i){if(ql===null)throw Error(s(475));var o=ql;if(a.type==="stylesheet"&&(typeof i.media!="string"||matchMedia(i.media).matches!==!1)&&(a.state.loading&4)===0){if(a.instance===null){var u=ji(i.href),d=e.querySelector(Ul(u));if(d){e=d._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(o.count++,o=hf.bind(o),e.then(o,o)),a.state.loading|=4,a.instance=d,Fe(d);return}d=e.ownerDocument||e,i=LS(i),(u=qn.get(u))&&Ly(i,u),d=d.createElement("link"),Fe(d);var v=d;v._p=new Promise(function(b,R){v.onload=b,v.onerror=R}),Ve(d,"link",i),a.instance=d}o.stylesheets===null&&(o.stylesheets=new Map),o.stylesheets.set(a,e),(e=a.state.preload)&&(a.state.loading&3)===0&&(o.count++,a=hf.bind(o),e.addEventListener("load",a),e.addEventListener("error",a))}}function sC(){if(ql===null)throw Error(s(475));var e=ql;return e.stylesheets&&e.count===0&&Fy(e,e.stylesheets),0<e.count?function(a){var i=setTimeout(function(){if(e.stylesheets&&Fy(e,e.stylesheets),e.unsuspend){var o=e.unsuspend;e.unsuspend=null,o()}},6e4);return e.unsuspend=a,function(){e.unsuspend=null,clearTimeout(i)}}:null}function hf(){if(this.count--,this.count===0){if(this.stylesheets)Fy(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var mf=null;function Fy(e,a){e.stylesheets=null,e.unsuspend!==null&&(e.count++,mf=new Map,a.forEach(iC,e),mf=null,hf.call(e))}function iC(e,a){if(!(a.state.loading&4)){var i=mf.get(e);if(i)var o=i.get(null);else{i=new Map,mf.set(e,i);for(var u=e.querySelectorAll("link[data-precedence],style[data-precedence]"),d=0;d<u.length;d++){var v=u[d];(v.nodeName==="LINK"||v.getAttribute("media")!=="not all")&&(i.set(v.dataset.precedence,v),o=v)}o&&i.set(null,o)}u=a.instance,v=u.getAttribute("data-precedence"),d=i.get(v)||o,d===o&&i.set(null,u),i.set(v,u),this.count++,o=hf.bind(this),u.addEventListener("load",o),u.addEventListener("error",o),d?d.parentNode.insertBefore(u,d.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(u,e.firstChild)),a.state.loading|=4}}var Hl={$$typeof:q,Provider:null,Consumer:null,_currentValue:lt,_currentValue2:lt,_threadCount:0};function oC(e,a,i,o,u,d,v,b){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Nh(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Nh(0),this.hiddenUpdates=Nh(null),this.identifierPrefix=o,this.onUncaughtError=u,this.onCaughtError=d,this.onRecoverableError=v,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=b,this.incompleteTransitions=new Map}function BS(e,a,i,o,u,d,v,b,R,z,P,Y){return e=new oC(e,a,i,v,b,R,z,Y),a=1,d===!0&&(a|=24),d=Tn(3,null,null,a),e.current=d,d.stateNode=e,a=vm(),a.refCount++,e.pooledCache=a,a.refCount++,d.memoizedState={element:o,isDehydrated:i,cache:a},Em(d),e}function qS(e){return e?(e=pi,e):pi}function HS(e,a,i,o,u,d){u=qS(u),o.context===null?o.context=u:o.pendingContext=u,o=hr(a),o.payload={element:i},d=d===void 0?null:d,d!==null&&(o.callback=d),i=mr(e,o,a),i!==null&&(An(i,e,a),vl(i,e,a))}function IS(e,a){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var i=e.retryLane;e.retryLane=i!==0&&i<a?i:a}}function jy(e,a){IS(e,a),(e=e.alternate)&&IS(e,a)}function PS(e){if(e.tag===13){var a=yi(e,67108864);a!==null&&An(a,e,67108864),jy(e,67108864)}}var yf=!0;function lC(e,a,i,o){var u=k.T;k.T=null;var d=X.p;try{X.p=2,Uy(e,a,i,o)}finally{X.p=d,k.T=u}}function cC(e,a,i,o){var u=k.T;k.T=null;var d=X.p;try{X.p=8,Uy(e,a,i,o)}finally{X.p=d,k.T=u}}function Uy(e,a,i,o){if(yf){var u=By(o);if(u===null)My(e,a,o,pf,i),GS(e,o);else if(fC(u,e,a,i,o))o.stopPropagation();else if(GS(e,o),a&4&&-1<uC.indexOf(e)){for(;u!==null;){var d=ai(u);if(d!==null)switch(d.tag){case 3:if(d=d.stateNode,d.current.memoizedState.isDehydrated){var v=Xr(d.pendingLanes);if(v!==0){var b=d;for(b.pendingLanes|=2,b.entangledLanes|=2;v;){var R=1<<31-Sn(v);b.entanglements[1]|=R,v&=~R}ga(d),(Kt&6)===0&&(Ju=$e()+500,Ll(0))}}break;case 13:b=yi(d,2),b!==null&&An(b,d,2),tf(),jy(d,2)}if(d=By(o),d===null&&My(e,a,o,pf,i),d===u)break;u=d}u!==null&&o.stopPropagation()}else My(e,a,o,null,i)}}function By(e){return e=Vh(e),qy(e)}var pf=null;function qy(e){if(pf=null,e=ni(e),e!==null){var a=c(e);if(a===null)e=null;else{var i=a.tag;if(i===13){if(e=f(a),e!==null)return e;e=null}else if(i===3){if(a.stateNode.current.memoizedState.isDehydrated)return a.tag===3?a.stateNode.containerInfo:null;e=null}else a!==e&&(e=null)}}return pf=e,null}function VS(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ws()){case Yo:return 2;case Xo:return 8;case Vt:case Me:return 32;case ti:return 268435456;default:return 32}default:return 32}}var Hy=!1,wr=null,Ar=null,xr=null,Il=new Map,Pl=new Map,Cr=[],uC="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function GS(e,a){switch(e){case"focusin":case"focusout":wr=null;break;case"dragenter":case"dragleave":Ar=null;break;case"mouseover":case"mouseout":xr=null;break;case"pointerover":case"pointerout":Il.delete(a.pointerId);break;case"gotpointercapture":case"lostpointercapture":Pl.delete(a.pointerId)}}function Vl(e,a,i,o,u,d){return e===null||e.nativeEvent!==d?(e={blockedOn:a,domEventName:i,eventSystemFlags:o,nativeEvent:d,targetContainers:[u]},a!==null&&(a=ai(a),a!==null&&PS(a)),e):(e.eventSystemFlags|=o,a=e.targetContainers,u!==null&&a.indexOf(u)===-1&&a.push(u),e)}function fC(e,a,i,o,u){switch(a){case"focusin":return wr=Vl(wr,e,a,i,o,u),!0;case"dragenter":return Ar=Vl(Ar,e,a,i,o,u),!0;case"mouseover":return xr=Vl(xr,e,a,i,o,u),!0;case"pointerover":var d=u.pointerId;return Il.set(d,Vl(Il.get(d)||null,e,a,i,o,u)),!0;case"gotpointercapture":return d=u.pointerId,Pl.set(d,Vl(Pl.get(d)||null,e,a,i,o,u)),!0}return!1}function KS(e){var a=ni(e.target);if(a!==null){var i=c(a);if(i!==null){if(a=i.tag,a===13){if(a=f(i),a!==null){e.blockedOn=a,rA(e.priority,function(){if(i.tag===13){var o=wn();o=zh(o);var u=yi(i,o);u!==null&&An(u,i,o),jy(i,o)}});return}}else if(a===3&&i.stateNode.current.memoizedState.isDehydrated){e.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}e.blockedOn=null}function gf(e){if(e.blockedOn!==null)return!1;for(var a=e.targetContainers;0<a.length;){var i=By(e.nativeEvent);if(i===null){i=e.nativeEvent;var o=new i.constructor(i.type,i);Ph=o,i.target.dispatchEvent(o),Ph=null}else return a=ai(i),a!==null&&PS(a),e.blockedOn=i,!1;a.shift()}return!0}function QS(e,a,i){gf(e)&&i.delete(a)}function dC(){Hy=!1,wr!==null&&gf(wr)&&(wr=null),Ar!==null&&gf(Ar)&&(Ar=null),xr!==null&&gf(xr)&&(xr=null),Il.forEach(QS),Pl.forEach(QS)}function vf(e,a){e.blockedOn===a&&(e.blockedOn=null,Hy||(Hy=!0,t.unstable_scheduleCallback(t.unstable_NormalPriority,dC)))}var _f=null;function YS(e){_f!==e&&(_f=e,t.unstable_scheduleCallback(t.unstable_NormalPriority,function(){_f===e&&(_f=null);for(var a=0;a<e.length;a+=3){var i=e[a],o=e[a+1],u=e[a+2];if(typeof o!="function"){if(qy(o||i)===null)continue;break}var d=ai(i);d!==null&&(e.splice(a,3),a-=3,qm(d,{pending:!0,data:u,method:i.method,action:o},o,u))}}))}function Gl(e){function a(R){return vf(R,e)}wr!==null&&vf(wr,e),Ar!==null&&vf(Ar,e),xr!==null&&vf(xr,e),Il.forEach(a),Pl.forEach(a);for(var i=0;i<Cr.length;i++){var o=Cr[i];o.blockedOn===e&&(o.blockedOn=null)}for(;0<Cr.length&&(i=Cr[0],i.blockedOn===null);)KS(i),i.blockedOn===null&&Cr.shift();if(i=(e.ownerDocument||e).$$reactFormReplay,i!=null)for(o=0;o<i.length;o+=3){var u=i[o],d=i[o+1],v=u[dn]||null;if(typeof d=="function")v||YS(i);else if(v){var b=null;if(d&&d.hasAttribute("formAction")){if(u=d,v=d[dn]||null)b=v.formAction;else if(qy(u)!==null)continue}else b=v.action;typeof b=="function"?i[o+1]=b:(i.splice(o,3),o-=3),YS(i)}}}function Iy(e){this._internalRoot=e}bf.prototype.render=Iy.prototype.render=function(e){var a=this._internalRoot;if(a===null)throw Error(s(409));var i=a.current,o=wn();HS(i,o,e,a,null,null)},bf.prototype.unmount=Iy.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var a=e.containerInfo;HS(e.current,2,null,e,null,null),tf(),a[ei]=null}};function bf(e){this._internalRoot=e}bf.prototype.unstable_scheduleHydration=function(e){if(e){var a=dv();e={blockedOn:null,target:e,priority:a};for(var i=0;i<Cr.length&&a!==0&&a<Cr[i].priority;i++);Cr.splice(i,0,e),i===0&&KS(e)}};var XS=n.version;if(XS!=="19.1.0")throw Error(s(527,XS,"19.1.0"));X.findDOMNode=function(e){var a=e._reactInternals;if(a===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=m(a),e=e!==null?y(e):null,e=e===null?null:e.stateNode,e};var hC={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:k,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Sf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Sf.isDisabled&&Sf.supportsFiber)try{Yr=Sf.inject(hC),bn=Sf}catch{}}return Ql.createRoot=function(e,a){if(!l(e))throw Error(s(299));var i=!1,o="",u=db,d=hb,v=mb,b=null;return a!=null&&(a.unstable_strictMode===!0&&(i=!0),a.identifierPrefix!==void 0&&(o=a.identifierPrefix),a.onUncaughtError!==void 0&&(u=a.onUncaughtError),a.onCaughtError!==void 0&&(d=a.onCaughtError),a.onRecoverableError!==void 0&&(v=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(b=a.unstable_transitionCallbacks)),a=BS(e,1,!1,null,null,i,o,u,d,v,b,null),e[ei]=a.current,Oy(e),new Iy(a)},Ql.hydrateRoot=function(e,a,i){if(!l(e))throw Error(s(299));var o=!1,u="",d=db,v=hb,b=mb,R=null,z=null;return i!=null&&(i.unstable_strictMode===!0&&(o=!0),i.identifierPrefix!==void 0&&(u=i.identifierPrefix),i.onUncaughtError!==void 0&&(d=i.onUncaughtError),i.onCaughtError!==void 0&&(v=i.onCaughtError),i.onRecoverableError!==void 0&&(b=i.onRecoverableError),i.unstable_transitionCallbacks!==void 0&&(R=i.unstable_transitionCallbacks),i.formState!==void 0&&(z=i.formState)),a=BS(e,1,!0,a,i??null,o,u,d,v,b,R,z),a.context=qS(null),i=a.current,o=wn(),o=zh(o),u=hr(o),u.callback=null,mr(i,u,o),i=o,a.current.lanes=i,Jo(a,i),ga(a),e[ei]=a.current,Oy(e),new bf(a)},Ql.version="19.1.0",Ql}var o1;function OC(){if(o1)return Ky.exports;o1=1;function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(n){console.error(n)}}return t(),Ky.exports=RC(),Ky.exports}var MC=OC();function c2(t){if(Array.isArray(t))return t.flatMap(p=>c2(p));if(typeof t!="string")return[];const n=[];let r=0,s,l,c,f,h;const m=()=>{for(;r<t.length&&/\s/.test(t.charAt(r));)r+=1;return r<t.length},y=()=>(l=t.charAt(r),l!=="="&&l!==";"&&l!==",");for(;r<t.length;){for(s=r,h=!1;m();)if(l=t.charAt(r),l===","){for(c=r,r+=1,m(),f=r;r<t.length&&y();)r+=1;r<t.length&&t.charAt(r)==="="?(h=!0,r=f,n.push(t.slice(s,c)),s=r):r=c+1}else r+=1;(!h||r>=t.length)&&n.push(t.slice(s,t.length))}return n}function wC(t){return t instanceof Headers?new Headers(t):Array.isArray(t)?new Headers(t):typeof t=="object"?new Headers(t):new Headers}function AC(...t){return t.reduce((n,r)=>{const s=wC(r);for(const[l,c]of s.entries())l==="set-cookie"?c2(c).forEach(h=>n.append("set-cookie",h)):n.set(l,c);return n},new Headers)}const Yi=new WeakMap,zf=new WeakMap,Gf={current:[]};let Zy=!1,ic=0;const rc=new Set,Tf=new Map;function u2(t){const n=Array.from(t).sort((r,s)=>r instanceof Xi&&r.options.deps.includes(s)?1:s instanceof Xi&&s.options.deps.includes(r)?-1:0);for(const r of n){if(Gf.current.includes(r))continue;Gf.current.push(r),r.recompute();const s=zf.get(r);if(s)for(const l of s){const c=Yi.get(l);c&&u2(c)}}}function xC(t){t.listeners.forEach(n=>n({prevVal:t.prevState,currentVal:t.state}))}function CC(t){t.listeners.forEach(n=>n({prevVal:t.prevState,currentVal:t.state}))}function f2(t){if(ic>0&&!Tf.has(t)&&Tf.set(t,t.prevState),rc.add(t),!(ic>0)&&!Zy)try{for(Zy=!0;rc.size>0;){const n=Array.from(rc);rc.clear();for(const r of n){const s=Tf.get(r)??r.prevState;r.prevState=s,xC(r)}for(const r of n){const s=Yi.get(r);s&&(Gf.current.push(r),u2(s))}for(const r of n){const s=Yi.get(r);if(s)for(const l of s)CC(l)}}}finally{Zy=!1,Gf.current=[],Tf.clear()}}function l1(t){ic++;try{t()}finally{if(ic--,ic===0){const n=Array.from(rc)[0];n&&f2(n)}}}function DC(t){return typeof t=="function"}class Sp{constructor(n,r){this.listeners=new Set,this.subscribe=s=>{var l,c;this.listeners.add(s);const f=(c=(l=this.options)==null?void 0:l.onSubscribe)==null?void 0:c.call(l,s,this);return()=>{this.listeners.delete(s),f==null||f()}},this.prevState=n,this.state=n,this.options=r}setState(n){var r,s,l;this.prevState=this.state,(r=this.options)!=null&&r.updateFn?this.state=this.options.updateFn(this.prevState)(n):DC(n)?this.state=n(this.prevState):this.state=n,(l=(s=this.options)==null?void 0:s.onUpdate)==null||l.call(s),f2(this)}}class Xi{constructor(n){this.listeners=new Set,this._subscriptions=[],this.lastSeenDepValues=[],this.getDepVals=()=>{const r=[],s=[];for(const l of this.options.deps)r.push(l.prevState),s.push(l.state);return this.lastSeenDepValues=s,{prevDepVals:r,currDepVals:s,prevVal:this.prevState??void 0}},this.recompute=()=>{var r,s;this.prevState=this.state;const{prevDepVals:l,currDepVals:c,prevVal:f}=this.getDepVals();this.state=this.options.fn({prevDepVals:l,currDepVals:c,prevVal:f}),(s=(r=this.options).onUpdate)==null||s.call(r)},this.checkIfRecalculationNeededDeeply=()=>{for(const c of this.options.deps)c instanceof Xi&&c.checkIfRecalculationNeededDeeply();let r=!1;const s=this.lastSeenDepValues,{currDepVals:l}=this.getDepVals();for(let c=0;c<l.length;c++)if(l[c]!==s[c]){r=!0;break}r&&this.recompute()},this.mount=()=>(this.registerOnGraph(),this.checkIfRecalculationNeededDeeply(),()=>{this.unregisterFromGraph();for(const r of this._subscriptions)r()}),this.subscribe=r=>{var s,l;this.listeners.add(r);const c=(l=(s=this.options).onSubscribe)==null?void 0:l.call(s,r,this);return()=>{this.listeners.delete(r),c==null||c()}},this.options=n,this.state=n.fn({prevDepVals:void 0,prevVal:void 0,currDepVals:this.getDepVals().currDepVals})}registerOnGraph(n=this.options.deps){for(const r of n)if(r instanceof Xi)r.registerOnGraph(),this.registerOnGraph(r.options.deps);else if(r instanceof Sp){let s=Yi.get(r);s||(s=new Set,Yi.set(r,s)),s.add(this);let l=zf.get(this);l||(l=new Set,zf.set(this,l)),l.add(r)}}unregisterFromGraph(n=this.options.deps){for(const r of n)if(r instanceof Xi)this.unregisterFromGraph(r.options.deps);else if(r instanceof Sp){const s=Yi.get(r);s&&s.delete(this);const l=zf.get(this);l&&l.delete(r)}}}const Br="__TSR_index",c1="popstate",u1="beforeunload";function d2(t){let n=t.getLocation();const r=new Set,s=f=>{n=t.getLocation(),r.forEach(h=>h({location:n,action:f}))},l=f=>{t.notifyOnIndexChange??!0?s(f):n=t.getLocation()},c=async({task:f,navigateOpts:h,...m})=>{var y,p;if((h==null?void 0:h.ignoreBlocker)??!1){f();return}const _=((y=t.getBlockers)==null?void 0:y.call(t))??[],S=m.type==="PUSH"||m.type==="REPLACE";if(typeof document<"u"&&_.length&&S)for(const T of _){const A=yc(m.path,m.state);if(await T.blockerFn({currentLocation:n,nextLocation:A,action:m.type})){(p=t.onBlocked)==null||p.call(t);return}}f()};return{get location(){return n},get length(){return t.getLength()},subscribers:r,subscribe:f=>(r.add(f),()=>{r.delete(f)}),push:(f,h,m)=>{const y=n.state[Br];h=Ep(y+1,h),c({task:()=>{t.pushState(f,h),s({type:"PUSH"})},navigateOpts:m,type:"PUSH",path:f,state:h})},replace:(f,h,m)=>{const y=n.state[Br];h=Ep(y,h),c({task:()=>{t.replaceState(f,h),s({type:"REPLACE"})},navigateOpts:m,type:"REPLACE",path:f,state:h})},go:(f,h)=>{c({task:()=>{t.go(f),l({type:"GO",index:f})},navigateOpts:h,type:"GO"})},back:f=>{c({task:()=>{t.back((f==null?void 0:f.ignoreBlocker)??!1),l({type:"BACK"})},navigateOpts:f,type:"BACK"})},forward:f=>{c({task:()=>{t.forward((f==null?void 0:f.ignoreBlocker)??!1),l({type:"FORWARD"})},navigateOpts:f,type:"FORWARD"})},canGoBack:()=>n.state[Br]!==0,createHref:f=>t.createHref(f),block:f=>{var h;if(!t.setBlockers)return()=>{};const m=((h=t.getBlockers)==null?void 0:h.call(t))??[];return t.setBlockers([...m,f]),()=>{var y,p;const g=((y=t.getBlockers)==null?void 0:y.call(t))??[];(p=t.setBlockers)==null||p.call(t,g.filter(_=>_!==f))}},flush:()=>{var f;return(f=t.flush)==null?void 0:f.call(t)},destroy:()=>{var f;return(f=t.destroy)==null?void 0:f.call(t)},notify:s}}function Ep(t,n){return n||(n={}),{...n,key:Cg(),[Br]:t}}function kC(t){var n;const r=typeof document<"u"?window:void 0,s=r.history.pushState,l=r.history.replaceState;let c=[];const f=()=>c,h=F=>c=F,m=F=>F,y=()=>yc(`${r.location.pathname}${r.location.search}${r.location.hash}`,r.history.state);(n=r.history.state)!=null&&n.key||r.history.replaceState({[Br]:0,key:Cg()},"");let p=y(),g,_=!1,S=!1,T=!1,A=!1;const x=()=>p;let L,j;const q=()=>{L&&($._ignoreSubscribers=!0,(L.isPush?r.history.pushState:r.history.replaceState)(L.state,"",L.href),$._ignoreSubscribers=!1,L=void 0,j=void 0,g=void 0)},K=(F,W,rt)=>{const nt=m(W);j||(g=p),p=yc(W,rt),L={href:nt,state:rt,isPush:(L==null?void 0:L.isPush)||F==="push"},j||(j=Promise.resolve().then(()=>q()))},Z=F=>{p=y(),$.notify({type:F})},I=async()=>{if(S){S=!1;return}const F=y(),W=F.state[Br]-p.state[Br],rt=W===1,nt=W===-1,pt=!rt&&!nt||_;_=!1;const St=pt?"GO":nt?"BACK":"FORWARD",vt=pt?{type:"GO",index:W}:{type:nt?"BACK":"FORWARD"};if(T)T=!1;else{const k=f();if(typeof document<"u"&&k.length){for(const X of k)if(await X.blockerFn({currentLocation:p,nextLocation:F,action:St})){S=!0,r.history.go(1),$.notify(vt);return}}}p=y(),$.notify(vt)},Q=F=>{if(A){A=!1;return}let W=!1;const rt=f();if(typeof document<"u"&&rt.length)for(const nt of rt){const pt=nt.enableBeforeUnload??!0;if(pt===!0){W=!0;break}if(typeof pt=="function"&&pt()===!0){W=!0;break}}if(W)return F.preventDefault(),F.returnValue=""},$=d2({getLocation:x,getLength:()=>r.history.length,pushState:(F,W)=>K("push",F,W),replaceState:(F,W)=>K("replace",F,W),back:F=>(F&&(T=!0),A=!0,r.history.back()),forward:F=>{F&&(T=!0),A=!0,r.history.forward()},go:F=>{_=!0,r.history.go(F)},createHref:F=>m(F),flush:q,destroy:()=>{r.history.pushState=s,r.history.replaceState=l,r.removeEventListener(u1,Q,{capture:!0}),r.removeEventListener(c1,I)},onBlocked:()=>{g&&p!==g&&(p=g)},getBlockers:f,setBlockers:h,notifyOnIndexChange:!1});return r.addEventListener(u1,Q,{capture:!0}),r.addEventListener(c1,I),r.history.pushState=function(...F){const W=s.apply(r.history,F);return $._ignoreSubscribers||Z("PUSH"),W},r.history.replaceState=function(...F){const W=l.apply(r.history,F);return $._ignoreSubscribers||Z("REPLACE"),W},$}function NC(t={initialEntries:["/"]}){const n=t.initialEntries;let r=t.initialIndex?Math.min(Math.max(t.initialIndex,0),n.length-1):n.length-1;const s=n.map((c,f)=>Ep(f,void 0));return d2({getLocation:()=>yc(n[r],s[r]),getLength:()=>n.length,pushState:(c,f)=>{r<n.length-1&&(n.splice(r+1),s.splice(r+1)),s.push(f),n.push(c),r=Math.max(n.length-1,0)},replaceState:(c,f)=>{s[r]=f,n[r]=c},back:()=>{r=Math.max(r-1,0)},forward:()=>{r=Math.min(r+1,n.length-1)},go:c=>{r=Math.min(Math.max(r+c,0),n.length-1)},createHref:c=>c})}function yc(t,n){const r=t.indexOf("#"),s=t.indexOf("?");return{href:t,pathname:t.substring(0,r>0?s>0?Math.min(r,s):r:s>0?s:t.length),hash:r>-1?t.substring(r):"",search:s>-1?t.slice(s,r===-1?void 0:r):"",state:n||{[Br]:0,key:Cg()}}}function Cg(){return(Math.random()+1).toString(36).substring(7)}var zC="Invariant failed";function Kn(t,n){if(!t)throw new Error(zC)}function Tp(t){return t[t.length-1]}function LC(t){return typeof t=="function"}function Ts(t,n){return LC(t)?t(n):t}function Rp(t,n){return n.reduce((r,s)=>(r[s]=t[s],r),{})}function Pn(t,n){if(t===n)return t;const r=n,s=h1(t)&&h1(r);if(s||f1(t)&&f1(r)){const l=s?t:Object.keys(t).concat(Object.getOwnPropertySymbols(t)),c=l.length,f=s?r:Object.keys(r).concat(Object.getOwnPropertySymbols(r)),h=f.length,m=s?[]:{};let y=0;for(let p=0;p<h;p++){const g=s?p:f[p];(!s&&l.includes(g)||s)&&t[g]===void 0&&r[g]===void 0?(m[g]=void 0,y++):(m[g]=Pn(t[g],r[g]),m[g]===t[g]&&t[g]!==void 0&&y++)}return c===h&&y===c?t:m}return r}function f1(t){return Ja(t)&&Object.getOwnPropertyNames(t).length===Object.keys(t).length}function Ja(t){if(!d1(t))return!1;const n=t.constructor;if(typeof n>"u")return!0;const r=n.prototype;return!(!d1(r)||!r.hasOwnProperty("isPrototypeOf"))}function d1(t){return Object.prototype.toString.call(t)==="[object Object]"}function h1(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function m1(t,n){let r=Object.keys(t);return n&&(r=r.filter(s=>t[s]!==void 0)),r}function Zi(t,n,r){if(t===n)return!0;if(typeof t!=typeof n)return!1;if(Ja(t)&&Ja(n)){const s=(r==null?void 0:r.ignoreUndefined)??!0,l=m1(t,s),c=m1(n,s);return!(r!=null&&r.partial)&&l.length!==c.length?!1:c.every(f=>Zi(t[f],n[f],r))}return Array.isArray(t)&&Array.isArray(n)?t.length!==n.length?!1:!t.some((s,l)=>!Zi(s,n[l],r)):!1}function Ii(t){let n,r;const s=new Promise((l,c)=>{n=l,r=c});return s.status="pending",s.resolve=l=>{s.status="resolved",s.value=l,n(l),t==null||t(l)},s.reject=l=>{s.status="rejected",r(l)},s}function Oa(t){return Dd(t.filter(n=>n!==void 0).join("/"))}function Dd(t){return t.replace(/\/{2,}/g,"/")}function Dg(t){return t==="/"?t:t.replace(/^\/{1,}/,"")}function fo(t){return t==="/"?t:t.replace(/\/{1,}$/,"")}function $C(t){return fo(Dg(t))}function Kf(t,n){return t!=null&&t.endsWith("/")&&t!=="/"&&t!==`${n}/`?t.slice(0,-1):t}function FC(t,n,r){return Kf(t,r)===Kf(n,r)}function jC({basepath:t,base:n,to:r,trailingSlash:s="never",caseSensitive:l}){var c,f;n=Qf(t,n,l),r=Qf(t,r,l);let h=ho(n);const m=ho(r);h.length>1&&((c=Tp(h))==null?void 0:c.value)==="/"&&h.pop(),m.forEach((g,_)=>{g.value==="/"?_?_===m.length-1&&h.push(g):h=[g]:g.value===".."?h.pop():g.value==="."||h.push(g)}),h.length>1&&(((f=Tp(h))==null?void 0:f.value)==="/"?s==="never"&&h.pop():s==="always"&&h.push({type:"pathname",value:"/"}));const y=h.map(g=>{if(g.type==="param"){const _=g.value.substring(1);if(g.prefixSegment&&g.suffixSegment)return`${g.prefixSegment}{$${_}}${g.suffixSegment}`;if(g.prefixSegment)return`${g.prefixSegment}{$${_}}`;if(g.suffixSegment)return`{$${_}}${g.suffixSegment}`}if(g.type==="wildcard"){if(g.prefixSegment&&g.suffixSegment)return`${g.prefixSegment}{$}${g.suffixSegment}`;if(g.prefixSegment)return`${g.prefixSegment}{$}`;if(g.suffixSegment)return`{$}${g.suffixSegment}`}return g.value}),p=Oa([t,...y]);return Dd(p)}const UC=/^\$.{1,}$/,BC=/^(.*?)\{(\$[a-zA-Z_$][a-zA-Z0-9_$]*)\}(.*)$/,qC=/^\$$/,HC=/^(.*?)\{\$\}(.*)$/;function ho(t){if(!t)return[];t=Dd(t);const n=[];if(t.slice(0,1)==="/"&&(t=t.substring(1),n.push({type:"pathname",value:"/"})),!t)return n;const r=t.split("/").filter(Boolean);return n.push(...r.map(s=>{const l=s.match(HC);if(l){const f=l[1],h=l[2];return{type:"wildcard",value:"$",prefixSegment:f||void 0,suffixSegment:h||void 0}}const c=s.match(BC);if(c){const f=c[1],h=c[2],m=c[3];return{type:"param",value:""+h,prefixSegment:f||void 0,suffixSegment:m||void 0}}return UC.test(s)?{type:"param",value:"$"+s.substring(1),prefixSegment:void 0,suffixSegment:void 0}:qC.test(s)?{type:"wildcard",value:"$",prefixSegment:void 0,suffixSegment:void 0}:{type:"pathname",value:s.includes("%25")?s.split("%25").map(f=>decodeURI(f)).join("%25"):decodeURI(s)}})),t.slice(-1)==="/"&&(t=t.substring(1),n.push({type:"pathname",value:"/"})),n}function Jy({path:t,params:n,leaveWildcards:r,leaveParams:s,decodeCharMap:l}){const c=ho(t);function f(p){const g=n[p],_=typeof g=="string";return["*","_splat"].includes(p)?_?encodeURI(g):g:_?IC(g,l):g}let h=!1;const m={},y=Oa(c.map(p=>{if(p.type==="wildcard"){m._splat=n._splat;const g=p.prefixSegment||"",_=p.suffixSegment||"",S=f("_splat");return r?`${g}${p.value}${S??""}${_}`:`${g}${S}${_}`}if(p.type==="param"){const g=p.value.substring(1);!h&&!(g in n)&&(h=!0),m[g]=n[g];const _=p.prefixSegment||"",S=p.suffixSegment||"";if(s){const T=f(p.value);return`${_}${p.value}${T??""}${S}`}return`${_}${f(g)??"undefined"}${S}`}return p.value}));return{usedParams:m,interpolatedPath:y,isMissingParams:h}}function IC(t,n){let r=encodeURIComponent(t);if(n)for(const[s,l]of n)r=r.replaceAll(s,l);return r}function Op(t,n,r){const s=PC(t,n,r);if(!(r.to&&!s))return s??{}}function Qf(t,n,r=!1){const s=r?t:t.toLowerCase(),l=r?n:n.toLowerCase();switch(!0){case s==="/":return n;case l===s:return"";case n.length<t.length:return n;case l[s.length]!=="/":return n;case l.startsWith(s):return n.slice(t.length);default:return n}}function PC(t,n,r){if(t!=="/"&&!n.startsWith(t))return;n=Qf(t,n,r.caseSensitive);const s=Qf(t,`${r.to??"$"}`,r.caseSensitive),l=ho(n),c=ho(s);n.startsWith("/")||l.unshift({type:"pathname",value:"/"}),s.startsWith("/")||c.unshift({type:"pathname",value:"/"});const f={};return(()=>{var m;for(let y=0;y<Math.max(l.length,c.length);y++){const p=l[y],g=c[y],_=y>=l.length-1,S=y>=c.length-1;if(g){if(g.type==="wildcard"){const T=l.slice(y);let A;if(g.prefixSegment||g.suffixSegment){if(!p)return!1;const x=g.prefixSegment||"",L=g.suffixSegment||"",j=p.value;if("prefixSegment"in g&&!j.startsWith(x)||"suffixSegment"in g&&!((m=l[l.length-1])!=null&&m.value.endsWith(L)))return!1;let q=decodeURI(Oa(T.map(K=>K.value)));x&&q.startsWith(x)&&(q=q.slice(x.length)),L&&q.endsWith(L)&&(q=q.slice(0,q.length-L.length)),A=q}else A=decodeURI(Oa(T.map(x=>x.value)));return f["*"]=A,f._splat=A,!0}if(g.type==="pathname"){if(g.value==="/"&&!(p!=null&&p.value))return!0;if(p){if(r.caseSensitive){if(g.value!==p.value)return!1}else if(g.value.toLowerCase()!==p.value.toLowerCase())return!1}}if(!p)return!1;if(g.type==="param"){if(p.value==="/")return!1;let T;if(g.prefixSegment||g.suffixSegment){const A=g.prefixSegment||"",x=g.suffixSegment||"",L=p.value;if(A&&!L.startsWith(A)||x&&!L.endsWith(x))return!1;let j=L;A&&j.startsWith(A)&&(j=j.slice(A.length)),x&&j.endsWith(x)&&(j=j.slice(0,j.length-x.length)),T=decodeURIComponent(j)}else T=decodeURIComponent(p.value);f[g.value.substring(1)]=T}}if(!_&&S)return f["**"]=Oa(l.slice(y+1).map(T=>T.value)),!!r.fuzzy&&(g==null?void 0:g.value)!=="/"}return!0})()?f:void 0}function vn(t){return!!(t!=null&&t.isNotFound)}function VC(){try{if(typeof window<"u"&&typeof window.sessionStorage=="object")return window.sessionStorage}catch{return}}const Yf="tsr-scroll-restoration-v1_3",GC=(t,n)=>{let r;return(...s)=>{r||(r=setTimeout(()=>{t(...s),r=null},n))}};function KC(){const t=VC();if(!t)return;const n=t.getItem(Yf);let r=n?JSON.parse(n):{};return{state:r,set:s=>(r=Ts(s,r)||r,t.setItem(Yf,JSON.stringify(r)))}}const Wy=KC(),Mp=t=>t.state.key||t.href;function QC(t){const n=[];let r;for(;r=t.parentNode;)n.unshift(`${t.tagName}:nth-child(${[].indexOf.call(r.children,t)+1})`),t=r;return`${n.join(" > ")}`.toLowerCase()}let Xf=!1;function h2(t,n,r,s,l){var c;let f;try{f=JSON.parse(sessionStorage.getItem(t)||"{}")}catch(y){console.error(y);return}const h=n||((c=window.history.state)==null?void 0:c.key),m=f[h];Xf=!0,(()=>{if(s&&m){for(const p in m){const g=m[p];if(p==="window")window.scrollTo({top:g.scrollY,left:g.scrollX,behavior:r});else if(p){const _=document.querySelector(p);_&&(_.scrollLeft=g.scrollX,_.scrollTop=g.scrollY)}}return}const y=window.location.hash.split("#")[1];if(y){const p=(window.history.state||{}).__hashScrollIntoViewOptions??!0;if(p){const g=document.getElementById(y);g&&g.scrollIntoView(p)}return}["window",...(l==null?void 0:l.filter(p=>p!=="window"))??[]].forEach(p=>{const g=p==="window"?window:typeof p=="function"?p():document.querySelector(p);g&&g.scrollTo({top:0,left:0,behavior:r})})})(),Xf=!1}function YC(t,n){if(Wy===void 0||((t.options.scrollRestoration??!1)&&(t.isScrollRestoring=!0),typeof document>"u"||t.isScrollRestorationSetup))return;t.isScrollRestorationSetup=!0,Xf=!1;const s=t.options.getScrollRestorationKey||Mp;window.history.scrollRestoration="manual";const l=c=>{if(Xf||!t.isScrollRestoring)return;let f="";if(c.target===document||c.target===window)f="window";else{const m=c.target.getAttribute("data-scroll-restoration-id");m?f=`[data-scroll-restoration-id="${m}"]`:f=QC(c.target)}const h=s(t.state.location);Wy.set(m=>{const y=m[h]=m[h]||{},p=y[f]=y[f]||{};if(f==="window")p.scrollX=window.scrollX||0,p.scrollY=window.scrollY||0;else if(f){const g=document.querySelector(f);g&&(p.scrollX=g.scrollLeft||0,p.scrollY=g.scrollTop||0)}return m})};typeof document<"u"&&document.addEventListener("scroll",GC(l,100),!0),t.subscribe("onRendered",c=>{const f=s(c.toLocation);if(!t.resetNextScroll){t.resetNextScroll=!0;return}h2(Yf,f,t.options.scrollRestorationBehavior||void 0,t.isScrollRestoring||void 0,t.options.scrollToTopSelectors||void 0),t.isScrollRestoring&&Wy.set(h=>(h[f]=h[f]||{},h))})}function XC(t){if(typeof document<"u"&&document.querySelector){const n=t.state.location.state.__hashScrollIntoViewOptions??!0;if(n&&t.state.location.hash!==""){const r=document.getElementById(t.state.location.hash);r&&r.scrollIntoView(n)}}}function m2(t,n){const r=Object.entries(t).flatMap(([l,c])=>Array.isArray(c)?c.map(f=>[l,String(f)]):[[l,String(c)]]);return""+new URLSearchParams(r).toString()}function tp(t){return t?t==="false"?!1:t==="true"?!0:+t*0===0&&+t+""===t?+t:t:""}function ZC(t,n){const r=t;return[...new URLSearchParams(r).entries()].reduce((c,[f,h])=>{const m=c[f];return m==null?c[f]=tp(h):c[f]=Array.isArray(m)?[...m,tp(h)]:[m,tp(h)],c},{})}const JC=t3(JSON.parse),WC=e3(JSON.stringify,JSON.parse);function t3(t){return n=>{n.substring(0,1)==="?"&&(n=n.substring(1));const r=ZC(n);for(const s in r){const l=r[s];if(typeof l=="string")try{r[s]=t(l)}catch{}}return r}}function e3(t,n){function r(s){if(typeof s=="object"&&s!==null)try{return t(s)}catch{}else if(typeof s=="string"&&typeof n=="function")try{return n(s),t(s)}catch{}return s}return s=>{s={...s},Object.keys(s).forEach(c=>{const f=s[c];typeof f>"u"||f===void 0?delete s[c]:s[c]=r(f)});const l=m2(s).toString();return l?`?${l}`:""}}const aa="__root__";function n3(t){if(t.statusCode=t.statusCode||t.code||307,!t.reloadDocument)try{new URL(`${t.href}`),t.reloadDocument=!0}catch{}const n=new Headers(t.headers||{}),r=new Response(null,{status:t.statusCode,headers:n});if(r.options=t,t.throw)throw r;return r}function gn(t){return t instanceof Response&&!!t.options}function a3(t){if(typeof t=="object"&&t.isSerializedRedirect)return n3(t)}function r3(t){return t instanceof Error?{name:t.name,message:t.message}:{data:t}}function Rs(t){const n=t.resolvedLocation,r=t.location,s=(n==null?void 0:n.pathname)!==r.pathname,l=(n==null?void 0:n.href)!==r.href,c=(n==null?void 0:n.hash)!==r.hash;return{fromLocation:n,toLocation:r,pathChanged:s,hrefChanged:l,hashChanged:c}}class s3{constructor(n){this.tempLocationKey=`${Math.round(Math.random()*1e7)}`,this.resetNextScroll=!0,this.shouldViewTransition=void 0,this.isViewTransitionTypesSupported=void 0,this.subscribers=new Set,this.isScrollRestoring=!1,this.isScrollRestorationSetup=!1,this.startTransition=r=>r(),this.isShell=!1,this.update=r=>{var s;r.notFoundRoute&&console.warn("The notFoundRoute API is deprecated and will be removed in the next major version. See https://tanstack.com/router/v1/docs/framework/react/guide/not-found-errors#migrating-from-notfoundroute for more info.");const l=this.options;this.options={...this.options,...r},this.isServer=this.options.isServer??typeof document>"u",this.pathParamsDecodeCharMap=this.options.pathParamsAllowedCharacters?new Map(this.options.pathParamsAllowedCharacters.map(c=>[encodeURIComponent(c),c])):void 0,(!this.basepath||r.basepath&&r.basepath!==l.basepath)&&(r.basepath===void 0||r.basepath===""||r.basepath==="/"?this.basepath="/":this.basepath=`/${$C(r.basepath)}`),(!this.history||this.options.history&&this.options.history!==this.history)&&(this.history=this.options.history??(this.isServer?NC({initialEntries:[this.basepath||"/"]}):kC()),this.latestLocation=this.parseLocation()),this.options.routeTree!==this.routeTree&&(this.routeTree=this.options.routeTree,this.buildRouteTree()),this.__store||(this.__store=new Sp(o3(this.latestLocation),{onUpdate:()=>{this.__store.state={...this.state,cachedMatches:this.state.cachedMatches.filter(c=>!["redirected"].includes(c.status))}}}),YC(this)),typeof window<"u"&&"CSS"in window&&typeof((s=window.CSS)==null?void 0:s.supports)=="function"&&(this.isViewTransitionTypesSupported=window.CSS.supports("selector(:active-view-transition-type(a)")),this.latestLocation.search.__TSS_SHELL&&(this.isShell=!0)},this.buildRouteTree=()=>{const{routesById:r,routesByPath:s,flatRoutes:l}=l3({routeTree:this.routeTree,initRoute:(f,h)=>{f.init({originalIndex:h,defaultSsr:this.options.defaultSsr})}});this.routesById=r,this.routesByPath=s,this.flatRoutes=l;const c=this.options.notFoundRoute;c&&(c.init({originalIndex:99999999999,defaultSsr:this.options.defaultSsr}),this.routesById[c.id]=c)},this.subscribe=(r,s)=>{const l={eventType:r,fn:s};return this.subscribers.add(l),()=>{this.subscribers.delete(l)}},this.emit=r=>{this.subscribers.forEach(s=>{s.eventType===r.type&&s.fn(r)})},this.parseLocation=(r,s)=>{const l=({pathname:m,search:y,hash:p,state:g})=>{const _=this.options.parseSearch(y),S=this.options.stringifySearch(_);return{pathname:m,searchStr:S,search:Pn(r==null?void 0:r.search,_),hash:p.split("#").reverse()[0]??"",href:`${m}${S}${p}`,state:Pn(r==null?void 0:r.state,g)}},c=l(s??this.history.location),{__tempLocation:f,__tempKey:h}=c.state;if(f&&(!h||h===this.tempLocationKey)){const m=l(f);return m.state.key=c.state.key,delete m.state.__tempLocation,{...m,maskedLocation:c}}return c},this.resolvePathWithBase=(r,s)=>jC({basepath:this.basepath,base:r,to:Dd(s),trailingSlash:this.options.trailingSlash,caseSensitive:this.options.caseSensitive}),this.matchRoutes=(r,s,l)=>typeof r=="string"?this.matchRoutesInternal({pathname:r,search:s},l):this.matchRoutesInternal(r,s),this.getMatchedRoutes=(r,s)=>c3({pathname:r,routePathname:s,basepath:this.basepath,caseSensitive:this.options.caseSensitive,routesByPath:this.routesByPath,routesById:this.routesById,flatRoutes:this.flatRoutes}),this.cancelMatch=r=>{const s=this.getMatch(r);s&&(s.abortController.abort(),clearTimeout(s.pendingTimeout))},this.cancelMatches=()=>{var r;(r=this.state.pendingMatches)==null||r.forEach(s=>{this.cancelMatch(s.id)})},this.buildLocation=r=>{const s=(c={})=>{var f;const h=c._fromLocation||this.latestLocation,m=this.matchRoutes(h,{_buildLocation:!0}),y=Tp(m);let p=y.fullPath;c.unsafeRelative==="path"?p=h.pathname:c.to&&c.from&&(p=c.from,[...m].reverse().find(Q=>Q.fullPath===p||Q.fullPath===Oa([p,"/"]))||console.warn(`Could not find match for from: ${c.from}`));const g=y.search,_={...y.params},S=c.to?this.resolvePathWithBase(p,`${c.to}`):p;let T=(c.params??!0)===!0?_:{..._,...Ts(c.params,_)};const A=this.matchRoutes(S,{},{_buildLocation:!0}).map(I=>this.looseRoutesById[I.routeId]);Object.keys(T).length>0&&A.map(I=>{var Q;return((Q=I.options.params)==null?void 0:Q.stringify)??I.options.stringifyParams}).filter(Boolean).forEach(I=>{T={...T,...I(T)}});const x=Jy({path:S,params:T??{},leaveWildcards:!1,leaveParams:r.leaveParams,decodeCharMap:this.pathParamsDecodeCharMap}).interpolatedPath;let L=g;if(r._includeValidateSearch&&((f=this.options.search)!=null&&f.strict)){let I={};A.forEach(Q=>{try{Q.options.validateSearch&&(I={...I,...wp(Q.options.validateSearch,{...I,...L})??{}})}catch{}}),L=I}L=u3({search:L,dest:c,destRoutes:A,_includeValidateSearch:r._includeValidateSearch}),L=Pn(g,L);const j=this.options.stringifySearch(L),q=c.hash===!0?h.hash:c.hash?Ts(c.hash,h.hash):void 0,K=q?`#${q}`:"";let Z=c.state===!0?h.state:c.state?Ts(c.state,h.state):{};return Z=Pn(h.state,Z),{pathname:x,search:L,searchStr:j,state:Z,hash:q??"",href:`${x}${j}${K}`,unmaskOnReload:c.unmaskOnReload}},l=(c={},f)=>{var h;const m=s(c);let y=f?s(f):void 0;if(!y){let p={};const g=(h=this.options.routeMasks)==null?void 0:h.find(_=>{const S=Op(this.basepath,m.pathname,{to:_.from,caseSensitive:!1,fuzzy:!1});return S?(p=S,!0):!1});if(g){const{from:_,...S}=g;f={...Rp(r,["from"]),...S,params:p},y=s(f)}}if(y){const p=s(f);m.maskedLocation=p}return m};return r.mask?l(r,{...Rp(r,["from"]),...r.mask}):l(r)},this.commitLocation=({viewTransition:r,ignoreBlocker:s,...l})=>{const c=()=>{const m=["key","__TSR_index","__hashScrollIntoViewOptions"];m.forEach(p=>{l.state[p]=this.latestLocation.state[p]});const y=Zi(l.state,this.latestLocation.state);return m.forEach(p=>{delete l.state[p]}),y},f=this.latestLocation.href===l.href,h=this.commitLocationPromise;if(this.commitLocationPromise=Ii(()=>{h==null||h.resolve()}),f&&c())this.load();else{let{maskedLocation:m,hashScrollIntoView:y,...p}=l;m&&(p={...m,state:{...m.state,__tempKey:void 0,__tempLocation:{...p,search:p.searchStr,state:{...p.state,__tempKey:void 0,__tempLocation:void 0,key:void 0}}}},(p.unmaskOnReload??this.options.unmaskOnReload??!1)&&(p.state.__tempKey=this.tempLocationKey)),p.state.__hashScrollIntoViewOptions=y??this.options.defaultHashScrollIntoView??!0,this.shouldViewTransition=r,this.history[l.replace?"replace":"push"](p.href,p.state,{ignoreBlocker:s})}return this.resetNextScroll=l.resetScroll??!0,this.history.subscribers.size||this.load(),this.commitLocationPromise},this.buildAndCommitLocation=({replace:r,resetScroll:s,hashScrollIntoView:l,viewTransition:c,ignoreBlocker:f,href:h,...m}={})=>{if(h){const p=this.history.location.state.__TSR_index,g=yc(h,{__TSR_index:r?p:p+1});m.to=g.pathname,m.search=this.options.parseSearch(g.search),m.hash=g.hash.slice(1)}const y=this.buildLocation({...m,_includeValidateSearch:!0});return this.commitLocation({...y,viewTransition:c,replace:r,resetScroll:s,hashScrollIntoView:l,ignoreBlocker:f})},this.navigate=({to:r,reloadDocument:s,href:l,...c})=>{if(!s&&l)try{new URL(`${l}`),s=!0}catch{}if(s){if(!l){const f=this.buildLocation({to:r,...c});l=this.history.createHref(f.href)}c.replace?window.location.replace(l):window.location.href=l;return}return this.buildAndCommitLocation({...c,href:l,to:r})},this.beforeLoad=()=>{this.cancelMatches(),this.latestLocation=this.parseLocation(this.latestLocation);const r=this.matchRoutes(this.latestLocation);this.__store.setState(s=>({...s,status:"pending",isLoading:!0,location:this.latestLocation,pendingMatches:r,cachedMatches:s.cachedMatches.filter(l=>!r.find(c=>c.id===l.id))}))},this.load=async r=>{let s,l,c;for(c=new Promise(f=>{this.startTransition(async()=>{var h;try{this.beforeLoad();const m=this.latestLocation,y=this.state.resolvedLocation;this.state.redirect||this.emit({type:"onBeforeNavigate",...Rs({resolvedLocation:y,location:m})}),this.emit({type:"onBeforeLoad",...Rs({resolvedLocation:y,location:m})}),await this.loadMatches({sync:r==null?void 0:r.sync,matches:this.state.pendingMatches,location:m,onReady:async()=>{this.startViewTransition(async()=>{let p,g,_;l1(()=>{this.__store.setState(S=>{const T=S.matches,A=S.pendingMatches||S.matches;return p=T.filter(x=>!A.find(L=>L.id===x.id)),g=A.filter(x=>!T.find(L=>L.id===x.id)),_=T.filter(x=>A.find(L=>L.id===x.id)),{...S,isLoading:!1,loadedAt:Date.now(),matches:A,pendingMatches:void 0,cachedMatches:[...S.cachedMatches,...p.filter(x=>x.status!=="error")]}}),this.clearExpiredCache()}),[[p,"onLeave"],[g,"onEnter"],[_,"onStay"]].forEach(([S,T])=>{S.forEach(A=>{var x,L;(L=(x=this.looseRoutesById[A.routeId].options)[T])==null||L.call(x,A)})})})}})}catch(m){gn(m)?(s=m,this.isServer||this.navigate({...s.options,replace:!0,ignoreBlocker:!0})):vn(m)&&(l=m),this.__store.setState(y=>({...y,statusCode:s?s.status:l?404:y.matches.some(p=>p.status==="error")?500:200,redirect:s}))}this.latestLoadPromise===c&&((h=this.commitLocationPromise)==null||h.resolve(),this.latestLoadPromise=void 0,this.commitLocationPromise=void 0),f()})}),this.latestLoadPromise=c,await c;this.latestLoadPromise&&c!==this.latestLoadPromise;)await this.latestLoadPromise;this.hasNotFoundMatch()&&this.__store.setState(f=>({...f,statusCode:404}))},this.startViewTransition=r=>{const s=this.shouldViewTransition??this.options.defaultViewTransition;if(delete this.shouldViewTransition,s&&typeof document<"u"&&"startViewTransition"in document&&typeof document.startViewTransition=="function"){let l;if(typeof s=="object"&&this.isViewTransitionTypesSupported){const c=this.latestLocation,f=this.state.resolvedLocation,h=typeof s.types=="function"?s.types(Rs({resolvedLocation:f,location:c})):s.types;l={update:r,types:h}}else l=r;document.startViewTransition(l)}else r()},this.updateMatch=(r,s)=>{var l;let c;const f=(l=this.state.pendingMatches)==null?void 0:l.find(p=>p.id===r),h=this.state.matches.find(p=>p.id===r),m=this.state.cachedMatches.find(p=>p.id===r),y=f?"pendingMatches":h?"matches":m?"cachedMatches":"";return y&&this.__store.setState(p=>{var g;return{...p,[y]:(g=p[y])==null?void 0:g.map(_=>_.id===r?c=s(_):_)}}),c},this.getMatch=r=>[...this.state.cachedMatches,...this.state.pendingMatches??[],...this.state.matches].find(s=>s.id===r),this.loadMatches=async({location:r,matches:s,preload:l,onReady:c,updateMatch:f=this.updateMatch,sync:h})=>{let m,y=!1;const p=async()=>{y||(y=!0,await(c==null?void 0:c()))},g=S=>!!(l&&!this.state.matches.find(T=>T.id===S)),_=(S,T)=>{var A,x,L,j;if(gn(T)||vn(T)){if(gn(T)&&T.redirectHandled&&!T.options.reloadDocument)throw T;if(f(S.id,q=>({...q,status:gn(T)?"redirected":vn(T)?"notFound":"error",isFetching:!1,error:T,beforeLoadPromise:void 0,loaderPromise:void 0})),T.routeId||(T.routeId=S.routeId),(A=S.beforeLoadPromise)==null||A.resolve(),(x=S.loaderPromise)==null||x.resolve(),(L=S.loadPromise)==null||L.resolve(),gn(T))throw y=!0,T.options._fromLocation=r,T.redirectHandled=!0,T=this.resolveRedirect(T),T;if(vn(T))throw this._handleNotFound(s,T,{updateMatch:f}),(j=this.serverSsr)==null||j.onMatchSettled({router:this,match:this.getMatch(S.id)}),T}};try{await new Promise((S,T)=>{(async()=>{var A,x,L,j;try{const q=(I,Q,$)=>{var F,W;const{id:rt,routeId:nt}=s[I],pt=this.looseRoutesById[nt];if(Q instanceof Promise)throw Q;Q.routerCode=$,m=m??I,_(this.getMatch(rt),Q);try{(W=(F=pt.options).onError)==null||W.call(F,Q)}catch(St){Q=St,_(this.getMatch(rt),Q)}f(rt,St=>{var vt,k;return(vt=St.beforeLoadPromise)==null||vt.resolve(),(k=St.loadPromise)==null||k.resolve(),{...St,error:Q,status:"error",isFetching:!1,updatedAt:Date.now(),abortController:new AbortController,beforeLoadPromise:void 0}})};for(const[I,{id:Q,routeId:$}]of s.entries()){const F=this.getMatch(Q),W=(A=s[I-1])==null?void 0:A.id,rt=this.looseRoutesById[$],nt=rt.options.pendingMs??this.options.defaultPendingMs,pt=!!(c&&!this.isServer&&!g(Q)&&(rt.options.loader||rt.options.beforeLoad||y1(rt))&&typeof nt=="number"&&nt!==1/0&&(rt.options.pendingComponent??((x=this.options)==null?void 0:x.defaultPendingComponent)));let St=!0;if((F.beforeLoadPromise||F.loaderPromise)&&(pt&&setTimeout(()=>{try{p()}catch{}},nt),await F.beforeLoadPromise,St=this.getMatch(Q).status!=="success"),St){try{f(Q,Nt=>{const dt=Nt.loadPromise;return{...Nt,loadPromise:Ii(()=>{dt==null||dt.resolve()}),beforeLoadPromise:Ii()}});const vt=new AbortController;let k;pt&&(k=setTimeout(()=>{try{p()}catch{}},nt));const{paramsError:X,searchError:lt}=this.getMatch(Q);X&&q(I,X,"PARSE_PARAMS"),lt&&q(I,lt,"VALIDATE_SEARCH");const At=()=>W?this.getMatch(W).context:this.options.context??{};f(Q,Nt=>({...Nt,isFetching:"beforeLoad",fetchCount:Nt.fetchCount+1,abortController:vt,pendingTimeout:k,context:{...At(),...Nt.__routeContext}}));const{search:M,params:H,context:st,cause:at}=this.getMatch(Q),it=g(Q),ft={search:M,abortController:vt,params:H,preload:it,context:st,location:r,navigate:Nt=>this.navigate({...Nt,_fromLocation:r}),buildLocation:this.buildLocation,cause:it?"preload":at,matches:s},ct=await((j=(L=rt.options).beforeLoad)==null?void 0:j.call(L,ft))??{};(gn(ct)||vn(ct))&&q(I,ct,"BEFORE_LOAD"),f(Q,Nt=>({...Nt,__beforeLoadContext:ct,context:{...At(),...Nt.__routeContext,...ct},abortController:vt}))}catch(vt){q(I,vt,"BEFORE_LOAD")}f(Q,vt=>{var k;return(k=vt.beforeLoadPromise)==null||k.resolve(),{...vt,beforeLoadPromise:void 0,isFetching:!1}})}}const K=s.slice(0,m),Z=[];K.forEach(({id:I,routeId:Q},$)=>{Z.push((async()=>{const{loaderPromise:F}=this.getMatch(I);let W=!1,rt=!1;if(F){await F;const nt=this.getMatch(I);nt.error&&_(nt,nt.error)}else{const nt=Z[$-1],pt=this.looseRoutesById[Q],St=()=>{const{params:it,loaderDeps:ft,abortController:ct,context:Nt,cause:dt}=this.getMatch(I),zt=g(I);return{params:it,deps:ft,preload:!!zt,parentMatchPromise:nt,abortController:ct,context:Nt,location:r,navigate:ee=>this.navigate({...ee,_fromLocation:r}),cause:zt?"preload":dt,route:pt}},vt=Date.now()-this.getMatch(I).updatedAt,k=g(I),X=k?pt.options.preloadStaleTime??this.options.defaultPreloadStaleTime??3e4:pt.options.staleTime??this.options.defaultStaleTime??0,lt=pt.options.shouldReload,At=typeof lt=="function"?lt(St()):lt;f(I,it=>({...it,loaderPromise:Ii(),preload:!!k&&!this.state.matches.find(ft=>ft.id===I)}));const M=async()=>{var it,ft,ct,Nt,dt,zt;const ee=this.getMatch(I);if(!ee)return;const me={matches:s,match:ee,params:ee.params,loaderData:ee.loaderData},ue=await((ft=(it=pt.options).head)==null?void 0:ft.call(it,me)),fn=ue==null?void 0:ue.meta,Zn=ue==null?void 0:ue.links,or=ue==null?void 0:ue.scripts,kn=await((Nt=(ct=pt.options).scripts)==null?void 0:Nt.call(ct,me)),$e=await((zt=(dt=pt.options).headers)==null?void 0:zt.call(dt,me));return{meta:fn,links:Zn,headScripts:or,headers:$e,scripts:kn}},H=async()=>{var it,ft,ct,Nt,dt;try{const zt=async()=>{const ee=this.getMatch(I);ee.minPendingPromise&&await ee.minPendingPromise};try{this.loadRouteChunk(pt),f(I,ue=>({...ue,isFetching:"loader"}));const ee=await((ft=(it=pt.options).loader)==null?void 0:ft.call(it,St()));_(this.getMatch(I),ee),await pt._lazyPromise,await zt(),await pt._componentsPromise,f(I,ue=>({...ue,error:void 0,status:"success",isFetching:!1,updatedAt:Date.now(),loaderData:ee}));const me=await M();f(I,ue=>({...ue,...me}))}catch(ee){let me=ee;await zt(),_(this.getMatch(I),ee);try{(Nt=(ct=pt.options).onError)==null||Nt.call(ct,ee)}catch(fn){me=fn,_(this.getMatch(I),fn)}const ue=await M();f(I,fn=>({...fn,error:me,status:"error",isFetching:!1,...ue}))}(dt=this.serverSsr)==null||dt.onMatchSettled({router:this,match:this.getMatch(I)})}catch(zt){const ee=await M();f(I,me=>({...me,loaderPromise:void 0,...ee})),_(this.getMatch(I),zt)}},{status:st,invalid:at}=this.getMatch(I);if(W=st==="success"&&(at||(At??vt>X)),!(k&&pt.options.preload===!1))if(W&&!h)rt=!0,(async()=>{try{await H();const{loaderPromise:it,loadPromise:ft}=this.getMatch(I);it==null||it.resolve(),ft==null||ft.resolve(),f(I,ct=>({...ct,loaderPromise:void 0}))}catch(it){gn(it)&&await this.navigate(it.options)}})();else if(st!=="success"||W&&h)await H();else{const it=await M();f(I,ft=>({...ft,...it}))}}if(!rt){const{loaderPromise:nt,loadPromise:pt}=this.getMatch(I);nt==null||nt.resolve(),pt==null||pt.resolve()}return f(I,nt=>({...nt,isFetching:rt?nt.isFetching:!1,loaderPromise:rt?nt.loaderPromise:void 0,invalid:!1})),this.getMatch(I)})())}),await Promise.all(Z),S()}catch(q){T(q)}})()}),await p()}catch(S){if(gn(S)||vn(S))throw vn(S)&&!l&&await p(),S}return s},this.invalidate=r=>{const s=l=>{var c;return((c=r==null?void 0:r.filter)==null?void 0:c.call(r,l))??!0?{...l,invalid:!0,...l.status==="error"?{status:"pending",error:void 0}:{}}:l};return this.__store.setState(l=>{var c;return{...l,matches:l.matches.map(s),cachedMatches:l.cachedMatches.map(s),pendingMatches:(c=l.pendingMatches)==null?void 0:c.map(s)}}),this.shouldViewTransition=!1,this.load({sync:r==null?void 0:r.sync})},this.resolveRedirect=r=>(r.options.href||(r.options.href=this.buildLocation(r.options).href,r.headers.set("Location",r.options.href)),r.headers.get("Location")||r.headers.set("Location",r.options.href),r),this.clearCache=r=>{const s=r==null?void 0:r.filter;s!==void 0?this.__store.setState(l=>({...l,cachedMatches:l.cachedMatches.filter(c=>!s(c))})):this.__store.setState(l=>({...l,cachedMatches:[]}))},this.clearExpiredCache=()=>{const r=s=>{const l=this.looseRoutesById[s.routeId];if(!l.options.loader)return!0;const c=(s.preload?l.options.preloadGcTime??this.options.defaultPreloadGcTime:l.options.gcTime??this.options.defaultGcTime)??5*60*1e3;return!(s.status!=="error"&&Date.now()-s.updatedAt<c)};this.clearCache({filter:r})},this.loadRouteChunk=r=>(r._lazyPromise===void 0&&(r.lazyFn?r._lazyPromise=r.lazyFn().then(s=>{const{id:l,...c}=s.options;Object.assign(r.options,c)}):r._lazyPromise=Promise.resolve()),r._componentsPromise===void 0&&(r._componentsPromise=r._lazyPromise.then(()=>Promise.all(y2.map(async s=>{const l=r.options[s];l!=null&&l.preload&&await l.preload()})))),r._componentsPromise),this.preloadRoute=async r=>{const s=this.buildLocation(r);let l=this.matchRoutes(s,{throwOnError:!0,preload:!0,dest:r});const c=new Set([...this.state.matches,...this.state.pendingMatches??[]].map(h=>h.id)),f=new Set([...c,...this.state.cachedMatches.map(h=>h.id)]);l1(()=>{l.forEach(h=>{f.has(h.id)||this.__store.setState(m=>({...m,cachedMatches:[...m.cachedMatches,h]}))})});try{return l=await this.loadMatches({matches:l,location:s,preload:!0,updateMatch:(h,m)=>{c.has(h)?l=l.map(y=>y.id===h?m(y):y):this.updateMatch(h,m)}}),l}catch(h){if(gn(h))return h.options.reloadDocument?void 0:await this.preloadRoute({...h.options,_fromLocation:s});vn(h)||console.error(h);return}},this.matchRoute=(r,s)=>{const l={...r,to:r.to?this.resolvePathWithBase(r.from||"",r.to):void 0,params:r.params||{},leaveParams:!0},c=this.buildLocation(l);if(s!=null&&s.pending&&this.state.status!=="pending")return!1;const h=((s==null?void 0:s.pending)===void 0?!this.state.isLoading:s.pending)?this.latestLocation:this.state.resolvedLocation||this.state.location,m=Op(this.basepath,h.pathname,{...s,to:c.pathname});return!m||r.params&&!Zi(m,r.params,{partial:!0})?!1:m&&((s==null?void 0:s.includeSearch)??!0)?Zi(h.search,c.search,{partial:!0})?m:!1:m},this._handleNotFound=(r,s,{updateMatch:l=this.updateMatch}={})=>{var c;const f=this.routesById[s.routeId??""]??this.routeTree,h={};for(const y of r)h[y.routeId]=y;!f.options.notFoundComponent&&((c=this.options)!=null&&c.defaultNotFoundComponent)&&(f.options.notFoundComponent=this.options.defaultNotFoundComponent),Kn(f.options.notFoundComponent);const m=h[f.id];Kn(m,"Could not find match for route: "+f.id),l(m.id,y=>({...y,status:"notFound",error:s,isFetching:!1})),s.routerCode==="BEFORE_LOAD"&&f.parentRoute&&(s.routeId=f.parentRoute.id,this._handleNotFound(r,s,{updateMatch:l}))},this.hasNotFoundMatch=()=>this.__store.state.matches.some(r=>r.status==="notFound"||r.globalNotFound),this.update({defaultPreloadDelay:50,defaultPendingMs:1e3,defaultPendingMinMs:500,context:void 0,...n,caseSensitive:n.caseSensitive??!1,notFoundMode:n.notFoundMode??"fuzzy",stringifySearch:n.stringifySearch??WC,parseSearch:n.parseSearch??JC}),typeof document<"u"&&(window.__TSR_ROUTER__=this)}get state(){return this.__store.state}get looseRoutesById(){return this.routesById}matchRoutesInternal(n,r){var s;const{foundRoute:l,matchedRoutes:c,routeParams:f}=this.getMatchedRoutes(n.pathname,(s=r==null?void 0:r.dest)==null?void 0:s.to);let h=!1;(l?l.path!=="/"&&f["**"]:fo(n.pathname))&&(this.options.notFoundRoute?c.push(this.options.notFoundRoute):h=!0);const m=(()=>{if(h){if(this.options.notFoundMode!=="root")for(let _=c.length-1;_>=0;_--){const S=c[_];if(S.children)return S.id}return aa}})(),y=c.map(_=>{var S;let T;const A=((S=_.options.params)==null?void 0:S.parse)??_.options.parseParams;if(A)try{const x=A(f);Object.assign(f,x)}catch(x){if(T=new i3(x.message,{cause:x}),r!=null&&r.throwOnError)throw T;return T}}),p=[],g=_=>(_==null?void 0:_.id)?_.context??this.options.context??{}:this.options.context??{};return c.forEach((_,S)=>{var T,A;const x=p[S-1],[L,j,q]=(()=>{const St=(x==null?void 0:x.search)??n.search,vt=(x==null?void 0:x._strictSearch)??{};try{const k=wp(_.options.validateSearch,{...St})??{};return[{...St,...k},{...vt,...k},void 0]}catch(k){let X=k;if(k instanceof Zf||(X=new Zf(k.message,{cause:k})),r!=null&&r.throwOnError)throw X;return[St,{},X]}})(),K=((A=(T=_.options).loaderDeps)==null?void 0:A.call(T,{search:L}))??"",Z=K?JSON.stringify(K):"",{usedParams:I,interpolatedPath:Q}=Jy({path:_.fullPath,params:f,decodeCharMap:this.pathParamsDecodeCharMap}),$=Jy({path:_.id,params:f,leaveWildcards:!0,decodeCharMap:this.pathParamsDecodeCharMap}).interpolatedPath+Z,F=this.getMatch($),W=this.state.matches.find(St=>St.routeId===_.id),rt=W?"stay":"enter";let nt;if(F)nt={...F,cause:rt,params:W?Pn(W.params,f):f,_strictParams:I,search:Pn(W?W.search:F.search,L),_strictSearch:j};else{const St=_.options.loader||_.options.beforeLoad||_.lazyFn||y1(_)?"pending":"success";nt={id:$,index:S,routeId:_.id,params:W?Pn(W.params,f):f,_strictParams:I,pathname:Oa([this.basepath,Q]),updatedAt:Date.now(),search:W?Pn(W.search,L):L,_strictSearch:j,searchError:void 0,status:St,isFetching:!1,error:void 0,paramsError:y[S],__routeContext:{},__beforeLoadContext:{},context:{},abortController:new AbortController,fetchCount:0,cause:rt,loaderDeps:W?Pn(W.loaderDeps,K):K,invalid:!1,preload:!1,links:void 0,scripts:void 0,headScripts:void 0,meta:void 0,staticData:_.options.staticData||{},loadPromise:Ii(),fullPath:_.fullPath}}r!=null&&r.preload||(nt.globalNotFound=m===_.id),nt.searchError=q;const pt=g(x);nt.context={...pt,...nt.__routeContext,...nt.__beforeLoadContext},p.push(nt)}),p.forEach((_,S)=>{var T,A;const x=this.looseRoutesById[_.routeId];if(!this.getMatch(_.id)&&(r==null?void 0:r._buildLocation)!==!0){const j=p[S-1],q=g(j),K={deps:_.loaderDeps,params:_.params,context:q,location:n,navigate:Z=>this.navigate({...Z,_fromLocation:n}),buildLocation:this.buildLocation,cause:_.cause,abortController:_.abortController,preload:!!_.preload,matches:p};_.__routeContext=((A=(T=x.options).context)==null?void 0:A.call(T,K))??{},_.context={...q,..._.__routeContext,..._.__beforeLoadContext}}}),p}}class Zf extends Error{}class i3 extends Error{}function o3(t){return{loadedAt:0,isLoading:!1,isTransitioning:!1,status:"idle",resolvedLocation:void 0,location:t,matches:[],pendingMatches:[],cachedMatches:[],statusCode:200}}function wp(t,n){if(t==null)return{};if("~standard"in t){const r=t["~standard"].validate(n);if(r instanceof Promise)throw new Zf("Async validation not supported");if(r.issues)throw new Zf(JSON.stringify(r.issues,void 0,2),{cause:r});return r.value}return"parse"in t?t.parse(n):typeof t=="function"?t(n):{}}const y2=["component","errorComponent","pendingComponent","notFoundComponent"];function y1(t){var n;for(const r of y2)if((n=t.options[r])!=null&&n.preload)return!0;return!1}function l3({routeTree:t,initRoute:n}){const r={},s={},l=m=>{m.forEach((y,p)=>{n==null||n(y,p);const g=r[y.id];if(Kn(!g,`Duplicate routes found with id: ${String(y.id)}`),r[y.id]=y,!y.isRoot&&y.path){const S=fo(y.fullPath);(!s[S]||y.fullPath.endsWith("/"))&&(s[S]=y)}const _=y.children;_!=null&&_.length&&l(_)})};l([t]);const c=[];Object.values(r).forEach((m,y)=>{var p;if(m.isRoot||!m.path)return;const g=Dg(m.fullPath),_=ho(g);for(;_.length>1&&((p=_[0])==null?void 0:p.value)==="/";)_.shift();const S=_.map(T=>T.value==="/"?.75:T.type==="param"&&T.prefixSegment&&T.suffixSegment?.55:T.type==="param"&&T.prefixSegment?.52:T.type==="param"&&T.suffixSegment?.51:T.type==="param"?.5:T.type==="wildcard"&&T.prefixSegment&&T.suffixSegment?.3:T.type==="wildcard"&&T.prefixSegment?.27:T.type==="wildcard"&&T.suffixSegment?.26:T.type==="wildcard"?.25:1);c.push({child:m,trimmed:g,parsed:_,index:y,scores:S})});const h=c.sort((m,y)=>{const p=Math.min(m.scores.length,y.scores.length);for(let g=0;g<p;g++)if(m.scores[g]!==y.scores[g])return y.scores[g]-m.scores[g];if(m.scores.length!==y.scores.length)return y.scores.length-m.scores.length;for(let g=0;g<p;g++)if(m.parsed[g].value!==y.parsed[g].value)return m.parsed[g].value>y.parsed[g].value?1:-1;return m.index-y.index}).map((m,y)=>(m.child.rank=y,m.child));return{routesById:r,routesByPath:s,flatRoutes:h}}function c3({pathname:t,routePathname:n,basepath:r,caseSensitive:s,routesByPath:l,routesById:c,flatRoutes:f}){let h={};const m=fo(t),y=S=>{var T;return Op(r,m,{to:S.fullPath,caseSensitive:((T=S.options)==null?void 0:T.caseSensitive)??s,fuzzy:!0})};let p=n!==void 0?l[n]:void 0;p?h=y(p):p=f.find(S=>{const T=y(S);return T?(h=T,!0):!1});let g=p||c[aa];const _=[g];for(;g.parentRoute;)g=g.parentRoute,_.unshift(g);return{matchedRoutes:_,routeParams:h,foundRoute:p}}function u3({search:t,dest:n,destRoutes:r,_includeValidateSearch:s}){const l=r.reduce((h,m)=>{var y;const p=[];if("search"in m.options)(y=m.options.search)!=null&&y.middlewares&&p.push(...m.options.search.middlewares);else if(m.options.preSearchFilters||m.options.postSearchFilters){const g=({search:_,next:S})=>{let T=_;"preSearchFilters"in m.options&&m.options.preSearchFilters&&(T=m.options.preSearchFilters.reduce((x,L)=>L(x),_));const A=S(T);return"postSearchFilters"in m.options&&m.options.postSearchFilters?m.options.postSearchFilters.reduce((x,L)=>L(x),A):A};p.push(g)}if(s&&m.options.validateSearch){const g=({search:_,next:S})=>{const T=S(_);try{return{...T,...wp(m.options.validateSearch,T)??{}}}catch{return T}};p.push(g)}return h.concat(p)},[])??[],c=({search:h})=>n.search?n.search===!0?h:Ts(n.search,h):{};l.push(c);const f=(h,m)=>{if(h>=l.length)return m;const y=l[h];return y({search:m,next:g=>f(h+1,g)})};return f(0,t)}const Sa=Symbol.for("TSR_DEFERRED_PROMISE");function f3(t,n){const r=t;return r[Sa]||(r[Sa]={status:"pending"},r.then(s=>{r[Sa].status="success",r[Sa].data=s}).catch(s=>{r[Sa].status="error",r[Sa].error={data:r3(s),__isServerError:!0}})),r}const d3="Error preloading route! ☝️";class p2{constructor(n){if(this.init=r=>{var s,l;this.originalIndex=r.originalIndex;const c=this.options,f=!(c!=null&&c.path)&&!(c!=null&&c.id);this.parentRoute=(l=(s=this.options).getParentRoute)==null?void 0:l.call(s),f?this._path=aa:this.parentRoute||Kn(!1);let h=f?aa:c==null?void 0:c.path;h&&h!=="/"&&(h=Dg(h));const m=(c==null?void 0:c.id)||h;let y=f?aa:Oa([this.parentRoute.id===aa?"":this.parentRoute.id,m]);h===aa&&(h="/"),y!==aa&&(y=Oa(["/",y]));const p=y===aa?"/":Oa([this.parentRoute.fullPath,h]);this._path=h,this._id=y,this._fullPath=p,this._to=p,this._ssr=(c==null?void 0:c.ssr)??r.defaultSsr??!0},this.clone=r=>{this._path=r._path,this._id=r._id,this._fullPath=r._fullPath,this._to=r._to,this._ssr=r._ssr,this.options.getParentRoute=r.options.getParentRoute,this.children=r.children},this.addChildren=r=>this._addFileChildren(r),this._addFileChildren=r=>(Array.isArray(r)&&(this.children=r),typeof r=="object"&&r!==null&&(this.children=Object.values(r)),this),this._addFileTypes=()=>this,this.updateLoader=r=>(Object.assign(this.options,r),this),this.update=r=>(Object.assign(this.options,r),this),this.lazy=r=>(this.lazyFn=r,this),this.options=n||{},this.isRoot=!(n!=null&&n.getParentRoute),n!=null&&n.id&&(n!=null&&n.path))throw new Error("Route cannot have both an 'id' and a 'path' option.")}get to(){return this._to}get id(){return this._id}get path(){return this._path}get fullPath(){return this._fullPath}get ssr(){return this._ssr}}class h3 extends p2{constructor(n){super(n)}}const an={stringify:t=>JSON.stringify(t,function(r,s){const l=this[r],c=Rf.find(f=>f.stringifyCondition(l));return c?c.stringify(l):s}),parse:t=>JSON.parse(t,function(r,s){const l=this[r];if(Ja(l)){const c=Rf.find(f=>f.parseCondition(l));if(c)return c.parse(l)}return s}),encode:t=>{if(Array.isArray(t))return t.map(r=>an.encode(r));if(Ja(t))return Object.fromEntries(Object.entries(t).map(([r,s])=>[r,an.encode(s)]));const n=Rf.find(r=>r.stringifyCondition(t));return n?n.stringify(t):t},decode:t=>{if(Ja(t)){const n=Rf.find(r=>r.parseCondition(t));if(n)return n.parse(t)}return Array.isArray(t)?t.map(n=>an.decode(n)):Ja(t)?Object.fromEntries(Object.entries(t).map(([n,r])=>[n,an.decode(r)])):t}},Yl=(t,n,r,s)=>({key:t,stringifyCondition:n,stringify:l=>({[`$${t}`]:r(l)}),parseCondition:l=>Object.hasOwn(l,`$${t}`),parse:l=>s(l[`$${t}`])}),Rf=[Yl("undefined",t=>t===void 0,()=>0,()=>{}),Yl("date",t=>t instanceof Date,t=>t.toISOString(),t=>new Date(t)),Yl("error",t=>t instanceof Error,t=>({...t,message:t.message,stack:void 0,cause:t.cause}),t=>Object.assign(new Error(t.message),t)),Yl("formData",t=>t instanceof FormData,t=>{const n={};return t.forEach((r,s)=>{const l=n[s];l!==void 0?Array.isArray(l)?l.push(r):n[s]=[l,r]:n[s]=r}),n},t=>{const n=new FormData;return Object.entries(t).forEach(([r,s])=>{Array.isArray(s)?s.forEach(l=>n.append(r,l)):n.append(r,s)}),n}),Yl("bigint",t=>typeof t=="bigint",t=>t.toString(),t=>BigInt(t))];async function m3(t){var n,r,s;Kn((n=window.__TSR_SSR__)==null?void 0:n.dehydrated);const{manifest:l,dehydratedData:c,lastMatchId:f}=an.parse(window.__TSR_SSR__.dehydrated);t.ssr={manifest:l,serializer:an},t.clientSsr={getStreamedValue:y=>{var p;if(t.isServer)return;const g=(p=window.__TSR_SSR__)==null?void 0:p.streamedValues[y];if(g)return g.parsed||(g.parsed=t.ssr.serializer.parse(g.value)),g.parsed}};const h=t.matchRoutes(t.state.location),m=Promise.all(h.map(y=>{const p=t.looseRoutesById[y.routeId];return t.loadRouteChunk(p)}));return h.forEach(y=>{var p;const g=window.__TSR_SSR__.matches.find(_=>_.id===y.id);if(g)return Object.assign(y,g),g.__beforeLoadContext&&(y.__beforeLoadContext=t.ssr.serializer.parse(g.__beforeLoadContext)),g.loaderData&&(y.loaderData=t.ssr.serializer.parse(g.loaderData)),g.error&&(y.error=t.ssr.serializer.parse(g.error)),(p=y.extracted)==null||p.forEach(_=>{Ap(y,["loaderData",..._.path],_.value)}),y}),t.__store.setState(y=>({...y,matches:h})),await((s=(r=t.options).hydrate)==null?void 0:s.call(r,c)),await Promise.all(t.state.matches.map(async y=>{var p,g,_,S,T,A;const x=t.looseRoutesById[y.routeId],L=t.state.matches[y.index-1],j=(L==null?void 0:L.context)??t.options.context??{},q={deps:y.loaderDeps,params:y.params,context:j,location:t.state.location,navigate:Q=>t.navigate({...Q,_fromLocation:t.state.location}),buildLocation:t.buildLocation,cause:y.cause,abortController:y.abortController,preload:!1,matches:h};y.__routeContext=((g=(p=x.options).context)==null?void 0:g.call(p,q))??{},y.context={...j,...y.__routeContext,...y.__beforeLoadContext};const K={matches:t.state.matches,match:y,params:y.params,loaderData:y.loaderData},Z=await((S=(_=x.options).head)==null?void 0:S.call(_,K)),I=await((A=(T=x.options).scripts)==null?void 0:A.call(T,K));y.meta=Z==null?void 0:Z.meta,y.links=Z==null?void 0:Z.links,y.headScripts=Z==null?void 0:Z.scripts,y.scripts=I})),h[h.length-1].id!==f?await Promise.all([m,t.load()]):m}function Ap(t,n,r){n.length===1&&(t[n[0]]=r);const[s,...l]=n;Array.isArray(t)?Ap(t[Number(s)],l,r):Ja(t)&&Ap(t[s],l,r)}const y3="modulepreload",p3=function(t){return"/"+t},p1={},rn=function(n,r,s){let l=Promise.resolve();if(r&&r.length>0){let f=function(y){return Promise.all(y.map(p=>Promise.resolve(p).then(g=>({status:"fulfilled",value:g}),g=>({status:"rejected",reason:g}))))};document.getElementsByTagName("link");const h=document.querySelector("meta[property=csp-nonce]"),m=(h==null?void 0:h.nonce)||(h==null?void 0:h.getAttribute("nonce"));l=f(r.map(y=>{if(y=p3(y),y in p1)return;p1[y]=!0;const p=y.endsWith(".css"),g=p?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${y}"]${g}`))return;const _=document.createElement("link");if(_.rel=p?"stylesheet":y3,p||(_.as="script"),_.crossOrigin="",_.href=y,m&&_.setAttribute("nonce",m),document.head.appendChild(_),p)return new Promise((S,T)=>{_.addEventListener("load",S),_.addEventListener("error",()=>T(new Error(`Unable to preload CSS for ${y}`)))})}))}function c(f){const h=new Event("vite:preloadError",{cancelable:!0});if(h.payload=f,window.dispatchEvent(h),!h.defaultPrevented)throw f}return l.then(f=>{for(const h of f||[])h.status==="rejected"&&c(h.reason);return n().catch(c)})};const g3=[];function oc(t,n){const r=n||t||{};return typeof r.method>"u"&&(r.method="GET"),{options:r,middleware:s=>oc(void 0,Object.assign(r,{middleware:s})),validator:s=>oc(void 0,Object.assign(r,{validator:s})),type:s=>oc(void 0,Object.assign(r,{type:s})),handler:(...s)=>{const[l,c]=s;Object.assign(r,{...l,extractedFn:l,serverFn:c});const f=[...r.middleware||[],R3(r)];return Object.assign(async h=>g1(f,"client",{...l,...r,data:h==null?void 0:h.data,headers:h==null?void 0:h.headers,signal:h==null?void 0:h.signal,context:{}}).then(m=>{if(r.response==="full")return m;if(m.error)throw m.error;return m.result}),{...l,__executeServer:async(h,m)=>{const y=h instanceof FormData?b3(h):h;y.type=typeof r.type=="function"?r.type(y):r.type;const p={...l,...y,signal:m},g=()=>g1(f,"server",p).then(_=>({result:_.result,error:_.error,context:_.sendContext}));if(p.type==="static"){let _;if(sa!=null&&sa.getItem&&(_=await sa.getItem(p)),_||(_=await g().then(S=>({ctx:S,error:null})).catch(S=>({ctx:void 0,error:S})),sa!=null&&sa.setItem&&await sa.setItem(p,_)),Kn(_),_.error)throw _.error;return _.ctx}return g()}})}}}async function g1(t,n,r){const s=S3([...g3,...t]),l=async c=>{const f=s.shift();if(!f)return c;f.options.validator&&(n!=="client"||f.options.validateClient)&&(c.data=await T3(f.options.validator,c.data));const h=n==="client"?f.options.client:f.options.server;return h?E3(h,c,async m=>l(m).catch(y=>{if(gn(y)||vn(y))return{...m,error:y};throw y})):l(c)};return l({...r,headers:r.headers||{},sendContext:r.sendContext||{},context:r.context||{}})}let sa;function v3(t){const n=sa;return sa=typeof t=="function"?t():t,()=>{sa=n}}async function _3(t){const n=new TextEncoder().encode(t),r=await crypto.subtle.digest("SHA-1",n);return Array.from(new Uint8Array(r)).map(c=>c.toString(16).padStart(2,"0")).join("")}v3(()=>{const t=async(s,l)=>`/__tsr/staticServerFnCache/${await _3(`${s.functionId}__${l}`)}.json`,n=s=>JSON.stringify(s??"",(f,h)=>h&&typeof h=="object"&&!Array.isArray(h)?Object.keys(h).sort().reduce((m,y)=>(m[y]=h[y],m),{}):h).replace(/[/\\?%*:|"<>]/g,"-").replace(/\s+/g,"_"),r=typeof document<"u"?new Map:null;return{getItem:async s=>{if(typeof document>"u"){const l=n(s.data),c=await t(s,l),f="/home/<USER>/Work/schedhold/schedhold-frontend/.output/public",{promises:h}=await rn(async()=>{const{promises:_}=await import("node:fs");return{promises:_}},[]),y=(await rn(()=>import("node:path"),[])).join(f,c),[p,g]=await h.readFile(y,"utf-8").then(_=>[an.parse(_),null]).catch(_=>[null,_]);if(g&&g.code!=="ENOENT")throw g;return p}},setItem:async(s,l)=>{const{promises:c}=await rn(async()=>{const{promises:g}=await import("node:fs");return{promises:g}},[]),f=await rn(()=>import("node:path"),[]),h=n(s.data),m=await t(s,h),p=f.join("/home/<USER>/Work/schedhold/schedhold-frontend/.output/public",m);await c.mkdir(f.dirname(p),{recursive:!0}),await c.writeFile(p,an.stringify(l))},fetchItem:async s=>{const l=n(s.data),c=await t(s,l);let f=r==null?void 0:r.get(c);return f||(f=await fetch(c,{method:"GET"}).then(h=>h.text()).then(h=>an.parse(h)),r==null||r.set(c,f)),f}}});function b3(t){const n=t.get("__TSR_CONTEXT");if(t.delete("__TSR_CONTEXT"),typeof n!="string")return{context:{},data:t};try{return{context:an.parse(n),data:t}}catch{return{data:t}}}function S3(t){const n=new Set,r=[],s=l=>{l.forEach(c=>{c.options.middleware&&s(c.options.middleware),n.has(c)||(n.add(c),r.push(c))})};return s(t),r}const E3=async(t,n,r)=>t({...n,next:async(s={})=>r({...n,...s,context:{...n.context,...s.context},sendContext:{...n.sendContext,...s.sendContext??{}},headers:AC(n.headers,s.headers),result:s.result!==void 0?s.result:n.response==="raw"?s:n.result,error:s.error??n.error})});function T3(t,n){if(t==null)return{};if("~standard"in t){const r=t["~standard"].validate(n);if(r instanceof Promise)throw new Error("Async validation not supported");if(r.issues)throw new Error(JSON.stringify(r.issues,void 0,2));return r.value}if("parse"in t)return t.parse(n);if(typeof t=="function")return t(n);throw new Error("Invalid validator type!")}function R3(t){return{_types:void 0,options:{validator:t.validator,validateClient:t.validateClient,client:async({next:n,sendContext:r,...s})=>{var l;const c={...s,context:r,type:typeof s.type=="function"?s.type(s):s.type};if(s.type==="static"&&typeof document<"u"){Kn(sa);const h=await sa.fetchItem(c);if(h){if(h.error)throw h.error;return n(h.ctx)}`${c.functionId}${JSON.stringify(c.data)}`}const f=await((l=t.extractedFn)==null?void 0:l.call(t,c));return n(f)},server:async({next:n,...r})=>{var s;const l=await((s=t.serverFn)==null?void 0:s.call(t,r));return n({...r,result:l})}}}}function O3({promise:t}){const n=f3(t);if(n[Sa].status==="pending")throw n;if(n[Sa].status==="error")throw n[Sa].error;return[n[Sa].data,n]}function M3(t){const n=tt.jsx(w3,{...t});return t.fallback?tt.jsx(et.Suspense,{fallback:t.fallback,children:n}):n}function w3(t){const[n]=O3(t);return t.children(n)}function kg(t){const n=t.errorComponent??kd;return tt.jsx(A3,{getResetKey:t.getResetKey,onCatch:t.onCatch,children:({error:r,reset:s})=>r?et.createElement(n,{error:r,reset:s}):t.children})}class A3 extends et.Component{constructor(){super(...arguments),this.state={error:null}}static getDerivedStateFromProps(n){return{resetKey:n.getResetKey()}}static getDerivedStateFromError(n){return{error:n}}reset(){this.setState({error:null})}componentDidUpdate(n,r){r.error&&r.resetKey!==this.state.resetKey&&this.reset()}componentDidCatch(n,r){this.props.onCatch&&this.props.onCatch(n,r)}render(){return this.props.children({error:this.state.resetKey!==this.props.getResetKey()?null:this.state.error,reset:()=>{this.reset()}})}}function kd({error:t}){const[n,r]=et.useState(!1);return tt.jsxs("div",{style:{padding:".5rem",maxWidth:"100%"},children:[tt.jsxs("div",{style:{display:"flex",alignItems:"center",gap:".5rem"},children:[tt.jsx("strong",{style:{fontSize:"1rem"},children:"Something went wrong!"}),tt.jsx("button",{style:{appearance:"none",fontSize:".6em",border:"1px solid currentColor",padding:".1rem .2rem",fontWeight:"bold",borderRadius:".25rem"},onClick:()=>r(s=>!s),children:n?"Hide Error":"Show Error"})]}),tt.jsx("div",{style:{height:".25rem"}}),n?tt.jsx("div",{children:tt.jsx("pre",{style:{fontSize:".7em",border:"1px solid red",borderRadius:".25rem",padding:".3rem",color:"red",overflow:"auto"},children:t.message?tt.jsx("code",{children:t.message}):null})}):null]})}function x3({children:t,fallback:n=null}){return C3()?tt.jsx(Gt.Fragment,{children:t}):tt.jsx(Gt.Fragment,{children:n})}function C3(){return Gt.useSyncExternalStore(D3,()=>!0,()=>!1)}function D3(){return()=>{}}var ep={exports:{}},np={},ap={exports:{}},rp={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var v1;function k3(){if(v1)return rp;v1=1;var t=jc();function n(g,_){return g===_&&(g!==0||1/g===1/_)||g!==g&&_!==_}var r=typeof Object.is=="function"?Object.is:n,s=t.useState,l=t.useEffect,c=t.useLayoutEffect,f=t.useDebugValue;function h(g,_){var S=_(),T=s({inst:{value:S,getSnapshot:_}}),A=T[0].inst,x=T[1];return c(function(){A.value=S,A.getSnapshot=_,m(A)&&x({inst:A})},[g,S,_]),l(function(){return m(A)&&x({inst:A}),g(function(){m(A)&&x({inst:A})})},[g]),f(S),S}function m(g){var _=g.getSnapshot;g=g.value;try{var S=_();return!r(g,S)}catch{return!0}}function y(g,_){return _()}var p=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?y:h;return rp.useSyncExternalStore=t.useSyncExternalStore!==void 0?t.useSyncExternalStore:p,rp}var _1;function N3(){return _1||(_1=1,ap.exports=k3()),ap.exports}/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var b1;function z3(){if(b1)return np;b1=1;var t=jc(),n=N3();function r(y,p){return y===p&&(y!==0||1/y===1/p)||y!==y&&p!==p}var s=typeof Object.is=="function"?Object.is:r,l=n.useSyncExternalStore,c=t.useRef,f=t.useEffect,h=t.useMemo,m=t.useDebugValue;return np.useSyncExternalStoreWithSelector=function(y,p,g,_,S){var T=c(null);if(T.current===null){var A={hasValue:!1,value:null};T.current=A}else A=T.current;T=h(function(){function L(I){if(!j){if(j=!0,q=I,I=_(I),S!==void 0&&A.hasValue){var Q=A.value;if(S(Q,I))return K=Q}return K=I}if(Q=K,s(q,I))return Q;var $=_(I);return S!==void 0&&S(Q,$)?(q=I,Q):(q=I,K=$)}var j=!1,q,K,Z=g===void 0?null:g;return[function(){return L(p())},Z===null?void 0:function(){return L(Z())}]},[p,g,_,S]);var x=l(y,T[0],T[1]);return f(function(){A.hasValue=!0,A.value=x},[x]),m(x),x},np}var S1;function L3(){return S1||(S1=1,ep.exports=z3()),ep.exports}var $3=L3();function F3(t,n=r=>r){return $3.useSyncExternalStoreWithSelector(t.subscribe,()=>t.state,()=>t.state,n,j3)}function j3(t,n){if(Object.is(t,n))return!0;if(typeof t!="object"||t===null||typeof n!="object"||n===null)return!1;if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(const[s,l]of t)if(!n.has(s)||!Object.is(l,n.get(s)))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(const s of t)if(!n.has(s))return!1;return!0}const r=Object.keys(t);if(r.length!==Object.keys(n).length)return!1;for(let s=0;s<r.length;s++)if(!Object.prototype.hasOwnProperty.call(n,r[s])||!Object.is(t[r[s]],n[r[s]]))return!1;return!0}const sp=et.createContext(null);function g2(){return typeof document>"u"?sp:window.__TSR_ROUTER_CONTEXT__?window.__TSR_ROUTER_CONTEXT__:(window.__TSR_ROUTER_CONTEXT__=sp,sp)}function un(t){const n=et.useContext(g2());return t==null||t.warn,n}function Te(t){const n=un({warn:(t==null?void 0:t.router)===void 0}),r=(t==null?void 0:t.router)||n,s=et.useRef(void 0);return F3(r.__store,l=>{if(t!=null&&t.select){if(t.structuralSharing??r.options.defaultStructuralSharing){const c=Pn(s.current,t.select(l));return s.current=c,c}return t.select(l)}return l})}const Nd=et.createContext(void 0),U3=et.createContext(void 0);function Qn(t){const n=et.useContext(t.from?U3:Nd);return Te({select:s=>{const l=s.matches.find(c=>t.from?t.from===c.routeId:c.id===n);if(Kn(!((t.shouldThrow??!0)&&!l),`Could not find ${t.from?`an active match from "${t.from}"`:"a nearest match!"}`),l!==void 0)return t.select?t.select(l):l},structuralSharing:t.structuralSharing})}function Ng(t){return Qn({from:t.from,strict:t.strict,structuralSharing:t.structuralSharing,select:n=>t.select?t.select(n.loaderData):n.loaderData})}function zg(t){const{select:n,...r}=t;return Qn({...r,select:s=>n?n(s.loaderDeps):s.loaderDeps})}function Lg(t){return Qn({from:t.from,strict:t.strict,shouldThrow:t.shouldThrow,structuralSharing:t.structuralSharing,select:n=>t.select?t.select(n.params):n.params})}function $g(t){return Qn({from:t.from,strict:t.strict,shouldThrow:t.shouldThrow,structuralSharing:t.structuralSharing,select:n=>t.select?t.select(n.search):n.search})}function zd(t){const{navigate:n,state:r}=un(),s=Qn({strict:!1,select:l=>l.index});return et.useCallback(l=>{const c=l.from??(t==null?void 0:t.from)??r.matches[s].fullPath;return n({...l,from:c})},[t==null?void 0:t.from,n])}function Nq(t){const n=un(),r=zd(),s=et.useRef(null);return et.useEffect(()=>{s.current!==t&&(r(t),s.current=t)},[n,t,r]),null}var B3=l2();const sc=typeof window<"u"?et.useLayoutEffect:et.useEffect;function ip(t){const n=et.useRef({value:t,prev:null}),r=n.current.value;return t!==r&&(n.current={value:t,prev:r}),n.current.prev}function q3(t,n,r={},s={}){const l=et.useRef(typeof IntersectionObserver=="function"),c=et.useRef(null);return et.useEffect(()=>{if(!(!t.current||!l.current||s.disabled))return c.current=new IntersectionObserver(([f])=>{n(f)},r),c.current.observe(t.current),()=>{var f;(f=c.current)==null||f.disconnect()}},[n,r,s.disabled,t]),c.current}function H3(t){const n=et.useRef(null);return et.useImperativeHandle(t,()=>n.current,[]),n}function I3(t,n){const r=un(),[s,l]=et.useState(!1),c=et.useRef(!1),f=H3(n),{activeProps:h=()=>({className:"active"}),inactiveProps:m=()=>({}),activeOptions:y,to:p,preload:g,preloadDelay:_,hashScrollIntoView:S,replace:T,startTransition:A,resetScroll:x,viewTransition:L,children:j,target:q,disabled:K,style:Z,className:I,onClick:Q,onFocus:$,onMouseEnter:F,onMouseLeave:W,onTouchStart:rt,ignoreBlocker:nt,...pt}=t,{params:St,search:vt,hash:k,state:X,mask:lt,reloadDocument:At,...M}=pt,H=et.useMemo(()=>{try{return new URL(`${p}`),"external"}catch{}return"internal"},[p]),st=Te({select:Vt=>Vt.location.search,structuralSharing:!0}),at=Qn({strict:!1,select:Vt=>Vt.fullPath}),it=t.from??at;t={...t,from:it};const ft=et.useMemo(()=>r.buildLocation(t),[r,t,st]),ct=et.useMemo(()=>t.reloadDocument?!1:g??r.options.defaultPreload,[r.options.defaultPreload,g,t.reloadDocument]),Nt=_??r.options.defaultPreloadDelay??0,dt=Te({select:Vt=>{if(y!=null&&y.exact){if(!FC(Vt.location.pathname,ft.pathname,r.basepath))return!1}else{const Me=Kf(Vt.location.pathname,r.basepath).split("/");if(!Kf(ft.pathname,r.basepath).split("/").every((kh,Yr)=>kh===Me[Yr]))return!1}return((y==null?void 0:y.includeSearch)??!0)&&!Zi(Vt.location.search,ft.search,{partial:!(y!=null&&y.exact),ignoreUndefined:!(y!=null&&y.explicitUndefined)})?!1:y!=null&&y.includeHash?Vt.location.hash===ft.hash:!0}}),zt=et.useCallback(()=>{r.preloadRoute(t).catch(Vt=>{console.warn(Vt),console.warn(d3)})},[t,r]),ee=et.useCallback(Vt=>{Vt!=null&&Vt.isIntersecting&&zt()},[zt]);if(q3(f,ee,{rootMargin:"100px"},{disabled:!!K||ct!=="viewport"}),sc(()=>{c.current||!K&&ct==="render"&&(zt(),c.current=!0)},[K,zt,ct]),H==="external")return{...M,ref:f,type:H,href:p,...j&&{children:j},...q&&{target:q},...K&&{disabled:K},...Z&&{style:Z},...I&&{className:I},...Q&&{onClick:Q},...$&&{onFocus:$},...F&&{onMouseEnter:F},...W&&{onMouseLeave:W},...rt&&{onTouchStart:rt}};const me=Vt=>{if(!K&&!P3(Vt)&&!Vt.defaultPrevented&&(!q||q==="_self")&&Vt.button===0){Vt.preventDefault(),B3.flushSync(()=>{l(!0)});const Me=r.subscribe("onResolved",()=>{Me(),l(!1)});return r.navigate({...t,replace:T,resetScroll:x,hashScrollIntoView:S,startTransition:A,viewTransition:L,ignoreBlocker:nt})}},ue=Vt=>{K||ct&&zt()},fn=ue,Zn=Vt=>{if(K)return;const Me=Vt.target||{};if(ct){if(Me.preloadTimeout)return;Nt?Me.preloadTimeout=setTimeout(()=>{Me.preloadTimeout=null,zt()},Nt):zt()}},or=Vt=>{if(K)return;const Me=Vt.target||{};Me.preloadTimeout&&(clearTimeout(Me.preloadTimeout),Me.preloadTimeout=null)},kn=Vt=>Me=>{var ti;(ti=Me.persist)==null||ti.call(Me),Vt.filter(Boolean).forEach(ou=>{Me.defaultPrevented||ou(Me)})},$e=dt?Ts(h,{})??{}:{},Ws=dt?{}:Ts(m,{}),Yo=[I,$e.className,Ws.className].filter(Boolean).join(" "),Xo={...Z,...$e.style,...Ws.style};return{...M,...$e,...Ws,href:K?void 0:ft.maskedLocation?r.history.createHref(ft.maskedLocation.href):r.history.createHref(ft.href),ref:f,onClick:kn([Q,me]),onFocus:kn([$,ue]),onMouseEnter:kn([F,Zn]),onMouseLeave:kn([W,or]),onTouchStart:kn([rt,fn]),disabled:!!K,target:q,...Object.keys(Xo).length&&{style:Xo},...Yo&&{className:Yo},...K&&{role:"link","aria-disabled":!0},...dt&&{"data-status":"active","aria-current":"page"},...s&&{"data-transitioning":"transitioning"}}}const v2=et.forwardRef((t,n)=>{const{_asChild:r,...s}=t,{type:l,ref:c,...f}=I3(s,n),h=typeof s.children=="function"?s.children({isActive:f["data-status"]==="active"}):s.children;return typeof r>"u"&&delete f.disabled,et.createElement(r||"a",{...f,ref:c},h)});function P3(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}let V3=class extends p2{constructor(n){super(n),this.useMatch=r=>Qn({select:r==null?void 0:r.select,from:this.id,structuralSharing:r==null?void 0:r.structuralSharing}),this.useRouteContext=r=>Qn({...r,from:this.id,select:s=>r!=null&&r.select?r.select(s.context):s.context}),this.useSearch=r=>$g({select:r==null?void 0:r.select,structuralSharing:r==null?void 0:r.structuralSharing,from:this.id}),this.useParams=r=>Lg({select:r==null?void 0:r.select,structuralSharing:r==null?void 0:r.structuralSharing,from:this.id}),this.useLoaderDeps=r=>zg({...r,from:this.id}),this.useLoaderData=r=>Ng({...r,from:this.id}),this.useNavigate=()=>zd({from:this.fullPath}),this.Link=Gt.forwardRef((r,s)=>tt.jsx(v2,{ref:s,from:this.fullPath,...r})),this.$$typeof=Symbol.for("react.memo")}};function G3(t){return new V3(t)}function K3(){return t=>Y3(t)}class Q3 extends h3{constructor(n){super(n),this.useMatch=r=>Qn({select:r==null?void 0:r.select,from:this.id,structuralSharing:r==null?void 0:r.structuralSharing}),this.useRouteContext=r=>Qn({...r,from:this.id,select:s=>r!=null&&r.select?r.select(s.context):s.context}),this.useSearch=r=>$g({select:r==null?void 0:r.select,structuralSharing:r==null?void 0:r.structuralSharing,from:this.id}),this.useParams=r=>Lg({select:r==null?void 0:r.select,structuralSharing:r==null?void 0:r.structuralSharing,from:this.id}),this.useLoaderDeps=r=>zg({...r,from:this.id}),this.useLoaderData=r=>Ng({...r,from:this.id}),this.useNavigate=()=>zd({from:this.fullPath}),this.Link=Gt.forwardRef((r,s)=>tt.jsx(v2,{ref:s,from:this.fullPath,...r})),this.$$typeof=Symbol.for("react.memo")}}function Y3(t){return new Q3(t)}function Dn(t){return typeof t=="object"?new E1(t,{silent:!0}).createRoute(t):new E1(t,{silent:!0}).createRoute}class E1{constructor(n,r){this.path=n,this.createRoute=s=>{this.silent;const l=G3(s);return l.isRoot=!1,l},this.silent=r==null?void 0:r.silent}}class T1{constructor(n){this.useMatch=r=>Qn({select:r==null?void 0:r.select,from:this.options.id,structuralSharing:r==null?void 0:r.structuralSharing}),this.useRouteContext=r=>Qn({from:this.options.id,select:s=>r!=null&&r.select?r.select(s.context):s.context}),this.useSearch=r=>$g({select:r==null?void 0:r.select,structuralSharing:r==null?void 0:r.structuralSharing,from:this.options.id}),this.useParams=r=>Lg({select:r==null?void 0:r.select,structuralSharing:r==null?void 0:r.structuralSharing,from:this.options.id}),this.useLoaderDeps=r=>zg({...r,from:this.options.id}),this.useLoaderData=r=>Ng({...r,from:this.options.id}),this.useNavigate=()=>{const r=un();return zd({from:r.routesById[this.options.id].fullPath})},this.options=n,this.$$typeof=Symbol.for("react.memo")}}function R1(t){return typeof t=="object"?new T1(t):n=>new T1({id:t,...n})}function X3(t){const n=Te({select:r=>`not-found-${r.location.pathname}-${r.status}`});return tt.jsx(kg,{getResetKey:()=>n,onCatch:(r,s)=>{var l;if(vn(r))(l=t.onCatch)==null||l.call(t,r,s);else throw r},errorComponent:({error:r})=>{var s;if(vn(r))return(s=t.fallback)==null?void 0:s.call(t,r);throw r},children:t.children})}function Z3(){return tt.jsx("p",{children:"Not Found"})}function Lf(t){return tt.jsx(tt.Fragment,{children:t.children})}function _2(t,n,r){return n.options.notFoundComponent?tt.jsx(n.options.notFoundComponent,{data:r}):t.options.defaultNotFoundComponent?tt.jsx(t.options.defaultNotFoundComponent,{data:r}):tt.jsx(Z3,{})}var op,O1;function J3(){if(O1)return op;O1=1;const t={},n=t.hasOwnProperty,r=($,F)=>{for(const W in $)n.call($,W)&&F(W,$[W])},s=($,F)=>(F&&r(F,(W,rt)=>{$[W]=rt}),$),l=($,F)=>{const W=$.length;let rt=-1;for(;++rt<W;)F($[rt])},c=$=>"\\u"+("0000"+$).slice(-4),f=($,F)=>{let W=$.toString(16);return F?W:W.toUpperCase()},h=t.toString,m=Array.isArray,y=$=>typeof Buffer=="function"&&Buffer.isBuffer($),p=$=>h.call($)=="[object Object]",g=$=>typeof $=="string"||h.call($)=="[object String]",_=$=>typeof $=="number"||h.call($)=="[object Number]",S=$=>typeof $=="bigint",T=$=>typeof $=="function",A=$=>h.call($)=="[object Map]",x=$=>h.call($)=="[object Set]",L={"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t"},j=/[\\\b\f\n\r\t]/,q=/[0-9]/,K=/[\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/,Z=/([\uD800-\uDBFF][\uDC00-\uDFFF])|([\uD800-\uDFFF])|(['"`])|[^]/g,I=/([\uD800-\uDBFF][\uDC00-\uDFFF])|([\uD800-\uDFFF])|(['"`])|[^ !#-&\(-\[\]-_a-~]/g,Q=($,F)=>{const W=()=>{X=k,++F.indentLevel,k=F.indent.repeat(F.indentLevel)},rt={escapeEverything:!1,minimal:!1,isScriptContext:!1,quotes:"single",wrap:!1,es6:!1,json:!1,compact:!0,lowercaseHex:!1,numbers:"decimal",indent:"	",indentLevel:0,__inline1__:!1,__inline2__:!1},nt=F&&F.json;nt&&(rt.quotes="double",rt.wrap=!0),F=s(rt,F),F.quotes!="single"&&F.quotes!="double"&&F.quotes!="backtick"&&(F.quotes="single");const pt=F.quotes=="double"?'"':F.quotes=="backtick"?"`":"'",St=F.compact,vt=F.lowercaseHex;let k=F.indent.repeat(F.indentLevel),X="";const lt=F.__inline1__,At=F.__inline2__,M=St?"":`
`;let H,st=!0;const at=F.numbers=="binary",it=F.numbers=="octal",ft=F.numbers=="decimal",ct=F.numbers=="hexadecimal";if(nt&&$&&T($.toJSON)&&($=$.toJSON()),!g($)){if(A($))return $.size==0?"new Map()":(St||(F.__inline1__=!0,F.__inline2__=!1),"new Map("+Q(Array.from($),F)+")");if(x($))return $.size==0?"new Set()":"new Set("+Q(Array.from($),F)+")";if(y($))return $.length==0?"Buffer.from([])":"Buffer.from("+Q(Array.from($),F)+")";if(m($))return H=[],F.wrap=!0,lt&&(F.__inline1__=!1,F.__inline2__=!0),At||W(),l($,dt=>{st=!1,At&&(F.__inline2__=!1),H.push((St||At?"":k)+Q(dt,F))}),st?"[]":At?"["+H.join(", ")+"]":"["+M+H.join(","+M)+M+(St?"":X)+"]";if(_($)||S($)){if(nt)return JSON.stringify(Number($));let dt;if(ft)dt=String($);else if(ct){let zt=$.toString(16);vt||(zt=zt.toUpperCase()),dt="0x"+zt}else at?dt="0b"+$.toString(2):it&&(dt="0o"+$.toString(8));return S($)?dt+"n":dt}else return S($)?nt?JSON.stringify(Number($)):$+"n":p($)?(H=[],F.wrap=!0,W(),r($,(dt,zt)=>{st=!1,H.push((St?"":k)+Q(dt,F)+":"+(St?"":" ")+Q(zt,F))}),st?"{}":"{"+M+H.join(","+M)+M+(St?"":X)+"}"):nt?JSON.stringify($)||"null":String($)}const Nt=F.escapeEverything?Z:I;return H=$.replace(Nt,(dt,zt,ee,me,ue,fn)=>{if(zt){if(F.minimal)return zt;const or=zt.charCodeAt(0),kn=zt.charCodeAt(1);if(F.es6){const $e=(or-55296)*1024+kn-56320+65536;return"\\u{"+f($e,vt)+"}"}return c(f(or,vt))+c(f(kn,vt))}if(ee)return c(f(ee.charCodeAt(0),vt));if(dt=="\0"&&!nt&&!q.test(fn.charAt(ue+1)))return"\\0";if(me)return me==pt||F.escapeEverything?"\\"+me:me;if(j.test(dt))return L[dt];if(F.minimal&&!K.test(dt))return dt;const Zn=f(dt.charCodeAt(0),vt);return nt||Zn.length>2?c(Zn):"\\x"+("00"+Zn).slice(-2)}),pt=="`"&&(H=H.replace(/\$\{/g,"\\${")),F.isScriptContext&&(H=H.replace(/<\/(script|style)/gi,"<\\/$1").replace(/<!--/g,nt?"\\u003C!--":"\\x3C!--")),F.wrap&&(H=pt+H+pt),H};return Q.version="3.0.2",op=Q,op}J3();function W3({children:t,log:n}){return typeof document<"u"?null:tt.jsx("script",{className:"tsr-once",dangerouslySetInnerHTML:{__html:[t,"",'if (typeof __TSR_SSR__ !== "undefined") __TSR_SSR__.cleanScripts()'].filter(Boolean).join(`
`)}})}function tD(){const t=un(),r=(t.options.getScrollRestorationKey||Mp)(t.latestLocation),s=r!==Mp(t.latestLocation)?r:null;return!t.isScrollRestoring||!t.isServer?null:tt.jsx(W3,{children:`(${h2.toString()})(${JSON.stringify(Yf)},${JSON.stringify(s)}, undefined, true)`,log:!1})}const b2=et.memo(function({matchId:n}){var r,s;const l=un(),c=Te({select:L=>{var j;return(j=L.matches.find(q=>q.id===n))==null?void 0:j.routeId}});Kn(c);const f=l.routesById[c],h=f.options.pendingComponent??l.options.defaultPendingComponent,m=h?tt.jsx(h,{}):null,y=f.options.errorComponent??l.options.defaultErrorComponent,p=f.options.onCatch??l.options.defaultOnCatch,g=f.isRoot?f.options.notFoundComponent??((r=l.options.notFoundRoute)==null?void 0:r.options.component):f.options.notFoundComponent,_=(!f.isRoot||f.options.wrapInSuspense)&&(f.options.wrapInSuspense??h??((s=f.options.errorComponent)==null?void 0:s.preload))?et.Suspense:Lf,S=y?kg:Lf,T=g?X3:Lf,A=Te({select:L=>L.loadedAt}),x=Te({select:L=>{var j;const q=L.matches.findIndex(K=>K.id===n);return(j=L.matches[q-1])==null?void 0:j.routeId}});return tt.jsxs(tt.Fragment,{children:[tt.jsx(Nd.Provider,{value:n,children:tt.jsx(_,{fallback:m,children:tt.jsx(S,{getResetKey:()=>A,errorComponent:y||kd,onCatch:(L,j)=>{if(vn(L))throw L;p==null||p(L,j)},children:tt.jsx(T,{fallback:L=>{if(!g||L.routeId&&L.routeId!==c||!L.routeId&&!f.isRoot)throw L;return et.createElement(g,L)},children:tt.jsx(nD,{matchId:n})})})})}),x===aa&&l.options.scrollRestoration?tt.jsxs(tt.Fragment,{children:[tt.jsx(eD,{}),tt.jsx(tD,{})]}):null]})});function eD(){const t=un(),n=et.useRef(void 0);return tt.jsx("script",{suppressHydrationWarning:!0,ref:r=>{r&&(n.current===void 0||n.current.href!==t.latestLocation.href)&&(t.emit({type:"onRendered",...Rs(t.state)}),n.current=t.latestLocation)}},t.latestLocation.state.key)}const nD=et.memo(function({matchId:n}){var r,s,l;const c=un(),{match:f,key:h,routeId:m}=Te({select:_=>{const S=_.matches.findIndex(q=>q.id===n),T=_.matches[S],A=T.routeId,x=c.routesById[A].options.remountDeps??c.options.defaultRemountDeps,L=x==null?void 0:x({routeId:A,loaderDeps:T.loaderDeps,params:T._strictParams,search:T._strictSearch});return{key:L?JSON.stringify(L):void 0,routeId:A,match:Rp(T,["id","status","error"])}},structuralSharing:!0}),y=c.routesById[m],p=et.useMemo(()=>{const _=y.options.component??c.options.defaultComponent;return _?tt.jsx(_,{},h):tt.jsx(Fg,{})},[h,y.options.component,c.options.defaultComponent]),g=(y.options.errorComponent??c.options.defaultErrorComponent)||kd;if(f.status==="notFound")return Kn(vn(f.error)),_2(c,y,f.error);if(f.status==="redirected")throw Kn(gn(f.error)),(r=c.getMatch(f.id))==null?void 0:r.loadPromise;if(f.status==="error"){if(c.isServer)return tt.jsx(g,{error:f.error,reset:void 0,info:{componentStack:""}});throw f.error}if(f.status==="pending"){const _=y.options.pendingMinMs??c.options.defaultPendingMinMs;if(_&&!((s=c.getMatch(f.id))!=null&&s.minPendingPromise)&&!c.isServer){const S=Ii();Promise.resolve().then(()=>{c.updateMatch(f.id,T=>({...T,minPendingPromise:S}))}),setTimeout(()=>{S.resolve(),c.updateMatch(f.id,T=>({...T,minPendingPromise:void 0}))},_)}throw(l=c.getMatch(f.id))==null?void 0:l.loadPromise}return p}),Fg=et.memo(function(){const n=un(),r=et.useContext(Nd),s=Te({select:y=>{var p;return(p=y.matches.find(g=>g.id===r))==null?void 0:p.routeId}}),l=n.routesById[s],c=Te({select:y=>{const g=y.matches.find(_=>_.id===r);return Kn(g),g.globalNotFound}}),f=Te({select:y=>{var p;const g=y.matches,_=g.findIndex(S=>S.id===r);return(p=g[_+1])==null?void 0:p.id}}),h=n.options.defaultPendingComponent?tt.jsx(n.options.defaultPendingComponent,{}):null;if(n.isShell)return tt.jsx(et.Suspense,{fallback:h,children:tt.jsx(aD,{})});if(c)return _2(n,l,void 0);if(!f)return null;const m=tt.jsx(b2,{matchId:f});return r===aa?tt.jsx(et.Suspense,{fallback:h,children:m}):m});function aD(){throw new Error("ShellBoundaryError")}function rD(t){return typeof(t==null?void 0:t.message)!="string"?!1:t.message.startsWith("Failed to fetch dynamically imported module")||t.message.startsWith("error loading dynamically imported module")||t.message.startsWith("Importing a module script failed")}function da(t,n,r){let s,l,c,f;const h=()=>typeof document>"u"&&(r==null?void 0:r())===!1?(l=()=>null,Promise.resolve()):(s||(s=t().then(y=>{s=void 0,l=y[n]}).catch(y=>{if(c=y,rD(c)&&c instanceof Error&&typeof window<"u"&&typeof sessionStorage<"u"){const p=`tanstack_router_reload:${c.message}`;sessionStorage.getItem(p)||(sessionStorage.setItem(p,"1"),f=!0)}})),s),m=function(p){if(f)throw window.location.reload(),new Promise(()=>{});if(c)throw c;if(!l)throw h();return(r==null?void 0:r())===!1?tt.jsx(x3,{fallback:tt.jsx(Fg,{}),children:et.createElement(l,p)}):et.createElement(l,p)};return m.preload=h,m}function sD(){const t=un(),n=et.useRef({router:t,mounted:!1}),r=Te({select:({isLoading:g})=>g}),[s,l]=et.useState(!1),c=Te({select:g=>g.matches.some(_=>_.status==="pending"),structuralSharing:!0}),f=ip(r),h=r||s||c,m=ip(h),y=r||c,p=ip(y);return t.isServer||(t.startTransition=g=>{l(!0),et.startTransition(()=>{g(),l(!1)})}),et.useEffect(()=>{const g=t.history.subscribe(t.load),_=t.buildLocation({to:t.latestLocation.pathname,search:!0,params:!0,hash:!0,state:!0,_includeValidateSearch:!0});return fo(t.latestLocation.href)!==fo(_.href)&&t.commitLocation({..._,replace:!0}),()=>{g()}},[t,t.history]),sc(()=>{if(typeof window<"u"&&t.clientSsr||n.current.router===t&&n.current.mounted)return;n.current={router:t,mounted:!0},(async()=>{try{await t.load()}catch(_){console.error(_)}})()},[t]),sc(()=>{f&&!r&&t.emit({type:"onLoad",...Rs(t.state)})},[f,t,r]),sc(()=>{p&&!y&&t.emit({type:"onBeforeRouteMount",...Rs(t.state)})},[y,p,t]),sc(()=>{m&&!h&&(t.emit({type:"onResolved",...Rs(t.state)}),t.__store.setState(g=>({...g,status:"idle",resolvedLocation:g.location})),XC(t))},[h,m,t]),null}function iD(){const t=un(),n=t.options.defaultPendingComponent?tt.jsx(t.options.defaultPendingComponent,{}):null,r=t.isServer||typeof document<"u"&&t.clientSsr?Lf:et.Suspense,s=tt.jsxs(r,{fallback:n,children:[tt.jsx(sD,{}),tt.jsx(oD,{})]});return t.options.InnerWrap?tt.jsx(t.options.InnerWrap,{children:s}):s}function oD(){const t=Te({select:r=>{var s;return(s=r.matches[0])==null?void 0:s.id}}),n=Te({select:r=>r.loadedAt});return tt.jsx(Nd.Provider,{value:t,children:tt.jsx(kg,{getResetKey:()=>n,errorComponent:kd,onCatch:r=>{r.message||r.toString()},children:t?tt.jsx(b2,{matchId:t}):null})})}const lD=t=>new cD(t);class cD extends s3{constructor(n){super(n)}}typeof globalThis<"u"?(globalThis.createFileRoute=Dn,globalThis.createLazyFileRoute=R1):typeof window<"u"&&(window.createFileRoute=Dn,window.createFileRoute=R1);function uD({router:t,children:n,...r}){Object.keys(r).length>0&&t.update({...t.options,...r,context:{...t.options.context,...r.context}});const s=g2(),l=tt.jsx(s.Provider,{value:t,children:n});return t.options.Wrap?tt.jsx(t.options.Wrap,{children:l}):l}function fD({router:t,...n}){return tt.jsx(uD,{router:t,...n,children:tt.jsx(iD,{})})}function S2({tag:t,attrs:n,children:r}){switch(t){case"title":return tt.jsx("title",{...n,suppressHydrationWarning:!0,children:r});case"meta":return tt.jsx("meta",{...n,suppressHydrationWarning:!0});case"link":return tt.jsx("link",{...n,suppressHydrationWarning:!0});case"style":return tt.jsx("style",{...n,dangerouslySetInnerHTML:{__html:r}});case"script":return n&&n.src?tt.jsx("script",{...n,suppressHydrationWarning:!0}):typeof r=="string"?tt.jsx("script",{...n,dangerouslySetInnerHTML:{__html:r},suppressHydrationWarning:!0}):null;default:return null}}const dD=()=>{const t=un(),n=Te({select:f=>f.matches.map(h=>h.meta).filter(Boolean)}),r=et.useMemo(()=>{const f=[],h={};let m;return[...n].reverse().forEach(y=>{[...y].reverse().forEach(p=>{if(p)if(p.title)m||(m={tag:"title",children:p.title});else{const g=p.name??p.property;if(g){if(h[g])return;h[g]=!0}f.push({tag:"meta",attrs:{...p}})}})}),m&&f.push(m),f.reverse(),f},[n]),s=Te({select:f=>{var h;const m=f.matches.map(g=>g.links).filter(Boolean).flat(1).map(g=>({tag:"link",attrs:{...g}})),y=(h=t.ssr)==null?void 0:h.manifest,p=f.matches.map(g=>{var _;return((_=y==null?void 0:y.routes[g.routeId])==null?void 0:_.assets)??[]}).filter(Boolean).flat(1).filter(g=>g.tag==="link").map(g=>({tag:"link",attrs:{...g.attrs,suppressHydrationWarning:!0}}));return[...m,...p]},structuralSharing:!0}),l=Te({select:f=>{const h=[];return f.matches.map(m=>t.looseRoutesById[m.routeId]).forEach(m=>{var y,p,g,_;return(_=(g=(p=(y=t.ssr)==null?void 0:y.manifest)==null?void 0:p.routes[m.id])==null?void 0:g.preloads)==null?void 0:_.filter(Boolean).forEach(S=>{h.push({tag:"link",attrs:{rel:"modulepreload",href:S}})})}),h},structuralSharing:!0}),c=Te({select:f=>f.matches.map(h=>h.headScripts).flat(1).filter(Boolean).map(({children:h,...m})=>({tag:"script",attrs:{...m},children:h})),structuralSharing:!0});return mD([...r,...l,...s,...c],f=>JSON.stringify(f))};function hD(){return dD().map(n=>et.createElement(S2,{...n,key:`tsr-meta-${JSON.stringify(n)}`}))}function mD(t,n){const r=new Set;return t.filter(s=>{const l=n(s);return r.has(l)?!1:(r.add(l),!0)})}const yD=()=>{const t=un(),n=Te({select:l=>{var c;const f=[],h=(c=t.ssr)==null?void 0:c.manifest;return h?(l.matches.map(m=>t.looseRoutesById[m.routeId]).forEach(m=>{var y,p;return(p=(y=h.routes[m.id])==null?void 0:y.assets)==null?void 0:p.filter(g=>g.tag==="script").forEach(g=>{f.push({tag:"script",attrs:g.attrs,children:g.children})})}),f):[]},structuralSharing:!0}),{scripts:r}=Te({select:l=>({scripts:l.matches.map(c=>c.scripts).flat(1).filter(Boolean).map(({children:c,...f})=>({tag:"script",attrs:{...f,suppressHydrationWarning:!0},children:c}))}),structuralSharing:!0}),s=[...r,...n];return tt.jsx(tt.Fragment,{children:s.map((l,c)=>et.createElement(S2,{...l,key:`tsr-scripts-${l.tag}-${c}`}))})};let Of;function pD(t){return Of||(t.router.state.matches.length?Of=Promise.resolve():Of=m3(t.router)),tt.jsx(M3,{promise:Of,children:()=>tt.jsx(fD,{router:t.router})})}var Ld=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},$d=typeof window>"u"||"Deno"in globalThis;function xn(){}function gD(t,n){return typeof t=="function"?t(n):t}function vD(t){return typeof t=="number"&&t>=0&&t!==1/0}function _D(t,n){return Math.max(t+(n||0)-Date.now(),0)}function xp(t,n){return typeof t=="function"?t(n):t}function bD(t,n){return typeof t=="function"?t(n):t}function M1(t,n){const{type:r="all",exact:s,fetchStatus:l,predicate:c,queryKey:f,stale:h}=t;if(f){if(s){if(n.queryHash!==jg(f,n.options))return!1}else if(!pc(n.queryKey,f))return!1}if(r!=="all"){const m=n.isActive();if(r==="active"&&!m||r==="inactive"&&m)return!1}return!(typeof h=="boolean"&&n.isStale()!==h||l&&l!==n.state.fetchStatus||c&&!c(n))}function w1(t,n){const{exact:r,status:s,predicate:l,mutationKey:c}=t;if(c){if(!n.options.mutationKey)return!1;if(r){if(ks(n.options.mutationKey)!==ks(c))return!1}else if(!pc(n.options.mutationKey,c))return!1}return!(s&&n.state.status!==s||l&&!l(n))}function jg(t,n){return((n==null?void 0:n.queryKeyHashFn)||ks)(t)}function ks(t){return JSON.stringify(t,(n,r)=>Cp(r)?Object.keys(r).sort().reduce((s,l)=>(s[l]=r[l],s),{}):r)}function pc(t,n){return t===n?!0:typeof t!=typeof n?!1:t&&n&&typeof t=="object"&&typeof n=="object"?Object.keys(n).every(r=>pc(t[r],n[r])):!1}function E2(t,n){if(t===n)return t;const r=A1(t)&&A1(n);if(r||Cp(t)&&Cp(n)){const s=r?t:Object.keys(t),l=s.length,c=r?n:Object.keys(n),f=c.length,h=r?[]:{},m=new Set(s);let y=0;for(let p=0;p<f;p++){const g=r?p:c[p];(!r&&m.has(g)||r)&&t[g]===void 0&&n[g]===void 0?(h[g]=void 0,y++):(h[g]=E2(t[g],n[g]),h[g]===t[g]&&t[g]!==void 0&&y++)}return l===f&&y===l?t:h}return n}function Lq(t,n){if(!n||Object.keys(t).length!==Object.keys(n).length)return!1;for(const r in t)if(t[r]!==n[r])return!1;return!0}function A1(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function Cp(t){if(!x1(t))return!1;const n=t.constructor;if(n===void 0)return!0;const r=n.prototype;return!(!x1(r)||!r.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(t)!==Object.prototype)}function x1(t){return Object.prototype.toString.call(t)==="[object Object]"}function SD(t){return new Promise(n=>{setTimeout(n,t)})}function ED(t,n,r){return typeof r.structuralSharing=="function"?r.structuralSharing(t,n):r.structuralSharing!==!1?E2(t,n):n}function TD(t,n,r=0){const s=[...t,n];return r&&s.length>r?s.slice(1):s}function RD(t,n,r=0){const s=[n,...t];return r&&s.length>r?s.slice(0,-1):s}var Ug=Symbol();function T2(t,n){return!t.queryFn&&(n!=null&&n.initialPromise)?()=>n.initialPromise:!t.queryFn||t.queryFn===Ug?()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`)):t.queryFn}function $q(t,n){return typeof t=="function"?t(...n):!!t}var vs,Lr,no,WE,OD=(WE=class extends Ld{constructor(){super();Ht(this,vs);Ht(this,Lr);Ht(this,no);wt(this,no,n=>{if(!$d&&window.addEventListener){const r=()=>n();return window.addEventListener("visibilitychange",r,!1),()=>{window.removeEventListener("visibilitychange",r)}}})}onSubscribe(){G(this,Lr)||this.setEventListener(G(this,no))}onUnsubscribe(){var n;this.hasListeners()||((n=G(this,Lr))==null||n.call(this),wt(this,Lr,void 0))}setEventListener(n){var r;wt(this,no,n),(r=G(this,Lr))==null||r.call(this),wt(this,Lr,n(s=>{typeof s=="boolean"?this.setFocused(s):this.onFocus()}))}setFocused(n){G(this,vs)!==n&&(wt(this,vs,n),this.onFocus())}onFocus(){const n=this.isFocused();this.listeners.forEach(r=>{r(n)})}isFocused(){var n;return typeof G(this,vs)=="boolean"?G(this,vs):((n=globalThis.document)==null?void 0:n.visibilityState)!=="hidden"}},vs=new WeakMap,Lr=new WeakMap,no=new WeakMap,WE),R2=new OD,ao,$r,ro,tT,MD=(tT=class extends Ld{constructor(){super();Ht(this,ao,!0);Ht(this,$r);Ht(this,ro);wt(this,ro,n=>{if(!$d&&window.addEventListener){const r=()=>n(!0),s=()=>n(!1);return window.addEventListener("online",r,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",r),window.removeEventListener("offline",s)}}})}onSubscribe(){G(this,$r)||this.setEventListener(G(this,ro))}onUnsubscribe(){var n;this.hasListeners()||((n=G(this,$r))==null||n.call(this),wt(this,$r,void 0))}setEventListener(n){var r;wt(this,ro,n),(r=G(this,$r))==null||r.call(this),wt(this,$r,n(this.setOnline.bind(this)))}setOnline(n){G(this,ao)!==n&&(wt(this,ao,n),this.listeners.forEach(s=>{s(n)}))}isOnline(){return G(this,ao)}},ao=new WeakMap,$r=new WeakMap,ro=new WeakMap,tT),Jf=new MD;function wD(){let t,n;const r=new Promise((l,c)=>{t=l,n=c});r.status="pending",r.catch(()=>{});function s(l){Object.assign(r,l),delete r.resolve,delete r.reject}return r.resolve=l=>{s({status:"fulfilled",value:l}),t(l)},r.reject=l=>{s({status:"rejected",reason:l}),n(l)},r}function AD(t){var r;let n;if((r=t.then(s=>(n=s,s),xn))==null||r.catch(xn),n!==void 0)return{data:n}}function xD(t){return Math.min(1e3*2**t,3e4)}function O2(t){return(t??"online")==="online"?Jf.isOnline():!0}var M2=class extends Error{constructor(t){super("CancelledError"),this.revert=t==null?void 0:t.revert,this.silent=t==null?void 0:t.silent}};function lp(t){return t instanceof M2}function w2(t){let n=!1,r=0,s=!1,l;const c=wD(),f=A=>{var x;s||(_(new M2(A)),(x=t.abort)==null||x.call(t))},h=()=>{n=!0},m=()=>{n=!1},y=()=>R2.isFocused()&&(t.networkMode==="always"||Jf.isOnline())&&t.canRun(),p=()=>O2(t.networkMode)&&t.canRun(),g=A=>{var x;s||(s=!0,(x=t.onSuccess)==null||x.call(t,A),l==null||l(),c.resolve(A))},_=A=>{var x;s||(s=!0,(x=t.onError)==null||x.call(t,A),l==null||l(),c.reject(A))},S=()=>new Promise(A=>{var x;l=L=>{(s||y())&&A(L)},(x=t.onPause)==null||x.call(t)}).then(()=>{var A;l=void 0,s||(A=t.onContinue)==null||A.call(t)}),T=()=>{if(s)return;let A;const x=r===0?t.initialPromise:void 0;try{A=x??t.fn()}catch(L){A=Promise.reject(L)}Promise.resolve(A).then(g).catch(L=>{var I;if(s)return;const j=t.retry??($d?0:3),q=t.retryDelay??xD,K=typeof q=="function"?q(r,L):q,Z=j===!0||typeof j=="number"&&r<j||typeof j=="function"&&j(r,L);if(n||!Z){_(L);return}r++,(I=t.onFail)==null||I.call(t,r,L),SD(K).then(()=>y()?void 0:S()).then(()=>{n?_(L):T()})})};return{promise:c,cancel:f,continue:()=>(l==null||l(),c),cancelRetry:h,continueRetry:m,canStart:p,start:()=>(p()?T():S().then(T),c)}}var CD=t=>setTimeout(t,0);function DD(){let t=[],n=0,r=h=>{h()},s=h=>{h()},l=CD;const c=h=>{n?t.push(h):l(()=>{r(h)})},f=()=>{const h=t;t=[],h.length&&l(()=>{s(()=>{h.forEach(m=>{r(m)})})})};return{batch:h=>{let m;n++;try{m=h()}finally{n--,n||f()}return m},batchCalls:h=>(...m)=>{c(()=>{h(...m)})},schedule:c,setNotifyFunction:h=>{r=h},setBatchNotifyFunction:h=>{s=h},setScheduler:h=>{l=h}}}var nn=DD(),_s,eT,A2=(eT=class{constructor(){Ht(this,_s)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),vD(this.gcTime)&&wt(this,_s,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??($d?1/0:5*60*1e3))}clearGcTimeout(){G(this,_s)&&(clearTimeout(G(this,_s)),wt(this,_s,void 0))}},_s=new WeakMap,eT),so,io,Vn,bs,Je,Lc,Ss,ea,Xa,nT,kD=(nT=class extends A2{constructor(n){super();Ht(this,ea);Ht(this,so);Ht(this,io);Ht(this,Vn);Ht(this,bs);Ht(this,Je);Ht(this,Lc);Ht(this,Ss);wt(this,Ss,!1),wt(this,Lc,n.defaultOptions),this.setOptions(n.options),this.observers=[],wt(this,bs,n.client),wt(this,Vn,G(this,bs).getQueryCache()),this.queryKey=n.queryKey,this.queryHash=n.queryHash,wt(this,so,zD(this.options)),this.state=n.state??G(this,so),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var n;return(n=G(this,Je))==null?void 0:n.promise}setOptions(n){this.options={...G(this,Lc),...n},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&G(this,Vn).remove(this)}setData(n,r){const s=ED(this.state.data,n,this.options);return Xe(this,ea,Xa).call(this,{data:s,type:"success",dataUpdatedAt:r==null?void 0:r.updatedAt,manual:r==null?void 0:r.manual}),s}setState(n,r){Xe(this,ea,Xa).call(this,{type:"setState",state:n,setStateOptions:r})}cancel(n){var s,l;const r=(s=G(this,Je))==null?void 0:s.promise;return(l=G(this,Je))==null||l.cancel(n),r?r.then(xn).catch(xn):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(G(this,so))}isActive(){return this.observers.some(n=>bD(n.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Ug||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(n=>xp(n.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(n=>n.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(n=0){return this.state.data===void 0?!0:n==="static"?!1:this.state.isInvalidated?!0:!_D(this.state.dataUpdatedAt,n)}onFocus(){var r;const n=this.observers.find(s=>s.shouldFetchOnWindowFocus());n==null||n.refetch({cancelRefetch:!1}),(r=G(this,Je))==null||r.continue()}onOnline(){var r;const n=this.observers.find(s=>s.shouldFetchOnReconnect());n==null||n.refetch({cancelRefetch:!1}),(r=G(this,Je))==null||r.continue()}addObserver(n){this.observers.includes(n)||(this.observers.push(n),this.clearGcTimeout(),G(this,Vn).notify({type:"observerAdded",query:this,observer:n}))}removeObserver(n){this.observers.includes(n)&&(this.observers=this.observers.filter(r=>r!==n),this.observers.length||(G(this,Je)&&(G(this,Ss)?G(this,Je).cancel({revert:!0}):G(this,Je).cancelRetry()),this.scheduleGc()),G(this,Vn).notify({type:"observerRemoved",query:this,observer:n}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||Xe(this,ea,Xa).call(this,{type:"invalidate"})}fetch(n,r){var y,p,g;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(r!=null&&r.cancelRefetch))this.cancel({silent:!0});else if(G(this,Je))return G(this,Je).continueRetry(),G(this,Je).promise}if(n&&this.setOptions(n),!this.options.queryFn){const _=this.observers.find(S=>S.options.queryFn);_&&this.setOptions(_.options)}const s=new AbortController,l=_=>{Object.defineProperty(_,"signal",{enumerable:!0,get:()=>(wt(this,Ss,!0),s.signal)})},c=()=>{const _=T2(this.options,r),T=(()=>{const A={client:G(this,bs),queryKey:this.queryKey,meta:this.meta};return l(A),A})();return wt(this,Ss,!1),this.options.persister?this.options.persister(_,T,this):_(T)},h=(()=>{const _={fetchOptions:r,options:this.options,queryKey:this.queryKey,client:G(this,bs),state:this.state,fetchFn:c};return l(_),_})();(y=this.options.behavior)==null||y.onFetch(h,this),wt(this,io,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((p=h.fetchOptions)==null?void 0:p.meta))&&Xe(this,ea,Xa).call(this,{type:"fetch",meta:(g=h.fetchOptions)==null?void 0:g.meta});const m=_=>{var S,T,A,x;lp(_)&&_.silent||Xe(this,ea,Xa).call(this,{type:"error",error:_}),lp(_)||((T=(S=G(this,Vn).config).onError)==null||T.call(S,_,this),(x=(A=G(this,Vn).config).onSettled)==null||x.call(A,this.state.data,_,this)),this.scheduleGc()};return wt(this,Je,w2({initialPromise:r==null?void 0:r.initialPromise,fn:h.fetchFn,abort:s.abort.bind(s),onSuccess:_=>{var S,T,A,x;if(_===void 0){m(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(_)}catch(L){m(L);return}(T=(S=G(this,Vn).config).onSuccess)==null||T.call(S,_,this),(x=(A=G(this,Vn).config).onSettled)==null||x.call(A,_,this.state.error,this),this.scheduleGc()},onError:m,onFail:(_,S)=>{Xe(this,ea,Xa).call(this,{type:"failed",failureCount:_,error:S})},onPause:()=>{Xe(this,ea,Xa).call(this,{type:"pause"})},onContinue:()=>{Xe(this,ea,Xa).call(this,{type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay,networkMode:h.options.networkMode,canRun:()=>!0})),G(this,Je).start()}},so=new WeakMap,io=new WeakMap,Vn=new WeakMap,bs=new WeakMap,Je=new WeakMap,Lc=new WeakMap,Ss=new WeakMap,ea=new WeakSet,Xa=function(n){const r=s=>{switch(n.type){case"failed":return{...s,fetchFailureCount:n.failureCount,fetchFailureReason:n.error};case"pause":return{...s,fetchStatus:"paused"};case"continue":return{...s,fetchStatus:"fetching"};case"fetch":return{...s,...ND(s.data,this.options),fetchMeta:n.meta??null};case"success":return{...s,data:n.data,dataUpdateCount:s.dataUpdateCount+1,dataUpdatedAt:n.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!n.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const l=n.error;return lp(l)&&l.revert&&G(this,io)?{...G(this,io),fetchStatus:"idle"}:{...s,error:l,errorUpdateCount:s.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:s.fetchFailureCount+1,fetchFailureReason:l,fetchStatus:"idle",status:"error"};case"invalidate":return{...s,isInvalidated:!0};case"setState":return{...s,...n.state}}};this.state=r(this.state),nn.batch(()=>{this.observers.forEach(s=>{s.onQueryUpdate()}),G(this,Vn).notify({query:this,type:"updated",action:n})})},nT);function ND(t,n){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:O2(n.networkMode)?"fetching":"paused",...t===void 0&&{error:null,status:"pending"}}}function zD(t){const n=typeof t.initialData=="function"?t.initialData():t.initialData,r=n!==void 0,s=r?typeof t.initialDataUpdatedAt=="function"?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:n,dataUpdateCount:0,dataUpdatedAt:r?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}var va,aT,LD=(aT=class extends Ld{constructor(n={}){super();Ht(this,va);this.config=n,wt(this,va,new Map)}build(n,r,s){const l=r.queryKey,c=r.queryHash??jg(l,r);let f=this.get(c);return f||(f=new kD({client:n,queryKey:l,queryHash:c,options:n.defaultQueryOptions(r),state:s,defaultOptions:n.getQueryDefaults(l)}),this.add(f)),f}add(n){G(this,va).has(n.queryHash)||(G(this,va).set(n.queryHash,n),this.notify({type:"added",query:n}))}remove(n){const r=G(this,va).get(n.queryHash);r&&(n.destroy(),r===n&&G(this,va).delete(n.queryHash),this.notify({type:"removed",query:n}))}clear(){nn.batch(()=>{this.getAll().forEach(n=>{this.remove(n)})})}get(n){return G(this,va).get(n)}getAll(){return[...G(this,va).values()]}find(n){const r={exact:!0,...n};return this.getAll().find(s=>M1(r,s))}findAll(n={}){const r=this.getAll();return Object.keys(n).length>0?r.filter(s=>M1(n,s)):r}notify(n){nn.batch(()=>{this.listeners.forEach(r=>{r(n)})})}onFocus(){nn.batch(()=>{this.getAll().forEach(n=>{n.onFocus()})})}onOnline(){nn.batch(()=>{this.getAll().forEach(n=>{n.onOnline()})})}},va=new WeakMap,aT),_a,en,Es,ba,kr,rT,$D=(rT=class extends A2{constructor(n){super();Ht(this,ba);Ht(this,_a);Ht(this,en);Ht(this,Es);this.mutationId=n.mutationId,wt(this,en,n.mutationCache),wt(this,_a,[]),this.state=n.state||FD(),this.setOptions(n.options),this.scheduleGc()}setOptions(n){this.options=n,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(n){G(this,_a).includes(n)||(G(this,_a).push(n),this.clearGcTimeout(),G(this,en).notify({type:"observerAdded",mutation:this,observer:n}))}removeObserver(n){wt(this,_a,G(this,_a).filter(r=>r!==n)),this.scheduleGc(),G(this,en).notify({type:"observerRemoved",mutation:this,observer:n})}optionalRemove(){G(this,_a).length||(this.state.status==="pending"?this.scheduleGc():G(this,en).remove(this))}continue(){var n;return((n=G(this,Es))==null?void 0:n.continue())??this.execute(this.state.variables)}async execute(n){var c,f,h,m,y,p,g,_,S,T,A,x,L,j,q,K,Z,I,Q,$;const r=()=>{Xe(this,ba,kr).call(this,{type:"continue"})};wt(this,Es,w2({fn:()=>this.options.mutationFn?this.options.mutationFn(n):Promise.reject(new Error("No mutationFn found")),onFail:(F,W)=>{Xe(this,ba,kr).call(this,{type:"failed",failureCount:F,error:W})},onPause:()=>{Xe(this,ba,kr).call(this,{type:"pause"})},onContinue:r,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>G(this,en).canRun(this)}));const s=this.state.status==="pending",l=!G(this,Es).canStart();try{if(s)r();else{Xe(this,ba,kr).call(this,{type:"pending",variables:n,isPaused:l}),await((f=(c=G(this,en).config).onMutate)==null?void 0:f.call(c,n,this));const W=await((m=(h=this.options).onMutate)==null?void 0:m.call(h,n));W!==this.state.context&&Xe(this,ba,kr).call(this,{type:"pending",context:W,variables:n,isPaused:l})}const F=await G(this,Es).start();return await((p=(y=G(this,en).config).onSuccess)==null?void 0:p.call(y,F,n,this.state.context,this)),await((_=(g=this.options).onSuccess)==null?void 0:_.call(g,F,n,this.state.context)),await((T=(S=G(this,en).config).onSettled)==null?void 0:T.call(S,F,null,this.state.variables,this.state.context,this)),await((x=(A=this.options).onSettled)==null?void 0:x.call(A,F,null,n,this.state.context)),Xe(this,ba,kr).call(this,{type:"success",data:F}),F}catch(F){try{throw await((j=(L=G(this,en).config).onError)==null?void 0:j.call(L,F,n,this.state.context,this)),await((K=(q=this.options).onError)==null?void 0:K.call(q,F,n,this.state.context)),await((I=(Z=G(this,en).config).onSettled)==null?void 0:I.call(Z,void 0,F,this.state.variables,this.state.context,this)),await(($=(Q=this.options).onSettled)==null?void 0:$.call(Q,void 0,F,n,this.state.context)),F}finally{Xe(this,ba,kr).call(this,{type:"error",error:F})}}finally{G(this,en).runNext(this)}}},_a=new WeakMap,en=new WeakMap,Es=new WeakMap,ba=new WeakSet,kr=function(n){const r=s=>{switch(n.type){case"failed":return{...s,failureCount:n.failureCount,failureReason:n.error};case"pause":return{...s,isPaused:!0};case"continue":return{...s,isPaused:!1};case"pending":return{...s,context:n.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:n.isPaused,status:"pending",variables:n.variables,submittedAt:Date.now()};case"success":return{...s,data:n.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...s,data:void 0,error:n.error,failureCount:s.failureCount+1,failureReason:n.error,isPaused:!1,status:"error"}}};this.state=r(this.state),nn.batch(()=>{G(this,_a).forEach(s=>{s.onMutationUpdate(n)}),G(this,en).notify({mutation:this,type:"updated",action:n})})},rT);function FD(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Za,na,$c,sT,jD=(sT=class extends Ld{constructor(n={}){super();Ht(this,Za);Ht(this,na);Ht(this,$c);this.config=n,wt(this,Za,new Set),wt(this,na,new Map),wt(this,$c,0)}build(n,r,s){const l=new $D({mutationCache:this,mutationId:++Ef(this,$c)._,options:n.defaultMutationOptions(r),state:s});return this.add(l),l}add(n){G(this,Za).add(n);const r=Mf(n);if(typeof r=="string"){const s=G(this,na).get(r);s?s.push(n):G(this,na).set(r,[n])}this.notify({type:"added",mutation:n})}remove(n){if(G(this,Za).delete(n)){const r=Mf(n);if(typeof r=="string"){const s=G(this,na).get(r);if(s)if(s.length>1){const l=s.indexOf(n);l!==-1&&s.splice(l,1)}else s[0]===n&&G(this,na).delete(r)}}this.notify({type:"removed",mutation:n})}canRun(n){const r=Mf(n);if(typeof r=="string"){const s=G(this,na).get(r),l=s==null?void 0:s.find(c=>c.state.status==="pending");return!l||l===n}else return!0}runNext(n){var s;const r=Mf(n);if(typeof r=="string"){const l=(s=G(this,na).get(r))==null?void 0:s.find(c=>c!==n&&c.state.isPaused);return(l==null?void 0:l.continue())??Promise.resolve()}else return Promise.resolve()}clear(){nn.batch(()=>{G(this,Za).forEach(n=>{this.notify({type:"removed",mutation:n})}),G(this,Za).clear(),G(this,na).clear()})}getAll(){return Array.from(G(this,Za))}find(n){const r={exact:!0,...n};return this.getAll().find(s=>w1(r,s))}findAll(n={}){return this.getAll().filter(r=>w1(n,r))}notify(n){nn.batch(()=>{this.listeners.forEach(r=>{r(n)})})}resumePausedMutations(){const n=this.getAll().filter(r=>r.state.isPaused);return nn.batch(()=>Promise.all(n.map(r=>r.continue().catch(xn))))}},Za=new WeakMap,na=new WeakMap,$c=new WeakMap,sT);function Mf(t){var n;return(n=t.options.scope)==null?void 0:n.id}function C1(t){return{onFetch:(n,r)=>{var p,g,_,S,T;const s=n.options,l=(_=(g=(p=n.fetchOptions)==null?void 0:p.meta)==null?void 0:g.fetchMore)==null?void 0:_.direction,c=((S=n.state.data)==null?void 0:S.pages)||[],f=((T=n.state.data)==null?void 0:T.pageParams)||[];let h={pages:[],pageParams:[]},m=0;const y=async()=>{let A=!1;const x=q=>{Object.defineProperty(q,"signal",{enumerable:!0,get:()=>(n.signal.aborted?A=!0:n.signal.addEventListener("abort",()=>{A=!0}),n.signal)})},L=T2(n.options,n.fetchOptions),j=async(q,K,Z)=>{if(A)return Promise.reject();if(K==null&&q.pages.length)return Promise.resolve(q);const Q=(()=>{const rt={client:n.client,queryKey:n.queryKey,pageParam:K,direction:Z?"backward":"forward",meta:n.options.meta};return x(rt),rt})(),$=await L(Q),{maxPages:F}=n.options,W=Z?RD:TD;return{pages:W(q.pages,$,F),pageParams:W(q.pageParams,K,F)}};if(l&&c.length){const q=l==="backward",K=q?UD:D1,Z={pages:c,pageParams:f},I=K(s,Z);h=await j(Z,I,q)}else{const q=t??c.length;do{const K=m===0?f[0]??s.initialPageParam:D1(s,h);if(m>0&&K==null)break;h=await j(h,K),m++}while(m<q)}return h};n.options.persister?n.fetchFn=()=>{var A,x;return(x=(A=n.options).persister)==null?void 0:x.call(A,y,{client:n.client,queryKey:n.queryKey,meta:n.options.meta,signal:n.signal},r)}:n.fetchFn=y}}}function D1(t,{pages:n,pageParams:r}){const s=n.length-1;return n.length>0?t.getNextPageParam(n[s],n,r[s],r):void 0}function UD(t,{pages:n,pageParams:r}){var s;return n.length>0?(s=t.getPreviousPageParam)==null?void 0:s.call(t,n[0],n,r[0],r):void 0}var ge,Fr,jr,oo,lo,Ur,co,uo,iT,BD=(iT=class{constructor(t={}){Ht(this,ge);Ht(this,Fr);Ht(this,jr);Ht(this,oo);Ht(this,lo);Ht(this,Ur);Ht(this,co);Ht(this,uo);wt(this,ge,t.queryCache||new LD),wt(this,Fr,t.mutationCache||new jD),wt(this,jr,t.defaultOptions||{}),wt(this,oo,new Map),wt(this,lo,new Map),wt(this,Ur,0)}mount(){Ef(this,Ur)._++,G(this,Ur)===1&&(wt(this,co,R2.subscribe(async t=>{t&&(await this.resumePausedMutations(),G(this,ge).onFocus())})),wt(this,uo,Jf.subscribe(async t=>{t&&(await this.resumePausedMutations(),G(this,ge).onOnline())})))}unmount(){var t,n;Ef(this,Ur)._--,G(this,Ur)===0&&((t=G(this,co))==null||t.call(this),wt(this,co,void 0),(n=G(this,uo))==null||n.call(this),wt(this,uo,void 0))}isFetching(t){return G(this,ge).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return G(this,Fr).findAll({...t,status:"pending"}).length}getQueryData(t){var r;const n=this.defaultQueryOptions({queryKey:t});return(r=G(this,ge).get(n.queryHash))==null?void 0:r.state.data}ensureQueryData(t){const n=this.defaultQueryOptions(t),r=G(this,ge).build(this,n),s=r.state.data;return s===void 0?this.fetchQuery(t):(t.revalidateIfStale&&r.isStaleByTime(xp(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(s))}getQueriesData(t){return G(this,ge).findAll(t).map(({queryKey:n,state:r})=>{const s=r.data;return[n,s]})}setQueryData(t,n,r){const s=this.defaultQueryOptions({queryKey:t}),l=G(this,ge).get(s.queryHash),c=l==null?void 0:l.state.data,f=gD(n,c);if(f!==void 0)return G(this,ge).build(this,s).setData(f,{...r,manual:!0})}setQueriesData(t,n,r){return nn.batch(()=>G(this,ge).findAll(t).map(({queryKey:s})=>[s,this.setQueryData(s,n,r)]))}getQueryState(t){var r;const n=this.defaultQueryOptions({queryKey:t});return(r=G(this,ge).get(n.queryHash))==null?void 0:r.state}removeQueries(t){const n=G(this,ge);nn.batch(()=>{n.findAll(t).forEach(r=>{n.remove(r)})})}resetQueries(t,n){const r=G(this,ge);return nn.batch(()=>(r.findAll(t).forEach(s=>{s.reset()}),this.refetchQueries({type:"active",...t},n)))}cancelQueries(t,n={}){const r={revert:!0,...n},s=nn.batch(()=>G(this,ge).findAll(t).map(l=>l.cancel(r)));return Promise.all(s).then(xn).catch(xn)}invalidateQueries(t,n={}){return nn.batch(()=>(G(this,ge).findAll(t).forEach(r=>{r.invalidate()}),(t==null?void 0:t.refetchType)==="none"?Promise.resolve():this.refetchQueries({...t,type:(t==null?void 0:t.refetchType)??(t==null?void 0:t.type)??"active"},n)))}refetchQueries(t,n={}){const r={...n,cancelRefetch:n.cancelRefetch??!0},s=nn.batch(()=>G(this,ge).findAll(t).filter(l=>!l.isDisabled()&&!l.isStatic()).map(l=>{let c=l.fetch(void 0,r);return r.throwOnError||(c=c.catch(xn)),l.state.fetchStatus==="paused"?Promise.resolve():c}));return Promise.all(s).then(xn)}fetchQuery(t){const n=this.defaultQueryOptions(t);n.retry===void 0&&(n.retry=!1);const r=G(this,ge).build(this,n);return r.isStaleByTime(xp(n.staleTime,r))?r.fetch(n):Promise.resolve(r.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(xn).catch(xn)}fetchInfiniteQuery(t){return t.behavior=C1(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(xn).catch(xn)}ensureInfiniteQueryData(t){return t.behavior=C1(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return Jf.isOnline()?G(this,Fr).resumePausedMutations():Promise.resolve()}getQueryCache(){return G(this,ge)}getMutationCache(){return G(this,Fr)}getDefaultOptions(){return G(this,jr)}setDefaultOptions(t){wt(this,jr,t)}setQueryDefaults(t,n){G(this,oo).set(ks(t),{queryKey:t,defaultOptions:n})}getQueryDefaults(t){const n=[...G(this,oo).values()],r={};return n.forEach(s=>{pc(t,s.queryKey)&&Object.assign(r,s.defaultOptions)}),r}setMutationDefaults(t,n){G(this,lo).set(ks(t),{mutationKey:t,defaultOptions:n})}getMutationDefaults(t){const n=[...G(this,lo).values()],r={};return n.forEach(s=>{pc(t,s.mutationKey)&&Object.assign(r,s.defaultOptions)}),r}defaultQueryOptions(t){if(t._defaulted)return t;const n={...G(this,jr).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return n.queryHash||(n.queryHash=jg(n.queryKey,n)),n.refetchOnReconnect===void 0&&(n.refetchOnReconnect=n.networkMode!=="always"),n.throwOnError===void 0&&(n.throwOnError=!!n.suspense),!n.networkMode&&n.persister&&(n.networkMode="offlineFirst"),n.queryFn===Ug&&(n.enabled=!1),n}defaultMutationOptions(t){return t!=null&&t._defaulted?t:{...G(this,jr).mutations,...(t==null?void 0:t.mutationKey)&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){G(this,ge).clear(),G(this,Fr).clear()}},ge=new WeakMap,Fr=new WeakMap,jr=new WeakMap,oo=new WeakMap,lo=new WeakMap,Ur=new WeakMap,co=new WeakMap,uo=new WeakMap,iT);function x2(t){return t}function qD(t){return{mutationKey:t.options.mutationKey,state:t.state,...t.options.scope&&{scope:t.options.scope},...t.meta&&{meta:t.meta}}}function HD(t,n,r){var s;return{dehydratedAt:Date.now(),state:{...t.state,...t.state.data!==void 0&&{data:n(t.state.data)}},queryKey:t.queryKey,queryHash:t.queryHash,...t.state.status==="pending"&&{promise:(s=t.promise)==null?void 0:s.then(n).catch(l=>r(l)?Promise.reject(new Error("redacted")):Promise.reject(l))},...t.meta&&{meta:t.meta}}}function ID(t){return t.state.isPaused}function PD(t){return t.state.status==="success"}function VD(t){return!0}function k1(t,n={}){var m,y,p,g;const r=n.shouldDehydrateMutation??((m=t.getDefaultOptions().dehydrate)==null?void 0:m.shouldDehydrateMutation)??ID,s=t.getMutationCache().getAll().flatMap(_=>r(_)?[qD(_)]:[]),l=n.shouldDehydrateQuery??((y=t.getDefaultOptions().dehydrate)==null?void 0:y.shouldDehydrateQuery)??PD,c=n.shouldRedactErrors??((p=t.getDefaultOptions().dehydrate)==null?void 0:p.shouldRedactErrors)??VD,f=n.serializeData??((g=t.getDefaultOptions().dehydrate)==null?void 0:g.serializeData)??x2,h=t.getQueryCache().getAll().flatMap(_=>l(_)?[HD(_,f,c)]:[]);return{mutations:s,queries:h}}function N1(t,n,r){var m;if(typeof n!="object"||n===null)return;const s=t.getMutationCache(),l=t.getQueryCache(),c=((m=t.getDefaultOptions().hydrate)==null?void 0:m.deserializeData)??x2,f=n.mutations||[],h=n.queries||[];f.forEach(({state:y,...p})=>{var g,_;s.build(t,{...(g=t.getDefaultOptions().hydrate)==null?void 0:g.mutations,...(_=r==null?void 0:r.defaultOptions)==null?void 0:_.mutations,...p},y)}),h.forEach(({queryKey:y,state:p,queryHash:g,meta:_,promise:S,dehydratedAt:T})=>{var Z,I;const A=S?AD(S):void 0,x=p.data===void 0?A==null?void 0:A.data:p.data,L=x===void 0?x:c(x);let j=l.get(g);const q=(j==null?void 0:j.state.status)==="pending",K=(j==null?void 0:j.state.fetchStatus)==="fetching";if(j){const Q=A&&T!==void 0&&T>j.state.dataUpdatedAt;if(p.dataUpdatedAt>j.state.dataUpdatedAt||Q){const{fetchStatus:$,...F}=p;j.setState({...F,data:L})}}else j=l.build(t,{...(Z=t.getDefaultOptions().hydrate)==null?void 0:Z.queries,...(I=r==null?void 0:r.defaultOptions)==null?void 0:I.queries,queryKey:y,queryHash:g,meta:_},{...p,data:L,fetchStatus:"idle",status:L!==void 0?"success":p.status});S&&!q&&!K&&(T===void 0||T>j.state.dataUpdatedAt)&&j.fetch(void 0,{initialPromise:Promise.resolve(S).then(c)})})}var C2=et.createContext(void 0),Fq=t=>{const n=et.useContext(C2);if(!n)throw new Error("No QueryClient set, use QueryClientProvider to set one");return n},GD=({client:t,children:n})=>(et.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),tt.jsx(C2.Provider,{value:t,children:n})),KD=function(){return null};const QD=function(){return null};function D2(t){var n,r,s="";if(typeof t=="string"||typeof t=="number")s+=t;else if(typeof t=="object")if(Array.isArray(t)){var l=t.length;for(n=0;n<l;n++)t[n]&&(r=D2(t[n]))&&(s&&(s+=" "),s+=r)}else for(r in t)t[r]&&(s&&(s+=" "),s+=r);return s}function Os(){for(var t,n,r=0,s="",l=arguments.length;r<l;r++)(t=arguments[r])&&(n=D2(t))&&(s&&(s+=" "),s+=n);return s}function YD(t){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=t:r.appendChild(document.createTextNode(t))}YD(`:root{--toastify-color-light: #fff;--toastify-color-dark: #121212;--toastify-color-info: #3498db;--toastify-color-success: #07bc0c;--toastify-color-warning: #f1c40f;--toastify-color-error: hsl(6, 78%, 57%);--toastify-color-transparent: rgba(255, 255, 255, .7);--toastify-icon-color-info: var(--toastify-color-info);--toastify-icon-color-success: var(--toastify-color-success);--toastify-icon-color-warning: var(--toastify-color-warning);--toastify-icon-color-error: var(--toastify-color-error);--toastify-container-width: fit-content;--toastify-toast-width: 320px;--toastify-toast-offset: 16px;--toastify-toast-top: max(var(--toastify-toast-offset), env(safe-area-inset-top));--toastify-toast-right: max(var(--toastify-toast-offset), env(safe-area-inset-right));--toastify-toast-left: max(var(--toastify-toast-offset), env(safe-area-inset-left));--toastify-toast-bottom: max(var(--toastify-toast-offset), env(safe-area-inset-bottom));--toastify-toast-background: #fff;--toastify-toast-padding: 14px;--toastify-toast-min-height: 64px;--toastify-toast-max-height: 800px;--toastify-toast-bd-radius: 6px;--toastify-toast-shadow: 0px 4px 12px rgba(0, 0, 0, .1);--toastify-font-family: sans-serif;--toastify-z-index: 9999;--toastify-text-color-light: #757575;--toastify-text-color-dark: #fff;--toastify-text-color-info: #fff;--toastify-text-color-success: #fff;--toastify-text-color-warning: #fff;--toastify-text-color-error: #fff;--toastify-spinner-color: #616161;--toastify-spinner-color-empty-area: #e0e0e0;--toastify-color-progress-light: linear-gradient(to right, #4cd964, #5ac8fa, #007aff, #34aadc, #5856d6, #ff2d55);--toastify-color-progress-dark: #bb86fc;--toastify-color-progress-info: var(--toastify-color-info);--toastify-color-progress-success: var(--toastify-color-success);--toastify-color-progress-warning: var(--toastify-color-warning);--toastify-color-progress-error: var(--toastify-color-error);--toastify-color-progress-bgo: .2}.Toastify__toast-container{z-index:var(--toastify-z-index);-webkit-transform:translate3d(0,0,var(--toastify-z-index));position:fixed;width:var(--toastify-container-width);box-sizing:border-box;color:#fff;display:flex;flex-direction:column}.Toastify__toast-container--top-left{top:var(--toastify-toast-top);left:var(--toastify-toast-left)}.Toastify__toast-container--top-center{top:var(--toastify-toast-top);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--top-right{top:var(--toastify-toast-top);right:var(--toastify-toast-right);align-items:end}.Toastify__toast-container--bottom-left{bottom:var(--toastify-toast-bottom);left:var(--toastify-toast-left)}.Toastify__toast-container--bottom-center{bottom:var(--toastify-toast-bottom);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--bottom-right{bottom:var(--toastify-toast-bottom);right:var(--toastify-toast-right);align-items:end}.Toastify__toast{--y: 0;position:relative;touch-action:none;width:var(--toastify-toast-width);min-height:var(--toastify-toast-min-height);box-sizing:border-box;margin-bottom:1rem;padding:var(--toastify-toast-padding);border-radius:var(--toastify-toast-bd-radius);box-shadow:var(--toastify-toast-shadow);max-height:var(--toastify-toast-max-height);font-family:var(--toastify-font-family);z-index:0;display:flex;flex:1 auto;align-items:center;word-break:break-word}@media only screen and (max-width: 480px){.Toastify__toast-container{width:100vw;left:env(safe-area-inset-left);margin:0}.Toastify__toast-container--top-left,.Toastify__toast-container--top-center,.Toastify__toast-container--top-right{top:env(safe-area-inset-top);transform:translate(0)}.Toastify__toast-container--bottom-left,.Toastify__toast-container--bottom-center,.Toastify__toast-container--bottom-right{bottom:env(safe-area-inset-bottom);transform:translate(0)}.Toastify__toast-container--rtl{right:env(safe-area-inset-right);left:initial}.Toastify__toast{--toastify-toast-width: 100%;margin-bottom:0;border-radius:0}}.Toastify__toast-container[data-stacked=true]{width:var(--toastify-toast-width)}.Toastify__toast--stacked{position:absolute;width:100%;transform:translate3d(0,var(--y),0) scale(var(--s));transition:transform .3s}.Toastify__toast--stacked[data-collapsed] .Toastify__toast-body,.Toastify__toast--stacked[data-collapsed] .Toastify__close-button{transition:opacity .1s}.Toastify__toast--stacked[data-collapsed=false]{overflow:visible}.Toastify__toast--stacked[data-collapsed=true]:not(:last-child)>*{opacity:0}.Toastify__toast--stacked:after{content:"";position:absolute;left:0;right:0;height:calc(var(--g) * 1px);bottom:100%}.Toastify__toast--stacked[data-pos=top]{top:0}.Toastify__toast--stacked[data-pos=bot]{bottom:0}.Toastify__toast--stacked[data-pos=bot].Toastify__toast--stacked:before{transform-origin:top}.Toastify__toast--stacked[data-pos=top].Toastify__toast--stacked:before{transform-origin:bottom}.Toastify__toast--stacked:before{content:"";position:absolute;left:0;right:0;bottom:0;height:100%;transform:scaleY(3);z-index:-1}.Toastify__toast--rtl{direction:rtl}.Toastify__toast--close-on-click{cursor:pointer}.Toastify__toast-icon{margin-inline-end:10px;width:22px;flex-shrink:0;display:flex}.Toastify--animate{animation-fill-mode:both;animation-duration:.5s}.Toastify--animate-icon{animation-fill-mode:both;animation-duration:.3s}.Toastify__toast-theme--dark{background:var(--toastify-color-dark);color:var(--toastify-text-color-dark)}.Toastify__toast-theme--light,.Toastify__toast-theme--colored.Toastify__toast--default{background:var(--toastify-color-light);color:var(--toastify-text-color-light)}.Toastify__toast-theme--colored.Toastify__toast--info{color:var(--toastify-text-color-info);background:var(--toastify-color-info)}.Toastify__toast-theme--colored.Toastify__toast--success{color:var(--toastify-text-color-success);background:var(--toastify-color-success)}.Toastify__toast-theme--colored.Toastify__toast--warning{color:var(--toastify-text-color-warning);background:var(--toastify-color-warning)}.Toastify__toast-theme--colored.Toastify__toast--error{color:var(--toastify-text-color-error);background:var(--toastify-color-error)}.Toastify__progress-bar-theme--light{background:var(--toastify-color-progress-light)}.Toastify__progress-bar-theme--dark{background:var(--toastify-color-progress-dark)}.Toastify__progress-bar--info{background:var(--toastify-color-progress-info)}.Toastify__progress-bar--success{background:var(--toastify-color-progress-success)}.Toastify__progress-bar--warning{background:var(--toastify-color-progress-warning)}.Toastify__progress-bar--error{background:var(--toastify-color-progress-error)}.Toastify__progress-bar-theme--colored.Toastify__progress-bar--info,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--success,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--warning,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--error{background:var(--toastify-color-transparent)}.Toastify__close-button{color:#fff;position:absolute;top:6px;right:6px;background:transparent;outline:none;border:none;padding:0;cursor:pointer;opacity:.7;transition:.3s ease;z-index:1}.Toastify__toast--rtl .Toastify__close-button{left:6px;right:unset}.Toastify__close-button--light{color:#000;opacity:.3}.Toastify__close-button>svg{fill:currentColor;height:16px;width:14px}.Toastify__close-button:hover,.Toastify__close-button:focus{opacity:1}@keyframes Toastify__trackProgress{0%{transform:scaleX(1)}to{transform:scaleX(0)}}.Toastify__progress-bar{position:absolute;bottom:0;left:0;width:100%;height:100%;z-index:1;opacity:.7;transform-origin:left}.Toastify__progress-bar--animated{animation:Toastify__trackProgress linear 1 forwards}.Toastify__progress-bar--controlled{transition:transform .2s}.Toastify__progress-bar--rtl{right:0;left:initial;transform-origin:right;border-bottom-left-radius:initial}.Toastify__progress-bar--wrp{position:absolute;overflow:hidden;bottom:0;left:0;width:100%;height:5px;border-bottom-left-radius:var(--toastify-toast-bd-radius);border-bottom-right-radius:var(--toastify-toast-bd-radius)}.Toastify__progress-bar--wrp[data-hidden=true]{opacity:0}.Toastify__progress-bar--bg{opacity:var(--toastify-color-progress-bgo);width:100%;height:100%}.Toastify__spinner{width:20px;height:20px;box-sizing:border-box;border:2px solid;border-radius:100%;border-color:var(--toastify-spinner-color-empty-area);border-right-color:var(--toastify-spinner-color);animation:Toastify__spin .65s linear infinite}@keyframes Toastify__bounceInRight{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0)}60%{opacity:1;transform:translate3d(-25px,0,0)}75%{transform:translate3d(10px,0,0)}90%{transform:translate3d(-5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutRight{20%{opacity:1;transform:translate3d(-20px,var(--y),0)}to{opacity:0;transform:translate3d(2000px,var(--y),0)}}@keyframes Toastify__bounceInLeft{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0)}60%{opacity:1;transform:translate3d(25px,0,0)}75%{transform:translate3d(-10px,0,0)}90%{transform:translate3d(5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutLeft{20%{opacity:1;transform:translate3d(20px,var(--y),0)}to{opacity:0;transform:translate3d(-2000px,var(--y),0)}}@keyframes Toastify__bounceInUp{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0)}60%{opacity:1;transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,10px,0)}90%{transform:translate3d(0,-5px,0)}to{transform:translateZ(0)}}@keyframes Toastify__bounceOutUp{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,-2000px,0)}}@keyframes Toastify__bounceInDown{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0)}60%{opacity:1;transform:translate3d(0,25px,0)}75%{transform:translate3d(0,-10px,0)}90%{transform:translate3d(0,5px,0)}to{transform:none}}@keyframes Toastify__bounceOutDown{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,2000px,0)}}.Toastify__bounce-enter--top-left,.Toastify__bounce-enter--bottom-left{animation-name:Toastify__bounceInLeft}.Toastify__bounce-enter--top-right,.Toastify__bounce-enter--bottom-right{animation-name:Toastify__bounceInRight}.Toastify__bounce-enter--top-center{animation-name:Toastify__bounceInDown}.Toastify__bounce-enter--bottom-center{animation-name:Toastify__bounceInUp}.Toastify__bounce-exit--top-left,.Toastify__bounce-exit--bottom-left{animation-name:Toastify__bounceOutLeft}.Toastify__bounce-exit--top-right,.Toastify__bounce-exit--bottom-right{animation-name:Toastify__bounceOutRight}.Toastify__bounce-exit--top-center{animation-name:Toastify__bounceOutUp}.Toastify__bounce-exit--bottom-center{animation-name:Toastify__bounceOutDown}@keyframes Toastify__zoomIn{0%{opacity:0;transform:scale3d(.3,.3,.3)}50%{opacity:1}}@keyframes Toastify__zoomOut{0%{opacity:1}50%{opacity:0;transform:translate3d(0,var(--y),0) scale3d(.3,.3,.3)}to{opacity:0}}.Toastify__zoom-enter{animation-name:Toastify__zoomIn}.Toastify__zoom-exit{animation-name:Toastify__zoomOut}@keyframes Toastify__flipIn{0%{transform:perspective(400px) rotateX(90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotateX(-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotateX(10deg);opacity:1}80%{transform:perspective(400px) rotateX(-5deg)}to{transform:perspective(400px)}}@keyframes Toastify__flipOut{0%{transform:translate3d(0,var(--y),0) perspective(400px)}30%{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(-20deg);opacity:1}to{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(90deg);opacity:0}}.Toastify__flip-enter{animation-name:Toastify__flipIn}.Toastify__flip-exit{animation-name:Toastify__flipOut}@keyframes Toastify__slideInRight{0%{transform:translate3d(110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInLeft{0%{transform:translate3d(-110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInUp{0%{transform:translate3d(0,110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInDown{0%{transform:translate3d(0,-110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideOutRight{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(110%,var(--y),0)}}@keyframes Toastify__slideOutLeft{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(-110%,var(--y),0)}}@keyframes Toastify__slideOutDown{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,500px,0)}}@keyframes Toastify__slideOutUp{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,-500px,0)}}.Toastify__slide-enter--top-left,.Toastify__slide-enter--bottom-left{animation-name:Toastify__slideInLeft}.Toastify__slide-enter--top-right,.Toastify__slide-enter--bottom-right{animation-name:Toastify__slideInRight}.Toastify__slide-enter--top-center{animation-name:Toastify__slideInDown}.Toastify__slide-enter--bottom-center{animation-name:Toastify__slideInUp}.Toastify__slide-exit--top-left,.Toastify__slide-exit--bottom-left{animation-name:Toastify__slideOutLeft;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-right,.Toastify__slide-exit--bottom-right{animation-name:Toastify__slideOutRight;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-center{animation-name:Toastify__slideOutUp;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--bottom-center{animation-name:Toastify__slideOutDown;animation-timing-function:ease-in;animation-duration:.3s}@keyframes Toastify__spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}
`);var Uc=t=>typeof t=="number"&&!isNaN(t),Ns=t=>typeof t=="string",nr=t=>typeof t=="function",XD=t=>Ns(t)||Uc(t),Dp=t=>Ns(t)||nr(t)?t:null,ZD=(t,n)=>t===!1||Uc(t)&&t>0?t:n,kp=t=>et.isValidElement(t)||Ns(t)||nr(t)||Uc(t);function JD(t,n,r=300){let{scrollHeight:s,style:l}=t;requestAnimationFrame(()=>{l.minHeight="initial",l.height=s+"px",l.transition=`all ${r}ms`,requestAnimationFrame(()=>{l.height="0",l.padding="0",l.margin="0",setTimeout(n,r)})})}function WD({enter:t,exit:n,appendPosition:r=!1,collapse:s=!0,collapseDuration:l=300}){return function({children:c,position:f,preventExitTransition:h,done:m,nodeRef:y,isIn:p,playToast:g}){let _=r?`${t}--${f}`:t,S=r?`${n}--${f}`:n,T=et.useRef(0);return et.useLayoutEffect(()=>{let A=y.current,x=_.split(" "),L=j=>{j.target===y.current&&(g(),A.removeEventListener("animationend",L),A.removeEventListener("animationcancel",L),T.current===0&&j.type!=="animationcancel"&&A.classList.remove(...x))};A.classList.add(...x),A.addEventListener("animationend",L),A.addEventListener("animationcancel",L)},[]),et.useEffect(()=>{let A=y.current,x=()=>{A.removeEventListener("animationend",x),s?JD(A,m,l):m()};p||(h?x():(T.current=1,A.className+=` ${S}`,A.addEventListener("animationend",x)))},[p]),Gt.createElement(Gt.Fragment,null,c)}}function z1(t,n){return{content:k2(t.content,t.props),containerId:t.props.containerId,id:t.props.toastId,theme:t.props.theme,type:t.props.type,data:t.props.data||{},isLoading:t.props.isLoading,icon:t.props.icon,reason:t.removalReason,status:n}}function k2(t,n,r=!1){return et.isValidElement(t)&&!Ns(t.type)?et.cloneElement(t,{closeToast:n.closeToast,toastProps:n,data:n.data,isPaused:r}):nr(t)?t({closeToast:n.closeToast,toastProps:n,data:n.data,isPaused:r}):t}function tk({closeToast:t,theme:n,ariaLabel:r="close"}){return Gt.createElement("button",{className:`Toastify__close-button Toastify__close-button--${n}`,type:"button",onClick:s=>{s.stopPropagation(),t(!0)},"aria-label":r},Gt.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},Gt.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}function ek({delay:t,isRunning:n,closeToast:r,type:s="default",hide:l,className:c,controlledProgress:f,progress:h,rtl:m,isIn:y,theme:p}){let g=l||f&&h===0,_={animationDuration:`${t}ms`,animationPlayState:n?"running":"paused"};f&&(_.transform=`scaleX(${h})`);let S=Os("Toastify__progress-bar",f?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${p}`,`Toastify__progress-bar--${s}`,{"Toastify__progress-bar--rtl":m}),T=nr(c)?c({rtl:m,type:s,defaultClassName:S}):Os(S,c),A={[f&&h>=1?"onTransitionEnd":"onAnimationEnd"]:f&&h<1?null:()=>{y&&r()}};return Gt.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":g},Gt.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${p} Toastify__progress-bar--${s}`}),Gt.createElement("div",{role:"progressbar","aria-hidden":g?"true":"false","aria-label":"notification timer",className:T,style:_,...A}))}var nk=1,N2=()=>`${nk++}`;function ak(t,n,r){let s=1,l=0,c=[],f=[],h=n,m=new Map,y=new Set,p=j=>(y.add(j),()=>y.delete(j)),g=()=>{f=Array.from(m.values()),y.forEach(j=>j())},_=({containerId:j,toastId:q,updateId:K})=>{let Z=j?j!==t:t!==1,I=m.has(q)&&K==null;return Z||I},S=(j,q)=>{m.forEach(K=>{var Z;(q==null||q===K.props.toastId)&&((Z=K.toggle)==null||Z.call(K,j))})},T=j=>{var q,K;(K=(q=j.props)==null?void 0:q.onClose)==null||K.call(q,j.removalReason),j.isActive=!1},A=j=>{if(j==null)m.forEach(T);else{let q=m.get(j);q&&T(q)}g()},x=()=>{l-=c.length,c=[]},L=j=>{var q,K;let{toastId:Z,updateId:I}=j.props,Q=I==null;j.staleId&&m.delete(j.staleId),j.isActive=!0,m.set(Z,j),g(),r(z1(j,Q?"added":"updated")),Q&&((K=(q=j.props).onOpen)==null||K.call(q))};return{id:t,props:h,observe:p,toggle:S,removeToast:A,toasts:m,clearQueue:x,buildToast:(j,q)=>{if(_(q))return;let{toastId:K,updateId:Z,data:I,staleId:Q,delay:$}=q,F=Z==null;F&&l++;let W={...h,style:h.toastStyle,key:s++,...Object.fromEntries(Object.entries(q).filter(([nt,pt])=>pt!=null)),toastId:K,updateId:Z,data:I,isIn:!1,className:Dp(q.className||h.toastClassName),progressClassName:Dp(q.progressClassName||h.progressClassName),autoClose:q.isLoading?!1:ZD(q.autoClose,h.autoClose),closeToast(nt){m.get(K).removalReason=nt,A(K)},deleteToast(){let nt=m.get(K);if(nt!=null){if(r(z1(nt,"removed")),m.delete(K),l--,l<0&&(l=0),c.length>0){L(c.shift());return}g()}}};W.closeButton=h.closeButton,q.closeButton===!1||kp(q.closeButton)?W.closeButton=q.closeButton:q.closeButton===!0&&(W.closeButton=kp(h.closeButton)?h.closeButton:!0);let rt={content:j,props:W,staleId:Q};h.limit&&h.limit>0&&l>h.limit&&F?c.push(rt):Uc($)?setTimeout(()=>{L(rt)},$):L(rt)},setProps(j){h=j},setToggle:(j,q)=>{let K=m.get(j);K&&(K.toggle=q)},isToastActive:j=>{var q;return(q=m.get(j))==null?void 0:q.isActive},getSnapshot:()=>f}}var sn=new Map,gc=[],Np=new Set,rk=t=>Np.forEach(n=>n(t)),z2=()=>sn.size>0;function sk(){gc.forEach(t=>$2(t.content,t.options)),gc=[]}var ik=(t,{containerId:n})=>{var r;return(r=sn.get(n||1))==null?void 0:r.toasts.get(t)};function L2(t,n){var r;if(n)return!!((r=sn.get(n))!=null&&r.isToastActive(t));let s=!1;return sn.forEach(l=>{l.isToastActive(t)&&(s=!0)}),s}function ok(t){if(!z2()){gc=gc.filter(n=>t!=null&&n.options.toastId!==t);return}if(t==null||XD(t))sn.forEach(n=>{n.removeToast(t)});else if(t&&("containerId"in t||"id"in t)){let n=sn.get(t.containerId);n?n.removeToast(t.id):sn.forEach(r=>{r.removeToast(t.id)})}}var lk=(t={})=>{sn.forEach(n=>{n.props.limit&&(!t.containerId||n.id===t.containerId)&&n.clearQueue()})};function $2(t,n){kp(t)&&(z2()||gc.push({content:t,options:n}),sn.forEach(r=>{r.buildToast(t,n)}))}function ck(t){var n;(n=sn.get(t.containerId||1))==null||n.setToggle(t.id,t.fn)}function F2(t,n){sn.forEach(r=>{(n==null||!(n!=null&&n.containerId)||(n==null?void 0:n.containerId)===r.id)&&r.toggle(t,n==null?void 0:n.id)})}function uk(t){let n=t.containerId||1;return{subscribe(r){let s=ak(n,t,rk);sn.set(n,s);let l=s.observe(r);return sk(),()=>{l(),sn.delete(n)}},setProps(r){var s;(s=sn.get(n))==null||s.setProps(r)},getSnapshot(){var r;return(r=sn.get(n))==null?void 0:r.getSnapshot()}}}function fk(t){return Np.add(t),()=>{Np.delete(t)}}function dk(t){return t&&(Ns(t.toastId)||Uc(t.toastId))?t.toastId:N2()}function Bc(t,n){return $2(t,n),n.toastId}function Fd(t,n){return{...n,type:n&&n.type||t,toastId:dk(n)}}function jd(t){return(n,r)=>Bc(n,Fd(t,r))}function re(t,n){return Bc(t,Fd("default",n))}re.loading=(t,n)=>Bc(t,Fd("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...n}));function hk(t,{pending:n,error:r,success:s},l){let c;n&&(c=Ns(n)?re.loading(n,l):re.loading(n.render,{...l,...n}));let f={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},h=(y,p,g)=>{if(p==null){re.dismiss(c);return}let _={type:y,...f,...l,data:g},S=Ns(p)?{render:p}:p;return c?re.update(c,{..._,...S}):re(S.render,{..._,...S}),g},m=nr(t)?t():t;return m.then(y=>h("success",s,y)).catch(y=>h("error",r,y)),m}re.promise=hk;re.success=jd("success");re.info=jd("info");re.error=jd("error");re.warning=jd("warning");re.warn=re.warning;re.dark=(t,n)=>Bc(t,Fd("default",{theme:"dark",...n}));function mk(t){ok(t)}re.dismiss=mk;re.clearWaitingQueue=lk;re.isActive=L2;re.update=(t,n={})=>{let r=ik(t,n);if(r){let{props:s,content:l}=r,c={delay:100,...s,...n,toastId:n.toastId||t,updateId:N2()};c.toastId!==t&&(c.staleId=t);let f=c.render||l;delete c.render,Bc(f,c)}};re.done=t=>{re.update(t,{progress:1})};re.onChange=fk;re.play=t=>F2(!0,t);re.pause=t=>F2(!1,t);function yk(t){var n;let{subscribe:r,getSnapshot:s,setProps:l}=et.useRef(uk(t)).current;l(t);let c=(n=et.useSyncExternalStore(r,s,s))==null?void 0:n.slice();function f(h){if(!c)return[];let m=new Map;return t.newestOnTop&&c.reverse(),c.forEach(y=>{let{position:p}=y.props;m.has(p)||m.set(p,[]),m.get(p).push(y)}),Array.from(m,y=>h(y[0],y[1]))}return{getToastToRender:f,isToastActive:L2,count:c==null?void 0:c.length}}function pk(t){let[n,r]=et.useState(!1),[s,l]=et.useState(!1),c=et.useRef(null),f=et.useRef({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:h,pauseOnHover:m,closeToast:y,onClick:p,closeOnClick:g}=t;ck({id:t.toastId,containerId:t.containerId,fn:r}),et.useEffect(()=>{if(t.pauseOnFocusLoss)return _(),()=>{S()}},[t.pauseOnFocusLoss]);function _(){document.hasFocus()||L(),window.addEventListener("focus",x),window.addEventListener("blur",L)}function S(){window.removeEventListener("focus",x),window.removeEventListener("blur",L)}function T(Q){if(t.draggable===!0||t.draggable===Q.pointerType){j();let $=c.current;f.canCloseOnClick=!0,f.canDrag=!0,$.style.transition="none",t.draggableDirection==="x"?(f.start=Q.clientX,f.removalDistance=$.offsetWidth*(t.draggablePercent/100)):(f.start=Q.clientY,f.removalDistance=$.offsetHeight*(t.draggablePercent===80?t.draggablePercent*1.5:t.draggablePercent)/100)}}function A(Q){let{top:$,bottom:F,left:W,right:rt}=c.current.getBoundingClientRect();Q.nativeEvent.type!=="touchend"&&t.pauseOnHover&&Q.clientX>=W&&Q.clientX<=rt&&Q.clientY>=$&&Q.clientY<=F?L():x()}function x(){r(!0)}function L(){r(!1)}function j(){f.didMove=!1,document.addEventListener("pointermove",K),document.addEventListener("pointerup",Z)}function q(){document.removeEventListener("pointermove",K),document.removeEventListener("pointerup",Z)}function K(Q){let $=c.current;if(f.canDrag&&$){f.didMove=!0,n&&L(),t.draggableDirection==="x"?f.delta=Q.clientX-f.start:f.delta=Q.clientY-f.start,f.start!==Q.clientX&&(f.canCloseOnClick=!1);let F=t.draggableDirection==="x"?`${f.delta}px, var(--y)`:`0, calc(${f.delta}px + var(--y))`;$.style.transform=`translate3d(${F},0)`,$.style.opacity=`${1-Math.abs(f.delta/f.removalDistance)}`}}function Z(){q();let Q=c.current;if(f.canDrag&&f.didMove&&Q){if(f.canDrag=!1,Math.abs(f.delta)>f.removalDistance){l(!0),t.closeToast(!0),t.collapseAll();return}Q.style.transition="transform 0.2s, opacity 0.2s",Q.style.removeProperty("transform"),Q.style.removeProperty("opacity")}}let I={onPointerDown:T,onPointerUp:A};return h&&m&&(I.onMouseEnter=L,t.stacked||(I.onMouseLeave=x)),g&&(I.onClick=Q=>{p&&p(Q),f.canCloseOnClick&&y(!0)}),{playToast:x,pauseToast:L,isRunning:n,preventExitTransition:s,toastRef:c,eventHandlers:I}}var gk=typeof window<"u"?et.useLayoutEffect:et.useEffect,Ud=({theme:t,type:n,isLoading:r,...s})=>Gt.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:t==="colored"?"currentColor":`var(--toastify-icon-color-${n})`,...s});function vk(t){return Gt.createElement(Ud,{...t},Gt.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))}function _k(t){return Gt.createElement(Ud,{...t},Gt.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))}function bk(t){return Gt.createElement(Ud,{...t},Gt.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))}function Sk(t){return Gt.createElement(Ud,{...t},Gt.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))}function Ek(){return Gt.createElement("div",{className:"Toastify__spinner"})}var zp={info:_k,warning:vk,success:bk,error:Sk,spinner:Ek},Tk=t=>t in zp;function Rk({theme:t,type:n,isLoading:r,icon:s}){let l=null,c={theme:t,type:n};return s===!1||(nr(s)?l=s({...c,isLoading:r}):et.isValidElement(s)?l=et.cloneElement(s,c):r?l=zp.spinner():Tk(n)&&(l=zp[n](c))),l}var Ok=t=>{let{isRunning:n,preventExitTransition:r,toastRef:s,eventHandlers:l,playToast:c}=pk(t),{closeButton:f,children:h,autoClose:m,onClick:y,type:p,hideProgressBar:g,closeToast:_,transition:S,position:T,className:A,style:x,progressClassName:L,updateId:j,role:q,progress:K,rtl:Z,toastId:I,deleteToast:Q,isIn:$,isLoading:F,closeOnClick:W,theme:rt,ariaLabel:nt}=t,pt=Os("Toastify__toast",`Toastify__toast-theme--${rt}`,`Toastify__toast--${p}`,{"Toastify__toast--rtl":Z},{"Toastify__toast--close-on-click":W}),St=nr(A)?A({rtl:Z,position:T,type:p,defaultClassName:pt}):Os(pt,A),vt=Rk(t),k=!!K||!m,X={closeToast:_,type:p,theme:rt},lt=null;return f===!1||(nr(f)?lt=f(X):et.isValidElement(f)?lt=et.cloneElement(f,X):lt=tk(X)),Gt.createElement(S,{isIn:$,done:Q,position:T,preventExitTransition:r,nodeRef:s,playToast:c},Gt.createElement("div",{id:I,tabIndex:0,onClick:y,"data-in":$,className:St,...l,style:x,ref:s,...$&&{role:q,"aria-label":nt}},vt!=null&&Gt.createElement("div",{className:Os("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!F})},vt),k2(h,t,!n),lt,!t.customProgressBar&&Gt.createElement(ek,{...j&&!k?{key:`p-${j}`}:{},rtl:Z,theme:rt,delay:m,isRunning:n,isIn:$,closeToast:_,hide:g,type:p,className:L,controlledProgress:k,progress:K||0})))},Mk=(t,n=!1)=>({enter:`Toastify--animate Toastify__${t}-enter`,exit:`Toastify--animate Toastify__${t}-exit`,appendPosition:n}),wk=WD(Mk("bounce",!0)),Ak={position:"top-right",transition:wk,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light","aria-label":"Notifications Alt+T",hotKeys:t=>t.altKey&&t.code==="KeyT"};function xk(t){let n={...Ak,...t},r=t.stacked,[s,l]=et.useState(!0),c=et.useRef(null),{getToastToRender:f,isToastActive:h,count:m}=yk(n),{className:y,style:p,rtl:g,containerId:_,hotKeys:S}=n;function T(x){let L=Os("Toastify__toast-container",`Toastify__toast-container--${x}`,{"Toastify__toast-container--rtl":g});return nr(y)?y({position:x,rtl:g,defaultClassName:L}):Os(L,Dp(y))}function A(){r&&(l(!0),re.play())}return gk(()=>{var x;if(r){let L=c.current.querySelectorAll('[data-in="true"]'),j=12,q=(x=n.position)==null?void 0:x.includes("top"),K=0,Z=0;Array.from(L).reverse().forEach((I,Q)=>{let $=I;$.classList.add("Toastify__toast--stacked"),Q>0&&($.dataset.collapsed=`${s}`),$.dataset.pos||($.dataset.pos=q?"top":"bot");let F=K*(s?.2:1)+(s?0:j*Q);$.style.setProperty("--y",`${q?F:F*-1}px`),$.style.setProperty("--g",`${j}`),$.style.setProperty("--s",`${1-(s?Z:0)}`),K+=$.offsetHeight,Z+=.025})}},[s,m,r]),et.useEffect(()=>{function x(L){var j;let q=c.current;S(L)&&((j=q.querySelector('[tabIndex="0"]'))==null||j.focus(),l(!1),re.pause()),L.key==="Escape"&&(document.activeElement===q||q!=null&&q.contains(document.activeElement))&&(l(!0),re.play())}return document.addEventListener("keydown",x),()=>{document.removeEventListener("keydown",x)}},[S]),Gt.createElement("section",{ref:c,className:"Toastify",id:_,onMouseEnter:()=>{r&&(l(!1),re.pause())},onMouseLeave:A,"aria-live":"polite","aria-atomic":"false","aria-relevant":"additions text","aria-label":n["aria-label"]},f((x,L)=>{let j=L.length?{...p}:{...p,pointerEvents:"none"};return Gt.createElement("div",{tabIndex:-1,className:T(x),"data-stacked":r,style:j,key:`c-${x}`},L.map(({content:q,props:K})=>Gt.createElement(Ok,{...K,stacked:r,collapseAll:A,isIn:h(K.toastId,K.containerId),key:`t-${K.key}`},q)))}))}const j2=et.createContext(void 0),Ck=({children:t,service:n})=>tt.jsx(j2.Provider,{value:n,children:t}),jq=()=>{const t=et.useContext(j2);if(!t)throw new Error("No service found");return t};async function Dk(t,n,r){const s=n[0];if(Ja(s)&&s.method){const l=s,c=l.data instanceof FormData?"formData":"payload",f=new Headers({...c==="payload"?{"content-type":"application/json",accept:"application/json","x-tsr-redirect":"manual"}:{},...l.headers instanceof Headers?Object.fromEntries(l.headers.entries()):l.headers});if(l.method==="GET"){const h=m2({payload:an.stringify({data:l.data,context:l.context})});h&&(t.includes("?")?t+=`&${h}`:t+=`?${h}`)}return t.includes("?")?t+="&createServerFn":t+="?createServerFn",l.response==="raw"&&(t+="&raw"),await L1(()=>r(t,{method:l.method,headers:f,signal:l.signal,...kk(l)}))}return await L1(()=>r(t,{method:"POST",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(n),redirect:"manual"}))}function kk(t){return t.method==="POST"?t.data instanceof FormData?(t.data.set("__TSR_CONTEXT",an.stringify(t.context)),{body:t.data}):{body:an.stringify({data:t.data??null,context:t.context})}:{}}async function L1(t){var n;const r=await(async()=>{try{return await t()}catch(s){if(s instanceof Response)return s;throw s}})();if(!r.ok){const s=r.headers.get("content-type");throw s&&s.includes("application/json")?an.decode(await r.json()):new Error(await r.text())}if((n=r.headers.get("content-type"))!=null&&n.includes("application/json")){let s=an.decode(await r.json());const l=a3(s);if(l&&(s=l),gn(s)||vn(s)||s instanceof Error)throw s;return s}return r}function $1(t){return t.replace(/^\/|\/$/g,"")}const U2=(t,n)=>{const r=$1("/"),s=$1(n),l=`${r?`/${r}`:""}/${s}/${t}`;return Object.assign((...f)=>Dk(l,f,fetch),{url:l,functionId:t})},Nk=U2("src_modules_auth_server_theme_ts--getThemeServerFn_createServerFn_handler","/_serverFn"),zk=oc().handler(Nk),Lk=U2("src_modules_auth_server_theme_ts--setThemeServerFn_createServerFn_handler","/_serverFn"),$k=oc({method:"POST"}).handler(Lk),B2=et.createContext(null);function Fk({children:t,theme:n}){const r=un();function s(l){$k({data:l}),r.invalidate()}return tt.jsx(B2,{value:{theme:n,setTheme:s},children:t})}function jk(){const t=et.use(B2);if(!t)throw new Error("useTheme called outside of ThemeProvider!");return t}const Uk=t=>typeof t=="function",O=function(t,n){if(typeof t=="function")return function(){return t(arguments)?n.apply(this,arguments):r=>n(r,...arguments)};switch(t){case 0:case 1:throw new RangeError(`Invalid arity ${t}`);case 2:return function(r,s){return arguments.length>=2?n(r,s):function(l){return n(l,r)}};case 3:return function(r,s,l){return arguments.length>=3?n(r,s,l):function(c){return n(c,r,s)}};case 4:return function(r,s,l,c){return arguments.length>=4?n(r,s,l,c):function(f){return n(f,r,s,l)}};case 5:return function(r,s,l,c,f){return arguments.length>=5?n(r,s,l,c,f):function(h){return n(h,r,s,l,c)}};default:return function(){if(arguments.length>=t)return n.apply(this,arguments);const r=arguments;return function(s){return n(s,...r)}}}},Yt=t=>t,Bd=t=>()=>t,F1=Bd(!0),Wf=Bd(!1),q2=Bd(void 0),Lp=q2;function D(t,n,r,s,l,c,f,h,m){switch(arguments.length){case 1:return t;case 2:return n(t);case 3:return r(n(t));case 4:return s(r(n(t)));case 5:return l(s(r(n(t))));case 6:return c(l(s(r(n(t)))));case 7:return f(c(l(s(r(n(t))))));case 8:return h(f(c(l(s(r(n(t)))))));case 9:return m(h(f(c(l(s(r(n(t))))))));default:{let y=arguments[0];for(let p=1;p<arguments.length;p++)y=arguments[p](y);return y}}}const Bg=t=>(n,r)=>n===r||t(n,r),Bk=O(2,(t,n)=>Bg((r,s)=>t(n(r),n(s)))),qk=t=>Bg((n,r)=>{if(n.length!==r.length)return!1;for(let s=0;s<n.length;s++)if(!t(n[s],r[s]))return!1;return!0});let Hk="3.16.7";const qd=()=>Hk,wf=`effect/GlobalValue/globalStoreId/${qd()}`;let Xl;const Ut=(t,n)=>(Xl||(globalThis[wf]??(globalThis[wf]=new Map),Xl=globalThis[wf]),Xl.has(t)||Xl.set(t,n()),Xl.get(t)),H2=t=>typeof t=="string",$p=t=>typeof t=="number",Uq=t=>typeof t=="boolean",Ik=t=>typeof t=="bigint",Bq=t=>typeof t=="symbol",Hd=Uk,qq=t=>t===void 0,Hq=t=>!1,I2=t=>typeof t=="object"&&t!==null,Id=t=>I2(t)||Hd(t),Mt=O(2,(t,n)=>Id(t)&&n in t),P2=O(2,(t,n)=>Mt(t,"_tag")&&t._tag===n),Bi=t=>t==null,Iq=t=>t!=null,Pq=t=>t instanceof Date,V2=t=>Mt(t,Symbol.iterator),Vq=t=>I2(t)&&!Array.isArray(t),G2=t=>Mt(t,"then")&&Hd(t.then),Pd=t=>`BUG: ${t} - please report an issue at https://github.com/Effect-TS/effect/issues`;let K2=class Q2{constructor(n){E(this,"self");E(this,"called",!1);this.self=n}next(n){return this.called?{value:n,done:!0}:(this.called=!0,{value:this.self,done:!1})}return(n){return{value:n,done:!0}}throw(n){throw n}[Symbol.iterator](){return new Q2(this.self)}};const Pk=335903614,Vk=4150755663,Gk=1481765933,Kk=1284865837,Qk=9007199254740992,Yk=134217728;class Xk{constructor(n,r,s,l){E(this,"_state");return Bi(r)&&Bi(n)?(r=Math.random()*4294967295>>>0,n=0):Bi(r)&&(r=n,n=0),Bi(l)&&Bi(s)?(l=this._state?this._state[3]:Vk,s=this._state?this._state[2]:Pk):Bi(l)&&(l=s,s=0),this._state=new Int32Array([0,0,s>>>0,((l||0)|1)>>>0]),this._next(),j1(this._state,this._state[0],this._state[1],n>>>0,r>>>0),this._next(),this}getState(){return[this._state[0],this._state[1],this._state[2],this._state[3]]}setState(n){this._state[0]=n[0],this._state[1]=n[1],this._state[2]=n[2],this._state[3]=n[3]|1}integer(n){return Math.round(this.number()*Number.MAX_SAFE_INTEGER)%n}number(){const n=(this._next()&67108863)*1,r=(this._next()&134217727)*1;return(n*Yk+r)/Qk}_next(){const n=this._state[0]>>>0,r=this._state[1]>>>0;Zk(this._state,n,r,Gk,Kk),j1(this._state,this._state[0],this._state[1],this._state[2],this._state[3]);let s=n>>>18,l=(r>>>18|n<<14)>>>0;s=(s^n)>>>0,l=(l^r)>>>0;const c=(l>>>27|s<<5)>>>0,f=n>>>27,h=(-f>>>0&31)>>>0;return(c>>>f|c<<h)>>>0}}function Zk(t,n,r,s,l){let c=(r>>>16)*(l&65535)>>>0,f=(r&65535)*(l>>>16)>>>0,h=(r&65535)*(l&65535)>>>0,m=(r>>>16)*(l>>>16)+((f>>>16)+(c>>>16))>>>0;f=f<<16>>>0,h=h+f>>>0,h>>>0<f>>>0&&(m=m+1>>>0),c=c<<16>>>0,h=h+c>>>0,h>>>0<c>>>0&&(m=m+1>>>0),m=m+Math.imul(r,s)>>>0,m=m+Math.imul(n,l)>>>0,t[0]=m,t[1]=h}function j1(t,n,r,s,l){let c=n+s>>>0;const f=r+l>>>0;f>>>0<r>>>0&&(c=c+1|0),t[0]=c,t[1]=f}const Fp=Symbol.for("effect/Utils/YieldWrap");var Fc;class qc{constructor(n){Ht(this,Fc);wt(this,Fc,n)}[Fp](){return G(this,Fc)}}Fc=new WeakMap;function Jk(t){if(typeof t=="object"&&t!==null&&Fp in t)return t[Fp]();throw new Error(Pd("yieldWrapGet"))}const Hn=Ut("effect/Utils/isStructuralRegion",()=>({enabled:!1,tester:void 0})),Y2={effect_internal_function:t=>t()},Wk={effect_internal_function:t=>{try{return t()}finally{}}};var oT;const tN=((oT=Y2.effect_internal_function(()=>new Error().stack))==null?void 0:oT.includes("effect_internal_function"))===!0,Ge=tN?Y2.effect_internal_function:Wk.effect_internal_function,cp=Ut(Symbol.for("effect/Hash/randomHashCache"),()=>new WeakMap),Dt=Symbol.for("effect/Hash"),mt=t=>{if(Hn.enabled===!0)return 0;switch(typeof t){case"number":return Hg(t);case"bigint":return Ee(t.toString(10));case"boolean":return Ee(String(t));case"symbol":return Ee(String(t));case"string":return Ee(t);case"undefined":return Ee("undefined");case"function":case"object":return t===null?Ee("null"):t instanceof Date?mt(t.toISOString()):t instanceof URL?mt(t.href):eN(t)?t[Dt]():qg(t);default:throw new Error(`BUG: unhandled typeof ${typeof t} - please report an issue at https://github.com/Effect-TS/effect/issues`)}},qg=t=>(cp.has(t)||cp.set(t,Hg(Math.floor(Math.random()*Number.MAX_SAFE_INTEGER))),cp.get(t)),jt=t=>n=>n*53^t,Vd=t=>t&3221225471|t>>>1&1073741824,eN=t=>Mt(t,Dt),Hg=t=>{if(t!==t||t===1/0)return 0;let n=t|0;for(n!==t&&(n^=t*4294967295);t>4294967295;)n^=t/=4294967295;return Vd(n)},Ee=t=>{let n=5381,r=t.length;for(;r;)n=n*33^t.charCodeAt(--r);return Vd(n)},nN=(t,n)=>{let r=12289;for(let s=0;s<n.length;s++)r^=D(Ee(n[s]),jt(mt(t[n[s]])));return Vd(r)},X2=t=>nN(t,Object.keys(t)),Hc=t=>{let n=6151;for(let r=0;r<t.length;r++)n=D(n,jt(mt(t[r])));return Vd(n)},he=function(){if(arguments.length===1){const r=arguments[0];return function(s){return Object.defineProperty(r,Dt,{value(){return s},enumerable:!1}),s}}const t=arguments[0],n=arguments[1];return Object.defineProperty(t,Dt,{value(){return n},enumerable:!1}),n},Ct=Symbol.for("effect/Equal");function Ot(){return arguments.length===1?t=>td(t,arguments[0]):td(arguments[0],arguments[1])}function td(t,n){if(t===n)return!0;const r=typeof t;if(r!==typeof n)return!1;if(r==="object"||r==="function"){if(t!==null&&n!==null){if(ed(t)&&ed(n))return mt(t)===mt(n)&&t[Ct](n)?!0:Hn.enabled&&Hn.tester?Hn.tester(t,n):!1;if(t instanceof Date&&n instanceof Date)return t.toISOString()===n.toISOString();if(t instanceof URL&&n instanceof URL)return t.href===n.href}if(Hn.enabled){if(Array.isArray(t)&&Array.isArray(n))return t.length===n.length&&t.every((s,l)=>td(s,n[l]));if(Object.getPrototypeOf(t)===Object.prototype&&Object.getPrototypeOf(t)===Object.prototype){const s=Object.keys(t),l=Object.keys(n);if(s.length===l.length){for(const c of s)if(!(c in n&&td(t[c],n[c])))return Hn.tester?Hn.tester(t,n):!1;return!0}}return Hn.tester?Hn.tester(t,n):!1}}return Hn.enabled&&Hn.tester?Hn.tester(t,n):!1}const ed=t=>Mt(t,Ct),Ig=()=>Ot,le=Symbol.for("nodejs.util.inspect.custom"),_e=t=>{try{if(Mt(t,"toJSON")&&Hd(t.toJSON)&&t.toJSON.length===0)return t.toJSON();if(Array.isArray(t))return t.map(_e)}catch{return{}}return rN(t)},Re=t=>JSON.stringify(t,null,2),Gq={toJSON(){return _e(this)},[le](){return this.toJSON()},toString(){return Re(this.toJSON())}};let Kq=class{[le](){return this.toJSON()}toString(){return Re(this.toJSON())}};const mo=(t,n=2)=>{if(typeof t=="string")return t;try{return typeof t=="object"?Z2(t,n):String(t)}catch{return String(t)}},Z2=(t,n)=>{let r=[];const s=JSON.stringify(t,(l,c)=>typeof c=="object"&&c!==null?r.includes(c)?void 0:r.push(c)&&(Ms.fiberRefs!==void 0&&J2(c)?c[Pg](Ms.fiberRefs):c):c,n);return r=void 0,s},Pg=Symbol.for("effect/Inspectable/Redactable"),J2=t=>typeof t=="object"&&t!==null&&Pg in t,Ms=Ut("effect/Inspectable/redactableState",()=>({fiberRefs:void 0})),aN=(t,n)=>{const r=Ms.fiberRefs;Ms.fiberRefs=t;try{return n()}finally{Ms.fiberRefs=r}},rN=t=>J2(t)&&Ms.fiberRefs!==void 0?t[Pg](Ms.fiberRefs):t,Tt=(t,n)=>{switch(n.length){case 0:return t;case 1:return n[0](t);case 2:return n[1](n[0](t));case 3:return n[2](n[1](n[0](t)));case 4:return n[3](n[2](n[1](n[0](t))));case 5:return n[4](n[3](n[2](n[1](n[0](t)))));case 6:return n[5](n[4](n[3](n[2](n[1](n[0](t))))));case 7:return n[6](n[5](n[4](n[3](n[2](n[1](n[0](t)))))));case 8:return n[7](n[6](n[5](n[4](n[3](n[2](n[1](n[0](t))))))));case 9:return n[8](n[7](n[6](n[5](n[4](n[3](n[2](n[1](n[0](t)))))))));default:{let r=t;for(let s=0,l=n.length;s<l;s++)r=n[s](r);return r}}},lc="Async",Gd="Commit",ze="Failure",$f="OnFailure",nd="OnSuccess",ad="OnSuccessAndFailure",Le="Success",W2="Sync",sN="Tag",zo="UpdateRuntimeFlags",rd="While",cc="Iterator",tR="WithRuntime",Ff="Yield",Vg="RevertFlags",iN=Symbol.for("effect/Effect"),oN=Symbol.for("effect/Stream"),lN=Symbol.for("effect/Sink"),cN=Symbol.for("effect/Channel"),yo={_R:t=>t,_E:t=>t,_A:t=>t,_V:qd()},uN={_A:t=>t,_In:t=>t,_L:t=>t,_E:t=>t,_R:t=>t},fN={_Env:t=>t,_InErr:t=>t,_InElem:t=>t,_InDone:t=>t,_OutErr:t=>t,_OutElem:t=>t,_OutDone:t=>t},Ic={[iN]:yo,[oN]:yo,[lN]:uN,[cN]:fN,[Ct](t){return this===t},[Dt](){return he(this,qg(this))},[Symbol.iterator](){return new K2(new qc(this))},pipe(){return Tt(this,arguments)}},Gg={[Dt](){return he(this,X2(this))},[Ct](t){const n=Object.keys(this),r=Object.keys(t);if(n.length!==r.length)return!1;for(const s of n)if(!(s in t&&Ot(this[s],t[s])))return!1;return!0}},Lo={...Ic,_op:Gd},dN={...Lo,...Gg},hN=function(){function t(){}return t.prototype=Lo,t}(),eR=Symbol.for("effect/Option"),nR={...Ic,[eR]:{_A:t=>t},[le](){return this.toJSON()},toString(){return Re(this.toJSON())}},mN=Object.assign(Object.create(nR),{_tag:"Some",_op:"Some",[Ct](t){return aR(t)&&sR(t)&&Ot(this.value,t.value)},[Dt](){return he(this,jt(mt(this._tag))(mt(this.value)))},toJSON(){return{_id:"Option",_tag:this._tag,value:_e(this.value)}}}),yN=mt("None"),pN=Object.assign(Object.create(nR),{_tag:"None",_op:"None",[Ct](t){return aR(t)&&rR(t)},[Dt](){return yN},toJSON(){return{_id:"Option",_tag:this._tag}}}),aR=t=>Mt(t,eR),rR=t=>t._tag==="None",sR=t=>t._tag==="Some",iR=Object.create(pN),jp=t=>{const n=Object.create(mN);return n.value=t,n},oR=Symbol.for("effect/Either"),lR={...Ic,[oR]:{_R:t=>t},[le](){return this.toJSON()},toString(){return Re(this.toJSON())}},gN=Object.assign(Object.create(lR),{_tag:"Right",_op:"Right",[Ct](t){return Kg(t)&&uR(t)&&Ot(this.right,t.right)},[Dt](){return jt(mt(this._tag))(mt(this.right))},toJSON(){return{_id:"Either",_tag:this._tag,right:_e(this.right)}}}),vN=Object.assign(Object.create(lR),{_tag:"Left",_op:"Left",[Ct](t){return Kg(t)&&cR(t)&&Ot(this.left,t.left)},[Dt](){return jt(mt(this._tag))(mt(this.left))},toJSON(){return{_id:"Either",_tag:this._tag,left:_e(this.left)}}}),Kg=t=>Mt(t,oR),cR=t=>t._tag==="Left",uR=t=>t._tag==="Right",_N=t=>{const n=Object.create(vN);return n.left=t,n},bN=t=>{const n=Object.create(gN);return n.right=t,n},ia=bN,Ir=_N,Yq=Kg,Pi=cR,Vi=uR,Xq=O(2,(t,n)=>Pi(t)?Ir(n(t.left)):ia(t.right)),Zq=O(2,(t,n)=>Vi(t)?ia(n(t.right)):Ir(t.left)),SN=O(2,(t,{onLeft:n,onRight:r})=>Pi(t)?n(t.left):r(t.right)),EN=SN({onLeft:Yt,onRight:Yt}),TN=O(2,(t,n)=>{if(Vi(t))return t.right;throw n(t.left)}),Jq=TN(()=>new Error("getOrThrow called on a Left")),fR=t=>t.length>0,dR=t=>(n,r)=>n===r?0:t(n,r),RN=dR((t,n)=>t<n?-1:1),ON=O(2,(t,n)=>dR((r,s)=>t(n(r),n(s)))),MN=t=>O(2,(n,r)=>t(n,r)===1),gt=()=>iR,Rt=jp,He=rR,Aa=sR,rr=O(2,(t,{onNone:n,onSome:r})=>He(t)?n():r(t.value)),Gn=O(2,(t,n)=>He(t)?n():t.value),Wq=O(2,(t,n)=>He(t)?n():t),wN=O(2,(t,n)=>He(t)?Rt(n()):t),Kd=t=>t==null?gt():Rt(t),ys=Gn(q2),t9=t=>(...n)=>{try{return Rt(t(...n))}catch{return gt()}},uc=O(2,(t,n)=>He(t)?gt():Rt(n(t.value))),hR=O(2,(t,n)=>He(t)?gt():n(t.value)),e9=O(2,(t,n)=>He(t)?gt():Kd(n(t.value))),AN=t=>O(2,(n,r)=>He(n)?!1:t(n.value,r)),xN=Ig(),CN=AN(xN),n9=O(2,(t,n)=>He(t)?!1:n(t.value)),DN=(...t)=>t,Qg=t=>new Array(t),kN=O(2,(t,n)=>{const r=Math.max(1,Math.floor(t)),s=new Array(r);for(let l=0;l<r;l++)s[l]=n(l);return s}),de=t=>Array.isArray(t)?t:Array.from(t),NN=t=>Array.isArray(t)?t:[t],a9=O(2,(t,{onEmpty:n,onNonEmpty:r})=>_n(t)?r(on(t),zs(t)):n()),sd=O(2,(t,n)=>[n,...t]),zN=O(2,(t,n)=>[...t,n]),mR=O(2,(t,n)=>de(t).concat(de(n))),r9=Array.isArray,LN=t=>t.length===0,$N=LN,FN=fR,_n=fR,yR=(t,n)=>t<0||t>=n.length,jN=(t,n)=>Math.floor(Math.min(Math.max(0,t),n.length)),UN=O(2,(t,n)=>{const r=Math.floor(n);return yR(r,t)?gt():Rt(t[r])}),pR=O(2,(t,n)=>{const r=Math.floor(n);if(yR(r,t))throw new Error(`Index ${r} out of bounds`);return t[r]}),fc=UN(0),on=pR(0),BN=t=>_n(t)?Rt(gR(t)):gt(),gR=t=>t[t.length-1],zs=t=>t.slice(1),qN=(t,n)=>{let r=0;for(const s of t){if(!n(s,r))break;r++}return r},HN=O(2,(t,n)=>GN(t,qN(t,n))),IN=O(2,(t,n)=>{const r=de(t);return r.slice(jN(n,r),r.length)}),U1=t=>Array.from(t).reverse(),id=O(2,(t,n)=>{const r=Array.from(t);return r.sort(n),r}),B1=O(2,(t,n)=>PN(t,n,DN)),PN=O(3,(t,n,r)=>{const s=de(t),l=de(n);if(_n(s)&&_n(l)){const c=[r(on(s),on(l))],f=Math.min(s.length,l.length);for(let h=1;h<f;h++)c[h]=r(s[h],l[h]);return c}return[]}),VN=Ig(),GN=O(2,(t,n)=>{const r=Array.from(t),s=Math.floor(n);return _n(r)?s>=1?KN(r,s):[[],r]:[r,[]]}),KN=O(2,(t,n)=>{const r=Math.max(1,Math.floor(n));return r>=t.length?[QN(t),[]]:[sd(t.slice(1,r),on(t)),t.slice(r)]}),QN=t=>t.slice(),YN=O(3,(t,n,r)=>{const s=de(t),l=de(n);return _n(s)?_n(l)?vR(r)(mR(s,l)):s:l}),jf=O(2,(t,n)=>YN(t,n,VN)),Ls=()=>[],ra=t=>[t],ws=O(2,(t,n)=>t.map(n)),XN=O(2,(t,n)=>{if($N(t))return[];const r=[];for(let s=0;s<t.length;s++){const l=n(t[s],s);for(let c=0;c<l.length;c++)r.push(l[c])}return r}),ZN=XN(Yt),JN=O(2,(t,n)=>{const r=de(t),s=[];for(let l=0;l<r.length;l++)n(r[l],l)&&s.push(r[l]);return s}),Yg=O(3,(t,n,r)=>de(t).reduce((s,l,c)=>r(s,l,c),n)),q1=(t,n)=>{const r=[];let s=t,l;for(;Aa(l=n(s));){const[c,f]=l.value;r.push(c),s=f}return r},Xg=qk,vR=O(2,(t,n)=>{const r=de(t);if(_n(r)){const s=[on(r)],l=zs(r);for(const c of l)s.every(f=>!n(c,f))&&s.push(c);return s}return[]}),WN=t=>vR(t,Ig()),$o=O(2,(t,n)=>de(t).join(n)),vc=RN,t4=t=>t.replace(/[/\\^$*+?.()|[\]{}]/g,"\\$&"),_R=Symbol.for("effect/Context/Tag"),od=Symbol.for("effect/Context/Reference"),e4="effect/STM",n4=Symbol.for(e4),Qd={...Ic,_op:"Tag",[n4]:yo,[_R]:{_Service:t=>t,_Identifier:t=>t},toString(){return Re(this.toJSON())},toJSON(){return{_id:"Tag",key:this.key,stack:this.stack}},[le](){return this.toJSON()},of(t){return t},context(t){return ER(this,t)}},a4={...Qd,[od]:od},r4=t=>{const n=Error.stackTraceLimit;Error.stackTraceLimit=2;const r=new Error;Error.stackTraceLimit=n;const s=Object.create(Qd);return Object.defineProperty(s,"stack",{get(){return r.stack}}),s.key=t,s},s4=t=>()=>{const n=Error.stackTraceLimit;Error.stackTraceLimit=2;const r=new Error;Error.stackTraceLimit=n;function s(){}return Object.setPrototypeOf(s,Qd),s.key=t,Object.defineProperty(s,"stack",{get(){return r.stack}}),s},i4=()=>(t,n)=>{const r=Error.stackTraceLimit;Error.stackTraceLimit=2;const s=new Error;Error.stackTraceLimit=r;function l(){}return Object.setPrototypeOf(l,a4),l.key=t,l.defaultValue=n.defaultValue,Object.defineProperty(l,"stack",{get(){return s.stack}}),l},bR=Symbol.for("effect/Context"),o4={[bR]:{_Services:t=>t},[Ct](t){if(SR(t)&&this.unsafeMap.size===t.unsafeMap.size){for(const n of this.unsafeMap.keys())if(!t.unsafeMap.has(n)||!Ot(this.unsafeMap.get(n),t.unsafeMap.get(n)))return!1;return!0}return!1},[Dt](){return he(this,Hg(this.unsafeMap.size))},pipe(){return Tt(this,arguments)},toString(){return Re(this.toJSON())},toJSON(){return{_id:"Context",services:Array.from(this.unsafeMap).map(_e)}},[le](){return this.toJSON()}},po=t=>{const n=Object.create(o4);return n.unsafeMap=t,n},l4=t=>{const n=new Error(`Service not found${t.key?`: ${String(t.key)}`:""}`);if(t.stack){const r=t.stack.split(`
`);if(r.length>2){const s=r[2].match(/at (.*)/);s&&(n.message=n.message+` (defined at ${s[1]})`)}}if(n.stack){const r=n.stack.split(`
`);r.splice(1,3),n.stack=r.join(`
`)}return n},SR=t=>Mt(t,bR),c4=t=>Mt(t,_R),u4=t=>Mt(t,od),f4=po(new Map),d4=()=>f4,ER=(t,n)=>po(new Map([[t.key,n]])),h4=O(3,(t,n,r)=>{const s=new Map(t.unsafeMap);return s.set(n.key,r),po(s)}),up=Ut("effect/Context/defaultValueCache",()=>new Map),Zg=t=>{if(up.has(t.key))return up.get(t.key);const n=t.defaultValue();return up.set(t.key,n),n},m4=(t,n)=>t.unsafeMap.has(n.key)?t.unsafeMap.get(n.key):Zg(n),TR=O(2,(t,n)=>{if(!t.unsafeMap.has(n.key)){if(od in n)return Zg(n);throw l4(n)}return t.unsafeMap.get(n.key)}),y4=TR,p4=O(2,(t,n)=>t.unsafeMap.has(n.key)?jp(t.unsafeMap.get(n.key)):u4(n)?jp(Zg(n)):iR),g4=O(2,(t,n)=>{const r=new Map(t.unsafeMap);for(const[s,l]of n.unsafeMap)r.set(s,l);return po(r)}),Qs=r4,v4=SR,RR=c4,_c=d4,Jg=ER,zr=h4,As=y4,OR=TR,Fo=p4,jo=g4,s9=s4,Pc=i4,MR=Symbol.for("effect/Chunk");function _4(t,n,r,s,l){for(let c=n;c<Math.min(t.length,n+l);c++)r[s+c-n]=t[c];return r}const wR=[],b4=t=>Bg((n,r)=>n.length===r.length&&Ma(n).every((s,l)=>t(s,xs(r,l)))),S4=b4(Ot),E4={[MR]:{_A:t=>t},toString(){return Re(this.toJSON())},toJSON(){return{_id:"Chunk",values:Ma(this).map(_e)}},[le](){return this.toJSON()},[Ct](t){return AR(t)&&S4(this,t)},[Dt](){return he(this,Hc(Ma(this)))},[Symbol.iterator](){switch(this.backing._tag){case"IArray":return this.backing.array[Symbol.iterator]();case"IEmpty":return wR[Symbol.iterator]();default:return Ma(this)[Symbol.iterator]()}},pipe(){return Tt(this,arguments)}},ke=t=>{const n=Object.create(E4);switch(n.backing=t,t._tag){case"IEmpty":{n.length=0,n.depth=0,n.left=n,n.right=n;break}case"IConcat":{n.length=t.left.length+t.right.length,n.depth=1+Math.max(t.left.depth,t.right.depth),n.left=t.left,n.right=t.right;break}case"IArray":{n.length=t.array.length,n.depth=0,n.left=Ea,n.right=Ea;break}case"ISingleton":{n.length=1,n.depth=0,n.left=Ea,n.right=Ea;break}case"ISlice":{n.length=t.length,n.depth=t.chunk.depth+1,n.left=Ea,n.right=Ea;break}}return n},AR=t=>Mt(t,MR),Ea=ke({_tag:"IEmpty"}),ua=()=>Ea,fp=(...t)=>M4(t),ln=t=>ke({_tag:"ISingleton",a:t}),xR=t=>AR(t)?t:Uo(de(t)),Up=(t,n,r)=>{switch(t.backing._tag){case"IArray":{_4(t.backing.array,0,n,r,t.length);break}case"IConcat":{Up(t.left,n,r),Up(t.right,n,r+t.left.length);break}case"ISingleton":{n[r]=t.backing.a;break}case"ISlice":{let s=0,l=r;for(;s<t.length;)n[l]=xs(t,s),s+=1,l+=1;break}}},T4=t=>{switch(t.backing._tag){case"IEmpty":return wR;case"IArray":return t.backing.array;default:{const n=new Array(t.length);return Up(t,n,0),t.backing={_tag:"IArray",array:n},t.left=Ea,t.right=Ea,t.depth=0,n}}},Ma=T4,R4=t=>{switch(t.backing._tag){case"IEmpty":case"ISingleton":return t;case"IArray":return ke({_tag:"IArray",array:U1(t.backing.array)});case"IConcat":return ke({_tag:"IConcat",left:$s(t.backing.right),right:$s(t.backing.left)});case"ISlice":return Uo(U1(Ma(t)))}},$s=R4,O4=O(2,(t,n)=>n<0||n>=t.length?gt():Rt(xs(t,n))),Uo=t=>t.length===0?ua():t.length===1?ln(t[0]):ke({_tag:"IArray",array:t}),M4=t=>Uo(t),xs=O(2,(t,n)=>{switch(t.backing._tag){case"IEmpty":throw new Error("Index out of bounds");case"ISingleton":{if(n!==0)throw new Error("Index out of bounds");return t.backing.a}case"IArray":{if(n>=t.length||n<0)throw new Error("Index out of bounds");return t.backing.array[n]}case"IConcat":return n<t.left.length?xs(t.left,n):xs(t.right,n-t.left.length);case"ISlice":return xs(t.backing.chunk,n+t.backing.offset)}}),w4=O(2,(t,n)=>oa(t,ln(n))),Yn=O(2,(t,n)=>oa(ln(n),t)),Bp=O(2,(t,n)=>{if(n<=0)return t;if(n>=t.length)return Ea;switch(t.backing._tag){case"ISlice":return ke({_tag:"ISlice",chunk:t.backing.chunk,offset:t.backing.offset+n,length:t.backing.length-n});case"IConcat":return n>t.left.length?Bp(t.right,n-t.left.length):ke({_tag:"IConcat",left:Bp(t.left,n),right:t.right});default:return ke({_tag:"ISlice",chunk:t,offset:n,length:t.length-n})}}),oa=O(2,(t,n)=>{if(t.backing._tag==="IEmpty")return n;if(n.backing._tag==="IEmpty")return t;const r=n.depth-t.depth;if(Math.abs(r)<=1)return ke({_tag:"IConcat",left:t,right:n});if(r<-1)if(t.left.depth>=t.right.depth){const s=oa(t.right,n);return ke({_tag:"IConcat",left:t.left,right:s})}else{const s=oa(t.right.right,n);if(s.depth===t.depth-3){const l=ke({_tag:"IConcat",left:t.right.left,right:s});return ke({_tag:"IConcat",left:t.left,right:l})}else{const l=ke({_tag:"IConcat",left:t.left,right:t.right.left});return ke({_tag:"IConcat",left:l,right:s})}}else if(n.right.depth>=n.left.depth){const s=oa(t,n.left);return ke({_tag:"IConcat",left:s,right:n.right})}else{const s=oa(t,n.left.left);if(s.depth===n.depth-3){const l=ke({_tag:"IConcat",left:s,right:n.left.right});return ke({_tag:"IConcat",left:l,right:n.right})}else{const l=ke({_tag:"IConcat",left:n.left.right,right:n.right});return ke({_tag:"IConcat",left:s,right:l})}}}),i9=O(2,(t,n)=>Uo(JN(t,n))),A4=t=>t.length===0,ar=t=>t.length>0,Wg=O4(0),CR=t=>xs(t,0),Ra=CR,o9=O(2,(t,n)=>t.backing._tag==="ISingleton"?ln(n(t.backing.a,0)):Uo(D(Ma(t),ws((r,s)=>n(r,s))))),Wa=t=>Bp(t,1),l9=Yg,qp=Symbol.for("effect/Duration"),DR=BigInt(0),H1=BigInt(24),Af=BigInt(60),Hp=BigInt(1e3),I1=BigInt(1e6),P1=BigInt(1e9),x4=/^(-?\d+(?:\.\d+)?)\s+(nanos?|micros?|millis?|seconds?|minutes?|hours?|days?|weeks?)$/,xa=t=>{if(kR(t))return t;if($p(t))return vo(t);if(Ik(t))return dp(t);if(Array.isArray(t)&&t.length===2&&t.every($p))return t[0]===-1/0||t[1]===-1/0||Number.isNaN(t[0])||Number.isNaN(t[1])?go:t[0]===1/0||t[1]===1/0?N4:dp(BigInt(Math.round(t[0]*1e9))+BigInt(Math.round(t[1])));if(H2(t)){const n=x4.exec(t);if(n){const[r,s,l]=n,c=Number(s);switch(l){case"nano":case"nanos":return dp(BigInt(s));case"micro":case"micros":return z4(BigInt(s));case"milli":case"millis":return vo(c);case"second":case"seconds":return L4(c);case"minute":case"minutes":return $4(c);case"hour":case"hours":return F4(c);case"day":case"days":return j4(c);case"week":case"weeks":return U4(c)}}}throw new Error("Invalid DurationInput")},V1={_tag:"Millis",millis:0},C4={_tag:"Infinity"},D4={[qp]:qp,[Dt](){return he(this,X2(this.value))},[Ct](t){return kR(t)&&G4(this,t)},toString(){return`Duration(${Q4(this)})`},toJSON(){switch(this.value._tag){case"Millis":return{_id:"Duration",_tag:"Millis",millis:this.value.millis};case"Nanos":return{_id:"Duration",_tag:"Nanos",hrtime:q4(this)};case"Infinity":return{_id:"Duration",_tag:"Infinity"}}},[le](){return this.toJSON()},pipe(){return Tt(this,arguments)}},Da=t=>{const n=Object.create(D4);return $p(t)?isNaN(t)||t<=0?n.value=V1:Number.isFinite(t)?Number.isInteger(t)?n.value={_tag:"Millis",millis:t}:n.value={_tag:"Nanos",nanos:BigInt(Math.round(t*1e6))}:n.value=C4:t<=DR?n.value=V1:n.value={_tag:"Nanos",nanos:t},n},kR=t=>Mt(t,qp),k4=t=>{switch(t.value._tag){case"Millis":return t.value.millis===0;case"Nanos":return t.value.nanos===DR;case"Infinity":return!1}},go=Da(0),N4=Da(1/0),dp=t=>Da(t),z4=t=>Da(t*Hp),vo=t=>Da(t),L4=t=>Da(t*1e3),$4=t=>Da(t*6e4),F4=t=>Da(t*36e5),j4=t=>Da(t*864e5),U4=t=>Da(t*6048e5),Ip=t=>H4(t,{onMillis:n=>n,onNanos:n=>Number(n)/1e6}),B4=t=>{const n=xa(t);switch(n.value._tag){case"Infinity":throw new Error("Cannot convert infinite duration to nanos");case"Nanos":return n.value.nanos;case"Millis":return BigInt(Math.round(n.value.millis*1e6))}},q4=t=>{const n=xa(t);switch(n.value._tag){case"Infinity":return[1/0,0];case"Nanos":return[Number(n.value.nanos/P1),Number(n.value.nanos%P1)];case"Millis":return[Math.floor(n.value.millis/1e3),Math.round(n.value.millis%1e3*1e6)]}},H4=O(2,(t,n)=>{const r=xa(t);switch(r.value._tag){case"Nanos":return n.onNanos(r.value.nanos);case"Infinity":return n.onMillis(1/0);case"Millis":return n.onMillis(r.value.millis)}}),t0=O(3,(t,n,r)=>{const s=xa(t),l=xa(n);if(s.value._tag==="Infinity"||l.value._tag==="Infinity")return r.onMillis(Ip(s),Ip(l));if(s.value._tag==="Nanos"||l.value._tag==="Nanos"){const c=s.value._tag==="Nanos"?s.value.nanos:BigInt(Math.round(s.value.millis*1e6)),f=l.value._tag==="Nanos"?l.value.nanos:BigInt(Math.round(l.value.millis*1e6));return r.onNanos(c,f)}return r.onMillis(s.value.millis,l.value.millis)}),I4=(t,n)=>t0(t,n,{onMillis:(r,s)=>r===s,onNanos:(r,s)=>r===s}),P4=O(2,(t,n)=>t0(t,n,{onMillis:(r,s)=>r<=s,onNanos:(r,s)=>r<=s})),V4=O(2,(t,n)=>t0(t,n,{onMillis:(r,s)=>r>=s,onNanos:(r,s)=>r>=s})),G4=O(2,(t,n)=>I4(xa(t),xa(n))),K4=t=>{const n=xa(t);if(n.value._tag==="Infinity")return{days:1/0,hours:1/0,minutes:1/0,seconds:1/0,millis:1/0,nanos:1/0};const r=B4(n),s=r/I1,l=s/Hp,c=l/Af,f=c/Af,h=f/H1;return{days:Number(h),hours:Number(f%H1),minutes:Number(c%Af),seconds:Number(l%Af),millis:Number(s%Hp),nanos:Number(r%I1)}},Q4=t=>{const n=xa(t);if(n.value._tag==="Infinity")return"Infinity";if(k4(n))return"0";const r=K4(n),s=[];return r.days!==0&&s.push(`${r.days}d`),r.hours!==0&&s.push(`${r.hours}h`),r.minutes!==0&&s.push(`${r.minutes}m`),r.seconds!==0&&s.push(`${r.seconds}s`),r.millis!==0&&s.push(`${r.millis}ms`),r.nanos!==0&&s.push(`${r.nanos}ns`),s.join(" ")},Fs=5,e0=Math.pow(2,Fs),Y4=e0-1,X4=e0/2,Z4=e0/4;function J4(t){return t-=t>>1&1431655765,t=(t&858993459)+(t>>2&858993459),t=t+(t>>4)&252645135,t+=t>>8,t+=t>>16,t&127}function _o(t,n){return n>>>t&Y4}function Gi(t){return 1<<t}function NR(t,n){return J4(t&n-1)}const W4=(t,n)=>({value:t,previous:n});function Ji(t,n,r,s){let l=s;if(!t){const c=s.length;l=new Array(c);for(let f=0;f<c;++f)l[f]=s[f]}return l[n]=r,l}function zR(t,n,r){const s=r.length-1;let l=0,c=0,f=r;if(t)l=c=n;else for(f=new Array(s);l<n;)f[c++]=r[l++];for(++l;l<=s;)f[c++]=r[l++];return t&&(f.length=s),f}function tz(t,n,r,s){const l=s.length;if(t){let m=l;for(;m>=n;)s[m--]=s[m];return s[n]=r,s}let c=0,f=0;const h=new Array(l+1);for(;c<n;)h[f++]=s[c++];for(h[n]=r;c<l;)h[++f]=s[c++];return h}class Pr{constructor(){E(this,"_tag","EmptyNode")}modify(n,r,s,l,c,f){const h=s(gt());return He(h)?new Pr:(++f.value,new Cs(n,l,c,h))}}function la(t){return P2(t,"EmptyNode")}function ez(t){return la(t)||t._tag==="LeafNode"||t._tag==="CollisionNode"}function Yd(t,n){return la(t)?!1:n===t.edit}class Cs{constructor(n,r,s,l){E(this,"edit");E(this,"hash");E(this,"key");E(this,"value");E(this,"_tag","LeafNode");this.edit=n,this.hash=r,this.key=s,this.value=l}modify(n,r,s,l,c,f){if(Ot(c,this.key)){const m=s(this.value);return m===this.value?this:He(m)?(--f.value,new Pr):Yd(this,n)?(this.value=m,this):new Cs(n,l,c,m)}const h=s(gt());return He(h)?this:(++f.value,LR(n,r,this.hash,this,l,new Cs(n,l,c,h)))}}class n0{constructor(n,r,s){E(this,"edit");E(this,"hash");E(this,"children");E(this,"_tag","CollisionNode");this.edit=n,this.hash=r,this.children=s}modify(n,r,s,l,c,f){if(l===this.hash){const m=Yd(this,n),y=this.updateCollisionList(m,n,this.hash,this.children,s,c,f);return y===this.children?this:y.length>1?new n0(n,this.hash,y):y[0]}const h=s(gt());return He(h)?this:(++f.value,LR(n,r,this.hash,this,l,new Cs(n,l,c,h)))}updateCollisionList(n,r,s,l,c,f,h){const m=l.length;for(let p=0;p<m;++p){const g=l[p];if("key"in g&&Ot(f,g.key)){const _=g.value,S=c(_);return S===_?l:He(S)?(--h.value,zR(n,p,l)):Ji(n,p,new Cs(r,s,f,S),l)}}const y=c(gt());return He(y)?l:(++h.value,Ji(n,m,new Cs(r,s,f,y),l))}}class bo{constructor(n,r,s){E(this,"edit");E(this,"mask");E(this,"children");E(this,"_tag","IndexedNode");this.edit=n,this.mask=r,this.children=s}modify(n,r,s,l,c,f){const h=this.mask,m=this.children,y=_o(r,l),p=Gi(y),g=NR(h,p),_=h&p,S=Yd(this,n);if(!_){const j=new Pr().modify(n,r+Fs,s,l,c,f);return j?m.length>=X4?az(n,y,j,h,m):new bo(n,h|p,tz(S,g,j,m)):this}const T=m[g],A=T.modify(n,r+Fs,s,l,c,f);if(T===A)return this;let x=h,L;if(la(A)){if(x&=~p,!x)return new Pr;if(m.length<=2&&ez(m[g^1]))return m[g^1];L=zR(S,g,m)}else L=Ji(S,g,A,m);return S?(this.mask=x,this.children=L,this):new bo(n,x,L)}}class a0{constructor(n,r,s){E(this,"edit");E(this,"size");E(this,"children");E(this,"_tag","ArrayNode");this.edit=n,this.size=r,this.children=s}modify(n,r,s,l,c,f){let h=this.size;const m=this.children,y=_o(r,l),p=m[y],g=(p||new Pr).modify(n,r+Fs,s,l,c,f);if(p===g)return this;const _=Yd(this,n);let S;if(la(p)&&!la(g))++h,S=Ji(_,y,g,m);else if(!la(p)&&la(g)){if(--h,h<=Z4)return nz(n,h,y,m);S=Ji(_,y,new Pr,m)}else S=Ji(_,y,g,m);return _?(this.size=h,this.children=S,this):new a0(n,h,S)}}function nz(t,n,r,s){const l=new Array(n-1);let c=0,f=0;for(let h=0,m=s.length;h<m;++h)if(h!==r){const y=s[h];y&&!la(y)&&(l[c++]=y,f|=1<<h)}return new bo(t,f,l)}function az(t,n,r,s,l){const c=[];let f=s,h=0;for(let m=0;f;++m)f&1&&(c[m]=l[h++]),f>>>=1;return c[n]=r,new a0(t,h+1,c)}function rz(t,n,r,s,l,c){if(r===l)return new n0(t,r,[c,s]);const f=_o(n,r),h=_o(n,l);if(f===h)return m=>new bo(t,Gi(f)|Gi(h),[m]);{const m=f<h?[s,c]:[c,s];return new bo(t,Gi(f)|Gi(h),m)}}function LR(t,n,r,s,l,c){let f,h=n;for(;;){const m=rz(t,h,r,s,l,c);if(typeof m=="function")f=W4(m,f),h=h+Fs;else{let y=m;for(;f!=null;)y=f.value(y),f=f.previous;return y}}}const $R="effect/HashMap",Pp=Symbol.for($R),sz={[Pp]:Pp,[Symbol.iterator](){return new Xd(this,(t,n)=>[t,n])},[Dt](){let t=mt($R);for(const n of this)t^=D(mt(n[0]),jt(mt(n[1])));return he(this,t)},[Ct](t){if(lz(t)){if(t._size!==this._size)return!1;for(const n of this){const r=D(t,s0(n[0],mt(n[0])));if(He(r))return!1;if(!Ot(n[1],r.value))return!1}return!0}return!1},toString(){return Re(this.toJSON())},toJSON(){return{_id:"HashMap",values:Array.from(this).map(_e)}},[le](){return this.toJSON()},pipe(){return Tt(this,arguments)}},r0=(t,n,r,s)=>{const l=Object.create(sz);return l._editable=t,l._edit=n,l._root=r,l._size=s,l};class Xd{constructor(n,r){E(this,"map");E(this,"f");E(this,"v");this.map=n,this.f=r,this.v=FR(this.map._root,this.f,void 0)}next(){if(He(this.v))return{done:!0,value:void 0};const n=this.v.value;return this.v=ld(n.cont),{done:!1,value:n.value}}[Symbol.iterator](){return new Xd(this.map,this.f)}}const ld=t=>t?jR(t[0],t[1],t[2],t[3],t[4]):gt(),FR=(t,n,r=void 0)=>{switch(t._tag){case"LeafNode":return Aa(t.value)?Rt({value:n(t.key,t.value.value),cont:r}):ld(r);case"CollisionNode":case"ArrayNode":case"IndexedNode":{const s=t.children;return jR(s.length,s,0,n,r)}default:return ld(r)}},jR=(t,n,r,s,l)=>{for(;r<t;){const c=n[r++];if(c&&!la(c))return FR(c,s,[t,n,r,s,l])}return ld(l)},iz=r0(!1,0,new Pr,0),Zd=()=>iz,oz=t=>{const n=BR(Zd());for(const r of t)bc(n,r[0],r[1]);return hz(n)},lz=t=>Mt(t,Pp),cz=t=>t&&la(t._root),uz=O(2,(t,n)=>s0(t,n,mt(n))),s0=O(3,(t,n,r)=>{let s=t._root,l=0;for(;;)switch(s._tag){case"LeafNode":return Ot(n,s.key)?s.value:gt();case"CollisionNode":{if(r===s.hash){const c=s.children;for(let f=0,h=c.length;f<h;++f){const m=c[f];if("key"in m&&Ot(n,m.key))return m.value}}return gt()}case"IndexedNode":{const c=_o(l,r),f=Gi(c);if(s.mask&f){s=s.children[NR(s.mask,f)],l+=Fs;break}return gt()}case"ArrayNode":{if(s=s.children[_o(l,r)],s){l+=Fs;break}return gt()}default:return gt()}}),fz=O(2,(t,n)=>Aa(s0(t,n,mt(n)))),bc=O(3,(t,n,r)=>i0(t,n,()=>Rt(r))),dz=O(3,(t,n,r)=>t._editable?(t._root=n,t._size=r,t):n===t._root?t:r0(t._editable,t._edit,n,r)),UR=t=>new Xd(t,n=>n),Vp=t=>t._size,BR=t=>r0(!0,t._edit+1,t._root,t._size),hz=t=>(t._editable=!1,t),i0=O(3,(t,n,r)=>mz(t,n,mt(n),r)),mz=O(4,(t,n,r,s)=>{const l={value:t._size},c=t._root.modify(t._editable?t._edit:NaN,0,s,r,n,l);return D(t,dz(c,l.value))}),G1=O(2,(t,n)=>i0(t,n,gt)),yz=O(2,(t,n)=>Jd(t,Zd(),(r,s,l)=>bc(r,l,n(s,l)))),qR=O(2,(t,n)=>Jd(t,void 0,(r,s,l)=>n(s,l))),Jd=O(3,(t,n,r)=>{const s=t._root;if(s._tag==="LeafNode")return Aa(s.value)?r(n,s.value.value,s.key):n;if(s._tag==="EmptyNode")return n;const l=[s.children];let c;for(;c=l.pop();)for(let f=0,h=c.length;f<h;){const m=c[f++];m&&!la(m)&&(m._tag==="LeafNode"?Aa(m.value)&&(n=r(n,m.value.value,m.key)):l.push(m.children))}return n}),HR="effect/HashSet",Gp=Symbol.for(HR),pz={[Gp]:Gp,[Symbol.iterator](){return UR(this._keyMap)},[Dt](){return he(this,jt(mt(this._keyMap))(mt(HR)))},[Ct](t){return gz(t)?Vp(this._keyMap)===Vp(t._keyMap)&&Ot(this._keyMap,t._keyMap):!1},toString(){return Re(this.toJSON())},toJSON(){return{_id:"HashSet",values:Array.from(this).map(_e)}},[le](){return this.toJSON()},pipe(){return Tt(this,arguments)}},Wd=t=>{const n=Object.create(pz);return n._keyMap=t,n},gz=t=>Mt(t,Gp),vz=Wd(Zd()),th=()=>vz,_z=t=>{const n=o0(th());for(const r of t)Sc(n,r);return l0(n)},bz=(...t)=>{const n=o0(th());for(const r of t)Sc(n,r);return l0(n)},Sz=O(2,(t,n)=>fz(t._keyMap,n)),Ez=t=>Vp(t._keyMap),o0=t=>Wd(BR(t._keyMap)),l0=t=>(t._keyMap._editable=!1,t),IR=O(2,(t,n)=>{const r=o0(t);return n(r),l0(r)}),Sc=O(2,(t,n)=>t._keyMap._editable?(bc(n,!0)(t._keyMap),t):Wd(bc(n,!0)(t._keyMap))),PR=O(2,(t,n)=>t._keyMap._editable?(G1(n)(t._keyMap),t):Wd(G1(n)(t._keyMap))),Tz=O(2,(t,n)=>IR(t,r=>{for(const s of n)PR(r,s)})),Rz=O(2,(t,n)=>IR(th(),r=>{Oz(t,s=>Sc(r,s));for(const s of n)Sc(r,s)})),Oz=O(2,(t,n)=>qR(t._keyMap,(r,s)=>n(s))),Mz=O(3,(t,n,r)=>Jd(t._keyMap,n,(s,l,c)=>r(s,c))),js=th,wz=_z,c0=bz,Az=Sz,VR=Ez,dc=Sc,GR=PR,K1=Tz,Ec=Rz,Tc=Mz,Q1=Symbol.for("effect/MutableRef"),xz={[Q1]:Q1,toString(){return Re(this.toJSON())},toJSON(){return{_id:"MutableRef",current:_e(this.current)}},[le](){return this.toJSON()},pipe(){return Tt(this,arguments)}},eh=t=>{const n=Object.create(xz);return n.current=t,n},Cz=O(3,(t,n,r)=>Ot(n,t.current)?(t.current=r,!0):!1),Vr=t=>t.current,nh=O(2,(t,n)=>(t.current=n,t)),ah="effect/FiberId",Us=Symbol.for(ah),So="None",Kp="Runtime",Qp="Composite",Dz=Ee(`${ah}-${So}`);var lT;let kz=class{constructor(){E(this,lT,Us);E(this,"_tag",So);E(this,"id",-1);E(this,"startTimeMillis",-1)}[(lT=Us,Dt)](){return Dz}[Ct](n){return u0(n)&&n._tag===So}toString(){return Re(this.toJSON())}toJSON(){return{_id:"FiberId",_tag:this._tag}}[le](){return this.toJSON()}};var cT;class Nz{constructor(n,r){E(this,"id");E(this,"startTimeMillis");E(this,cT,Us);E(this,"_tag",Kp);this.id=n,this.startTimeMillis=r}[(cT=Us,Dt)](){return he(this,Ee(`${ah}-${this._tag}-${this.id}-${this.startTimeMillis}`))}[Ct](n){return u0(n)&&n._tag===Kp&&this.id===n.id&&this.startTimeMillis===n.startTimeMillis}toString(){return Re(this.toJSON())}toJSON(){return{_id:"FiberId",_tag:this._tag,id:this.id,startTimeMillis:this.startTimeMillis}}[le](){return this.toJSON()}}var uT;class zz{constructor(n,r){E(this,"left");E(this,"right");E(this,uT,Us);E(this,"_tag",Qp);E(this,"_hash");this.left=n,this.right=r}[(uT=Us,Dt)](){return D(Ee(`${ah}-${this._tag}`),jt(mt(this.left)),jt(mt(this.right)),he(this))}[Ct](n){return u0(n)&&n._tag===Qp&&Ot(this.left,n.left)&&Ot(this.right,n.right)}toString(){return Re(this.toJSON())}toJSON(){return{_id:"FiberId",_tag:this._tag,left:_e(this.left),right:_e(this.right)}}[le](){return this.toJSON()}}const KR=new kz,u0=t=>Mt(t,Us),QR=O(2,(t,n)=>t._tag===So?n:n._tag===So?t:new zz(t,n)),Lz=t=>D(t,Tc(KR,(n,r)=>QR(r)(n))),cd=t=>{switch(t._tag){case So:return js();case Kp:return c0(t.id);case Qp:return D(cd(t.left),Ec(cd(t.right)))}},Y1=Ut(Symbol.for("effect/Fiber/Id/_fiberCounter"),()=>eh(0)),YR=t=>Array.from(cd(t)).map(r=>`#${r}`).join(","),$z=()=>{const t=Vr(Y1);return D(Y1,nh(t+1)),new Nz(t,Date.now())},Bs=KR,Fz=QR,u9=Lz,jz=cd,Uz=YR,XR=$z,rh=Zd,Bz=oz,qz=cz,ZR=uz,JR=bc,WR=UR,Hz=i0,Iz=yz,Pz=qR,tO=Jd,Rc=Symbol.for("effect/List"),Yp=t=>de(t),Vz=t=>Bk(Xg(t),Yp),Gz=Vz(Ot),Kz={[Rc]:Rc,_tag:"Cons",toString(){return Re(this.toJSON())},toJSON(){return{_id:"List",_tag:"Cons",values:Yp(this).map(_e)}},[le](){return this.toJSON()},[Ct](t){return nO(t)&&this._tag===t._tag&&Gz(this,t)},[Dt](){return he(this,Hc(Yp(this)))},[Symbol.iterator](){let t=!1,n=this;return{next(){if(t)return this.return();if(n._tag==="Nil")return t=!0,this.return();const r=n.head;return n=n.tail,{done:t,value:r}},return(r){return t||(t=!0),{done:!0,value:r}}}},pipe(){return Tt(this,arguments)}},ud=(t,n)=>{const r=Object.create(Kz);return r.head=t,r.tail=n,r},Qz=Ee("Nil"),Yz={[Rc]:Rc,_tag:"Nil",toString(){return Re(this.toJSON())},toJSON(){return{_id:"List",_tag:"Nil"}},[le](){return this.toJSON()},[Dt](){return Qz},[Ct](t){return nO(t)&&this._tag===t._tag},[Symbol.iterator](){return{next(){return{done:!0,value:void 0}}}},pipe(){return Tt(this,arguments)}},eO=Object.create(Yz),nO=t=>Mt(t,Rc),er=t=>t._tag==="Nil",Xz=t=>t._tag==="Cons",Zz=()=>eO,qs=(t,n)=>ud(t,n),Eo=Zz,f0=t=>ud(t,eO),Jz=O(2,(t,n)=>tL(n,t)),Wz=O(2,(t,n)=>qs(n,t)),tL=O(2,(t,n)=>{if(er(t))return n;if(er(n))return t;{const r=ud(n.head,t);let s=r,l=n.tail;for(;!er(l);){const c=ud(l.head,t);s.tail=c,s=c,l=l.tail}return r}}),eL=O(3,(t,n,r)=>{let s=n,l=t;for(;!er(l);)s=r(s,l.head),l=l.tail;return s}),nL=t=>{let n=Eo(),r=t;for(;!er(r);)n=Wz(n,r.head),r=r.tail;return n},d0=function(){function t(n){n&&Object.assign(this,n)}return t.prototype=Gg,t}(),aL=Symbol.for("effect/DifferContextPatch");function X1(t){return t}const Vc={...d0.prototype,[aL]:{_Value:X1,_Patch:X1}},rL=Object.assign(Object.create(Vc),{_tag:"Empty"}),sL=Object.create(rL),aO=()=>sL,iL=Object.assign(Object.create(Vc),{_tag:"AndThen"}),oL=(t,n)=>{const r=Object.create(iL);return r.first=t,r.second=n,r},lL=Object.assign(Object.create(Vc),{_tag:"AddService"}),cL=(t,n)=>{const r=Object.create(lL);return r.key=t,r.service=n,r},uL=Object.assign(Object.create(Vc),{_tag:"RemoveService"}),fL=t=>{const n=Object.create(uL);return n.key=t,n},dL=Object.assign(Object.create(Vc),{_tag:"UpdateService"}),hL=(t,n)=>{const r=Object.create(dL);return r.key=t,r.update=n,r},mL=(t,n)=>{const r=new Map(t.unsafeMap);let s=aO();for(const[l,c]of n.unsafeMap.entries())if(r.has(l)){const f=r.get(l);r.delete(l),Ot(f,c)||(s=Uf(hL(l,()=>c))(s))}else r.delete(l),s=Uf(cL(l,c))(s);for(const[l]of r.entries())s=Uf(fL(l))(s);return s},Uf=O(2,(t,n)=>oL(t,n)),yL=O(2,(t,n)=>{if(t._tag==="Empty")return n;let r=!1,s=ln(t);const l=new Map(n.unsafeMap);for(;ar(s);){const f=Ra(s),h=Wa(s);switch(f._tag){case"Empty":{s=h;break}case"AddService":{l.set(f.key,f.service),s=h;break}case"AndThen":{s=Yn(Yn(h,f.second),f.first);break}case"RemoveService":{l.delete(f.key),s=h;break}case"UpdateService":{l.set(f.key,f.update(l.get(f.key))),r=!0,s=h;break}}}if(!r)return po(l);const c=new Map;for(const[f]of n.unsafeMap)l.has(f)&&(c.set(f,l.get(f)),l.delete(f));for(const[f,h]of l)c.set(f,h);return po(c)}),pL=Symbol.for("effect/DifferHashSetPatch");function hp(t){return t}const sh={...d0.prototype,[pL]:{_Value:hp,_Key:hp,_Patch:hp}},gL=Object.assign(Object.create(sh),{_tag:"Empty"}),vL=Object.create(gL),rO=()=>vL,_L=Object.assign(Object.create(sh),{_tag:"AndThen"}),bL=(t,n)=>{const r=Object.create(_L);return r.first=t,r.second=n,r},SL=Object.assign(Object.create(sh),{_tag:"Add"}),EL=t=>{const n=Object.create(SL);return n.value=t,n},TL=Object.assign(Object.create(sh),{_tag:"Remove"}),RL=t=>{const n=Object.create(TL);return n.value=t,n},OL=(t,n)=>{const[r,s]=Tc([t,rO()],([l,c],f)=>Az(f)(l)?[GR(f)(l),c]:[l,Xp(EL(f))(c)])(n);return Tc(s,(l,c)=>Xp(RL(c))(l))(r)},Xp=O(2,(t,n)=>bL(t,n)),ML=O(2,(t,n)=>{if(t._tag==="Empty")return n;let r=n,s=ln(t);for(;ar(s);){const l=Ra(s),c=Wa(s);switch(l._tag){case"Empty":{s=c;break}case"AndThen":{s=Yn(l.first)(Yn(l.second)(c));break}case"Add":{r=dc(l.value)(r),s=c;break}case"Remove":r=GR(l.value)(r),s=c}}return r}),wL=Symbol.for("effect/DifferReadonlyArrayPatch");function Z1(t){return t}const Gc={...d0.prototype,[wL]:{_Value:Z1,_Patch:Z1}},AL=Object.assign(Object.create(Gc),{_tag:"Empty"}),xL=Object.create(AL),sO=()=>xL,CL=Object.assign(Object.create(Gc),{_tag:"AndThen"}),DL=(t,n)=>{const r=Object.create(CL);return r.first=t,r.second=n,r},kL=Object.assign(Object.create(Gc),{_tag:"Append"}),NL=t=>{const n=Object.create(kL);return n.values=t,n},zL=Object.assign(Object.create(Gc),{_tag:"Slice"}),LL=(t,n)=>{const r=Object.create(zL);return r.from=t,r.until=n,r},$L=Object.assign(Object.create(Gc),{_tag:"Update"}),FL=(t,n)=>{const r=Object.create($L);return r.index=t,r.patch=n,r},jL=t=>{let n=0,r=sO();for(;n<t.oldValue.length&&n<t.newValue.length;){const s=t.oldValue[n],l=t.newValue[n],c=t.differ.diff(s,l);Ot(c,t.differ.empty)||(r=Bf(r,FL(n,c))),n=n+1}return n<t.oldValue.length&&(r=Bf(r,LL(0,n))),n<t.newValue.length&&(r=Bf(r,NL(IN(n)(t.newValue)))),r},Bf=O(2,(t,n)=>DL(t,n)),UL=O(3,(t,n,r)=>{if(t._tag==="Empty")return n;let s=n.slice(),l=ra(t);for(;FN(l);){const c=on(l),f=zs(l);switch(c._tag){case"Empty":{l=f;break}case"AndThen":{f.unshift(c.first,c.second),l=f;break}case"Append":{for(const h of c.values)s.push(h);l=f;break}case"Slice":{s=s.slice(c.from,c.until),l=f;break}case"Update":{s[c.index]=r.patch(c.patch,s[c.index]),l=f;break}}}return s}),BL=Symbol.for("effect/Differ"),qL={[BL]:{_P:Yt,_V:Yt},pipe(){return Tt(this,arguments)}},Bo=t=>{const n=Object.create(qL);return n.empty=t.empty,n.diff=t.diff,n.combine=t.combine,n.patch=t.patch,n},HL=()=>Bo({empty:aO(),combine:(t,n)=>Uf(n)(t),diff:(t,n)=>mL(t,n),patch:(t,n)=>yL(n)(t)}),IL=()=>Bo({empty:rO(),combine:(t,n)=>Xp(n)(t),diff:(t,n)=>OL(t,n),patch:(t,n)=>ML(n)(t)}),PL=t=>Bo({empty:sO(),combine:(n,r)=>Bf(n,r),diff:(n,r)=>jL({oldValue:n,newValue:r,differ:t}),patch:(n,r)=>UL(n,r,t)}),iO=()=>VL((t,n)=>n),VL=t=>Bo({empty:Yt,combine:(n,r)=>n===Yt?r:r===Yt?n:s=>r(n(s)),diff:(n,r)=>Ot(n,r)?Yt:Bd(r),patch:(n,r)=>t(r,n(r))}),Oc=255,oO=8,Zp=t=>t&Oc,Jp=t=>t>>oO&Oc,Kc=(t,n)=>(t&Oc)+((n&t&Oc)<<oO),GL=Kc(0,0),KL=t=>Kc(t,t),QL=t=>Kc(t,0),YL=O(2,(t,n)=>Kc(Zp(t)&~n,Jp(t))),XL=O(2,(t,n)=>t|n),ZL=t=>~t>>>0&Oc,JL=0,Ys=1,WL=2,lO=4,Wp=16,cO=32,t$=t=>ih(t,cO),e$=O(2,(t,n)=>t&~n),n$=O(2,(t,n)=>t|n),Nr=t=>uO(t)&&!r$(t),uO=t=>ih(t,Ys),ih=O(2,(t,n)=>(t&n)!==0),fO=(...t)=>t.reduce((n,r)=>n|r,0),a$=fO(JL),J1=t=>ih(t,lO),r$=t=>ih(t,Wp),qr=O(2,(t,n)=>Kc(t^n,n)),Wi=O(2,(t,n)=>t&(ZL(Zp(n))|Jp(n))|Zp(n)&Jp(n)),W1=Bo({empty:GL,diff:(t,n)=>qr(t,n),combine:(t,n)=>XL(n)(t),patch:(t,n)=>Wi(n,t)}),s$=KL,dO=QL,tE=YL,hO=(t,n)=>({_tag:"Par",left:t,right:n}),xf=(t,n)=>({_tag:"Seq",left:t,right:n}),i$=t=>{let n=f0(t),r=Eo();for(;;){const[s,l]=eL(n,[mO(),Eo()],([c,f],h)=>{const[m,y]=o$(h);return[d$(c,m),Jz(f,y)]});if(r=l$(r,s),er(l))return nL(r);n=l}throw new Error("BUG: BlockedRequests.flatten - please report an issue at https://github.com/Effect-TS/effect/issues")},o$=t=>{let n=t,r=mO(),s=Eo(),l=Eo();for(;;)switch(n._tag){case"Empty":{if(er(s))return[r,l];n=s.head,s=s.tail;break}case"Par":{s=qs(n.right,s),n=n.left;break}case"Seq":{const c=n.left,f=n.right;switch(c._tag){case"Empty":{n=f;break}case"Par":{const h=c.left,m=c.right;n=hO(xf(h,f),xf(m,f));break}case"Seq":{const h=c.left,m=c.right;n=xf(h,xf(m,f));break}case"Single":{n=c,l=qs(f,l);break}}break}case"Single":{if(r=f$(r,n),er(s))return[r,l];n=s.head,s=s.tail;break}}throw new Error("BUG: BlockedRequests.step - please report an issue at https://github.com/Effect-TS/effect/issues")},l$=(t,n)=>{if(er(t))return f0(mp(n));if(h$(n))return t;const r=_$(t.head),s=m$(n);return r.length===1&&s.length===1&&Ot(r[0],s[0])?qs(v$(t.head,mp(n)),t.tail):qs(mp(n),t)},c$=Symbol.for("effect/RequestBlock/RequestBlockParallel"),u$={_R:t=>t};var fT;fT=c$;class h0{constructor(n){E(this,"map");E(this,fT,u$);this.map=n}}const mO=()=>new h0(rh()),f$=(t,n)=>new h0(Hz(t.map,n.dataSource,r=>wN(uc(r,w4(n.blockedRequest)),()=>ln(n.blockedRequest)))),d$=(t,n)=>new h0(tO(t.map,n.map,(r,s,l)=>JR(r,l,rr(ZR(r,l),{onNone:()=>s,onSome:c=>oa(s,c)})))),h$=t=>qz(t.map),m$=t=>Array.from(WR(t.map)),mp=t=>g$(Iz(t.map,n=>ln(n))),y$=Symbol.for("effect/RequestBlock/RequestBlockSequential"),p$={_R:t=>t};var dT;dT=y$;class yO{constructor(n){E(this,"map");E(this,dT,p$);this.map=n}}const g$=t=>new yO(t),v$=(t,n)=>new yO(tO(n.map,t.map,(r,s,l)=>JR(r,l,rr(ZR(r,l),{onNone:()=>ua(),onSome:c=>oa(c,s)})))),_$=t=>Array.from(WR(t.map)),b$=t=>Array.from(t.map),qo="Die",Hs="Empty",Xs="Fail",Ho="Interrupt",To="Parallel",Ro="Sequential",pO="effect/Cause",gO=Symbol.for(pO),S$={_E:t=>t},Io={[gO]:S$,[Dt](){return D(mt(pO),jt(mt(D$(this))),he(this))},[Ct](t){return E$(t)&&C$(this,t)},pipe(){return Tt(this,arguments)},toJSON(){switch(this._tag){case"Empty":return{_id:"Cause",_tag:this._tag};case"Die":return{_id:"Cause",_tag:this._tag,defect:_e(this.defect)};case"Interrupt":return{_id:"Cause",_tag:this._tag,fiberId:this.fiberId.toJSON()};case"Fail":return{_id:"Cause",_tag:this._tag,failure:_e(this.error)};case"Sequential":case"Parallel":return{_id:"Cause",_tag:this._tag,left:_e(this.left),right:_e(this.right)}}},toString(){return Qc(this)},[le](){return this.toJSON()}},Oo=(()=>{const t=Object.create(Io);return t._tag=Hs,t})(),Mo=t=>{const n=Object.create(Io);return n._tag=Xs,n.error=t,n},ca=t=>{const n=Object.create(Io);return n._tag=qo,n.defect=t,n},Ta=t=>{const n=Object.create(Io);return n._tag=Ho,n.fiberId=t,n},Is=(t,n)=>{const r=Object.create(Io);return r._tag=To,r.left=t,r.right=n,r},We=(t,n)=>{const r=Object.create(Io);return r._tag=Ro,r.left=t,r.right=n,r},E$=t=>Mt(t,gO),T$=t=>t._tag===Hs,f9=t=>t._tag===Xs,R$=t=>t._tag===qo,O$=t=>t._tag===Hs?!0:wo(t,!0,(n,r)=>{switch(r._tag){case Hs:return Rt(n);case qo:case Xs:case Ho:return Rt(!1);default:return gt()}}),vO=t=>Aa(w$(t)),m0=t=>g0(void 0,N$)(t),M$=t=>$s(wo(t,ua(),(n,r)=>r._tag===Xs?Rt(D(n,Yn(r.error))):gt())),_O=t=>$s(wo(t,ua(),(n,r)=>r._tag===qo?Rt(D(n,Yn(r.defect))):gt())),bO=t=>wo(t,js(),(n,r)=>r._tag===Ho?Rt(D(n,dc(r.fiberId))):gt()),SO=t=>y0(t,n=>n._tag===Xs?Rt(n.error):gt()),EO=t=>{const n=SO(t);switch(n._tag){case"None":return ia(t);case"Some":return Ir(n.value)}},w$=t=>y0(t,n=>n._tag===Ho?Rt(n.fiberId):gt()),eE=t=>p0(t,{onEmpty:Oo,onFail:()=>Oo,onDie:ca,onInterrupt:Ta,onSequential:We,onParallel:Is}),A$=t=>p0(t,{onEmpty:Oo,onFail:ca,onDie:ca,onInterrupt:Ta,onSequential:We,onParallel:Is}),d9=O(2,(t,n)=>x$(t,r=>Mo(n(r)))),x$=O(2,(t,n)=>p0(t,{onEmpty:Oo,onFail:r=>n(r),onDie:r=>ca(r),onInterrupt:r=>Ta(r),onSequential:(r,s)=>We(r,s),onParallel:(r,s)=>Is(r,s)})),C$=(t,n)=>{let r=ln(t),s=ln(n);for(;ar(r)&&ar(s);){const[l,c]=D(Ra(r),wo([js(),ua()],([m,y],p)=>{const[g,_]=tg(p);return Rt([D(m,Ec(g)),D(y,oa(_))])})),[f,h]=D(Ra(s),wo([js(),ua()],([m,y],p)=>{const[g,_]=tg(p);return Rt([D(m,Ec(g)),D(y,oa(_))])}));if(!Ot(l,f))return!1;r=c,s=h}return!0},D$=t=>k$(ln(t),ua()),k$=(t,n)=>{for(;;){const[r,s]=D(t,Yg([js(),ua()],([c,f],h)=>{const[m,y]=tg(h);return[D(c,Ec(m)),D(f,oa(y))]})),l=VR(r)>0?D(n,Yn(r)):n;if(A4(s))return $s(l);t=s,n=l}throw new Error(Pd("Cause.flattenCauseLoop"))},y0=O(2,(t,n)=>{const r=[t];for(;r.length>0;){const s=r.pop(),l=n(s);switch(l._tag){case"None":{switch(s._tag){case Ro:case To:{r.push(s.right),r.push(s.left);break}}break}case"Some":return l}}return gt()}),tg=t=>{let n=t;const r=[];let s=js(),l=ua();for(;n!==void 0;)switch(n._tag){case Hs:{if(r.length===0)return[s,l];n=r.pop();break}case Xs:{if(s=dc(s,fp(n._tag,n.error)),r.length===0)return[s,l];n=r.pop();break}case qo:{if(s=dc(s,fp(n._tag,n.defect)),r.length===0)return[s,l];n=r.pop();break}case Ho:{if(s=dc(s,fp(n._tag,n.fiberId)),r.length===0)return[s,l];n=r.pop();break}case Ro:{switch(n.left._tag){case Hs:{n=n.right;break}case Ro:{n=We(n.left.left,We(n.left.right,n.right));break}case To:{n=Is(We(n.left.left,n.right),We(n.left.right,n.right));break}default:{l=Yn(l,n.right),n=n.left;break}}break}case To:{r.push(n.right),n=n.left;break}}throw new Error(Pd("Cause.evaluateCauseLoop"))},N$={emptyCase:F1,failCase:Wf,dieCase:Wf,interruptCase:F1,sequentialCase:(t,n,r)=>n&&r,parallelCase:(t,n,r)=>n&&r},nE="SequentialCase",aE="ParallelCase",p0=O(2,(t,{onDie:n,onEmpty:r,onFail:s,onInterrupt:l,onParallel:c,onSequential:f})=>g0(t,void 0,{emptyCase:()=>r,failCase:(h,m)=>s(m),dieCase:(h,m)=>n(m),interruptCase:(h,m)=>l(m),sequentialCase:(h,m,y)=>f(m,y),parallelCase:(h,m,y)=>c(m,y)})),wo=O(3,(t,n,r)=>{let s=n,l=t;const c=[];for(;l!==void 0;){const f=r(s,l);switch(s=Aa(f)?f.value:s,l._tag){case Ro:{c.push(l.right),l=l.left;break}case To:{c.push(l.right),l=l.left;break}default:{l=void 0;break}}l===void 0&&c.length>0&&(l=c.pop())}return s}),g0=O(3,(t,n,r)=>{const s=[t],l=[];for(;s.length>0;){const f=s.pop();switch(f._tag){case Hs:{l.push(ia(r.emptyCase(n)));break}case Xs:{l.push(ia(r.failCase(n,f.error)));break}case qo:{l.push(ia(r.dieCase(n,f.defect)));break}case Ho:{l.push(ia(r.interruptCase(n,f.fiberId)));break}case Ro:{s.push(f.right),s.push(f.left),l.push(Ir({_tag:nE}));break}case To:{s.push(f.right),s.push(f.left),l.push(Ir({_tag:aE}));break}}}const c=[];for(;l.length>0;){const f=l.pop();switch(f._tag){case"Left":{switch(f.left._tag){case nE:{const h=c.pop(),m=c.pop(),y=r.sequentialCase(n,h,m);c.push(y);break}case aE:{const h=c.pop(),m=c.pop(),y=r.parallelCase(n,h,m);c.push(y);break}}break}case"Right":{c.push(f.right);break}}}if(c.length===0)throw new Error("BUG: Cause.reduceWithContext - please report an issue at https://github.com/Effect-TS/effect/issues");return c.pop()}),Qc=(t,n)=>m0(t)?"All fibers interrupted without errors.":RO(t).map(function(r){return(n==null?void 0:n.renderErrorCause)!==!0||r.cause===void 0?r.stack:`${r.stack} {
${TO(r.cause,"  ")}
}`}).join(`
`),TO=(t,n)=>{const r=t.stack.split(`
`);let s=`${n}[cause]: ${r[0]}`;for(let l=1,c=r.length;l<c;l++)s+=`
${n}${r[l]}`;return t.cause&&(s+=` {
${TO(t.cause,`${n}  `)}
${n}}`),s};class fd extends globalThis.Error{constructor(r){const s=typeof r=="object"&&r!==null,l=Error.stackTraceLimit;Error.stackTraceLimit=1;super(z$(r),s&&"cause"in r&&typeof r.cause<"u"?{cause:new fd(r.cause)}:void 0);E(this,"span");this.message===""&&(this.message="An error has occurred"),Error.stackTraceLimit=l,this.name=r instanceof Error?r.name:"Error",s&&(Ao in r&&(this.span=r[Ao]),Object.keys(r).forEach(c=>{c in this||(this[c]=r[c])})),this.stack=$$(`${this.name}: ${this.message}`,r instanceof Error&&r.stack?r.stack:"",this.span)}}const z$=t=>{if(typeof t=="string")return t;if(typeof t=="object"&&t!==null&&t instanceof Error)return t.message;try{if(Mt(t,"toString")&&Hd(t.toString)&&t.toString!==Object.prototype.toString&&t.toString!==globalThis.Array.prototype.toString)return t.toString()}catch{}return Z2(t)},L$=/\((.*)\)/g,dd=Ut("effect/Tracer/spanToTrace",()=>new WeakMap),$$=(t,n,r)=>{const s=[t],l=n.startsWith(t)?n.slice(t.length).split(`
`):n.split(`
`);for(let c=1;c<l.length;c++){if(l[c].includes(" at new BaseEffectError")||l[c].includes(" at new YieldableError")){c++;continue}if(l[c].includes("Generator.next")||l[c].includes("effect_internal_function"))break;s.push(l[c].replace(/at .*effect_instruction_i.*\((.*)\)/,"at $1").replace(/EffectPrimitive\.\w+/,"<anonymous>"))}if(r){let c=r,f=0;for(;c&&c._tag==="Span"&&f<10;){const h=dd.get(c);if(typeof h=="function"){const m=h();if(typeof m=="string"){const y=m.matchAll(L$);let p=!1;for(const[,g]of y)p=!0,s.push(`    at ${c.name} (${g})`);p||s.push(`    at ${c.name} (${m.replace(/^at /,"")})`)}else s.push(`    at ${c.name}`)}else s.push(`    at ${c.name}`);c=ys(c.parent),f++}}return s.join(`
`)},Ao=Symbol.for("effect/SpanAnnotation"),RO=t=>g0(t,void 0,{emptyCase:()=>[],dieCase:(n,r)=>[new fd(r)],failCase:(n,r)=>[new fd(r)],interruptCase:()=>[],parallelCase:(n,r,s)=>[...r,...s],sequentialCase:(n,r,s)=>[...r,...s]}),Yc="Pending",oh="Done",F$="effect/Deferred",j$=Symbol.for(F$),U$={_E:t=>t,_A:t=>t},B$=t=>({_tag:Yc,joiners:t}),OO=t=>({_tag:oh,effect:t});class Xc{constructor(n){E(this,"self");E(this,"called",!1);this.self=n}next(n){return this.called?{value:n,done:!0}:(this.called=!0,{value:this.self,done:!1})}return(n){return{value:n,done:!0}}throw(n){throw n}[Symbol.iterator](){return new Xc(this.self)}}const MO=(t,n)=>{const r=new xe("Blocked");return r.effect_instruction_i0=t,r.effect_instruction_i1=n,r},q$=t=>{const n=new xe("RunBlocked");return n.effect_instruction_i0=t,n},xo=Symbol.for("effect/Effect");class H${constructor(n,r){E(this,"patch");E(this,"op");E(this,"_op",Vg);this.patch=n,this.op=r}}var hT;class xe{constructor(n){E(this,"_op");E(this,"effect_instruction_i0");E(this,"effect_instruction_i1");E(this,"effect_instruction_i2");E(this,"trace");E(this,hT,yo);this._op=n}[(hT=xo,Ct)](n){return this===n}[Dt](){return he(this,qg(this))}pipe(){return Tt(this,arguments)}toJSON(){return{_id:"Effect",_op:this._op,effect_instruction_i0:_e(this.effect_instruction_i0),effect_instruction_i1:_e(this.effect_instruction_i1),effect_instruction_i2:_e(this.effect_instruction_i2)}}toString(){return Re(this.toJSON())}[le](){return this.toJSON()}[Symbol.iterator](){return new Xc(new qc(this))}}var mT;class wO{constructor(n){E(this,"_op");E(this,"effect_instruction_i0");E(this,"effect_instruction_i1");E(this,"effect_instruction_i2");E(this,"trace");E(this,mT,yo);this._op=n,this._tag=n}[(mT=xo,Ct)](n){return D0(n)&&n._op==="Failure"&&Ot(this.effect_instruction_i0,n.effect_instruction_i0)}[Dt](){return D(Ee(this._tag),jt(mt(this.effect_instruction_i0)),he(this))}get cause(){return this.effect_instruction_i0}pipe(){return Tt(this,arguments)}toJSON(){return{_id:"Exit",_tag:this._op,cause:this.cause.toJSON()}}toString(){return Re(this.toJSON())}[le](){return this.toJSON()}[Symbol.iterator](){return new Xc(new qc(this))}}var yT;class AO{constructor(n){E(this,"_op");E(this,"effect_instruction_i0");E(this,"effect_instruction_i1");E(this,"effect_instruction_i2");E(this,"trace");E(this,yT,yo);this._op=n,this._tag=n}[(yT=xo,Ct)](n){return D0(n)&&n._op==="Success"&&Ot(this.effect_instruction_i0,n.effect_instruction_i0)}[Dt](){return D(Ee(this._tag),jt(mt(this.effect_instruction_i0)),he(this))}get value(){return this.effect_instruction_i0}pipe(){return Tt(this,arguments)}toJSON(){return{_id:"Exit",_tag:this._op,value:_e(this.value)}}toString(){return Re(this.toJSON())}[le](){return this.toJSON()}[Symbol.iterator](){return new Xc(new qc(this))}}const ka=t=>Mt(t,xo),Xt=t=>{const n=new xe(tR);return n.effect_instruction_i0=t,n},xO=O(3,(t,n,r)=>fa(s=>J(t,l=>J(Hr(ae(()=>s(n(l)))),c=>ae(()=>r(l,c)).pipe(Xn({onFailure:f=>{switch(c._tag){case ze:return Oe(We(c.effect_instruction_i0,f));case Le:return Oe(f)}},onSuccess:()=>c})))))),cn=O(2,(t,n)=>J(t,()=>yt(n))),sr=t=>cn(t,void 0),CO=function(){const t=new xe(Gd);switch(arguments.length){case 2:{t.effect_instruction_i0=arguments[0],t.commit=arguments[1];break}case 3:{t.effect_instruction_i0=arguments[0],t.effect_instruction_i1=arguments[1],t.commit=arguments[2];break}case 4:{t.effect_instruction_i0=arguments[0],t.effect_instruction_i1=arguments[1],t.effect_instruction_i2=arguments[2],t.commit=arguments[3];break}default:throw new Error(Pd("you're not supposed to end up here"))}return t},Mc=(t,n=Bs)=>{const r=new xe(lc);let s;return r.effect_instruction_i0=l=>{s=t(l)},r.effect_instruction_i1=n,T0(r,l=>ka(s)?s:oe)},v0=(t,n=Bs)=>ae(()=>Mc(t,n)),Ca=(t,n=Bs)=>CO(t,function(){let r,s;function l(m){r?r(m):s===void 0&&(s=m)}const c=new xe(lc);c.effect_instruction_i0=m=>{r=m,s&&m(s)},c.effect_instruction_i1=n;let f,h;return this.effect_instruction_i0.length!==1?(h=new AbortController,f=Ge(()=>this.effect_instruction_i0(l,h.signal))):f=Ge(()=>this.effect_instruction_i0(l)),f||h?T0(c,m=>(h&&h.abort(),f??oe)):c}),_0=O(2,(t,n)=>{const r=new xe($f);return r.effect_instruction_i0=t,r.effect_instruction_i1=n,r}),hd=O(2,(t,n)=>Gr(t,{onFailure:n,onSuccess:yt})),I$=O(3,(t,n,r)=>_0(t,s=>{const l=EO(s);switch(l._tag){case"Left":return n(l.left)?r(l.left):Oe(s);case"Right":return Oe(l.right)}})),rE=Symbol.for("effect/OriginalAnnotation"),b0=(t,n)=>Aa(n)?new Proxy(t,{has(r,s){return s===Ao||s===rE||s in r},get(r,s){return s===Ao?n.value:s===rE?t:r[s]}}):t,md=t=>Id(t)&&!(Ao in t)?Xt(n=>Oe(ca(b0(t,L0(n))))):Oe(ca(t)),eg=t=>DO(()=>ca(new R6(t))),Co=t=>Gr(t,{onFailure:n=>yt(Ir(n)),onSuccess:n=>yt(ia(n))}),Hr=t=>kO(t,{onFailure:te,onSuccess:ie}),ve=t=>Id(t)&&!(Ao in t)?Xt(n=>Oe(Mo(b0(t,L0(n))))):Oe(Mo(t)),lh=t=>J(ot(t),ve),Oe=t=>{const n=new wO(ze);return n.effect_instruction_i0=t,n},DO=t=>J(ot(t),Oe),S0=Xt(t=>yt(t.id())),Zc=t=>Xt(n=>t(n.id())),J=O(2,(t,n)=>{const r=new xe(nd);return r.effect_instruction_i0=t,r.effect_instruction_i1=n,r}),Ki=O(2,(t,n)=>J(t,r=>{const s=typeof n=="function"?n(r):n;return ka(s)?s:G2(s)?Mc(l=>{s.then(c=>l(yt(c)),c=>l(ve(new gh(c,"An unknown error occurred in Effect.andThen"))))}):yt(s)})),P$=t=>{const n=new xe("OnStep");return n.effect_instruction_i0=t,n},ch=t=>J(t,Yt),kO=O(2,(t,n)=>Xn(t,{onFailure:r=>yt(n.onFailure(r)),onSuccess:r=>yt(n.onSuccess(r))})),Xn=O(2,(t,n)=>{const r=new xe(ad);return r.effect_instruction_i0=t,r.effect_instruction_i1=n.onFailure,r.effect_instruction_i2=n.onSuccess,r}),Gr=O(2,(t,n)=>Xn(t,{onFailure:r=>{if(_O(r).length>0)return Oe(A$(r));const l=M$(r);return l.length>0?n.onFailure(CR(l)):Oe(r)},onSuccess:n.onSuccess})),tr=O(2,(t,n)=>ae(()=>{const r=de(t),s=Qg(r.length);let l=0;return cn(O0({while:()=>l<r.length,body:()=>n(r[l],l),step:c=>{s[l++]=c}}),s)})),uh=O(2,(t,n)=>ae(()=>{const r=de(t);let s=0;return O0({while:()=>s<r.length,body:()=>n(r[s],s),step:()=>{s++}})})),V$=J(S0,t=>NO(t)),NO=t=>Oe(Ta(t)),E0=t=>{const n=new xe(zo);return n.effect_instruction_i0=s$(Ys),n.effect_instruction_i1=()=>t,n},zO=O(2,(t,n)=>fa(r=>J(Hr(r(t)),s=>F6(n,s)))),qt=O(2,(t,n)=>J(t,r=>ot(()=>n(r)))),LO=O(2,(t,n)=>Gr(t,{onFailure:r=>lh(()=>n.onFailure(r)),onSuccess:r=>ot(()=>n.onSuccess(r))})),fh=O(2,(t,n)=>Xn(t,{onFailure:r=>{const s=EO(r);switch(s._tag){case"Left":return lh(()=>n(s.left));case"Right":return Oe(s.right)}},onSuccess:yt})),Ps=O(2,(t,n)=>fa(r=>Xn(r(t),{onFailure:s=>{const l=te(s);return Xn(n(l),{onFailure:c=>te(We(s,c)),onSuccess:()=>l})},onSuccess:s=>{const l=ie(s);return qe(n(l),l)}}))),T0=O(2,(t,n)=>Ps(t,vh({onFailure:r=>m0(r)?sr(n(bO(r))):oe,onSuccess:()=>oe}))),G$=t=>K$(t,Yt),K$=O(2,(t,n)=>Gr(t,{onFailure:r=>md(n(r)),onSuccess:yt})),Q$=Xt((t,n)=>yt(n.runtimeFlags)),yt=t=>{const n=new AO(Le);return n.effect_instruction_i0=t,n},ae=t=>{const n=new xe(Gd);return n.commit=t,n},ot=t=>{const n=new xe(W2);return n.effect_instruction_i0=t,n},R0=O(t=>t.length===3||t.length===2&&!(Id(t[1])&&"onlyEffect"in t[1]),(t,n)=>J(t,r=>{const s=typeof n=="function"?n(r):n;return ka(s)?cn(s,r):G2(s)?Mc(l=>{s.then(c=>l(yt(r)),c=>l(ve(new gh(c,"An unknown error occurred in Effect.tap"))))}):yt(r)})),Y$=t=>Xt(n=>{const r=n.getFiberRef(ag),s=D(r,Gn(()=>n.scope()));return t(Jc(ag,Rt(s)))}),dh=t=>{const n=new xe(zo);return n.effect_instruction_i0=dO(Ys),n.effect_instruction_i1=()=>t,n},fa=t=>CO(t,function(){const n=new xe(zo);return n.effect_instruction_i0=dO(Ys),n.effect_instruction_i1=r=>uO(r)?Ge(()=>this.effect_instruction_i0(E0)):Ge(()=>this.effect_instruction_i0(dh)),n}),oe=yt(void 0),$O=t=>{const n=new xe(zo);return n.effect_instruction_i0=t,n.effect_instruction_i1=void 0,n},FO=O(2,(t,n)=>J(n,r=>r?D(t,qt(Rt)):yt(gt()))),O0=t=>{const n=new xe(rd);return n.effect_instruction_i0=t.while,n.effect_instruction_i1=t.body,n.effect_instruction_i2=t.step,n},X$=t=>ae(()=>{const n=new xe(cc);return n.effect_instruction_i0=t(),n}),Z$=function(){const t=arguments.length===1?arguments[0]:arguments[1].bind(arguments[0]);return X$(()=>t(D))},J$=O(2,(t,n)=>{const r=new xe(zo);return r.effect_instruction_i0=n,r.effect_instruction_i1=()=>t,r}),M0=t=>{const n=new xe(Ff);return typeof(t==null?void 0:t.priority)<"u"?p6(n,t.priority):n},hh=O(2,(t,n)=>J(t,r=>qt(n,s=>[r,s]))),mh=O(2,(t,n)=>J(t,r=>cn(n,r))),qe=O(2,(t,n)=>J(t,()=>n)),jO=O(3,(t,n,r)=>J(t,s=>qt(n,l=>r(s,l)))),w0=t=>J(S0,n=>D(t,wc(n))),wc=O(2,(t,n)=>J(t.interruptAsFork(n),()=>t.await)),W$={_tag:"All",syslog:0,label:"ALL",ordinal:Number.MIN_SAFE_INTEGER,pipe(){return Tt(this,arguments)}},t6={_tag:"Fatal",syslog:2,label:"FATAL",ordinal:5e4,pipe(){return Tt(this,arguments)}},e6={_tag:"Error",syslog:3,label:"ERROR",ordinal:4e4,pipe(){return Tt(this,arguments)}},n6={_tag:"Warning",syslog:4,label:"WARN",ordinal:3e4,pipe(){return Tt(this,arguments)}},UO={_tag:"Info",syslog:6,label:"INFO",ordinal:2e4,pipe(){return Tt(this,arguments)}},BO={_tag:"Debug",syslog:7,label:"DEBUG",ordinal:1e4,pipe(){return Tt(this,arguments)}},a6={_tag:"Trace",syslog:7,label:"TRACE",ordinal:0,pipe(){return Tt(this,arguments)}},r6={_tag:"None",syslog:7,label:"OFF",ordinal:Number.MAX_SAFE_INTEGER,pipe(){return Tt(this,arguments)}},s6="effect/FiberRef",i6=Symbol.for(s6),o6={_A:t=>t},A0=t=>Xt(n=>ie(n.getFiberRef(t))),yh=O(2,(t,n)=>J(A0(t),n)),sE=O(2,(t,n)=>l6(t,()=>[void 0,n])),l6=O(2,(t,n)=>Xt(r=>{const[s,l]=n(r.getFiberRef(t));return r.setFiberRef(t,l),yt(s)})),Jc=O(3,(t,n,r)=>xO(mh(A0(n),sE(n,r)),()=>t,s=>sE(n,s))),c6=O(3,(t,n,r)=>yh(n,s=>Jc(t,n,r(s)))),Ke=(t,n)=>Po(t,{differ:iO(),fork:(n==null?void 0:n.fork)??Yt,join:n==null?void 0:n.join}),u6=t=>{const n=IL();return Po(t,{differ:n,fork:n.empty})},f6=t=>{const n=PL(iO());return Po(t,{differ:n,fork:n.empty})},qO=t=>{const n=HL();return Po(t,{differ:n,fork:n.empty})},Po=(t,n)=>({...Lo,[i6]:o6,initial:t,commit(){return A0(this)},diff:(s,l)=>n.differ.diff(s,l),combine:(s,l)=>n.differ.combine(s,l),patch:s=>l=>n.differ.patch(s,l),fork:n.fork,join:n.join??((s,l)=>l)}),d6=t=>Po(t,{differ:W1,fork:W1.empty}),ir=Ut(Symbol.for("effect/FiberRef/currentContext"),()=>qO(_c())),Vo=Ut(Symbol.for("effect/FiberRef/currentSchedulingPriority"),()=>Ke(0)),HO=Ut(Symbol.for("effect/FiberRef/currentMaxOpsBeforeYield"),()=>Ke(2048)),h6=Ut(Symbol.for("effect/FiberRef/currentLogAnnotation"),()=>Ke(rh())),m6=Ut(Symbol.for("effect/FiberRef/currentLogLevel"),()=>Ke(UO)),y6=Ut(Symbol.for("effect/FiberRef/currentLogSpan"),()=>Ke(Eo())),p6=O(2,(t,n)=>Jc(t,Vo,n)),g6=Ut(Symbol.for("effect/FiberRef/currentConcurrency"),()=>Ke("unbounded")),v6=Ut(Symbol.for("effect/FiberRef/currentRequestBatching"),()=>Ke(!0)),_6=Ut(Symbol.for("effect/FiberRef/currentUnhandledErrorLogLevel"),()=>Ke(Rt(BO))),ng=Ut(Symbol.for("effect/FiberRef/currentMetricLabels"),()=>f6(Ls())),ag=Ut(Symbol.for("effect/FiberRef/currentForkScopeOverride"),()=>Ke(gt(),{fork:()=>gt(),join:(t,n)=>t})),Cf=Ut(Symbol.for("effect/FiberRef/currentInterruptedCause"),()=>Ke(Oo,{fork:()=>Oo,join:(t,n)=>t})),b6=Ut(Symbol.for("effect/FiberRef/currentTracerEnabled"),()=>Ke(!0)),IO=Ut(Symbol.for("effect/FiberRef/currentTracerTiming"),()=>Ke(!0)),S6=Ut(Symbol.for("effect/FiberRef/currentTracerSpanAnnotations"),()=>Ke(rh())),E6=Ut(Symbol.for("effect/FiberRef/currentTracerSpanLinks"),()=>Ke(ua())),iE=Symbol.for("effect/Scope"),oE=Symbol.for("effect/CloseableScope"),PO=(t,n)=>t.addFinalizer(()=>sr(n)),yd=(t,n)=>t.addFinalizer(n),rg=(t,n)=>t.close(n),ph=(t,n)=>t.fork(n),h9=t=>T6(Yt)(t),T6=O(2,(t,n)=>{const r=D(t,SO,uc(n));switch(r._tag){case"None":return D(_O(t),Wg,rr({onNone:()=>{const s=de(bO(t)).flatMap(l=>de(jz(l)).map(c=>`#${c}`));return new O6(s?`Interrupted by fibers: ${s.join(", ")}`:void 0)},onSome:Yt}));case"Some":return r.value}}),VO=function(){class t extends globalThis.Error{commit(){return ve(this)}toJSON(){const r={...this};return this.message&&(r.message=this.message),this.cause&&(r.cause=this.cause),r}[le](){return this.toString!==globalThis.Error.prototype.toString?this.stack?`${this.toString()}
${this.stack.split(`
`).slice(1).join(`
`)}`:this.toString():"Bun"in globalThis?Qc(Mo(this),{renderErrorCause:!0}):this}}return Object.assign(t.prototype,dN),t}(),x0=(t,n)=>{class r extends VO{constructor(){super(...arguments);E(this,"_tag",n)}}return Object.assign(r.prototype,t),r.prototype.name=n,r},lE=Symbol.for("effect/Cause/errors/RuntimeException"),R6=x0({[lE]:lE},"RuntimeException"),sg=Symbol.for("effect/Cause/errors/InterruptedException"),O6=x0({[sg]:sg},"InterruptedException"),M6=t=>Mt(t,sg),cE=Symbol.for("effect/Cause/errors/NoSuchElement"),C0=x0({[cE]:cE},"NoSuchElementException"),uE=Symbol.for("effect/Cause/errors/UnknownException"),gh=function(){class t extends VO{constructor(s,l){super(l??"An unknown error occurred",{cause:s});E(this,"_tag","UnknownException");E(this,"error");this.error=s}}return Object.assign(t.prototype,{[uE]:uE,name:"UnknownException"}),t}(),D0=t=>ka(t)&&"_tag"in t&&(t._tag==="Success"||t._tag==="Failure"),GO=t=>t._tag==="Failure",w6=t=>t._tag==="Success",A6=t=>{switch(t._tag){case ze:return vO(t.effect_instruction_i0);case Le:return!1}},x6=O(2,(t,n)=>{switch(t._tag){case ze:return te(t.effect_instruction_i0);case Le:return ie(n)}}),yp=t=>x6(t,void 0),to=(t,n)=>L6(t,n!=null&&n.parallel?Is:We),Qi=t=>te(ca(t)),ig=t=>te(Mo(t)),te=t=>{const n=new wO(ze);return n.effect_instruction_i0=t,n},C6=O(2,(t,n)=>{switch(t._tag){case ze:return te(t.effect_instruction_i0);case Le:return n(t.effect_instruction_i0)}}),D6=t=>D(t,C6(Yt)),k6=t=>te(Ta(t)),qf=O(2,(t,n)=>{switch(t._tag){case ze:return te(t.effect_instruction_i0);case Le:return ie(n(t.effect_instruction_i0))}}),vh=O(2,(t,{onFailure:n,onSuccess:r})=>{switch(t._tag){case ze:return n(t.effect_instruction_i0);case Le:return r(t.effect_instruction_i0)}}),og=O(2,(t,{onFailure:n,onSuccess:r})=>{switch(t._tag){case ze:return n(t.effect_instruction_i0);case Le:return r(t.effect_instruction_i0)}}),ie=t=>{const n=new AO(Le);return n.effect_instruction_i0=t,n},Cn=ie(void 0),N6=O(2,(t,n)=>k0(t,n,{onSuccess:(r,s)=>[r,s],onFailure:We})),z6=O(2,(t,n)=>k0(t,n,{onSuccess:(r,s)=>s,onFailure:We})),k0=O(3,(t,n,{onFailure:r,onSuccess:s})=>{switch(t._tag){case ze:switch(n._tag){case Le:return te(t.effect_instruction_i0);case ze:return te(r(t.effect_instruction_i0,n.effect_instruction_i0))}case Le:switch(n._tag){case Le:return ie(s(t.effect_instruction_i0,n.effect_instruction_i0));case ze:return te(n.effect_instruction_i0)}}}),L6=(t,n)=>{const r=xR(t);return ar(r)?D(Wa(r),Yg(D(Ra(r),qf(ln)),(s,l)=>D(s,k0(l,{onSuccess:(c,f)=>D(c,Yn(f)),onFailure:n}))),qf($s),qf(s=>Ma(s)),Rt):gt()},KO=t=>({...Lo,[j$]:U$,state:eh(B$([])),commit(){return _h(this)},blockingOn:t}),QO=()=>J(S0,t=>$6(t)),$6=t=>ot(()=>KO(t)),_h=t=>v0(n=>{const r=Vr(t.state);switch(r._tag){case oh:return n(r.effect);case Yc:return r.joiners.push(n),B6(t,n)}},t.blockingOn),bh=O(2,(t,n)=>ot(()=>{const r=Vr(t.state);switch(r._tag){case oh:return!1;case Yc:{nh(t.state,OO(n));for(let s=0,l=r.joiners.length;s<l;s++)r.joiners[s](n);return!0}}})),F6=O(2,(t,n)=>bh(t,n)),j6=O(2,(t,n)=>bh(t,Oe(n))),m9=O(2,(t,n)=>bh(t,NO(n))),y9=t=>ot(()=>Vr(t.state)._tag===oh),U6=O(2,(t,n)=>bh(t,yt(n))),YO=(t,n)=>{const r=Vr(t.state);if(r._tag===Yc){nh(t.state,OO(n));for(let s=0,l=r.joiners.length;s<l;s++)r.joiners[s](n)}},B6=(t,n)=>ot(()=>{const r=Vr(t.state);if(r._tag===Yc){const s=r.joiners.indexOf(n);s>=0&&r.joiners.splice(s,1)}}),q6=Xt(t=>ie(t.currentContext)),N0=()=>q6,Zs=t=>J(N0(),t),Wc=O(2,(t,n)=>Jc(ir,n)(t)),z0=O(2,(t,n)=>c6(ir,r=>jo(r,n))(t)),XO=O(2,(t,n)=>Zs(r=>Wc(t,n(r)))),L0=t=>{const n=t.currentSpan;return n!==void 0&&n._tag==="Span"?Rt(n):gt()},H6={_tag:"Span",spanId:"noop",traceId:"noop",sampled:!1,status:{_tag:"Ended",startTime:BigInt(0),endTime:BigInt(0),exit:Cn},attributes:new Map,links:[],kind:"internal",attribute(){},event(){},end(){},addLinks(){}},I6=t=>Object.assign(Object.create(H6),t),p9=GO,g9=w6,v9=A6,_9=to,b9=Qi,S9=ig,E9=te,P6=D6,T9=qf,R9=vh,O9=ie,M9=Cn,w9=N6,A9=z6,fE=Symbol.for("effect/MutableHashMap"),V6={[fE]:fE,[Symbol.iterator](){return new $0(this)},toString(){return Re(this.toJSON())},toJSON(){return{_id:"MutableHashMap",values:Array.from(this).map(_e)}},[le](){return this.toJSON()},pipe(){return Tt(this,arguments)}};class $0{constructor(n){E(this,"self");E(this,"referentialIterator");E(this,"bucketIterator");this.self=n,this.referentialIterator=n.referential[Symbol.iterator]()}next(){if(this.bucketIterator!==void 0)return this.bucketIterator.next();const n=this.referentialIterator.next();return n.done?(this.bucketIterator=new G6(this.self.buckets.values()),this.next()):n}[Symbol.iterator](){return new $0(this.self)}}class G6{constructor(n){E(this,"backing");E(this,"currentBucket");this.backing=n}next(){if(this.currentBucket===void 0){const r=this.backing.next();if(r.done)return r;this.currentBucket=r.value[Symbol.iterator]()}const n=this.currentBucket.next();return n.done?(this.currentBucket=void 0,this.next()):n}}const K6=()=>{const t=Object.create(V6);return t.referential=new Map,t.buckets=new Map,t.bucketsSize=0,t},ps=O(2,(t,n)=>{if(ed(n)===!1)return t.referential.has(n)?Rt(t.referential.get(n)):gt();const r=n[Dt](),s=t.buckets.get(r);return s===void 0?gt():Q6(t,s,n)}),Q6=(t,n,r,s=!1)=>{for(let l=0,c=n.length;l<c;l++)if(r[Ct](n[l][0])){const f=n[l][1];return s&&(n.splice(l,1),t.bucketsSize--),Rt(f)}return gt()},Zl=O(2,(t,n)=>Aa(ps(t,n))),Jl=O(3,(t,n,r)=>{if(ed(n)===!1)return t.referential.set(n,r),t;const s=n[Dt](),l=t.buckets.get(s);return l===void 0?(t.buckets.set(s,[[n,r]]),t.bucketsSize++,t):(Y6(t,l,n),l.push([n,r]),t.bucketsSize++,t)}),Y6=(t,n,r)=>{for(let s=0,l=n.length;s<l;s++)if(r[Ct](n[s][0])){n.splice(s,1),t.bucketsSize--;return}},X6="effect/Clock",dE=Symbol.for(X6),Go=Qs("effect/Clock"),Z6=2**31-1,hE={unsafeSchedule(t,n){const r=Ip(n);if(r>Z6)return Wf;let s=!1;const l=setTimeout(()=>{s=!0,t()},r);return()=>(clearTimeout(l),!s)}},mE=function(){const t=BigInt(1e6);if(typeof performance>"u")return()=>BigInt(Date.now())*t;let n;return()=>(n===void 0&&(n=BigInt(Date.now())*t-BigInt(Math.round(performance.now()*1e6))),n+BigInt(Math.round(performance.now()*1e6)))}(),J6=function(){const t=typeof process=="object"&&"hrtime"in process&&typeof process.hrtime.bigint=="function"?process.hrtime:void 0;if(!t)return mE;const n=mE()-t.bigint();return()=>n+t.bigint()}();var pT;pT=dE;class W6{constructor(){E(this,pT,dE);E(this,"currentTimeMillis",ot(()=>this.unsafeCurrentTimeMillis()));E(this,"currentTimeNanos",ot(()=>this.unsafeCurrentTimeNanos()))}unsafeCurrentTimeMillis(){return Date.now()}unsafeCurrentTimeNanos(){return J6()}scheduler(){return yt(hE)}sleep(n){return Ca(r=>{const s=hE.unsafeSchedule(()=>r(oe),n);return sr(ot(s))})}}const t5=()=>new W6,ZO="And",JO="Or",WO="InvalidData",tM="MissingData",eM="SourceUnavailable",nM="Unsupported",e5="effect/ConfigError",yE=Symbol.for(e5),Ko={_tag:"ConfigError",[yE]:yE},aM=(t,n)=>{const r=Object.create(Ko);return r._op=ZO,r.left=t,r.right=n,Object.defineProperty(r,"toString",{enumerable:!1,value(){return`${this.left} and ${this.right}`}}),Object.defineProperty(r,"message",{enumerable:!1,get(){return this.toString()}}),r},rM=(t,n)=>{const r=Object.create(Ko);return r._op=JO,r.left=t,r.right=n,Object.defineProperty(r,"toString",{enumerable:!1,value(){return`${this.left} or ${this.right}`}}),Object.defineProperty(r,"message",{enumerable:!1,get(){return this.toString()}}),r},n5=(t,n,r={pathDelim:"."})=>{const s=Object.create(Ko);return s._op=WO,s.path=t,s.message=n,Object.defineProperty(s,"toString",{enumerable:!1,value(){return`(Invalid data at ${D(this.path,$o(r.pathDelim))}: "${this.message}")`}}),s},Vs=(t,n,r={pathDelim:"."})=>{const s=Object.create(Ko);return s._op=tM,s.path=t,s.message=n,Object.defineProperty(s,"toString",{enumerable:!1,value(){return`(Missing data at ${D(this.path,$o(r.pathDelim))}: "${this.message}")`}}),s},a5=(t,n,r,s={pathDelim:"."})=>{const l=Object.create(Ko);return l._op=eM,l.path=t,l.message=n,l.cause=r,Object.defineProperty(l,"toString",{enumerable:!1,value(){return`(Source unavailable at ${D(this.path,$o(s.pathDelim))}: "${this.message}")`}}),l},r5=(t,n,r={pathDelim:"."})=>{const s=Object.create(Ko);return s._op=nM,s.path=t,s.message=n,Object.defineProperty(s,"toString",{enumerable:!1,value(){return`(Unsupported operation at ${D(this.path,$o(r.pathDelim))}: "${this.message}")`}}),s},gs=O(2,(t,n)=>{switch(t._op){case ZO:return aM(gs(t.left,n),gs(t.right,n));case JO:return rM(gs(t.left,n),gs(t.right,n));case WO:return n5([...n,...t.path],t.message);case tM:return Vs([...n,...t.path],t.message);case eM:return a5([...n,...t.path],t.message,t.cause);case nM:return r5([...n,...t.path],t.message)}}),s5={_tag:"Empty"},pp=O(2,(t,n)=>{let r=f0(n),s=t;for(;Xz(r);){const l=r.head;switch(l._tag){case"Empty":{r=r.tail;break}case"AndThen":{r=qs(l.first,qs(l.second,r.tail));break}case"MapName":{s=ws(s,l.f),r=r.tail;break}case"Nested":{s=sd(s,l.name),r=r.tail;break}case"Unnested":{if(D(fc(s),CN(l.name)))s=zs(s),r=r.tail;else return Ir(Vs(s,`Expected ${l.name} to be in path in ConfigProvider#unnested`));break}}}return ia(s)}),i5="Constant",o5="Fail",l5="Fallback",c5="Described",u5="Lazy",f5="MapOrFail",d5="Nested",h5="Primitive",m5="Sequence",y5="HashMap",p5="ZipWith";var pE={};const pd=(t,n)=>[...t,...n],g5="effect/ConfigProvider",gE=Symbol.for(g5),v5=Qs("effect/ConfigProvider"),_5="effect/ConfigProviderFlat",vE=Symbol.for(_5),b5=t=>({[gE]:gE,pipe(){return Tt(this,arguments)},...t}),S5=t=>({[vE]:vE,patch:t.patch,load:(n,r,s=!0)=>t.load(n,r,s),enumerateChildren:t.enumerateChildren}),E5=t=>b5({load:n=>J(In(t,Ls(),n,!1),r=>rr(fc(r),{onNone:()=>ve(Vs(Ls(),`Expected a single value having structure: ${n}`)),onSome:yt})),flattened:t}),T5=t=>{const{pathDelim:n,seqDelim:r}=Object.assign({},{pathDelim:"_",seqDelim:","},t),s=m=>D(m,$o(n)),l=m=>m.split(n),c=()=>typeof process<"u"&&"env"in process&&typeof pE=="object"?pE:{};return E5(S5({load:(m,y,p=!0)=>{const g=s(m),_=c(),S=g in _?Rt(_[g]):gt();return D(S,fh(()=>Vs(m,`Expected ${g} to exist in the process context`)),J(T=>A5(T,m,y,r,p)))},enumerateChildren:m=>ot(()=>{const y=c(),_=Object.keys(y).map(S=>l(S.toUpperCase())).filter(S=>{for(let T=0;T<m.length;T++){const A=D(m,pR(T)),x=S[T];if(x===void 0||A!==x)return!1}return!0}).flatMap(S=>S.slice(m.length,m.length+1));return wz(_)}),patch:s5}))},R5=(t,n,r,s)=>{const l=q1(r.length,m=>m>=s.length?gt():Rt([t(m),m+1])),c=q1(s.length,m=>m>=r.length?gt():Rt([n(m),m+1])),f=pd(r,l),h=pd(s,c);return[f,h]},O5=(t,n)=>{let r=n;if(r._tag==="Nested"){const s=t.slice();for(;r._tag==="Nested";)s.push(r.name),r=r.config;return s}return t},In=(t,n,r,s)=>{const l=r;switch(l._tag){case i5:return yt(ra(l.value));case c5:return ae(()=>In(t,n,l.config,s));case o5:return ve(Vs(n,l.message));case l5:return D(ae(()=>In(t,n,l.first,s)),hd(c=>l.condition(c)?D(In(t,n,l.second,s),hd(f=>ve(rM(c,f)))):ve(c)));case u5:return ae(()=>In(t,n,l.config(),s));case f5:return ae(()=>D(In(t,n,l.original,s),J(tr(c=>D(l.mapOrFail(c),fh(gs(O5(n,l.original))))))));case d5:return ae(()=>In(t,pd(n,ra(l.name)),l.config,s));case h5:return D(pp(n,t.patch),J(c=>D(t.load(c,l,s),J(f=>{if(f.length===0){const h=D(BN(c),Gn(()=>"<n/a>"));return ve(Vs([],`Expected ${l.description} with name ${h}`))}return yt(f)}))));case m5:return D(pp(n,t.patch),J(c=>D(t.enumerateChildren(c),J(C5),J(f=>f.length===0?ae(()=>qt(In(t,n,l.config,!0),ra)):D(tr(f,h=>In(t,zN(n,`[${h}]`),l.config,!0)),qt(h=>{const m=ZN(h);return m.length===0?ra(Ls()):ra(m)}))))));case y5:return ae(()=>D(pp(n,t.patch),J(c=>D(t.enumerateChildren(c),J(f=>D(f,tr(h=>In(t,pd(c,ra(h)),l.valueConfig,s)),qt(h=>h.length===0?ra(rh()):D(x5(h),ws(m=>Bz(B1(de(f),m)))))))))));case p5:return ae(()=>D(In(t,n,l.left,s),Co,J(c=>D(In(t,n,l.right,s),Co,J(f=>{if(Pi(c)&&Pi(f))return ve(aM(c.left,f.left));if(Pi(c)&&Vi(f))return ve(c.left);if(Vi(c)&&Pi(f))return ve(f.left);if(Vi(c)&&Vi(f)){const h=D(n,$o(".")),m=M5(n,h),[y,p]=R5(m,m,D(c.right,ws(ia)),D(f.right,ws(ia)));return D(y,B1(p),tr(([g,_])=>D(hh(g,_),qt(([S,T])=>l.zip(S,T)))))}throw new Error("BUG: ConfigProvider.fromFlatLoop - please report an issue at https://github.com/Effect-TS/effect/issues")})))))}},M5=(t,n)=>r=>Ir(Vs(t,`The element at index ${r} in a sequence at path "${n}" was missing`)),w5=(t,n)=>t.split(new RegExp(`\\s*${t4(n)}\\s*`)),A5=(t,n,r,s,l)=>l?D(w5(t,s),tr(c=>r.parse(c.trim())),fh(gs(n))):D(r.parse(t),LO({onFailure:gs(n),onSuccess:ra})),x5=t=>Object.keys(t[0]).map(n=>t.map(r=>r[n])),C5=t=>D(tr(t,k5),LO({onFailure:()=>Ls(),onSuccess:id(vc)}),Co,qt(EN)),D5=/^(\[(\d+)\])$/,k5=t=>{const n=t.match(D5);if(n!==null){const r=n[2];return D(r!==void 0&&r.length>0?Rt(r):gt(),hR(N5))}return gt()},N5=t=>{const n=Number.parseInt(t);return Number.isNaN(n)?gt():Rt(n)},_E=Symbol.for("effect/Console"),sM=Qs("effect/Console"),z5={[_E]:_E,assert(t,...n){return ot(()=>{console.assert(t,...n)})},clear:ot(()=>{console.clear()}),count(t){return ot(()=>{console.count(t)})},countReset(t){return ot(()=>{console.countReset(t)})},debug(...t){return ot(()=>{console.debug(...t)})},dir(t,n){return ot(()=>{console.dir(t,n)})},dirxml(...t){return ot(()=>{console.dirxml(...t)})},error(...t){return ot(()=>{console.error(...t)})},group(t){return t!=null&&t.collapsed?ot(()=>console.groupCollapsed(t==null?void 0:t.label)):ot(()=>console.group(t==null?void 0:t.label))},groupEnd:ot(()=>{console.groupEnd()}),info(...t){return ot(()=>{console.info(...t)})},log(...t){return ot(()=>{console.log(...t)})},table(t,n){return ot(()=>{console.table(t,n)})},time(t){return ot(()=>console.time(t))},timeEnd(t){return ot(()=>console.timeEnd(t))},timeLog(t,...n){return ot(()=>{console.timeLog(t,...n)})},trace(...t){return ot(()=>{console.trace(...t)})},warn(...t){return ot(()=>{console.warn(...t)})},unsafe:console},L5="effect/Random",bE=Symbol.for(L5),$5=Qs("effect/Random");var gT;gT=bE;class F5{constructor(n){E(this,"seed");E(this,gT,bE);E(this,"PRNG");this.seed=n,this.PRNG=new Xk(n)}get next(){return ot(()=>this.PRNG.number())}get nextBoolean(){return qt(this.next,n=>n>.5)}get nextInt(){return ot(()=>this.PRNG.integer(Number.MAX_SAFE_INTEGER))}nextRange(n,r){return qt(this.next,s=>(r-n)*s+n)}nextIntBetween(n,r){return ot(()=>this.PRNG.integer(r-n)+n)}shuffle(n){return j5(n,r=>this.nextIntBetween(0,r))}}const j5=(t,n)=>ae(()=>D(ot(()=>Array.from(t)),J(r=>{const s=[];for(let l=r.length;l>=2;l=l-1)s.push(l);return D(s,uh(l=>D(n(l),qt(c=>U5(r,l-1,c)))),cn(xR(r)))}))),U5=(t,n,r)=>{const s=t[n];return t[n]=t[r],t[r]=s,t},B5=t=>new F5(mt(t)),SE=Symbol.for("effect/Tracer"),q5=t=>({[SE]:SE,...t}),F0=Qs("effect/Tracer"),Sh=Qs("effect/ParentSpan"),EE=function(){const t="abcdef0123456789",n=t.length;return function(r){let s="";for(let l=0;l<r;l++)s+=t.charAt(Math.floor(Math.random()*n));return s}}();class H5{constructor(n,r,s,l,c,f){E(this,"name");E(this,"parent");E(this,"context");E(this,"startTime");E(this,"kind");E(this,"_tag","Span");E(this,"spanId");E(this,"traceId","native");E(this,"sampled",!0);E(this,"status");E(this,"attributes");E(this,"events",[]);E(this,"links");this.name=n,this.parent=r,this.context=s,this.startTime=c,this.kind=f,this.status={_tag:"Started",startTime:c},this.attributes=new Map,this.traceId=r._tag==="Some"?r.value.traceId:EE(32),this.spanId=EE(16),this.links=Array.from(l)}end(n,r){this.status={_tag:"Ended",endTime:n,exit:r,startTime:this.status.startTime}}attribute(n,r){this.attributes.set(n,r)}event(n,r,s){this.events.push([n,r,s??{}])}addLinks(n){this.links.push(...n)}}const I5=q5({span:(t,n,r,s,l,c)=>new H5(t,n,r,s,l,c),context:t=>t()}),P5=t=>{if((t==null?void 0:t.captureStackTrace)===!1)return t;if((t==null?void 0:t.captureStackTrace)!==void 0&&typeof t.captureStackTrace!="boolean")return t;const n=Error.stackTraceLimit;Error.stackTraceLimit=3;const r=new Error;Error.stackTraceLimit=n;let s=!1;return{...t,captureStackTrace:()=>{if(s!==!1)return s;if(r.stack!==void 0){const l=r.stack.split(`
`);if(l[3]!==void 0)return s=l[3].trim(),s}}}},lg=Pc()("effect/Tracer/DisablePropagation",{defaultValue:Wf}),V5=D(_c(),zr(Go,t5()),zr(sM,z5),zr($5,B5(Math.random())),zr(v5,T5()),zr(F0,I5)),Do=Ut(Symbol.for("effect/DefaultServices/currentServices"),()=>qO(V5)),G5=t=>{const n=xa(t);return iM(r=>r.sleep(n))},K5=t=>Xt(n=>t(n.currentDefaultServices)),iM=t=>K5(n=>t(n.unsafeMap.get(Go.key))),Q5=iM(t=>t.currentTimeMillis),Y5=G5,X5=Q5,Z5=Go;function J5(t){return new Kr(t)}function W5(){return J5(new Map)}const TE=Symbol.for("effect/FiberRefs");var vT;vT=TE;class Kr{constructor(n){E(this,"locals");E(this,vT,TE);this.locals=n}pipe(){return Tt(this,arguments)}}const tF=(t,n,r,s=!1)=>{const l=t;let c=n,f=r,h=s,m;for(;m===void 0;)if(_n(c)&&_n(f)){const y=on(c)[0],p=zs(c),g=on(f)[0],_=on(f)[1],S=zs(f);y.startTimeMillis<g.startTimeMillis?(f=S,h=!0):y.startTimeMillis>g.startTimeMillis?c=p:y.id<g.id?(f=S,h=!0):y.id>g.id?c=p:m=[_,h]}else m=[l.initial,!0];return m},eF=O(3,(t,n,r)=>{const s=new Map(t.locals);return r.locals.forEach((l,c)=>{const f=l[0][1];if(!l[0][0][Ct](n)){if(!s.has(c)){if(Ot(f,c.initial))return;s.set(c,[[n,c.join(c.initial,f)]]);return}const h=s.get(c),[m,y]=tF(c,h,l);if(y){const p=c.diff(m,f),g=h[0][1],_=c.join(g,c.patch(p)(g));if(!Ot(g,_)){let S;const T=h[0][0];T[Ct](n)?S=[[T,_],...h.slice(1)]:S=[[n,_],...h],s.set(c,S)}}}}),new Kr(s)}),nF=O(2,(t,n)=>{const r=new Map;return oM(t,r,n),new Kr(r)}),oM=(t,n,r)=>{t.locals.forEach((s,l)=>{const c=s[0][1],f=l.patch(l.fork)(c);Ot(c,f)?n.set(l,s):n.set(l,[[r,f],...s])})},lM=O(2,(t,n)=>{const r=new Map(t.locals);return r.delete(n),new Kr(r)}),cM=O(2,(t,n)=>t.locals.has(n)?Rt(on(t.locals.get(n))[1]):gt()),Ac=O(2,(t,n)=>D(cM(t,n),Gn(()=>n.initial))),cg=O(2,(t,{fiberId:n,fiberRef:r,value:s})=>{if(t.locals.size===0)return new Kr(new Map([[r,[[n,s]]]]));const l=new Map(t.locals);return ug(l,n,r,s),new Kr(l)}),ug=(t,n,r,s)=>{const l=t.get(r)??[];let c;if(_n(l)){const[f,h]=on(l);if(f[Ct](n)){if(Ot(h,s))return;c=[[n,s],...l.slice(1)]}else c=[[n,s],...l]}else c=[[n,s]];t.set(r,c)},aF=O(2,(t,{entries:n,forkAs:r})=>{if(t.locals.size===0)return new Kr(new Map(n));const s=new Map(t.locals);return r!==void 0&&oM(t,s,r),n.forEach(([l,c])=>{c.length===1?ug(s,c[0][0],l,c[0][1]):c.forEach(([f,h])=>{ug(s,f,l,h)})}),new Kr(s)}),RE=cM,rF=Ac,sF=aF,iF=W5,oF=W$,lF=t6,cF=e6,uF=n6,fF=UO,dF=BO,hF=a6,mF=r6,yF=D(vc,ON(t=>t.ordinal)),pF=MN(yF),gF=t=>{switch(t){case"All":return oF;case"Debug":return dF;case"Error":return cF;case"Fatal":return lF;case"Info":return fF;case"Trace":return hF;case"None":return mF;case"Warning":return uF}},uM=t=>t.replace(/[\s="]/g,"_"),vF=t=>n=>`${uM(n.label)}=${t-n.startTime}ms`,_F=Ic,x9=Lo,bF=hN;class Eh extends bF{}const gd=Symbol.for("effect/Readable"),fM=Symbol.for("effect/Ref"),dM={_A:t=>t};var _T,bT,ST;class SF extends(ST=Eh,bT=fM,_T=gd,ST){constructor(r){super();E(this,"ref");E(this,bT,dM);E(this,_T,gd);E(this,"get");this.ref=r,this.get=ot(()=>Vr(this.ref))}commit(){return this.get}modify(r){return ot(()=>{const s=Vr(this.ref),[l,c]=r(s);return s!==c&&nh(c)(this.ref),l})}}const j0=t=>new SF(eh(t)),vd=t=>ot(()=>j0(t)),wa=t=>t.get,hc=O(2,(t,n)=>t.modify(()=>[void 0,n])),EF=O(2,(t,n)=>t.modify(r=>[r,n])),hM=O(2,(t,n)=>t.modify(n)),_d=O(2,(t,n)=>t.modify(r=>[void 0,n(r)])),TF=vd,C9=wa,RF=EF,D9=hM,k9=_d,mM="Empty",yM="Add",pM="Remove",gM="Update",vM="AndThen",OF={_tag:mM},U0=(t,n)=>{const r=new Map(t.locals);let s=OF;for(const[l,c]of n.locals.entries()){const f=on(c)[1],h=r.get(l);if(h!==void 0){const m=on(h)[1];Ot(m,f)||(s=gp({_tag:gM,fiberRef:l,patch:l.diff(m,f)})(s))}else s=gp({_tag:yM,fiberRef:l,value:f})(s);r.delete(l)}for(const[l]of r.entries())s=gp({_tag:pM,fiberRef:l})(s);return s},gp=O(2,(t,n)=>({_tag:vM,first:t,second:n})),_M=O(3,(t,n,r)=>{let s=r,l=ra(t);for(;_n(l);){const c=on(l),f=zs(l);switch(c._tag){case mM:{l=f;break}case yM:{s=cg(s,{fiberId:n,fiberRef:c.fiberRef,value:c.value}),l=f;break}case pM:{s=lM(s,c.fiberRef),l=f;break}case gM:{const h=Ac(s,c.fiberRef);s=cg(s,{fiberId:n,fiberRef:c.fiberRef,value:c.fiberRef.patch(c.patch)(h)}),l=f;break}case vM:{l=sd(c.first)(sd(c.second)(f));break}}}return s}),bM="effect/MetricLabel",fg=Symbol.for(bM);var ET;class MF{constructor(n,r){E(this,"key");E(this,"value");E(this,ET,fg);E(this,"_hash");this.key=n,this.value=r,this._hash=Ee(bM+this.key+this.value)}[(ET=fg,Dt)](){return this._hash}[Ct](n){return AF(n)&&this.key===n.key&&this.value===n.value}pipe(){return Tt(this,arguments)}}const wF=(t,n)=>new MF(t,n),AF=t=>Mt(t,fg),xF=t=>qt(t,Rt),SM=t=>{let n,r;return typeof t=="function"?n=t:(n=t.try,r=t.catch),ae(()=>{try{return yt(Ge(n))}catch(s){return ve(r?Ge(()=>r(s)):new gh(s,"An unknown error occurred in Effect.try"))}})},CF=O(2,(t,n)=>{let r;return I$(t,s=>(r??(r=Object.keys(n)),Mt(s,"_tag")&&H2(s._tag)&&r.includes(s._tag)),s=>n[s._tag](s))}),DF=t=>MM(t,TM,U0),kF=t=>MM(t,hh(TM,Q$),([n,r],[s,l])=>[U0(n,s),qr(r,l)]),EM=O(2,(t,n)=>Gr(t,{onFailure:r=>yt(n.onFailure(r)),onSuccess:r=>yt(n.onSuccess(r))})),TM=Xt(t=>yt(t.getFiberRefs())),NF=t=>EM(t,{onFailure:Lp,onSuccess:Lp}),OE=O(2,(t,n)=>Xn(t,{onFailure:r=>DO(()=>n(r)),onSuccess:yt})),zF=t=>D(QO(),J(n=>D(kF(t),zO(n),$F,qt(r=>qe(r,D(_h(n),J(([s,l])=>cn(hh(RM(s[0]),$O(s[1])),l)))))))),LF=t=>qt(t,n=>!n),$F=t=>qt(TF(!0),n=>sr(FO(t,RF(n,!1)))),RM=t=>IF((n,r)=>D(t,_M(n,r))),FF=t=>t.length>=1?Ca((n,r)=>{try{t(r).then(s=>n(ie(s)),s=>n(Qi(s)))}catch(s){n(Qi(s))}}):Ca(n=>{try{t().then(r=>n(ie(r)),r=>n(Qi(r)))}catch(r){n(Qi(r))}}),OM=O(3,(t,n,r)=>Zs(s=>Wc(t,zr(s,n,r)))),ME=O(3,(t,n,r)=>Zs(s=>J(r,l=>Wc(t,D(s,zr(n,l)))))),jF=Y5,UF=yt(gt()),MM=O(3,(t,n,r)=>J(n,s=>J(t,l=>qt(n,c=>[r(s,c),l])))),BF=O(2,(t,n)=>Xn(t,{onFailure:r=>qe(n(r),Oe(r)),onSuccess:yt})),qF=t=>{let n,r;typeof t=="function"?n=t:(n=t.try,r=t.catch);const s=l=>r?lh(()=>r(l)):ve(new gh(l,"An unknown error occurred in Effect.tryPromise"));return n.length>=1?Ca((l,c)=>{try{n(c).then(f=>l(ie(f)),f=>l(s(f)))}catch(f){l(s(f))}}):Ca(l=>{try{n().then(c=>l(ie(c)),c=>l(s(c)))}catch(c){l(s(c))}})},HF=O(2,(t,n)=>J(t,r=>SM({try:()=>n.try(r),catch:n.catch}))),IF=t=>Xt(n=>(n.setFiberRefs(t(n.id(),n.getFiberRefs())),oe)),PF=O(2,(t,n)=>ae(()=>n()?qt(t,Rt):yt(gt()))),VF=t=>new Proxy({},{get(n,r,s){return(...l)=>J(t,c=>c[r](...l))}}),wM=BigInt(0),AM=hR(t=>As(t.context,lg)?t._tag==="Span"?AM(t.parent):gt():Rt(t)),GF=(t,n,r)=>{const s=!t.getFiberRef(b6)||r.context&&As(r.context,lg),l=t.getFiberRef(ir),c=r.parent?Rt(r.parent):r.root?gt():AM(Fo(l,Sh));let f;if(s)f=I6({name:n,parent:c,context:zr(r.context??_c(),lg,!0)});else{const h=t.getFiberRef(Do),m=As(h,F0),y=As(h,Z5),p=t.getFiberRef(IO),g=t.getFiberRefs(),_=RE(g,S6),S=RE(g,E6),T=S._tag==="Some"?r.links!==void 0?[...Ma(S.value),...r.links??[]]:Ma(S.value):r.links??Ls();f=m.span(n,c,r.context??_c(),T,p?y.unsafeCurrentTimeNanos():wM,r.kind??"internal"),_._tag==="Some"&&Pz(_.value,(A,x)=>f.attribute(x,A)),r.attributes!==void 0&&Object.entries(r.attributes).forEach(([A,x])=>f.attribute(A,x))}return typeof r.captureStackTrace=="function"&&dd.set(f,r.captureStackTrace),f},KF=(t,n,r,s)=>ot(()=>{t.status._tag!=="Ended"&&(GO(n)&&dd.has(t)&&t.attribute("code.stacktrace",dd.get(t)()),t.end(s?r.unsafeCurrentTimeNanos():wM,n))}),QF=(t,...n)=>{const r=P5(n.length===1?void 0:n[0]),s=n[n.length-1];return Xt(l=>{const c=GF(l,t,r),f=l.getFiberRef(IO),h=As(l.getFiberRef(Do),Go);return Ps(s(c),m=>KF(c,m,h,f))})},YF=O(2,(t,n)=>OM(t,Sh,n)),xM="Sequential",CM="Parallel",XF="ParallelN",Th={_tag:xM},ZF={_tag:CM},JF=t=>({_tag:XF,parallelism:t}),WF=t=>t._tag===xM,tj=t=>t._tag===CM,dg=Th,hg=ZF,mg=JF,xc=U0,Cc=_M,Rh="effect/FiberStatus",Gs=Symbol.for(Rh),bd="Done",wE="Running",AE="Suspended",ej=Ee(`${Rh}-${bd}`);var TT;class nj{constructor(){E(this,TT,Gs);E(this,"_tag",bd)}[(TT=Gs,Dt)](){return ej}[Ct](n){return B0(n)&&n._tag===bd}}var RT;class aj{constructor(n){E(this,"runtimeFlags");E(this,RT,Gs);E(this,"_tag",wE);this.runtimeFlags=n}[(RT=Gs,Dt)](){return D(mt(Rh),jt(mt(this._tag)),jt(mt(this.runtimeFlags)),he(this))}[Ct](n){return B0(n)&&n._tag===wE&&this.runtimeFlags===n.runtimeFlags}}var OT;class rj{constructor(n,r){E(this,"runtimeFlags");E(this,"blockingOn");E(this,OT,Gs);E(this,"_tag",AE);this.runtimeFlags=n,this.blockingOn=r}[(OT=Gs,Dt)](){return D(mt(Rh),jt(mt(this._tag)),jt(mt(this.runtimeFlags)),jt(mt(this.blockingOn)),he(this))}[Ct](n){return B0(n)&&n._tag===AE&&this.runtimeFlags===n.runtimeFlags&&Ot(this.blockingOn,n.blockingOn)}}const sj=new nj,ij=t=>new aj(t),oj=(t,n)=>new rj(t,n),B0=t=>Mt(t,Gs),lj=t=>t._tag===bd,cj=sj,DM=ij,uj=oj,fj=lj,dj=Symbol.for("effect/Micro"),Sd=Symbol.for("effect/Micro/MicroExit"),xE=Symbol.for("effect/Micro/MicroCause"),hj={_E:Yt};var MT;class kM extends globalThis.Error{constructor(r,s,l){const c=`MicroCause.${r}`;let f,h,m;if(s instanceof globalThis.Error){f=`(${c}) ${s.name}`,h=s.message;const y=h.split(`
`).length;m=s.stack?`(${c}) ${s.stack.split(`
`).slice(0,y+3).join(`
`)}`:`${f}: ${h}`}else f=c,h=mo(s,0),m=`${f}: ${h}`;l.length>0&&(m+=`
    ${l.join(`
    `)}`);super(h);E(this,"_tag");E(this,"traces");E(this,MT);this._tag=r,this.traces=l,this[xE]=hj,this.name=f,this.stack=m}pipe(){return Tt(this,arguments)}toString(){return this.stack}[(MT=xE,le)](){return this.stack}}class mj extends kM{constructor(r,s=[]){super("Die",r,s);E(this,"defect");this.defect=r}}const yj=(t,n=[])=>new mj(t,n);class pj extends kM{constructor(n=[]){super("Interrupt","interrupted",n)}}const gj=(t=[])=>new pj(t),vj=t=>t._tag==="Interrupt",CE=Symbol.for("effect/Micro/MicroFiber"),_j={_A:Yt,_E:Yt};var wT;wT=CE;class bj{constructor(n,r=!0){E(this,"context");E(this,"interruptible");E(this,wT);E(this,"_stack",[]);E(this,"_observers",[]);E(this,"_exit");E(this,"_children");E(this,"currentOpCount",0);E(this,"_interrupted",!1);E(this,"_yielded");this.context=n,this.interruptible=r,this[CE]=_j}getRef(n){return m4(this.context,n)}addObserver(n){return this._exit?(n(this._exit),Lp):(this._observers.push(n),()=>{const r=this._observers.indexOf(n);r>=0&&this._observers.splice(r,1)})}unsafeInterrupt(){this._exit||(this._interrupted=!0,this.interruptible&&this.evaluate(V0))}unsafePoll(){return this._exit}evaluate(n){if(this._exit)return;if(this._yielded!==void 0){const l=this._yielded;this._yielded=void 0,l()}const r=this.runLoop(n);if(r===Df)return;const s=DE.interruptChildren&&DE.interruptChildren(this);if(s!==void 0)return this.evaluate(Td(s,()=>r));this._exit=r;for(let l=0;l<this._observers.length;l++)this._observers[l](r);this._observers.length=0}runLoop(n){let r=!1,s=n;this.currentOpCount=0;try{for(;;){if(this.currentOpCount++,!r&&this.getRef(G0).shouldYield(this)){r=!0;const l=s;s=Td(Oj,()=>l)}if(s=s[yg](this),s===Df){const l=this._yielded;return Sd in l?(this._yielded=void 0,l):Df}}}catch(l){return Mt(s,yg)?pg(l):pg(`MicroFiber.runLoop: Not a valid effect: ${String(s)}`)}}getCont(n){for(;;){const r=this._stack.pop();if(!r)return;const s=r[Ed]&&r[Ed](this);if(s)return{[n]:s};if(r[n])return r}}yieldWith(n){return this._yielded=n,Df}children(){return this._children??(this._children=new Set)}}const DE=Ut("effect/Micro/fiberMiddleware",()=>({interruptChildren:void 0})),NM=Symbol.for("effect/Micro/identifier"),Ne=Symbol.for("effect/Micro/args"),yg=Symbol.for("effect/Micro/evaluate"),ko=Symbol.for("effect/Micro/successCont"),eo=Symbol.for("effect/Micro/failureCont"),Ed=Symbol.for("effect/Micro/ensureCont"),Df=Symbol.for("effect/Micro/Yield"),Sj={_A:Yt,_E:Yt,_R:Yt},Ej={..._F,_op:"Micro",[dj]:Sj,pipe(){return Tt(this,arguments)},[Symbol.iterator](){return new K2(new qc(this))},toJSON(){return{_id:"Micro",op:this[NM],...Ne in this?{args:this[Ne]}:void 0}},toString(){return Re(this)},[le](){return Re(this)}};function Tj(t){return pg("Micro.evaluate: Not implemented")}const Oh=t=>({...Ej,[NM]:t.op,[yg]:t.eval??Tj,[ko]:t.contA,[eo]:t.contE,[Ed]:t.ensure}),q0=t=>{const n=Oh(t);return function(){const r=Object.create(n);return r[Ne]=t.single===!1?arguments:arguments[0],r}},zM=t=>{const n={...Oh(t),[Sd]:Sd,_tag:t.op,get[t.prop](){return this[Ne]},toJSON(){return{_id:"MicroExit",_tag:t.op,[t.prop]:this[Ne]}},[Ct](r){return Aj(r)&&r._tag===t.op&&Ot(this[Ne],r[Ne])},[Dt](){return he(this,jt(Ee(t.op))(mt(this[Ne])))}};return function(r){const s=Object.create(n);return s[Ne]=r,s[ko]=void 0,s[eo]=void 0,s[Ed]=void 0,s}},H0=zM({op:"Success",prop:"value",eval(t){const n=t.getCont(ko);return n?n[ko](this[Ne],t):t.yieldWith(this)}}),LM=zM({op:"Failure",prop:"cause",eval(t){let n=t.getCont(eo);for(;vj(this[Ne])&&n&&t.interruptible;)n=t.getCont(eo);return n?n[eo](this[Ne],t):t.yieldWith(this)}}),Rj=q0({op:"Yield",eval(t){let n=!1;return t.getRef(G0).scheduleTask(()=>{n||t.evaluate(xj)},this[Ne]??0),t.yieldWith(()=>{n=!0})}}),Oj=Rj(0),Mj=H0(void 0),I0=q0({op:"WithMicroFiber",eval(t){return this[Ne](t)}}),Td=O(2,(t,n)=>{const r=Object.create(wj);return r[Ne]=t,r[ko]=n,r}),wj=Oh({op:"OnSuccess",eval(t){return t._stack.push(this),this[Ne]}}),Aj=t=>Mt(t,Sd),$M=H0,P0=LM,V0=P0(gj()),pg=t=>P0(yj(t)),xj=$M(void 0),Cj="setImmediate"in globalThis?globalThis.setImmediate:t=>setTimeout(t,0);class FM{constructor(){E(this,"tasks",[]);E(this,"running",!1);E(this,"afterScheduled",()=>{this.running=!1,this.runTasks()})}scheduleTask(n,r){this.tasks.push(n),this.running||(this.running=!0,Cj(this.afterScheduled))}runTasks(){const n=this.tasks;this.tasks=[];for(let r=0,s=n.length;r<s;r++)n[r]()}shouldYield(n){return n.currentOpCount>=n.getRef(Nj)}flush(){for(;this.tasks.length>0;)this.runTasks()}}const Dj=O(2,(t,n)=>I0(r=>{const s=r.context;return r.context=n(s),$j(t,()=>(r.context=s,Mj))})),kj=O(2,(t,n)=>Dj(t,jo(n)));class Nj extends Pc()("effect/Micro/currentMaxOpsBeforeYield",{defaultValue:()=>2048}){}class G0 extends Pc()("effect/Micro/currentScheduler",{defaultValue:()=>new FM}){}const zj=O(2,(t,n)=>{const r=Object.create(Lj);return r[Ne]=t,r[ko]=n.onSuccess,r[eo]=n.onFailure,r}),Lj=Oh({op:"OnSuccessAndFailure",eval(t){return t._stack.push(this),this[Ne]}}),$j=O(2,(t,n)=>jj(r=>zj(r(t),{onFailure:s=>Td(n(P0(s)),()=>LM(s)),onSuccess:s=>Td(n($M(s)),()=>H0(s))}))),jM=q0({op:"SetInterruptible",ensure(t){if(t.interruptible=this[Ne],t._interrupted&&t.interruptible)return()=>V0}}),Fj=t=>I0(n=>n.interruptible?t:(n.interruptible=!0,n._stack.push(jM(!1)),n._interrupted?V0:t)),jj=t=>I0(n=>n.interruptible?(n.interruptible=!1,n._stack.push(jM(!0)),t(Fj)):t(Yt)),Uj=(t,n)=>{const r=new bj(G0.context(new FM));return r.evaluate(t),r};class UM{constructor(){E(this,"buckets",[])}scheduleTask(n,r){const s=this.buckets.length;let l,c=0;for(;c<s&&this.buckets[c][0]<=r;c++)l=this.buckets[c];l&&l[0]===r?l[1].push(n):c===s?this.buckets.push([r,[n]]):this.buckets.splice(c,0,[r,[n]])}}class Bj{constructor(n){E(this,"maxNextTickBeforeTimer");E(this,"running",!1);E(this,"tasks",new UM);this.maxNextTickBeforeTimer=n}starveInternal(n){const r=this.tasks.buckets;this.tasks.buckets=[];for(const[s,l]of r)for(let c=0;c<l.length;c++)l[c]();this.tasks.buckets.length===0?this.running=!1:this.starve(n)}starve(n=0){n>=this.maxNextTickBeforeTimer?setTimeout(()=>this.starveInternal(0),0):Promise.resolve(void 0).then(()=>this.starveInternal(n+1))}shouldYield(n){return n.currentOpCount>n.getFiberRef(HO)?n.getFiberRef(Vo):!1}scheduleTask(n,r){this.tasks.scheduleTask(n,r),this.running||(this.running=!0,this.starve())}}const BM=Ut(Symbol.for("effect/Scheduler/defaultScheduler"),()=>new Bj(2048));class qj{constructor(){E(this,"tasks",new UM);E(this,"deferred",!1)}scheduleTask(n,r){this.deferred?BM.scheduleTask(n,r):this.tasks.scheduleTask(n,r)}shouldYield(n){return n.currentOpCount>n.getFiberRef(HO)?n.getFiberRef(Vo):!1}flush(){for(;this.tasks.buckets.length>0;){const n=this.tasks.buckets;this.tasks.buckets=[];for(const[r,s]of n)for(let l=0;l<s.length;l++)s[l]()}this.deferred=!0}}const K0=Ut(Symbol.for("effect/FiberRef/currentScheduler"),()=>Ke(BM)),qM=Ut(Symbol.for("effect/FiberRef/currentRequestMap"),()=>Ke(new Map)),kE=(t,n,r,s)=>{switch(t){case void 0:return n();case"unbounded":return r();case"inherit":return yh(g6,l=>l==="unbounded"?r():l>1?s(l):n());default:return t>1?s(t):n()}},Q0="InterruptSignal",Y0="Stateful",X0="Resume",Z0="YieldNow",vp=t=>({_tag:Q0,cause:t}),Hf=t=>({_tag:Y0,onFiber:t}),qi=t=>({_tag:X0,effect:t}),Hj=()=>({_tag:Z0}),Ij="effect/FiberScope",Rd=Symbol.for(Ij);var AT;AT=Rd;class Pj{constructor(){E(this,AT,Rd);E(this,"fiberId",Bs);E(this,"roots",new Set)}add(n,r){this.roots.add(r),r.addObserver(()=>{this.roots.delete(r)})}}var xT;xT=Rd;class Vj{constructor(n,r){E(this,"fiberId");E(this,"parent");E(this,xT,Rd);this.fiberId=n,this.parent=r}add(n,r){this.parent.tell(Hf(s=>{s.addChild(r),r.addObserver(()=>{s.removeChild(r)})}))}}const Gj=t=>new Vj(t.id(),t),Mh=Ut(Symbol.for("effect/FiberScope/Global"),()=>new Pj),Kj="effect/Fiber",Qj=Symbol.for(Kj),Yj={_E:t=>t,_A:t=>t},Xj="effect/Fiber",Zj=Symbol.for(Xj),Jj=t=>t.await,Wj=t=>t.inheritAll,Dc=t=>mh(ch(t.await),t.inheritAll);({...Lo});const ms="effect/FiberCurrent",tU="effect/Logger",eU=Symbol.for(tU),nU={_Message:t=>t,_Output:t=>t},J0=t=>({[eU]:nU,log:t,pipe(){return Tt(this,arguments)}}),aU=/^[^\s"=]*$/,rU=(t,n)=>({annotations:r,cause:s,date:l,fiberId:c,logLevel:f,message:h,spans:m})=>{const y=T=>T.match(aU)?T:t(T),p=(T,A)=>`${uM(T)}=${y(A)}`,g=(T,A)=>" "+p(T,A);let _=p("timestamp",l.toISOString());_+=g("level",f.label),_+=g("fiber",YR(c));const S=NN(h);for(let T=0;T<S.length;T++)_+=g("message",mo(S[T],n));T$(s)||(_+=g("cause",Qc(s,{renderErrorCause:!0})));for(const T of m)_+=" "+vF(l.getTime())(T);for(const[T,A]of r)_+=g(T,mo(A,n));return _},sU=t=>`"${t.replace(/\\([\s\S])|(")/g,"\\$1$2")}"`,iU=J0(rU(sU)),oU=typeof process=="object"&&process!==null&&typeof process.stdout=="object"&&process.stdout!==null;oU&&process.stdout.isTTY;const HM="effect/MetricBoundaries",gg=Symbol.for(HM);var CT;class lU{constructor(n){E(this,"values");E(this,CT,gg);E(this,"_hash");this.values=n,this._hash=D(Ee(HM),jt(Hc(this.values)))}[(CT=gg,Dt)](){return this._hash}[Ct](n){return cU(n)&&Ot(this.values,n.values)}pipe(){return Tt(this,arguments)}}const cU=t=>Mt(t,gg),uU=t=>{const n=D(t,mR(ln(Number.POSITIVE_INFINITY)),WN);return new lU(n)},fU=t=>D(kN(t.count-1,n=>t.start*Math.pow(t.factor,n)),Uo,uU),dU="effect/MetricKeyType",IM=Symbol.for(dU),PM="effect/MetricKeyType/Counter",vg=Symbol.for(PM),hU="effect/MetricKeyType/Frequency",mU=Symbol.for(hU),yU="effect/MetricKeyType/Gauge",pU=Symbol.for(yU),VM="effect/MetricKeyType/Histogram",_g=Symbol.for(VM),gU="effect/MetricKeyType/Summary",vU=Symbol.for(gU),GM={_In:t=>t,_Out:t=>t};var DT,kT;class _U{constructor(n,r){E(this,"incremental");E(this,"bigint");E(this,kT,GM);E(this,DT,vg);E(this,"_hash");this.incremental=n,this.bigint=r,this._hash=Ee(PM)}[(kT=IM,DT=vg,Dt)](){return this._hash}[Ct](n){return KM(n)}pipe(){return Tt(this,arguments)}}var NT,zT;class bU{constructor(n){E(this,"boundaries");E(this,zT,GM);E(this,NT,_g);E(this,"_hash");this.boundaries=n,this._hash=D(Ee(VM),jt(mt(this.boundaries)))}[(zT=IM,NT=_g,Dt)](){return this._hash}[Ct](n){return QM(n)&&Ot(this.boundaries,n.boundaries)}pipe(){return Tt(this,arguments)}}const SU=t=>new _U((t==null?void 0:t.incremental)??!1,(t==null?void 0:t.bigint)??!1),EU=t=>new bU(t),KM=t=>Mt(t,vg),TU=t=>Mt(t,mU),RU=t=>Mt(t,pU),QM=t=>Mt(t,_g),OU=t=>Mt(t,vU),MU="effect/MetricKey",YM=Symbol.for(MU),wU={_Type:t=>t},AU=Xg(Ot);var LT;class W0{constructor(n,r,s,l=[]){E(this,"name");E(this,"keyType");E(this,"description");E(this,"tags");E(this,LT,wU);E(this,"_hash");this.name=n,this.keyType=r,this.description=s,this.tags=l,this._hash=D(Ee(this.name+this.description),jt(mt(this.keyType)),jt(Hc(this.tags)))}[(LT=YM,Dt)](){return this._hash}[Ct](n){return xU(n)&&this.name===n.name&&Ot(this.keyType,n.keyType)&&Ot(this.description,n.description)&&AU(this.tags,n.tags)}pipe(){return Tt(this,arguments)}}const xU=t=>Mt(t,YM),CU=(t,n)=>new W0(t,SU(n),Kd(n==null?void 0:n.description)),DU=(t,n,r)=>new W0(t,EU(n),Kd(r)),kU=O(2,(t,n)=>n.length===0?t:new W0(t.name,t.keyType,t.description,jf(t.tags,n))),NU="effect/MetricState",tu=Symbol.for(NU),XM="effect/MetricState/Counter",bg=Symbol.for(XM),ZM="effect/MetricState/Frequency",Sg=Symbol.for(ZM),JM="effect/MetricState/Gauge",Eg=Symbol.for(JM),WM="effect/MetricState/Histogram",Tg=Symbol.for(WM),tw="effect/MetricState/Summary",Rg=Symbol.for(tw),eu={_A:t=>t};var $T,FT;class zU{constructor(n){E(this,"count");E(this,FT,eu);E(this,$T,bg);this.count=n}[(FT=tu,$T=bg,Dt)](){return D(mt(XM),jt(mt(this.count)),he(this))}[Ct](n){return VU(n)&&this.count===n.count}pipe(){return Tt(this,arguments)}}const LU=Xg(Ot);var jT,UT;class $U{constructor(n){E(this,"occurrences");E(this,UT,eu);E(this,jT,Sg);E(this,"_hash");this.occurrences=n}[(UT=tu,jT=Sg,Dt)](){return D(Ee(ZM),jt(Hc(de(this.occurrences.entries()))),he(this))}[Ct](n){return GU(n)&&LU(de(this.occurrences.entries()),de(n.occurrences.entries()))}pipe(){return Tt(this,arguments)}}var BT,qT;class FU{constructor(n){E(this,"value");E(this,qT,eu);E(this,BT,Eg);this.value=n}[(qT=tu,BT=Eg,Dt)](){return D(mt(JM),jt(mt(this.value)),he(this))}[Ct](n){return KU(n)&&this.value===n.value}pipe(){return Tt(this,arguments)}}var HT,IT;class jU{constructor(n,r,s,l,c){E(this,"buckets");E(this,"count");E(this,"min");E(this,"max");E(this,"sum");E(this,IT,eu);E(this,HT,Tg);this.buckets=n,this.count=r,this.min=s,this.max=l,this.sum=c}[(IT=tu,HT=Tg,Dt)](){return D(mt(WM),jt(mt(this.buckets)),jt(mt(this.count)),jt(mt(this.min)),jt(mt(this.max)),jt(mt(this.sum)),he(this))}[Ct](n){return QU(n)&&Ot(this.buckets,n.buckets)&&this.count===n.count&&this.min===n.min&&this.max===n.max&&this.sum===n.sum}pipe(){return Tt(this,arguments)}}var PT,VT;class UU{constructor(n,r,s,l,c,f){E(this,"error");E(this,"quantiles");E(this,"count");E(this,"min");E(this,"max");E(this,"sum");E(this,VT,eu);E(this,PT,Rg);this.error=n,this.quantiles=r,this.count=s,this.min=l,this.max=c,this.sum=f}[(VT=tu,PT=Rg,Dt)](){return D(mt(tw),jt(mt(this.error)),jt(mt(this.quantiles)),jt(mt(this.count)),jt(mt(this.min)),jt(mt(this.max)),jt(mt(this.sum)),he(this))}[Ct](n){return YU(n)&&this.error===n.error&&Ot(this.quantiles,n.quantiles)&&this.count===n.count&&this.min===n.min&&this.max===n.max&&this.sum===n.sum}pipe(){return Tt(this,arguments)}}const BU=t=>new zU(t),qU=t=>new $U(t),HU=t=>new FU(t),IU=t=>new jU(t.buckets,t.count,t.min,t.max,t.sum),PU=t=>new UU(t.error,t.quantiles,t.count,t.min,t.max,t.sum),VU=t=>Mt(t,bg),GU=t=>Mt(t,Sg),KU=t=>Mt(t,Eg),QU=t=>Mt(t,Tg),YU=t=>Mt(t,Rg),XU="effect/MetricHook",ZU=Symbol.for(XU),JU={_In:t=>t,_Out:t=>t},nu=t=>({[ZU]:JU,pipe(){return Tt(this,arguments)},...t}),NE=BigInt(0),WU=t=>{let n=t.keyType.bigint?NE:0;const r=t.keyType.incremental?t.keyType.bigint?l=>l>=NE:l=>l>=0:l=>!0,s=l=>{r(l)&&(n=n+l)};return nu({get:()=>BU(n),update:s,modify:s})},t8=t=>{const n=new Map;for(const s of t.keyType.preregisteredWords)n.set(s,0);const r=s=>{const l=n.get(s)??0;n.set(s,l+1)};return nu({get:()=>qU(n),update:r,modify:r})},e8=(t,n)=>{let r=n;return nu({get:()=>HU(r),update:s=>{r=s},modify:s=>{r=r+s}})},n8=t=>{const n=t.keyType.boundaries.values,r=n.length,s=new Uint32Array(r+1),l=new Float32Array(r);let c=0,f=0,h=Number.MAX_VALUE,m=Number.MIN_VALUE;D(n,id(vc),ws((g,_)=>{l[_]=g}));const y=g=>{let _=0,S=r;for(;_!==S;){const T=Math.floor(_+(S-_)/2),A=l[T];g<=A?S=T:_=T,S===_+1&&(g<=l[_]?S=_:_=S)}s[_]=s[_]+1,c=c+1,f=f+g,g<h&&(h=g),g>m&&(m=g)},p=()=>{const g=Qg(r);let _=0;for(let S=0;S<r;S++){const T=l[S],A=s[S];_=_+A,g[S]=[T,_]}return g};return nu({get:()=>IU({buckets:p(),count:c,min:h,max:m,sum:f}),update:y,modify:y})},a8=t=>{const{error:n,maxAge:r,maxSize:s,quantiles:l}=t.keyType,c=D(l,id(vc)),f=Qg(s);let h=0,m=0,y=0,p=0,g=0;const _=T=>{const A=[];let x=0;for(;x!==s-1;){const L=f[x];if(L!=null){const[j,q]=L,K=vo(T-j);V4(K,go)&&P4(K,r)&&A.push(q)}x=x+1}return r8(n,c,id(A,vc))},S=(T,A)=>{if(s>0){h=h+1;const x=h%s;f[x]=[A,T]}p=m===0?T:Math.min(p,T),g=m===0?T:Math.max(g,T),m=m+1,y=y+T};return nu({get:()=>PU({error:n,quantiles:_(Date.now()),count:m,min:p,max:g,sum:y}),update:([T,A])=>S(T,A),modify:([T,A])=>S(T,A)})},r8=(t,n,r)=>{const s=r.length;if(!_n(n))return Ls();const l=n[0],c=n.slice(1),f=zE(t,s,gt(),0,l,r),h=ra(f);return c.forEach(m=>{h.push(zE(t,s,f.value,f.consumed,m,f.rest))}),ws(h,m=>[m.quantile,m.value])},zE=(t,n,r,s,l,c)=>{let f=t,h=n,m=r,y=s,p=l,g=c,_=t,S=n,T=r,A=s,x=l,L=c;for(;;){if(!_n(g))return{quantile:p,value:gt(),consumed:y,rest:[]};if(p===1)return{quantile:p,value:Rt(gR(g)),consumed:y+g.length,rest:[]};const j=on(g),q=HN(g,$=>$===j),K=p*h,Z=f/2*K,I=y+q[0].length,Q=Math.abs(I-K);if(I<K-Z){_=f,S=h,T=fc(g),A=I,x=p,L=q[1],f=_,h=S,m=T,y=A,p=x,g=L;continue}if(I>K+Z){const $=He(m)?Rt(j):m;return{quantile:p,value:$,consumed:y,rest:g}}switch(m._tag){case"None":{_=f,S=h,T=fc(g),A=I,x=p,L=q[1],f=_,h=S,m=T,y=A,p=x,g=L;continue}case"Some":{const $=Math.abs(K-m.value);if(Q<$){_=f,S=h,T=fc(g),A=I,x=p,L=q[1],f=_,h=S,m=T,y=A,p=x,g=L;continue}return{quantile:p,value:Rt(m.value),consumed:y,rest:g}}}}throw new Error("BUG: MetricHook.resolveQuantiles - please report an issue at https://github.com/Effect-TS/effect/issues")},s8="effect/MetricPair",i8=Symbol.for(s8),o8={_Type:t=>t},l8=(t,n)=>({[i8]:o8,metricKey:t,metricState:n,pipe(){return Tt(this,arguments)}}),c8="effect/MetricRegistry",LE=Symbol.for(c8);var GT;GT=LE;class u8{constructor(){E(this,GT,LE);E(this,"map",K6())}snapshot(){const n=[];for(const[r,s]of this.map)n.push(l8(r,s.get()));return n}get(n){const r=D(this.map,ps(n),ys);if(r==null){if(KM(n.keyType))return this.getCounter(n);if(RU(n.keyType))return this.getGauge(n);if(TU(n.keyType))return this.getFrequency(n);if(QM(n.keyType))return this.getHistogram(n);if(OU(n.keyType))return this.getSummary(n);throw new Error("BUG: MetricRegistry.get - unknown MetricKeyType - please report an issue at https://github.com/Effect-TS/effect/issues")}else return r}getCounter(n){let r=D(this.map,ps(n),ys);if(r==null){const s=WU(n);D(this.map,Zl(n))||D(this.map,Jl(n,s)),r=s}return r}getFrequency(n){let r=D(this.map,ps(n),ys);if(r==null){const s=t8(n);D(this.map,Zl(n))||D(this.map,Jl(n,s)),r=s}return r}getGauge(n){let r=D(this.map,ps(n),ys);if(r==null){const s=e8(n,n.keyType.bigint?BigInt(0):0);D(this.map,Zl(n))||D(this.map,Jl(n,s)),r=s}return r}getHistogram(n){let r=D(this.map,ps(n),ys);if(r==null){const s=n8(n);D(this.map,Zl(n))||D(this.map,Jl(n,s)),r=s}return r}getSummary(n){let r=D(this.map,ps(n),ys);if(r==null){const s=a8(n);D(this.map,Zl(n))||D(this.map,Jl(n,s)),r=s}return r}}const f8=()=>new u8,d8="effect/Metric",h8=Symbol.for(d8),m8={_Type:t=>t,_In:t=>t,_Out:t=>t},$E=Ut(Symbol.for("effect/Metric/globalMetricRegistry"),()=>f8()),ew=function(t,n,r,s){const l=Object.assign(c=>R0(c,f=>v8(l,f)),{[h8]:m8,keyType:t,unsafeUpdate:n,unsafeValue:r,unsafeModify:s,register(){return this.unsafeValue([]),this},pipe(){return Tt(this,arguments)}});return l},wh=(t,n)=>nw(CU(t,n)),nw=t=>{let n;const r=new WeakMap,s=l=>{if(l.length===0)return n!==void 0||(n=$E.get(t)),n;let c=r.get(l);return c!==void 0||(c=$E.get(kU(t,l)),r.set(l,c)),c};return ew(t.keyType,(l,c)=>s(c).update(l),l=>s(l).get(),(l,c)=>s(c).modify(l))},y8=(t,n,r)=>nw(DU(t,n,r)),p8=O(3,(t,n,r)=>g8(t,[wF(n,r)])),g8=O(2,(t,n)=>ew(t.keyType,(r,s)=>t.unsafeUpdate(r,jf(n,s)),r=>t.unsafeValue(jf(n,r)),(r,s)=>t.unsafeModify(r,jf(n,s)))),v8=O(2,(t,n)=>yh(ng,r=>ot(()=>t.unsafeUpdate(n,r))));({...Gg});const _8=O(2,(t,n)=>yh(qM,r=>ot(()=>{if(r.has(t)){const s=r.get(t);s.state.completed||(s.state.completed=!0,YO(s.result,n))}}))),b8="effect/Supervisor",Ah=Symbol.for(b8),tv={_T:t=>t};var KT;KT=Ah;const iv=class iv{constructor(n,r){E(this,"underlying");E(this,"value0");E(this,KT,tv);this.underlying=n,this.value0=r}get value(){return this.value0}onStart(n,r,s,l){this.underlying.onStart(n,r,s,l)}onEnd(n,r){this.underlying.onEnd(n,r)}onEffect(n,r){this.underlying.onEffect(n,r)}onSuspend(n){this.underlying.onSuspend(n)}onResume(n){this.underlying.onResume(n)}map(n){return new iv(this,D(this.value,qt(n)))}zip(n){return new Md(this,n)}};let Od=iv;var QT;QT=Ah;const ov=class ov{constructor(n,r){E(this,"left");E(this,"right");E(this,"_tag","Zip");E(this,QT,tv);this.left=n,this.right=r}get value(){return hh(this.left.value,this.right.value)}onStart(n,r,s,l){this.left.onStart(n,r,s,l),this.right.onStart(n,r,s,l)}onEnd(n,r){this.left.onEnd(n,r),this.right.onEnd(n,r)}onEffect(n,r){this.left.onEffect(n,r),this.right.onEffect(n,r)}onSuspend(n){this.left.onSuspend(n),this.right.onSuspend(n)}onResume(n){this.left.onResume(n),this.right.onResume(n)}map(n){return new Od(this,D(this.value,qt(n)))}zip(n){return new ov(this,n)}};let Md=ov;const aw=t=>Mt(t,Ah)&&P2(t,"Zip");var YT;YT=Ah;class S8{constructor(n){E(this,"effect");E(this,YT,tv);this.effect=n}get value(){return this.effect}onStart(n,r,s,l){}onEnd(n,r){}onEffect(n,r){}onSuspend(n){}onResume(n){}map(n){return new Od(this,D(this.value,qt(n)))}zip(n){return new Md(this,n)}onRun(n,r){return n()}}const E8=t=>new S8(t),xh=Ut("effect/Supervisor/none",()=>E8(oe)),T8=Bo,rw="Empty",sw="AddSupervisor",iw="RemoveSupervisor",ow="AndThen",mc={_tag:rw},If=(t,n)=>({_tag:ow,first:t,second:n}),R8=(t,n)=>O8(n,ln(t)),O8=(t,n)=>{let r=t,s=n;for(;ar(s);){const l=Ra(s);switch(l._tag){case rw:{s=Wa(s);break}case sw:{r=r.zip(l.supervisor),s=Wa(s);break}case iw:{r=Og(r,l.supervisor),s=Wa(s);break}case ow:{s=Yn(l.first)(Yn(l.second)(Wa(s)));break}}}return r},Og=(t,n)=>Ot(t,n)?xh:aw(t)?Og(t.left,n).zip(Og(t.right,n)):t,wd=t=>Ot(t,xh)?js():aw(t)?D(wd(t.left),Ec(wd(t.right))):c0(t),M8=(t,n)=>{if(Ot(t,n))return mc;const r=wd(t),s=wd(n),l=D(s,K1(r),Tc(mc,(f,h)=>If(f,{_tag:sw,supervisor:h}))),c=D(r,K1(s),Tc(mc,(f,h)=>If(f,{_tag:iw,supervisor:h})));return If(l,c)},w8=T8({empty:mc,patch:R8,combine:If,diff:M8}),A8=wh("effect_fiber_started",{incremental:!0}),FE=wh("effect_fiber_active"),x8=wh("effect_fiber_successes",{incremental:!0}),C8=wh("effect_fiber_failures",{incremental:!0}),D8=p8(y8("effect_fiber_lifetimes",fU({start:.5,factor:2,count:35})),"time_unit","milliseconds"),Wl="Continue",k8="Done",jE="Yield",N8={_E:t=>t,_A:t=>t},kf=t=>{throw new Error(`BUG: FiberRuntime - ${mo(t)} - please report an issue at https://github.com/Effect-TS/effect/issues`)},Qa=Symbol.for("effect/internal/fiberRuntime/YieldedOp"),Ya=Ut("effect/internal/fiberRuntime/yieldedOpChannel",()=>({currentOp:null})),tc={[nd]:(t,n,r)=>Ge(()=>n.effect_instruction_i1(r)),OnStep:(t,n,r)=>ie(ie(r)),[ad]:(t,n,r)=>Ge(()=>n.effect_instruction_i2(r)),[Vg]:(t,n,r)=>(t.patchRuntimeFlags(t.currentRuntimeFlags,n.patch),Nr(t.currentRuntimeFlags)&&t.isInterrupted()?te(t.getInterruptedCause()):ie(r)),[rd]:(t,n,r)=>(Ge(()=>n.effect_instruction_i2(r)),Ge(()=>n.effect_instruction_i0())?(t.pushStack(n),Ge(()=>n.effect_instruction_i1())):oe),[cc]:(t,n,r)=>{const s=Ge(()=>n.effect_instruction_i0.next(r));return s.done?ie(s.value):(t.pushStack(n),Jk(s.value))}},z8={[Q0]:(t,n,r,s)=>(t.processNewInterruptSignal(s.cause),Nr(n)?te(s.cause):r),[X0]:(t,n,r,s)=>{throw new Error("It is illegal to have multiple concurrent run loops in a single fiber")},[Y0]:(t,n,r,s)=>(s.onFiber(t,DM(n)),r),[Z0]:(t,n,r,s)=>J(M0(),()=>r)},L8=t=>uh(i$(t),n=>Ds(b$(n),([r,s])=>{const l=new Map,c=[];for(const h of s){c.push(Ma(h));for(const m of h)l.set(m.request,m)}const f=c.flat();return Jc(r7(r.runAll(c),f,()=>f.forEach(h=>{h.listeners.interrupted=!0})),qM,l)},!1,!1)),$8=qd();var XT,ZT;class lw extends Eh{constructor(r,s,l){super();E(this,ZT,Yj);E(this,XT,N8);E(this,"_fiberRefs");E(this,"_fiberId");E(this,"_queue",new Array);E(this,"_children",null);E(this,"_observers",new Array);E(this,"_running",!1);E(this,"_stack",[]);E(this,"_asyncInterruptor",null);E(this,"_asyncBlockingOn",null);E(this,"_exitValue",null);E(this,"_steps",[]);E(this,"_isYielding",!1);E(this,"currentRuntimeFlags");E(this,"currentOpCount",0);E(this,"currentSupervisor");E(this,"currentScheduler");E(this,"currentTracer");E(this,"currentSpan");E(this,"currentContext");E(this,"currentDefaultServices");E(this,"run",()=>{this.drainQueueOnCurrentThread()});if(this.currentRuntimeFlags=l,this._fiberId=r,this._fiberRefs=s,J1(l)){const c=this.getFiberRef(ng);A8.unsafeUpdate(1,c),FE.unsafeUpdate(1,c)}this.refreshRefCache()}commit(){return Dc(this)}id(){return this._fiberId}resume(r){this.tell(qi(r))}get status(){return this.ask((r,s)=>s)}get runtimeFlags(){return this.ask((r,s)=>fj(s)?r.currentRuntimeFlags:s.runtimeFlags)}scope(){return Gj(this)}get children(){return this.ask(r=>Array.from(r.getChildren()))}getChildren(){return this._children===null&&(this._children=new Set),this._children}getInterruptedCause(){return this.getFiberRef(Cf)}fiberRefs(){return this.ask(r=>r.getFiberRefs())}ask(r){return ae(()=>{const s=KO(this._fiberId);return this.tell(Hf((l,c)=>{YO(s,ot(()=>r(l,c)))})),_h(s)})}tell(r){this._queue.push(r),this._running||(this._running=!0,this.drainQueueLaterOnExecutor())}get await(){return Ca(r=>{const s=l=>r(yt(l));return this.tell(Hf((l,c)=>{l._exitValue!==null?s(this._exitValue):l.addObserver(s)})),ot(()=>this.tell(Hf((l,c)=>{l.removeObserver(s)})))},this.id())}get inheritAll(){return Xt((r,s)=>{const l=r.id(),c=r.getFiberRefs(),f=s.runtimeFlags,h=this.getFiberRefs(),m=eF(c,l,h);r.setFiberRefs(m);const y=r.getFiberRef(HE),p=D(qr(f,y),tE(Ys),tE(Wp));return $O(p)})}get poll(){return ot(()=>Kd(this._exitValue))}unsafePoll(){return this._exitValue}interruptAsFork(r){return ot(()=>this.tell(vp(Ta(r))))}unsafeInterruptAsFork(r){this.tell(vp(Ta(r)))}addObserver(r){this._exitValue!==null?r(this._exitValue):this._observers.push(r)}removeObserver(r){this._observers=this._observers.filter(s=>s!==r)}getFiberRefs(){return this.setFiberRef(HE,this.currentRuntimeFlags),this._fiberRefs}unsafeDeleteFiberRef(r){this._fiberRefs=lM(this._fiberRefs,r)}getFiberRef(r){return this._fiberRefs.locals.has(r)?this._fiberRefs.locals.get(r)[0][1]:r.initial}setFiberRef(r,s){this._fiberRefs=cg(this._fiberRefs,{fiberId:this._fiberId,fiberRef:r,value:s}),this.refreshRefCache()}refreshRefCache(){this.currentDefaultServices=this.getFiberRef(Do),this.currentTracer=this.currentDefaultServices.unsafeMap.get(F0.key),this.currentSupervisor=this.getFiberRef(e7),this.currentScheduler=this.getFiberRef(K0),this.currentContext=this.getFiberRef(ir),this.currentSpan=this.currentContext.unsafeMap.get(Sh.key)}setFiberRefs(r){this._fiberRefs=r,this.refreshRefCache()}addChild(r){this.getChildren().add(r)}removeChild(r){this.getChildren().delete(r)}transferChildren(r){const s=this._children;if(this._children=null,s!==null&&s.size>0)for(const l of s)l._exitValue===null&&r.add(this.currentRuntimeFlags,l)}drainQueueOnCurrentThread(){let r=!0;for(;r;){let s=Wl;const l=globalThis[ms];globalThis[ms]=this;try{for(;s===Wl;)s=this._queue.length===0?k8:this.evaluateMessageWhileSuspended(this._queue.splice(0,1)[0])}finally{this._running=!1,globalThis[ms]=l}this._queue.length>0&&!this._running?(this._running=!0,s===jE?(this.drainQueueLaterOnExecutor(),r=!1):r=!0):r=!1}}drainQueueLaterOnExecutor(){this.currentScheduler.scheduleTask(this.run,this.getFiberRef(Vo))}drainQueueWhileRunning(r,s){let l=s;for(;this._queue.length>0;){const c=this._queue.splice(0,1)[0];l=z8[c._tag](this,r,l,c)}return l}isInterrupted(){return!O$(this.getFiberRef(Cf))}addInterruptedCause(r){const s=this.getFiberRef(Cf);this.setFiberRef(Cf,We(s,r))}processNewInterruptSignal(r){this.addInterruptedCause(r),this.sendInterruptSignalToAllChildren()}sendInterruptSignalToAllChildren(){if(this._children===null||this._children.size===0)return!1;let r=!1;for(const s of this._children)s.tell(vp(Ta(this.id()))),r=!0;return r}interruptAllChildren(){if(this.sendInterruptSignalToAllChildren()){const r=this._children.values();this._children=null;let s=!1;return O0({while:()=>!s,body:()=>{const c=r.next();return c.done?ot(()=>{s=!0}):sr(c.value.await)},step:()=>{}})}return null}reportExitValue(r){if(J1(this.currentRuntimeFlags)){const s=this.getFiberRef(ng),l=this.id().startTimeMillis,c=Date.now();switch(D8.unsafeUpdate(c-l,s),FE.unsafeUpdate(-1,s),r._tag){case Le:{x8.unsafeUpdate(1,s);break}case ze:{C8.unsafeUpdate(1,s);break}}}if(r._tag==="Failure"){const s=this.getFiberRef(_6);!m0(r.cause)&&s._tag==="Some"&&this.log("Fiber terminated with an unhandled error",r.cause,s)}}setExitValue(r){this._exitValue=r,this.reportExitValue(r);for(let s=this._observers.length-1;s>=0;s--)this._observers[s](r);this._observers=[]}getLoggers(){return this.getFiberRef(q8)}log(r,s,l){const c=Aa(l)?l.value:this.getFiberRef(m6),f=this.getFiberRef(F8);if(pF(f,c))return;const h=this.getFiberRef(y6),m=this.getFiberRef(h6),y=this.getLoggers(),p=this.getFiberRefs();if(VR(y)>0){const g=As(this.getFiberRef(Do),Go),_=new Date(g.unsafeCurrentTimeMillis());aN(p,()=>{for(const S of y)S.log({fiberId:this.id(),logLevel:c,message:r,cause:s,context:p,spans:h,annotations:m,date:_})})}}evaluateMessageWhileSuspended(r){switch(r._tag){case Z0:return jE;case Q0:return this.processNewInterruptSignal(r.cause),this._asyncInterruptor!==null&&(this._asyncInterruptor(te(r.cause)),this._asyncInterruptor=null),Wl;case X0:return this._asyncInterruptor=null,this._asyncBlockingOn=null,this.evaluateEffect(r.effect),Wl;case Y0:return r.onFiber(this,this._exitValue!==null?cj:uj(this.currentRuntimeFlags,this._asyncBlockingOn)),Wl;default:return kf(r)}}evaluateEffect(r){this.currentSupervisor.onResume(this);try{let s=Nr(this.currentRuntimeFlags)&&this.isInterrupted()?te(this.getInterruptedCause()):r;for(;s!==null;){const l=s,c=this.runLoop(l);if(c===Qa){const f=Ya.currentOp;Ya.currentOp=null,f._op===Ff?t$(this.currentRuntimeFlags)?(this.tell(Hj()),this.tell(qi(Cn)),s=null):s=Cn:f._op===lc&&(s=null)}else{this.currentRuntimeFlags=D(this.currentRuntimeFlags,n$(Wp));const f=this.interruptAllChildren();f!==null?s=J(f,()=>c):(this._queue.length===0?this.setExitValue(c):this.tell(qi(c)),s=null)}}}finally{this.currentSupervisor.onSuspend(this)}}start(r){if(this._running)this.tell(qi(r));else{this._running=!0;const s=globalThis[ms];globalThis[ms]=this;try{this.evaluateEffect(r)}finally{this._running=!1,globalThis[ms]=s,this._queue.length>0&&this.drainQueueLaterOnExecutor()}}}startFork(r){this.tell(qi(r))}patchRuntimeFlags(r,s){const l=Wi(r,s);return globalThis[ms]=this,this.currentRuntimeFlags=l,l}initiateAsync(r,s){let l=!1;const c=f=>{l||(l=!0,this.tell(qi(f)))};Nr(r)&&(this._asyncInterruptor=c);try{s(c)}catch(f){c(Oe(ca(f)))}}pushStack(r){this._stack.push(r),r._op==="OnStep"&&this._steps.push({refs:this.getFiberRefs(),flags:this.currentRuntimeFlags})}popStack(){const r=this._stack.pop();if(r)return r._op==="OnStep"&&this._steps.pop(),r}getNextSuccessCont(){let r=this.popStack();for(;r;){if(r._op!==$f)return r;r=this.popStack()}}getNextFailCont(){let r=this.popStack();for(;r;){if(r._op!==nd&&r._op!==rd&&r._op!==cc)return r;r=this.popStack()}}[(ZT=Qj,XT=Zj,sN)](r){return ot(()=>OR(this.currentContext,r))}Left(r){return ve(r.left)}None(r){return ve(new C0)}Right(r){return ie(r.right)}Some(r){return ie(r.value)}Micro(r){return Mc(s=>{let l=s;const c=Uj(kj(r,this.currentContext));return c.addObserver(f=>{if(f._tag==="Success")return l(ie(f.value));switch(f.cause._tag){case"Interrupt":return l(te(Ta(Bs)));case"Fail":return l(ve(f.cause.error));case"Die":return l(md(f.cause.defect))}}),Mc(f=>{l=h=>{f(oe)},c.unsafeInterrupt()})})}[W2](r){const s=Ge(()=>r.effect_instruction_i0()),l=this.getNextSuccessCont();return l!==void 0?(l._op in tc||kf(l),tc[l._op](this,l,s)):(Ya.currentOp=ie(s),Qa)}[Le](r){const s=r,l=this.getNextSuccessCont();return l!==void 0?(l._op in tc||kf(l),tc[l._op](this,l,s.effect_instruction_i0)):(Ya.currentOp=s,Qa)}[ze](r){const s=r.effect_instruction_i0,l=this.getNextFailCont();if(l!==void 0)switch(l._op){case $f:case ad:return Nr(this.currentRuntimeFlags)&&this.isInterrupted()?te(eE(s)):Ge(()=>l.effect_instruction_i1(s));case"OnStep":return Nr(this.currentRuntimeFlags)&&this.isInterrupted()?te(eE(s)):ie(te(s));case Vg:return this.patchRuntimeFlags(this.currentRuntimeFlags,l.patch),Nr(this.currentRuntimeFlags)&&this.isInterrupted()?te(We(s,this.getInterruptedCause())):te(s);default:kf(l)}else return Ya.currentOp=te(s),Qa}[tR](r){return Ge(()=>r.effect_instruction_i0(this,DM(this.currentRuntimeFlags)))}Blocked(r){const s=this.getFiberRefs(),l=this.currentRuntimeFlags;if(this._steps.length>0){const c=[],f=this._steps[this._steps.length-1];let h=this.popStack();for(;h&&h._op!=="OnStep";)c.push(h),h=this.popStack();this.setFiberRefs(f.refs),this.currentRuntimeFlags=f.flags;const m=xc(f.refs,s),y=qr(f.flags,l);return ie(MO(r.effect_instruction_i0,Xt(p=>{for(;c.length>0;)p.pushStack(c.pop());return p.setFiberRefs(Cc(p.id(),p.getFiberRefs())(m)),p.currentRuntimeFlags=Wi(y)(p.currentRuntimeFlags),r.effect_instruction_i1})))}return fa(c=>J(ev(q$(r.effect_instruction_i0)),()=>c(r.effect_instruction_i1)))}RunBlocked(r){return L8(r.effect_instruction_i0)}[zo](r){const s=r.effect_instruction_i0,l=this.currentRuntimeFlags,c=Wi(l,s);if(Nr(c)&&this.isInterrupted())return te(this.getInterruptedCause());if(this.patchRuntimeFlags(this.currentRuntimeFlags,s),r.effect_instruction_i1){const f=qr(c,l);return this.pushStack(new H$(f,r)),Ge(()=>r.effect_instruction_i1(l))}else return Cn}[nd](r){return this.pushStack(r),r.effect_instruction_i0}OnStep(r){return this.pushStack(r),r.effect_instruction_i0}[$f](r){return this.pushStack(r),r.effect_instruction_i0}[ad](r){return this.pushStack(r),r.effect_instruction_i0}[lc](r){return this._asyncBlockingOn=r.effect_instruction_i1,this.initiateAsync(this.currentRuntimeFlags,r.effect_instruction_i0),Ya.currentOp=r,Qa}[Ff](r){return this._isYielding=!1,Ya.currentOp=r,Qa}[rd](r){const s=r.effect_instruction_i0,l=r.effect_instruction_i1;return s()?(this.pushStack(r),l()):Cn}[cc](r){return tc[cc](this,r,void 0)}[Gd](r){return Ge(()=>r.commit())}runLoop(r){let s=r;for(this.currentOpCount=0;;){if((this.currentRuntimeFlags&WL)!==0&&this.currentSupervisor.onEffect(this,s),this._queue.length>0&&(s=this.drainQueueWhileRunning(this.currentRuntimeFlags,s)),!this._isYielding){this.currentOpCount+=1;const l=this.currentScheduler.shouldYield(this);if(l!==!1){this._isYielding=!0,this.currentOpCount=0;const c=s;s=J(M0({priority:l}),()=>c)}}try{if(s=this.currentTracer.context(()=>$8!==s[xo]._V?eg(`Cannot execute an Effect versioned ${s[xo]._V} with a Runtime of version ${qd()}`):this[s._op](s),this),s===Qa){const l=Ya.currentOp;return l._op===Ff||l._op===lc?Qa:(Ya.currentOp=null,l._op===Le||l._op===ze?l:te(ca(l)))}}catch(l){s!==Qa&&!Mt(s,"_op")||!(s._op in this)?s=eg(`Not a valid effect: ${mo(s)}`):M6(l)?s=te(We(ca(l),Ta(Bs))):s=md(l)}}}}const F8=Ut("effect/FiberRef/currentMinimumLogLevel",()=>Ke(gF("Info"))),j8=t=>J0(n=>{const r=rF(n.context,Do);As(r,sM).unsafe.log(t.log(n))}),U8=Ut(Symbol.for("effect/Logger/defaultLogger"),()=>j8(iU)),B8=Ut(Symbol.for("effect/Logger/tracerLogger"),()=>J0(({annotations:t,cause:n,context:r,fiberId:s,logLevel:l,message:c})=>{const f=Fo(Ac(r,ir),Sh);if(f._tag==="None"||f.value._tag==="ExternalSpan")return;const h=OR(Ac(r,Do),Go),m={};for(const[y,p]of t)m[y]=p;m["effect.fiberId"]=Uz(s),m["effect.logLevel"]=l.label,n!==null&&n._tag!=="Empty"&&(m["effect.cause"]=Qc(n,{renderErrorCause:!0})),f.value.event(mo(Array.isArray(c)?c[0]:c),h.unsafeCurrentTimeNanos(),m)})),q8=Ut(Symbol.for("effect/FiberRef/currentLoggers"),()=>u6(c0(U8,B8))),H8=O(t=>ka(t[0]),(t,n)=>dh(R0(t,r=>I8(s=>n(r,s))))),I8=t=>Xt(n=>{const r=n.getFiberRefs(),s=e$(n.currentRuntimeFlags,Ys);return J(mw,l=>yd(l,c=>Xt(f=>{const h=f.getFiberRefs(),m=f.currentRuntimeFlags,y=xc(h,r),p=qr(m,s),g=xc(r,h);return f.setFiberRefs(Cc(y,f.id(),r)),kc(J$(t(c),p),ot(()=>{f.setFiberRefs(Cc(g,f.id(),f.getFiberRefs()))}))})))}),P8=t=>{if(Array.isArray(t)||V2(t))return[t,gt()];const n=Object.keys(t),r=n.length;return[n.map(s=>t[s]),Rt(s=>{const l={};for(let c=0;c<r;c++)l[n[c]]=s[c];return l})]},V8=(t,n,r)=>{const s=[];for(const l of t)s.push(Co(l));return J(No(s,Yt,{concurrency:r==null?void 0:r.concurrency,batching:r==null?void 0:r.batching,concurrentFinalizers:r==null?void 0:r.concurrentFinalizers}),l=>{const c=gt(),f=l.length,h=new Array(f),m=new Array(f);let y=!1;for(let p=0;p<f;p++){const g=l[p];g._tag==="Left"?(h[p]=Rt(g.left),y=!0):(m[p]=g.right,h[p]=c)}return y?n._tag==="Some"?ve(n.value(h)):ve(h):r!=null&&r.discard?oe:n._tag==="Some"?yt(n.value(m)):yt(m)})},G8=(t,n,r)=>{const s=[];for(const l of t)s.push(Co(l));return r!=null&&r.discard?No(s,Yt,{concurrency:r==null?void 0:r.concurrency,batching:r==null?void 0:r.batching,discard:!0,concurrentFinalizers:r==null?void 0:r.concurrentFinalizers}):qt(No(s,Yt,{concurrency:r==null?void 0:r.concurrency,batching:r==null?void 0:r.batching,concurrentFinalizers:r==null?void 0:r.concurrentFinalizers}),l=>n._tag==="Some"?n.value(l):l)},cw=(t,n)=>{const[r,s]=P8(t);return(n==null?void 0:n.mode)==="validate"?V8(r,s,n):(n==null?void 0:n.mode)==="either"?G8(r,s,n):(n==null?void 0:n.discard)!==!0&&s._tag==="Some"?qt(No(r,Yt,n),s.value):No(r,Yt,n)},No=O(t=>V2(t[0]),(t,n,r)=>Xt(s=>{const l=(r==null?void 0:r.batching)===!0||(r==null?void 0:r.batching)==="inherit"&&s.getFiberRef(v6);return r!=null&&r.discard?kE(r.concurrency,()=>Hi(dg,r==null?void 0:r.concurrentFinalizers)(c=>l?Ds(t,(f,h)=>c(n(f,h)),!0,!1,1):uh(t,(f,h)=>c(n(f,h)))),()=>Hi(hg,r==null?void 0:r.concurrentFinalizers)(c=>Ds(t,(f,h)=>c(n(f,h)),l,!1)),c=>Hi(mg(c),r==null?void 0:r.concurrentFinalizers)(f=>Ds(t,(h,m)=>f(n(h,m)),l,!1,c))):kE(r==null?void 0:r.concurrency,()=>Hi(dg,r==null?void 0:r.concurrentFinalizers)(c=>l?Mg(t,1,(f,h)=>c(n(f,h)),!0):tr(t,(f,h)=>c(n(f,h)))),()=>Hi(hg,r==null?void 0:r.concurrentFinalizers)(c=>uw(t,(f,h)=>c(n(f,h)),l)),c=>Hi(mg(c),r==null?void 0:r.concurrentFinalizers)(f=>Mg(t,c,(h,m)=>f(n(h,m)),l)))})),uw=(t,n,r)=>ae(()=>{const s=de(t),l=new Array(s.length);return qe(Ds(s,(f,h)=>J(n(f,h),m=>ot(()=>l[h]=m)),r,!1),yt(l))}),Ds=(t,n,r,s,l)=>fa(c=>Y$(f=>Xt(h=>{let m=Array.from(t).reverse(),y=m.length;if(y===0)return oe;let p=0,g=!1;const _=l?Math.min(m.length,l):m.length,S=new Set,T=new Array,A=()=>S.forEach($=>{$.currentScheduler.scheduleTask(()=>{$.unsafeInterruptAsFork(h.id())},0)}),x=new Array,L=new Array,j=new Array,q=()=>{const $=T.filter(({exit:F})=>F._tag==="Failure").sort((F,W)=>F.index<W.index?-1:F.index===W.index?0:1).map(({exit:F})=>F);return $.length===0&&$.push(Cn),$},K=($,F=!1)=>{const W=dh(f($)),rt=K8(W,h,h.currentRuntimeFlags,Mh);return h.currentScheduler.scheduleTask(()=>{F&&rt.unsafeInterruptAsFork(h.id()),rt.resume(W)},0),rt},Z=()=>{s||(y-=m.length,m=[]),g=!0,A()},I=r?P$:Hr,Q=K(Ca($=>{const F=(rt,nt)=>{rt._op==="Blocked"?j.push(rt):(T.push({index:nt,exit:rt}),rt._op==="Failure"&&!g&&Z())},W=()=>{if(m.length>0){const rt=m.pop();let nt=p++;const pt=()=>{const X=m.pop();return nt=p++,J(M0(),()=>J(I(c(n(X,nt))),St))},St=X=>m.length>0&&(F(X,nt),m.length>0)?pt():yt(X),vt=J(I(c(n(rt,nt))),St),k=K(vt);x.push(k),S.add(k),g&&k.currentScheduler.scheduleTask(()=>{k.unsafeInterruptAsFork(h.id())},0),k.addObserver(X=>{let lt;if(X._op==="Failure"?lt=X:lt=X.effect_instruction_i0,L.push(k),S.delete(k),F(lt,nt),T.length===y)$(yt(Gn(to(q(),{parallel:!0}),()=>Cn)));else if(j.length+T.length===y){const At=q(),M=j.map(H=>H.effect_instruction_i0).reduce(hO);$(yt(MO(M,Ds([Gn(to(At,{parallel:!0}),()=>Cn),...j.map(H=>H.effect_instruction_i1)],H=>H,r,!0,l))))}else W()})}};for(let rt=0;rt<_;rt++)W()}));return sr(Ps(ch(c(Dc(Q))),vh({onFailure:$=>{Z();const F=j.length+1,W=Math.min(typeof l=="number"?l:j.length,j.length),rt=Array.from(j);return Ca(nt=>{let pt=0,St=0;const vt=(X,lt)=>At=>{pt++,pt===F&&nt(ie(te($))),rt.length>0&&lt&&k()},k=()=>{K(rt.pop(),!0).addObserver(vt(St,!0)),St++};Q.addObserver(vt(St,!1)),St++;for(let X=0;X<W;X++)k()})},onSuccess:()=>tr(L,$=>$.inheritAll)})))}))),Mg=(t,n,r,s)=>ae(()=>{const l=de(t),c=new Array(l.length);return qe(Ds(l,(h,m)=>qt(r(h,m),y=>c[m]=y),s,!1,n),yt(c))}),ev=t=>Q8(t,Mh),fw=(t,n,r,s=null)=>{const l=Ad(t,n,r,s);return l.resume(t),l},K8=(t,n,r,s=null)=>Ad(t,n,r,s),Ad=(t,n,r,s=null)=>{const l=XR(),c=n.getFiberRefs(),f=nF(c,l),h=new lw(l,f,r),m=Ac(f,ir),y=h.currentSupervisor;return y.onStart(m,t,Rt(n),h),h.addObserver(g=>y.onEnd(g,h)),(s!==null?s:D(n.getFiberRef(ag),Gn(()=>n.scope()))).add(r,h),h},Q8=(t,n)=>Xt((r,s)=>yt(fw(t,r,s.runtimeFlags,n))),UE=t=>Zs(n=>rr(Fo(n,Js),{onNone:()=>t,onSome:r=>{switch(r.strategy._tag){case"Parallel":return t;case"Sequential":case"ParallelN":return J(ph(r,hg),s=>ru(t,s))}}})),BE=t=>n=>Zs(r=>rr(Fo(r,Js),{onNone:()=>n,onSome:s=>s.strategy._tag==="ParallelN"&&s.strategy.parallelism===t?n:J(ph(s,mg(t)),l=>ru(n,l))})),Hi=(t,n)=>r=>Zs(s=>rr(Fo(s,Js),{onNone:()=>r(Yt),onSome:l=>{if(n===!0){const c=t._tag==="Parallel"?UE:t._tag==="Sequential"?qE:BE(t.parallelism);switch(l.strategy._tag){case"Parallel":return c(r(UE));case"Sequential":return c(r(qE));case"ParallelN":return c(r(BE(l.strategy.parallelism)))}}else return r(Yt)}})),dw=t=>J(Js,t),hw=t=>J(nv(),n=>Ps(t(n),r=>n.close(r))),qE=t=>Zs(n=>rr(Fo(n,Js),{onNone:()=>t,onSome:r=>{switch(r.strategy._tag){case"Sequential":return t;case"Parallel":case"ParallelN":return J(ph(r,dg),s=>ru(t,s))}}})),Y8=O(t=>ka(t[1]),(t,n,r)=>au(t,n,(s,l)=>[s,l],r)),X8=O(t=>ka(t[1]),(t,n,r)=>(r==null?void 0:r.concurrent)!==!0&&((r==null?void 0:r.batching)===void 0||r.batching===!1)?mh(t,n):au(t,n,(s,l)=>s,r)),Z8=O(t=>ka(t[1]),(t,n,r)=>(r==null?void 0:r.concurrent)!==!0&&((r==null?void 0:r.batching)===void 0||r.batching===!1)?qe(t,n):au(t,n,(s,l)=>l,r)),au=O(t=>ka(t[1]),(t,n,r,s)=>qt(cw([t,n],{concurrency:s!=null&&s.concurrent?2:1,batching:s==null?void 0:s.batching,concurrentFinalizers:s==null?void 0:s.concurrentFinalizers}),([l,c])=>r(l,c))),Js=Qs("effect/Scope"),mw=Js,J8=(t,n)=>{t.state._tag==="Open"&&t.state.finalizers.set({},n)},W8={[iE]:iE,[oE]:oE,pipe(){return Tt(this,arguments)},fork(t){return ot(()=>{const n=yw(t);if(this.state._tag==="Closed")return n.state=this.state,n;const r={},s=l=>n.close(l);return this.state.finalizers.set(r,s),J8(n,l=>ot(()=>{this.state._tag==="Open"&&this.state.finalizers.delete(r)})),n})},close(t){return ae(()=>{if(this.state._tag==="Closed")return oe;const n=Array.from(this.state.finalizers.values()).reverse();return this.state={_tag:"Closed",exit:t},n.length===0?oe:WF(this.strategy)?D(tr(n,r=>Hr(r(t))),J(r=>D(to(r),uc(yp),Gn(()=>Cn)))):tj(this.strategy)?D(uw(n,r=>Hr(r(t)),!1),J(r=>D(to(r,{parallel:!0}),uc(yp),Gn(()=>Cn)))):D(Mg(n,this.strategy.parallelism,r=>Hr(r(t)),!1),J(r=>D(to(r,{parallel:!0}),uc(yp),Gn(()=>Cn))))})},addFinalizer(t){return ae(()=>this.state._tag==="Closed"?t(this.state.exit):(this.state.finalizers.set({},t),oe))}},yw=(t=Th)=>{const n=Object.create(W8);return n.strategy=t,n.state={_tag:"Open",finalizers:new Map},n},nv=(t=Th)=>ot(()=>yw(t)),ru=O(2,(t,n)=>XO(t,jo(Jg(Js,n)))),t7=t=>Po(t,{differ:w8,fork:mc}),HE=d6(a$),e7=t7(xh),pw=O(3,(t,n,r)=>a7(t,n,{onSelfWin:(s,l)=>J(s.await,c=>{switch(c._tag){case Le:return J(s.inheritAll,()=>r.onSelfDone(c,l));case ze:return r.onSelfDone(c,l)}}),onOtherWin:(s,l)=>J(s.await,c=>{switch(c._tag){case Le:return J(s.inheritAll,()=>r.onOtherDone(c,l));case ze:return r.onOtherDone(c,l)}})})),n7=O(2,(t,n)=>Zc(r=>pw(t,n,{onSelfDone:(s,l)=>og(s,{onFailure:c=>D(Dc(l),OE(f=>Is(c,f))),onSuccess:c=>D(l,wc(r),cn(c))}),onOtherDone:(s,l)=>og(s,{onFailure:c=>D(Dc(l),OE(f=>Is(f,c))),onSuccess:c=>D(l,wc(r),cn(c))})}))),a7=O(3,(t,n,r)=>Xt((s,l)=>{const c=l.runtimeFlags,f=eh(!0),h=Ad(t,s,c,r.selfScope),m=Ad(n,s,c,r.otherScope);return Ca(y=>{h.addObserver(()=>IE(h,m,r.onSelfWin,f,y)),m.addObserver(()=>IE(m,h,r.onOtherWin,f,y)),h.startFork(t),m.startFork(n)},Fz(h.id(),m.id()))})),IE=(t,n,r,s,l)=>{Cz(!0,!1)(s)&&l(r(t,n))},kc=O(2,(t,n)=>fa(r=>Xn(r(t),{onFailure:s=>Xn(n,{onFailure:l=>Oe(We(s,l)),onSuccess:()=>Oe(s)}),onSuccess:s=>cn(n,s)}))),r7=(t,n,r)=>Zc(s=>J(J(ev(E0(t)),l=>Ca(c=>{const f=n.map(y=>y.listeners.count),h=()=>{f.every(y=>y===0)&&n.every(y=>y.result.state.current._tag==="Pending"?!0:!!(y.result.state.current._tag==="Done"&&D0(y.result.state.current.effect)&&y.result.state.current.effect._tag==="Failure"&&vO(y.result.state.current.effect.cause)))&&(m.forEach(y=>y()),r==null||r(),c(w0(l)))};l.addObserver(y=>{m.forEach(p=>p()),c(y)});const m=n.map((y,p)=>{const g=_=>{f[p]=_,h()};return y.listeners.addObserver(g),()=>y.listeners.removeObserver(g)});return h(),ot(()=>{m.forEach(y=>y())})})),()=>ae(()=>{const l=n.flatMap(c=>c.state.completed?[]:[c]);return uh(l,c=>_8(c.request,k6(s)))}))),s7="effect/ScheduleInterval",xd=Symbol.for(s7),gw={[xd]:xd,startMillis:0,endMillis:0},vw=(t,n)=>t>n?gw:{[xd]:xd,startMillis:t,endMillis:n},i7=O(2,(t,n)=>o7(t,n)===t),o7=O(2,(t,n)=>t.endMillis<=n.startMillis?t:n.endMillis<=t.startMillis?n:t.startMillis<n.startMillis?t:n.startMillis<t.startMillis?n:t.endMillis<=n.endMillis?t:n),l7=t=>t.startMillis>=t.endMillis,c7=O(2,(t,n)=>{const r=Math.max(t.startMillis,n.startMillis),s=Math.min(t.endMillis,n.endMillis);return vw(r,s)}),u7=t=>vw(t,Number.POSITIVE_INFINITY),_w=gw,f7=i7,d7=l7,h7=c7,m7=u7,y7="effect/ScheduleIntervals",PE=Symbol.for(y7),bw=t=>({[PE]:PE,intervals:t}),p7=O(2,(t,n)=>g7(t.intervals,n.intervals,ua())),g7=(t,n,r)=>{let s=t,l=n,c=r;for(;ar(s)&&ar(l);){const f=D(Ra(s),h7(Ra(l))),h=d7(f)?c:D(c,Yn(f));D(Ra(s),f7(Ra(l)))?s=Wa(s):l=Wa(l),c=h}return bw($s(c))},wg=t=>D(t.intervals,Wg,Gn(()=>_w)).startMillis,v7=t=>D(t.intervals,Wg,Gn(()=>_w)).endMillis,_7=O(2,(t,n)=>wg(t)<wg(n)),b7=t=>ar(t.intervals),S7=bw,E7=p7,T7=wg,VE=v7,R7=_7,O7=b7,av="Continue",Sw="Done",M7=t=>({_tag:av,intervals:t}),w7=t=>({_tag:av,intervals:S7(ln(t))}),A7={_tag:Sw},x7=t=>t._tag===av,C7=t=>t._tag===Sw,D7=M7,k7=w7,Nc=A7,GE=x7,Cd=C7,N9=PO,z9=yd,N7=rg,L9=ru,z7=ph,$9=nv;class L7{constructor(n){E(this,"permits");E(this,"waiters",new Set);E(this,"taken",0);E(this,"take",n=>v0(r=>{if(this.free<n){const s=()=>{this.free<n||(this.waiters.delete(s),this.taken+=n,r(yt(n)))};return this.waiters.add(s),ot(()=>{this.waiters.delete(s)})}return this.taken+=n,r(yt(n))}));E(this,"updateTaken",n=>Xt(r=>(this.taken=n(this.taken),this.waiters.size>0&&r.getFiberRef(K0).scheduleTask(()=>{const s=this.waiters.values();let l=s.next();for(;l.done===!1&&this.free>0;)l.value(),l=s.next()},r.getFiberRef(Vo)),yt(this.free))));E(this,"release",n=>this.updateTaken(r=>r-n));E(this,"releaseAll",this.updateTaken(n=>0));E(this,"withPermits",n=>r=>fa(s=>J(s(this.take(n)),l=>kc(s(r),this.release(l)))));E(this,"withPermitsIfAvailable",n=>r=>fa(s=>ae(()=>this.free<n?UF:(this.taken+=n,kc(s(xF(r)),this.release(n))))));this.permits=n}get free(){return this.permits-this.taken}}const Ew=t=>new L7(t),$7=t=>ot(()=>Ew(t));class F7 extends Eh{constructor(r){super();E(this,"isOpen");E(this,"waiters",[]);E(this,"scheduled",!1);E(this,"flushWaiters",()=>{this.scheduled=!1;const r=this.waiters;this.waiters=[];for(let s=0;s<r.length;s++)r[s](Cn)});E(this,"open",Xt(r=>this.isOpen?oe:(this.isOpen=!0,this.unsafeSchedule(r))));E(this,"release",Xt(r=>this.isOpen?oe:this.unsafeSchedule(r)));E(this,"await",v0(r=>this.isOpen?r(oe):(this.waiters.push(r),ot(()=>{const s=this.waiters.indexOf(r);s!==-1&&this.waiters.splice(s,1)}))));E(this,"close",ot(()=>{this.isOpen=!1}));E(this,"whenOpen",r=>qe(this.await,r));this.isOpen=r}commit(){return this.await}unsafeSchedule(r){return this.scheduled||this.waiters.length===0||(this.scheduled=!0,r.currentScheduler.scheduleTask(this.flushWaiters,r.getFiberRef(Vo))),oe}unsafeOpen(){this.isOpen||(this.isOpen=!0,this.flushWaiters())}unsafeClose(){this.isOpen=!1}}const j7=t=>new F7(t??!1),U7=O(2,(t,n)=>Xt((r,s)=>{const l=n,c=fw(t,r,s.runtimeFlags,Mh);if(l.state._tag==="Open"){const f=()=>Zc(m=>Ot(m,c.id())?oe:sr(w0(c))),h={};l.state.finalizers.set(h,f),c.addObserver(()=>{l.state._tag!=="Closed"&&l.state.finalizers.delete(h)})}else c.unsafeInterruptAsFork(r.id());return yt(c)})),B7="effect/Ref/SynchronizedRef",q7=Symbol.for(B7),H7={_A:t=>t};var JT,WT,t2,e2;class I7 extends(e2=Eh,t2=q7,WT=fM,JT=gd,e2){constructor(r,s){super();E(this,"ref");E(this,"withLock");E(this,t2,H7);E(this,WT,dM);E(this,JT,gd);E(this,"get");this.ref=r,this.withLock=s,this.get=wa(this.ref)}commit(){return this.get}modify(r){return this.modifyEffect(s=>yt(r(s)))}modifyEffect(r){return this.withLock(D(J(wa(this.ref),r),J(([s,l])=>cn(hc(this.ref,l),s))))}}const P7=t=>ot(()=>Tw(t)),Tw=t=>{const n=j0(t),r=Ew(1);return new I7(n,r.withPermits(1))},V7=Symbol.for("effect/ManagedRuntime"),G7="Fresh",K7="FromEffect",Q7="Suspend",Y7="Provide",X7="ProvideMerge",Z7="ZipWith",F9=Jj,j9=Wj,U9=w0,J7=wc,B9=Dc,Qo=t=>function(){if(arguments.length===1){const n=arguments[0];return(r,...s)=>t(n,r,...s)}return t.apply(this,arguments)},Ch=Qo((t,n,r)=>{const s=XR(),l=[[ir,[[s,t.context]]]];r!=null&&r.scheduler&&l.push([K0,[[s,r.scheduler]]]);let c=sF(t.fiberRefs,{entries:l,forkAs:s});r!=null&&r.updateRefs&&(c=r.updateRefs(c,s));const f=new lw(s,c,t.runtimeFlags);let h=n;r!=null&&r.scope&&(h=J(z7(r.scope,Th),y=>qe(PO(y,Zc(p=>Ot(p,f.id())?oe:wc(f,p))),Ps(n,p=>N7(y,p)))));const m=f.currentSupervisor;return m!==xh&&(m.onStart(t.context,h,gt(),f),f.addObserver(y=>m.onEnd(y,f))),Mh.add(t.runtimeFlags,f),(r==null?void 0:r.immediate)===!1?f.resume(h):f.start(h),f}),W7=Qo((t,n,r={})=>{const s=Ch(t,n,r);return r.onExit&&s.addObserver(l=>{r.onExit(l)}),(l,c)=>W7(t)(D(s,J7(l??Bs)),{...c,onExit:c!=null&&c.onExit?f=>c.onExit(P6(f)):void 0})}),tB=Qo((t,n)=>{const r=Mw(t)(n);if(r._tag==="Failure")throw Rw(r.effect_instruction_i0);return r.effect_instruction_i0});class eB extends Error{constructor(r){super(`Fiber #${r.id().id} cannot be resolved synchronously. This is caused by using runSync on an effect that performs async work`);E(this,"fiber");E(this,"_tag","AsyncFiberException");this.fiber=r,this.name=this._tag,this.stack=this.message}}const nB=t=>{const n=Error.stackTraceLimit;Error.stackTraceLimit=0;const r=new eB(t);return Error.stackTraceLimit=n,r},Pf=Symbol.for("effect/Runtime/FiberFailure"),Nf=Symbol.for("effect/Runtime/FiberFailure/Cause");var n2,a2;class aB extends Error{constructor(r){const s=RO(r)[0];super((s==null?void 0:s.message)||"An error has occurred");E(this,a2);E(this,n2);this[Pf]=Pf,this[Nf]=r,this.name=s?`(FiberFailure) ${s.name}`:"FiberFailure",s!=null&&s.stack&&(this.stack=s.stack)}toJSON(){return{_id:"FiberFailure",cause:this[Nf].toJSON()}}toString(){return"(FiberFailure) "+Qc(this[Nf],{renderErrorCause:!0})}[(a2=Pf,n2=Nf,le)](){return this.toString()}}const Rw=t=>{const n=Error.stackTraceLimit;Error.stackTraceLimit=0;const r=new aB(t);return Error.stackTraceLimit=n,r},q9=t=>Mt(t,Pf),Ow=t=>{const n=t;switch(n._op){case"Failure":case"Success":return n;case"Left":return ig(n.left);case"Right":return ie(n.right);case"Some":return ie(n.value);case"None":return ig(C0())}},Mw=Qo((t,n)=>{const r=Ow(n);if(r)return r;const s=new qj,l=Ch(t)(n,{scheduler:s});s.flush();const c=l.unsafePoll();return c||Qi(b0(nB(l),L0(l)))}),rB=Qo((t,n,r)=>ww(t,n,r).then(s=>{switch(s._tag){case Le:return s.effect_instruction_i0;case ze:throw Rw(s.effect_instruction_i0)}})),ww=Qo((t,n,r)=>new Promise(s=>{const l=Ow(n);l&&s(l);const c=Ch(t)(n);c.addObserver(f=>{s(f)}),(r==null?void 0:r.signal)!==void 0&&(r.signal.aborted?c.unsafeInterruptAsFork(c.id()):r.signal.addEventListener("abort",()=>{c.unsafeInterruptAsFork(c.id())},{once:!0}))}));class Aw{constructor(n,r,s){E(this,"context");E(this,"runtimeFlags");E(this,"fiberRefs");this.context=n,this.runtimeFlags=r,this.fiberRefs=s}pipe(){return Tt(this,arguments)}}const sB=t=>new Aw(t.context,t.runtimeFlags,t.fiberRefs),xw=()=>Xt((t,n)=>yt(new Aw(t.getFiberRef(ir),n.runtimeFlags,t.getFiberRefs()))),iB=fO(Ys,cO,lO),Ks=sB({context:_c(),runtimeFlags:iB,fiberRefs:iF()}),oB=Ch(Ks),lB=rB(Ks),H9=ww(Ks),cB=tB(Ks),I9=Mw(Ks),uB=O(2,(t,n)=>t.modifyEffect(n)),fB="effect/Layer",Cw=Symbol.for(fB),dB={_RIn:t=>t,_E:t=>t,_ROut:t=>t},zc={[Cw]:dB,pipe(){return Tt(this,arguments)}},hB="effect/Layer/MemoMap",_p=Symbol.for(hB),mB=Pc()("effect/Layer/CurrentMemoMap",{defaultValue:()=>vB()}),yB=t=>Mt(t,Cw),pB=t=>t._op_layer===G7;var r2;r2=_p;class Dw{constructor(n){E(this,"ref");E(this,r2);this.ref=n,this[_p]=_p}getOrElseMemoize(n,r){return D(uB(this.ref,s=>{const l=s.get(n);if(l!==void 0){const[c,f]=l,h=D(c,J(([m,y])=>D(RM(m),cn(y))),Ps(vh({onFailure:()=>oe,onSuccess:()=>yd(r,f)})));return yt([h,s])}return D(vd(0),J(c=>D(QO(),J(f=>D(vd(()=>oe),qt(h=>{const m=fa(p=>D(nv(),J(g=>D(p(J(zw(n,g,!0),_=>DF(_(this)))),Hr,J(_=>{switch(_._tag){case ze:return D(j6(f,_.effect_instruction_i0),qe(rg(g,_)),qe(Oe(_.effect_instruction_i0)));case Le:return D(hc(h,S=>D(rg(g,S),FO(hM(c,T=>[T===1,T-1])),sr)),qe(_d(c,S=>S+1)),qe(yd(r,S=>D(ot(()=>s.delete(n)),qe(wa(h)),J(T=>T(S))))),qe(U6(f,_.effect_instruction_i0)),cn(_.effect_instruction_i0[1]))}}))))),y=[D(_h(f),Ps(og({onFailure:()=>oe,onSuccess:()=>_d(c,p=>p+1)}))),p=>D(wa(h),J(g=>g(p)))];return[m,pB(n)?s:s.set(n,y)]}))))))}),ch)}}const gB=ae(()=>qt(P7(new Map),t=>new Dw(t))),vB=()=>new Dw(Tw(new Map)),kw=O(2,(t,n)=>J(gB,r=>Nw(t,r,n))),Nw=O(3,(t,n,r)=>J(zw(t,r),s=>OM(s(n),mB,n))),zw=(t,n,r=!1)=>{const s=t;switch(s._op_layer){case"Locally":return ot(()=>l=>s.f(l.getOrElseMemoize(s.self,n)));case"ExtendScope":return ot(()=>l=>dw(c=>l.getOrElseMemoize(s.layer,c)));case"Fold":return ot(()=>l=>D(l.getOrElseMemoize(s.layer,n),Xn({onFailure:c=>l.getOrElseMemoize(s.failureK(c),n),onSuccess:c=>l.getOrElseMemoize(s.successK(c),n)})));case"Fresh":return ot(()=>l=>D(s.layer,kw(n)));case"FromEffect":return ot(r?()=>l=>s.effect:()=>l=>l.getOrElseMemoize(t,n));case"Provide":return ot(()=>l=>D(l.getOrElseMemoize(s.first,n),J(c=>D(l.getOrElseMemoize(s.second,n),Wc(c)))));case"Scoped":return ot(r?()=>l=>ru(s.effect,n):()=>l=>l.getOrElseMemoize(t,n));case"Suspend":return ot(()=>l=>l.getOrElseMemoize(s.evaluate(),n));case"ProvideMerge":return ot(()=>l=>D(l.getOrElseMemoize(s.first,n),jO(l.getOrElseMemoize(s.second,n),s.zipK)));case"ZipWith":return ot(()=>l=>D(l.getOrElseMemoize(s.first,n),au(l.getOrElseMemoize(s.second,n),s.zipK,{concurrent:!0})))}},_B=()=>rv(N0()),P9=O(2,(t,n)=>{const r=RR(t),s=r?t:n;return rv(qt(r?n:t,c=>Jg(s,c)))});function rv(t){const n=Object.create(zc);return n._op_layer=K7,n.effect=t,n}const bB=O(2,(t,n)=>SB(t,n,(r,s)=>jo(r,s))),Lw=(...t)=>{let n=t[0];for(let r=1;r<t.length;r++)n=bB(n,t[r]);return n},V9=O(2,(t,n)=>{const r=RR(t);return rv(yt(Jg(r?t:n,r?n:t)))}),$w=t=>{const n=Object.create(zc);return n._op_layer=Q7,n.evaluate=t,n},G9=O(2,(t,n)=>J(dw(r=>Nw(t,n,r)),r=>D(xw(),Wc(r)))),K9=O(2,(t,n)=>$w(()=>{const r=Object.create(zc);return r._op_layer=Y7,r.first=Object.create(zc,{_op_layer:{value:X7,enumerable:!0},first:{value:_B(),enumerable:!0},second:{value:Array.isArray(n)?Lw(...n):n},zipK:{value:(s,l)=>D(s,jo(l))}}),r.second=t,r})),SB=O(3,(t,n,r)=>$w(()=>{const s=Object.create(zc);return s._op_layer=Z7,s.first=t,s.second=n,s.zipK=r,s})),KE=O(2,(t,n)=>hw(r=>J(kw(n,r),s=>z0(t,s)))),QE=O(2,(t,n)=>{const r=xc(Ks.fiberRefs,n.fiberRefs),s=qr(Ks.runtimeFlags,n.runtimeFlags);return fa(l=>Xt(c=>{const f=c.getFiberRef(ir),h=c.getFiberRefs(),m=Cc(c.id(),h)(r),y=c.currentRuntimeFlags,p=Wi(s)(y),g=xc(m,h),_=qr(p,y);return c.setFiberRefs(m),c.currentRuntimeFlags=p,kc(z0(l(t),jo(f,n.context)),Xt(S=>(S.setFiberRefs(Cc(S.id(),S.getFiberRefs())(g)),S.currentRuntimeFlags=Wi(_)(S.currentRuntimeFlags),oe)))}))}),EB=O(2,(t,n)=>Array.isArray(n)?KE(t,Lw(...n)):yB(n)?KE(t,n):v4(n)?z0(t,n):V7 in n?J(n.runtimeEffect,r=>QE(t,r)):QE(t,n)),TB="effect/Schedule",Fw=Symbol.for(TB),RB=t=>Mt(t,Fw),OB="effect/ScheduleDriver",MB=Symbol.for(OB),Ag={start:0,now:0,input:void 0,elapsed:go,elapsedSincePrevious:go,recurrence:0},YE=Pc()("effect/Schedule/CurrentIterationMetadata",{defaultValue:()=>Ag}),wB={_Out:t=>t,_In:t=>t,_R:t=>t},AB={_Out:t=>t,_In:t=>t,_R:t=>t};var s2;s2=Fw;class xB{constructor(n,r){E(this,"initial");E(this,"step");E(this,s2,wB);this.initial=n,this.step=r}pipe(){return Tt(this,arguments)}}const XE=(t,n,r)=>_d(t,s=>s.recurrence===0?{now:n,input:r,recurrence:s.recurrence+1,elapsed:go,elapsedSincePrevious:go,start:n}:{now:n,input:r,recurrence:s.recurrence+1,elapsed:vo(n-s.start),elapsedSincePrevious:vo(n-s.now),start:s.start});var i2;i2=MB;class CB{constructor(n,r){E(this,"schedule");E(this,"ref");E(this,i2,AB);E(this,"iterationMeta",j0(Ag));this.schedule=n,this.ref=r}get state(){return qt(wa(this.ref),n=>n[1])}get last(){return J(wa(this.ref),([n,r])=>{switch(n._tag){case"None":return lh(()=>new C0);case"Some":return yt(n.value)}})}get reset(){return hc(this.ref,[gt(),this.schedule.initial]).pipe(mh(hc(this.iterationMeta,Ag)))}next(n){return D(qt(wa(this.ref),r=>r[1]),J(r=>D(X5,J(s=>D(ae(()=>this.schedule.step(s,n,r)),J(([l,c,f])=>{const h=hc(this.ref,[Rt(c),l]);if(Cd(f))return h.pipe(qe(ve(gt())));const m=T7(f.intervals)-s;if(m<=0)return h.pipe(qe(XE(this.iterationMeta,s,n)),cn(c));const y=vo(m);return D(h,qe(XE(this.iterationMeta,s,n)),qe(jF(y)),cn(c))}))))))}}const su=(t,n)=>new xB(t,n),DB=O(2,(t,n)=>sv(t,(r,s)=>ot(()=>n(r,s)))),sv=O(2,(t,n)=>su(t.initial,(r,s,l)=>J(t.step(r,s,l),([c,f,h])=>Cd(h)?yt([c,f,Nc]):qt(n(s,f),m=>m?[c,f,h]:[c,f,Nc])))),kB=t=>D(vd([gt(),t.initial]),qt(n=>new CB(t,n))),NB=O(2,(t,n)=>zB(t,n,E7)),zB=O(3,(t,n,r)=>su([t.initial,n.initial],(s,l,c)=>D(jO(t.step(s,l,c[0]),n.step(s,l,c[1]),(f,h)=>[f,h]),J(([[f,h,m],[y,p,g]])=>GE(m)&&GE(g)?xg(t,n,l,f,h,m.intervals,y,p,g.intervals,r):yt([[f,y],[h,p],Nc]))))),xg=(t,n,r,s,l,c,f,h,m,y)=>{const p=y(c,m);return O7(p)?yt([[s,f],[l,h],D7(p)]):D(c,R7(m))?J(t.step(VE(c),r,s),([g,_,S])=>Cd(S)?yt([[g,f],[_,h],Nc]):xg(t,n,r,g,_,S.intervals,f,h,m,y)):J(n.step(VE(m),r,f),([g,_,S])=>Cd(S)?yt([[s,g],[l,_],Nc]):xg(t,n,r,s,l,c,g,_,S.intervals,y))},LB=O(2,(t,n)=>$B(t,r=>ot(()=>n(r)))),$B=O(2,(t,n)=>su(t.initial,(r,s,l)=>J(t.step(r,s,l),([c,f,h])=>qt(n(f),m=>[c,m,h])))),FB=t=>su(t.initial,(n,r,s)=>D(t.step(n,r,s),qt(([l,c,f])=>[l,r,f]))),jB=t=>HB(Uw,n=>n<t),UB=(t,n)=>su(t,(r,s,l)=>ot(()=>[n(l),l,k7(m7(r))])),BB=O(2,(t,n)=>sv(t,(r,s)=>LF(n(r)))),qB=O(2,(t,n)=>sv(t,(r,s)=>n(r))),HB=O(2,(t,n)=>DB(t,(r,s)=>n(s))),Vf=Symbol.for("effect/Schedule/ScheduleDefect");var o2;o2=Vf;class IB{constructor(n){E(this,"error");E(this,o2);this.error=n,this[Vf]=Vf}}const PB=t=>Mt(t,Vf),ZE=t=>hd(t,n=>md(new IB(n))),VB=t=>rr(y0(t,n=>R$(n)&&PB(n.defect)?Rt(n.defect):gt()),{onNone:()=>t,onSome:n=>Mo(n.error)}),GB=t=>_0(t,n=>Oe(VB(n))),JE=O(2,(t,n)=>QB(t,n,(r,s)=>ve(r))),KB=O(2,(t,n)=>{if(RB(n))return JE(t,n);const r=n.schedule??FB(Uw),s=n.while?qB(r,f=>{const h=n.while(f);return typeof h=="boolean"?yt(h):ZE(h)}):r,l=n.until?BB(s,f=>{const h=n.until(f);return typeof h=="boolean"?yt(h):ZE(h)}):s,c=n.times?NB(l,jB(n.times)).pipe(LB(f=>f[0])):l;return GB(JE(t,c))}),QB=O(3,(t,n,r)=>J(kB(n),s=>Gr(t,{onFailure:l=>r(l,gt()),onSuccess:l=>jw(ME(t,YE,wa(s.iterationMeta)),s,(c,f)=>ME(r(c,f),YE,wa(s.iterationMeta)),l)}))),jw=(t,n,r,s)=>Gr(n.next(s),{onFailure:()=>G$(n.last),onSuccess:l=>Gr(t,{onFailure:c=>r(c,Rt(l)),onSuccess:c=>jw(t,n,r,c)})}),Uw=UB(0,t=>t+1),Q9=ka,Y9=zF,X9=cw,Z9=No,J9=Xt,W9=ve,tH=Oe,eH=eg,nH=Z$,aH=FF,rH=yt,sH=ae,iH=ot,oH=oe,lH=hd,cH=_0,uH=CF,fH=NF,dH=SM,hH=HF,mH=qF,yH=V$,pH=E0,gH=T0,vH=dh,_H=fa,bH=cn,SH=sr,EH=qt,TH=fh,RH=H8,OH=xO,MH=kc,wH=mw,AH=hw,xH=Zc,CH=ev,DH=U7,kH=N0,NH=XO,zH=EB,LH=VF,$H=Co,FH=Hr,jH=zO,UH=PF,BH=J,qH=Ki,HH=ch,IH=n7,PH=pw,VH=R0,GH=BF,KH=KB,QH=EM,YH=kO,XH=Xn,ZH=xw,JH=$7,WH=j7,tI=oB,eI=lB,nI=cB,aI=Y8,rI=X8,sI=Z8,iI=au,oI=QF,lI=YF,YB=t=>{const n=new Map;return new Proxy(t,{get(r,s,l){if(s in r)return Reflect.get(r,s,l);if(n.has(s))return n.get(s);const c=(...h)=>Ki(r,m=>typeof m[s]=="function"?(n.set(s,(...y)=>Ki(r,p=>p[s](...y))),m[s](...h)):(n.set(s,Ki(r,y=>y[s])),m[s])),f=Ki(r,h=>h[s]);return Object.assign(c,f),Object.setPrototypeOf(c,Object.getPrototypeOf(f)),n.set(s,c),c}})},Dh=t=>()=>{const n=Error.stackTraceLimit;Error.stackTraceLimit=2;const r=new Error;Error.stackTraceLimit=n;function s(){}return Object.setPrototypeOf(s,Qd),s.key=t,Object.defineProperty(s,"use",{get(){return l=>Ki(this,l)}}),Object.defineProperty(s,"stack",{get(){return r.stack}}),YB(s)};class bp extends Dh("AuthUsecase")(){}class ec extends Dh("ClientUsecase")(){}class nc extends Dh("WorkerUsecase")(){}class ac extends Dh("ScheduleUsecase")(){}const XB={login:t=>bp.login(t),logout:()=>bp.logout(),getSession:()=>bp.getSession()},ZB={create:t=>ec.create(t),getAll:()=>ec.getAll(),getById:t=>ec.getById(t),update:t=>ec.update(t),delete:t=>ec.delete(t)},JB={create:t=>nc.create(t),getAll:()=>nc.getAll(),getById:t=>nc.getById(t),update:t=>nc.update(t),delete:t=>nc.delete(t)},WB={create:t=>ac.create(t),getAll:()=>ac.getAll(),getById:t=>ac.getById(t),update:t=>ac.update(t),delete:t=>ac.delete(t)},tq={auth:XB,client:ZB,worker:JB,schedule:WB},eq="/assets/app-D3qsgFYA.css",iu=K3()({head:()=>({meta:[{charSet:"utf-8"},{name:"viewport",content:"width=device-width, initial-scale=1"},{title:"TanStack Start Starter"}],links:[{rel:"stylesheet",href:eq}]}),loader:()=>zk(),component:nq});function nq(){const t=iu.useLoaderData();return tt.jsx(Fk,{theme:t,children:tt.jsx(aq,{children:tt.jsx(Ck,{service:tq,children:tt.jsx(Fg,{})})})})}function aq({children:t}){const{theme:n}=jk();return tt.jsxs("html",{lang:"es","data-theme":n,suppressHydrationWarning:!0,children:[tt.jsx("head",{children:tt.jsx(hD,{})}),tt.jsxs("body",{children:[t,tt.jsx(xk,{theme:"dark"}),tt.jsx(KD,{}),tt.jsx(QD,{}),tt.jsx(yD,{})]})]})}const rq=()=>rn(()=>import("./login-Ckz1Puxy.js"),__vite__mapDeps([0,1,2,3,4,5,6,7])),Bw=Dn("/login")({component:da(rq,"component",()=>Bw.ssr)}),sq=()=>rn(()=>import("./route-DP-pnEp9.js"),__vite__mapDeps([8,9,10,5,4])),qw=Dn("/_authed")({component:da(sq,"component",()=>qw.ssr)}),iq=()=>rn(()=>import("./index-CFxSTSbc.js"),[]),Hw=Dn("/")({component:da(iq,"component",()=>Hw.ssr)}),oq=()=>rn(()=>import("./route-DvpekX38.js"),__vite__mapDeps([11,3,12,13,6,5,7])),Iw=Dn("/_authed/admin")({component:da(oq,"component",()=>Iw.ssr)}),lq=()=>rn(()=>import("./index-BbWl0DHf.js"),__vite__mapDeps([14,12,3,13])),Pw=Dn("/_authed/admin/")({component:da(lq,"component",()=>Pw.ssr)}),cq=()=>rn(()=>import("./index-Q8CEboXZ.js"),__vite__mapDeps([15,16,2,5,1,3,4,6,10,7,17,13,18,9])),Vw=Dn("/_authed/admin/workers/")({component:da(cq,"component",()=>Vw.ssr)}),uq=()=>rn(()=>import("./index-Eltu2spq.js"),[]),Gw=Dn("/_authed/admin/sessions/")({component:da(uq,"component",()=>Gw.ssr)}),fq=()=>rn(()=>import("./index-DmGivCOT.js"),__vite__mapDeps([19,9,4,5,20,3,10,16,2,6,21])),Kw=Dn("/_authed/admin/schedules/")({component:da(fq,"component",()=>Kw.ssr)}),dq=()=>rn(()=>import("./index-BZf7DOmf.js"),__vite__mapDeps([22,9,4,5,10,16,2,6,17,3,1,7,13,18,21])),Qw=Dn("/_authed/admin/clients/")({component:da(dq,"component",()=>Qw.ssr)}),hq=()=>rn(()=>import("./create-Cw-MR3Bi.js"),__vite__mapDeps([23,1,2,3,4,5,6,20,10,24,18])),Yw=Dn("/_authed/admin/schedules/create")({component:da(hq,"component",()=>Yw.ssr)}),mq=()=>rn(()=>import("./_id-2ptn8Wpq.js"),__vite__mapDeps([25,9,4,5,20,3,10,1,2,6,24,18])),Xw=Dn("/_authed/admin/schedules/edit/$id")({component:da(mq,"component",()=>Xw.ssr)}),yq=Bw.update({id:"/login",path:"/login",getParentRoute:()=>iu}),Zw=qw.update({id:"/_authed",getParentRoute:()=>iu}),pq=Hw.update({id:"/",path:"/",getParentRoute:()=>iu}),Qr=Iw.update({id:"/admin",path:"/admin",getParentRoute:()=>Zw}),gq=Pw.update({id:"/",path:"/",getParentRoute:()=>Qr}),vq=Vw.update({id:"/workers/",path:"/workers/",getParentRoute:()=>Qr}),_q=Gw.update({id:"/sessions/",path:"/sessions/",getParentRoute:()=>Qr}),bq=Kw.update({id:"/schedules/",path:"/schedules/",getParentRoute:()=>Qr}),Sq=Qw.update({id:"/clients/",path:"/clients/",getParentRoute:()=>Qr}),Eq=Yw.update({id:"/schedules/create",path:"/schedules/create",getParentRoute:()=>Qr}),Tq=Xw.update({id:"/schedules/edit/$id",path:"/schedules/edit/$id",getParentRoute:()=>Qr}),Rq={AuthedAdminIndexRoute:gq,AuthedAdminSchedulesCreateRoute:Eq,AuthedAdminClientsIndexRoute:Sq,AuthedAdminSchedulesIndexRoute:bq,AuthedAdminSessionsIndexRoute:_q,AuthedAdminWorkersIndexRoute:vq,AuthedAdminSchedulesEditIdRoute:Tq},Oq=Qr._addFileChildren(Rq),Mq={AuthedAdminRouteRoute:Oq},wq=Zw._addFileChildren(Mq),Aq={IndexRoute:pq,AuthedRouteRoute:wq,LoginRoute:yq},xq=iu._addFileChildren(Aq)._addFileTypes();function Cq(t,n,r){const s=new Set,l=new Set,c=n.getDefaultOptions();n.setDefaultOptions({...c,queries:{...c.queries,_experimental_beforeQuery:h=>{var m,y;(y=(m=c.queries)==null?void 0:m._experimental_beforeQuery)==null||y.call(m,h);const p=h.queryKeyHashFn||ks;if(t.isServer){if(s.has(p(h.queryKey)))return;if(s.add(p(h.queryKey)),n.getQueryData(h.queryKey)!==void 0){h.__skipInjection=!0;return}}else{const g=t.clientSsr.getStreamedValue("__QueryClient__"+p(h.queryKey));g&&!g.hydrated&&(g.hydrated=!0,N1(n,g))}},_experimental_afterQuery:(h,m)=>{var y,p;const g=h.queryKeyHashFn||ks;t.isServer&&!h.__skipInjection&&n.getQueryData(h.queryKey)!==void 0&&!l.has(g(h.queryKey))&&(l.add(g(h.queryKey)),t.serverSsr.streamValue("__QueryClient__"+g(h.queryKey),k1(n,{shouldDehydrateMutation:()=>!1,shouldDehydrateQuery:_=>g(_.queryKey)===g(h.queryKey)}))),(p=(y=c.queries)==null?void 0:y._experimental_afterQuery)==null||p.call(y,h,m)}}});{const h=n.getMutationCache().config;n.getMutationCache().config={...h,onError:(y,p,g,_)=>{var S;return gn(y)?(y.options._fromLocation=t.state.location,t.navigate(t.resolveRedirect(y).options)):(S=h.onError)==null?void 0:S.call(h,y,p,g,_)}};const m=n.getQueryCache().config;n.getQueryCache().config={...m,onError:(y,p)=>{var g;return gn(y)?(y.options._fromLocation=t.state.location,t.navigate(t.resolveRedirect(y).options)):(g=m.onError)==null?void 0:g.call(m,y,p)}}}const f=t.options;return t.options={...t.options,dehydrate:()=>{var h;return{...(h=f.dehydrate)==null?void 0:h.call(f),dehydratedQueryClient:k1(n)}},hydrate:h=>{var m;(m=f.hydrate)==null||m.call(f,h),N1(n,h.dehydratedQueryClient)},context:{...f.context,queryClient:n},Wrap:({children:h})=>{const m=et.Fragment,y=f.Wrap||et.Fragment;return tt.jsx(m,{children:tt.jsx(GD,{client:n,children:tt.jsx(y,{children:h})})})}},t}function Jw(){const t=new BD({defaultOptions:{queries:{retry:!1}}});return Cq(lD({routeTree:xq,defaultPendingComponent:()=>tt.jsx("div",{children:"Cargando..."}),context:{queryClient:t},defaultPreload:"intent",defaultPreloadStaleTime:0,scrollRestoration:!0}),t)}Jw();const Dq=Jw();et.startTransition(()=>{MC.hydrateRoot(document,tt.jsx(et.StrictMode,{children:tt.jsx(pD,{router:Dq})}))});export{XN as $,FD as A,O as B,Pq as C,Xi as D,Mt as E,Hd as F,H2 as G,$p as H,Uq as I,Bq as J,Ik as K,v2 as L,V2 as M,Nq as N,Fg as O,Aa as P,Gn as Q,Xw as R,Sp as S,Wq as T,Rt as U,gt as V,rr as W,hR as X,t9 as Y,Ut as Z,Qg as _,zd as a,xR as a$,uc as a0,id as a1,a9 as a2,n9 as a3,ON as a4,vc as a5,t4 as a6,QO as a7,y9 as a8,U6 as a9,V9 as aA,Lw as aB,D as aC,J as aD,ot as aE,qt as aF,eh as aG,Eh as aH,ae as aI,hd as aJ,V$ as aK,Vr as aL,dh as aM,Xt as aN,nh as aO,sr as aP,FO as aQ,qe as aR,Ds as aS,m9 as aT,yt as aU,de as aV,Ls as aW,GN as aX,A4 as aY,KO as aZ,T0 as a_,_h as aa,j6 as ab,Tt as ac,le as ad,_e as ae,Re as af,Yn as ag,ua as ah,$s as ai,f9 as aj,Qc as ak,vO as al,Mo as am,h9 as an,EO as ao,ca as ap,R$ as aq,bO as ar,Ta as as,d9 as at,VO as au,Ke as av,b6 as aw,ir as ax,P9 as ay,K9 as az,Fq as b,YH as b$,YO as b0,i9 as b1,oa as b2,w4 as b3,oe as b4,S0 as b5,p9 as b6,SN as b7,Q9 as b8,Yt as b9,b9 as bA,zH as bB,XH as bC,R9 as bD,CH as bE,pH as bF,MH as bG,vH as bH,EH as bI,aI as bJ,O9 as bK,E9 as bL,Z9 as bM,rH as bN,_9 as bO,M9 as bP,eH as bQ,cH as bR,T9 as bS,TF as bT,HH as bU,D9 as bV,xH as bW,yH as bX,Ir as bY,ia as bZ,S9 as b_,oH as ba,fH as bb,iI as bc,FH as bd,A9 as be,_H as bf,X9 as bg,z7 as bh,dg as bi,BH as bj,OH as bk,DH as bl,sI as bm,F9 as bn,j9 as bo,J7 as bp,U9 as bq,VR as br,u9 as bs,iH as bt,sH as bu,jH as bv,rI as bw,GH as bx,N9 as by,tH as bz,F3 as c,QN as c$,AH as c0,$9 as c1,L9 as c2,N7 as c3,Ot as c4,xs as c5,Lp as c6,wH as c7,w9 as c8,nH as c9,W9 as cA,l9 as cB,o9 as cC,lH as cD,Ct as cE,Dt as cF,he as cG,jt as cH,mt as cI,TN as cJ,Jq as cK,Yq as cL,Zq as cM,ys as cN,I2 as cO,Vi as cP,Vq as cQ,Pi as cR,$H as cS,r9 as cT,_n as cU,tI as cV,qj as cW,g9 as cX,FN as cY,Xq as cZ,e9 as c_,JH as ca,PH as cb,UH as cc,IH as cd,bH as ce,C9 as cf,KH as cg,k9 as ch,SH as ci,J9 as cj,ZH as ck,VH as cl,z9 as cm,B9 as cn,q9 as co,Nf as cp,Ch as cq,TH as cr,QH as cs,ln as ct,WH as cu,nI as cv,eI as cw,RH as cx,aH as cy,mH as cz,Ld as d,Iq as d0,Id as d1,Hq as d2,qq as d3,vB as d4,cB as d5,nv as d6,oB as d7,R0 as d8,G9 as d9,dH as dA,Qs as dB,kH as dC,NH as dD,jo as dE,LH as dF,As as dG,Pc as dH,oI as dI,Wf as dJ,gH as dK,lI as dL,v9 as dM,s9 as dN,uH as dO,qH as dP,Dh as dQ,bp as dR,ec as dS,ac as dT,nc as dU,ch as da,V7 as db,x9 as dc,lB as dd,rB as de,W7 as df,Ks as dg,H9 as dh,ww as di,tB as dj,I9 as dk,Mw as dl,md as dm,Cn as dn,Wc as dp,Gq as dq,L4 as dr,Pg as ds,rF as dt,mR as du,JN as dv,rN as dw,Kq as dx,hH as dy,Y9 as dz,bD as e,xp as f,vD as g,R2 as h,$d as i,tt as j,ND as k,ED as l,nn as m,xn as n,$q as o,wD as p,Gt as q,et as r,Lq as s,_D as t,jq as u,l1 as v,gC as w,Os as x,re as y,ks as z};
