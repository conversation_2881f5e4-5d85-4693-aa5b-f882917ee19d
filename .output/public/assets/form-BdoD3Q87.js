import{v as ee,S as On,D as tt,r as p,j as y,c as lt,w as An,q as $e}from"./main-RY8ZkTMc.js";import{c as jn,a as ye}from"./classes-CraQI9Rs.js";import{c as ce}from"./createLucideIcon-78bvTjT9.js";/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rn=[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]],nn=ce("arrow-down",Rn);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pn=[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]],rn=ce("arrow-up",Pn);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _n=[["path",{d:"M21 21H8a2 2 0 0 1-1.42-.587l-3.994-3.999a2 2 0 0 1 0-2.828l10-10a2 2 0 0 1 2.829 0l5.999 6a2 2 0 0 1 0 2.828L12.834 21",key:"g5wo59"}],["path",{d:"m5.082 11.09 8.828 8.828",key:"1wx5vj"}]],sn=ce("eraser",_n);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kn=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Bn=ce("eye",Kn);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $n=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],Nn=ce("eye-off",$n);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ln=[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]],Hn=ce("key-round",Ln);/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wn=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],qn=ce("x",Wn);function ze(e,n){return typeof e=="function"?e(n):e}function xe(e,n){return ut(n).reduce((t,r)=>{if(t===null)return null;if(typeof t<"u")return t[r]},e)}function De(e,n,i){const t=ut(n);function r(s){if(!t.length)return ze(i,s);const a=t.shift();if(typeof a=="string"||typeof a=="number"&&!Array.isArray(s))return typeof s=="object"?(s===null&&(s={}),{...s,[a]:r(s[a])}):{[a]:r()};if(Array.isArray(s)&&typeof a=="number"){const o=s.slice(0,a);return[...o.length?o:new Array(a),r(s[a]),...s.slice(a+1)]}return[...new Array(a),r()]}return r(e)}function zn(e,n){const i=ut(n);function t(r){if(!r)return;if(i.length===1){const a=i[0];if(Array.isArray(r)&&typeof a=="number")return r.filter((c,d)=>d!==a);const{[a]:o,...l}=r;return l}const s=i.shift();if(typeof s=="string"&&typeof r=="object")return{...r,[s]:t(r[s])};if(typeof s=="number"&&Array.isArray(r)){if(s>=r.length)return r;const a=r.slice(0,s);return[...a.length?a:new Array(s),t(r[s]),...r.slice(s+1)]}throw new Error("It seems we have created an infinite loop in deleteBy. ")}return t(e)}const Un=/^(\d+)$/gm,Gn=/\.(\d+)(?=\.)/gm,Xn=/^(\d+)\./gm,Yn=/\.(\d+$)/gm,Jn=/\.{2,}/gm,nt="__int__",Te=`${nt}$1`;function ut(e){if(Array.isArray(e))return[...e];if(typeof e!="string")throw new Error("Path must be a string.");return e.replace(/(^\[)|]/gm,"").replace(/\[/g,".").replace(Un,Te).replace(Gn,`.${Te}.`).replace(Xn,`${Te}.`).replace(Yn,`.${Te}`).replace(Jn,".").split(".").map(n=>n.indexOf(nt)===0?parseInt(n.substring(nt.length),10):n)}function Qn(e){return!(Array.isArray(e)&&e.length===0)}function rt(e,n){const{asyncDebounceMs:i}=n,{onChangeAsync:t,onBlurAsync:r,onSubmitAsync:s,onBlurAsyncDebounceMs:a,onChangeAsyncDebounceMs:o}=n.validators||{},l=i??0,c={cause:"change",validate:t,debounceMs:o??l},d={cause:"blur",validate:r,debounceMs:a??l},f={cause:"submit",validate:s,debounceMs:0},u=v=>({...v,debounceMs:0});switch(e){case"submit":return[u(c),u(d),f];case"blur":return[d];case"change":return[c];case"server":default:return[]}}function it(e,n){const{onChange:i,onBlur:t,onSubmit:r,onMount:s}=n.validators||{},a={cause:"change",validate:i},o={cause:"blur",validate:t},l={cause:"submit",validate:r},c={cause:"mount",validate:s},d={cause:"server",validate:()=>{}};switch(e){case"mount":return[c];case"submit":return[a,o,l,d];case"server":return[d];case"blur":return[o,d];case"change":default:return[a,d]}}const st=e=>!!e&&typeof e=="object"&&"fields"in e;function me(e,n){if(Object.is(e,n))return!0;if(typeof e!="object"||e===null||typeof n!="object"||n===null)return!1;if(e instanceof Map&&n instanceof Map){if(e.size!==n.size)return!1;for(const[r,s]of e)if(!n.has(r)||!Object.is(s,n.get(r)))return!1;return!0}if(e instanceof Set&&n instanceof Set){if(e.size!==n.size)return!1;for(const r of e)if(!n.has(r))return!1;return!0}const i=Object.keys(e),t=Object.keys(n);if(i.length!==t.length)return!1;for(const r of i)if(!t.includes(r)||!me(e[r],n[r]))return!1;return!0}const Kt=({newFormValidatorError:e,isPreviousErrorFromFormValidator:n,previousErrorValue:i})=>e?{newErrorValue:e,newSource:"form"}:n?{newErrorValue:void 0,newSource:void 0}:i?{newErrorValue:i,newSource:"field"}:{newErrorValue:void 0,newSource:void 0},Bt=({formLevelError:e,fieldLevelError:n})=>n?{newErrorValue:n,newSource:"field"}:e?{newErrorValue:e,newSource:"form"}:{newErrorValue:void 0,newSource:void 0};function Zn(e){const n=new Map;for(const i of e){const t=[...i.path??[]].map(r=>{const s=typeof r=="object"?r.key:r;return typeof s=="number"?`[${s}]`:s}).join(".").replace(/\.\[/g,"[");n.set(t,(n.get(t)??[]).concat(i))}return Object.fromEntries(n)}const $t=e=>{const n=Zn(e);return{form:n,fields:n}},be={validate({value:e,validationSource:n},i){const t=i["~standard"].validate(e);if(t instanceof Promise)throw new Error("async function passed to sync validator");if(t.issues)return n==="field"?t.issues:$t(t.issues)},async validateAsync({value:e,validationSource:n},i){const t=await i["~standard"].validate(e);if(t.issues)return n==="field"?t.issues:$t(t.issues)}},an=e=>!!e&&"~standard"in e,Ne={isValidating:!1,isTouched:!1,isBlurred:!1,isDirty:!1,isPristine:!0,isValid:!0,isDefaultValue:!0,errors:[],errorMap:{},errorSourceMap:{}};function Oe(e){function n(f,u,v,m){const g=t(f,u,v,m);({insert:()=>o(g,f,u),remove:()=>l(g),swap:()=>m!==void 0&&d(g,f,u,m),move:()=>m!==void 0&&c(g,f,u,m)})[v]()}function i(f,u){return`${f}[${u}]`}function t(f,u,v,m){const g=[i(f,u)];if(v==="swap")g.push(i(f,m));else if(v==="move"){const[h,M]=[Math.min(u,m),Math.max(u,m)];for(let b=h;b<=M;b++)g.push(i(f,b))}else{const h=e.getFieldValue(f),M=Array.isArray(h)?h.length:0;for(let b=u+1;b<M;b++)g.push(i(f,b))}return Object.keys(e.fieldInfo).filter(h=>g.some(M=>h.startsWith(M)))}function r(f,u){return f.replace(/\[(\d+)\]/,(v,m)=>{const g=parseInt(m,10);return`[${u==="up"?g+1:Math.max(0,g-1)}]`})}function s(f,u){(u==="up"?f:[...f].reverse()).forEach(m=>{const g=r(m.toString(),u),h=e.getFieldMeta(g);h?e.setFieldMeta(m,h):e.setFieldMeta(m,a())})}const a=()=>Ne,o=(f,u,v)=>{s(f,"down"),f.forEach(m=>{m.toString().startsWith(i(u,v))&&e.setFieldMeta(m,a())})},l=f=>{s(f,"up")},c=(f,u,v,m)=>{const g=new Map(Object.keys(e.fieldInfo).filter(h=>h.startsWith(i(u,v))).map(h=>[h,e.getFieldMeta(h)]));s(f,v<m?"up":"down"),Object.keys(e.fieldInfo).filter(h=>h.startsWith(i(u,m))).forEach(h=>{const M=h.replace(i(u,m),i(u,v)),b=g.get(M);b&&e.setFieldMeta(h,b)})},d=(f,u,v,m)=>{f.forEach(g=>{if(!g.toString().startsWith(i(u,v)))return;const h=g.toString().replace(i(u,v),i(u,m)),[M,b]=[e.getFieldMeta(g),e.getFieldMeta(h)];M&&e.setFieldMeta(h,M),b&&e.setFieldMeta(g,b)})};return{handleArrayFieldMetaShift:n}}function Ye(e){return{values:e.values??{},errorMap:e.errorMap??{},fieldMetaBase:e.fieldMetaBase??{},isSubmitted:e.isSubmitted??!1,isSubmitting:e.isSubmitting??!1,isValidating:e.isValidating??!1,submissionAttempts:e.submissionAttempts??0,isSubmitSuccessful:e.isSubmitSuccessful??!1,validationMetaMap:e.validationMetaMap??{onChange:void 0,onBlur:void 0,onSubmit:void 0,onMount:void 0,onServer:void 0}}}class er{constructor(n){var i;this.options={},this.fieldInfo={},this.prevTransformArray=[],this.mount=()=>{var t,r;const s=this.fieldMetaDerived.mount(),a=this.store.mount(),o=()=>{s(),a()};(r=(t=this.options.listeners)==null?void 0:t.onMount)==null||r.call(t,{formApi:this});const{onMount:l}=this.options.validators||{};return l&&this.validateSync("mount"),o},this.update=t=>{var r,s;if(!t)return;const a=this.options;this.options=t;const o=!!((s=(r=t.transform)==null?void 0:r.deps)!=null&&s.some((d,f)=>d!==this.prevTransformArray[f])),l=t.defaultValues&&!me(t.defaultValues,a.defaultValues)&&!this.state.isTouched,c=!me(t.defaultState,a.defaultState)&&!this.state.isTouched;!l&&!c&&!o||ee(()=>{this.baseStore.setState(()=>Ye(Object.assign({},this.state,c?t.defaultState:{},l?{values:t.defaultValues}:{},o?{_force_re_eval:!this.state._force_re_eval}:{})))})},this.reset=(t,r)=>{const{fieldMeta:s}=this.state,a=this.resetFieldMeta(s);t&&!(r!=null&&r.keepDefaultValues)&&(this.options={...this.options,defaultValues:t}),this.baseStore.setState(()=>{var o;return Ye({...this.options.defaultState,values:t??this.options.defaultValues??((o=this.options.defaultState)==null?void 0:o.values),fieldMetaBase:a})})},this.validateAllFields=async t=>{const r=[];return ee(()=>{Object.values(this.fieldInfo).forEach(a=>{if(!a.instance)return;const o=a.instance;r.push(Promise.resolve().then(()=>o.validate(t,{skipFormValidation:!0}))),a.instance.state.meta.isTouched||a.instance.setMeta(l=>({...l,isTouched:!0}))})}),(await Promise.all(r)).flat()},this.validateArrayFieldsStartingFrom=async(t,r,s)=>{const a=this.getFieldValue(t),o=Array.isArray(a)?Math.max(a.length-1,0):null,l=[`${t}[${r}]`];for(let u=r+1;u<=(o??0);u++)l.push(`${t}[${u}]`);const c=Object.keys(this.fieldInfo).filter(u=>l.some(v=>u.startsWith(v))),d=[];return ee(()=>{c.forEach(u=>{d.push(Promise.resolve().then(()=>this.validateField(u,s)))})}),(await Promise.all(d)).flat()},this.validateField=(t,r)=>{var s;const a=(s=this.fieldInfo[t])==null?void 0:s.instance;return a?(a.state.meta.isTouched||a.setMeta(o=>({...o,isTouched:!0})),a.validate(r)):[]},this.validateSync=t=>{const r=it(t,this.options);let s=!1;const a={};return ee(()=>{var o,l;for(const d of r){if(!d.validate)continue;const f=this.runValidator({validate:d.validate,value:{value:this.state.values,formApi:this,validationSource:"form"},type:"validate"}),{formError:u,fieldErrors:v}=Ke(f),m=Ae(d.cause);for(const g of Object.keys(this.state.fieldMeta)){const h=this.getFieldMeta(g);if(!h)continue;const{errorMap:M,errorSourceMap:b}=h,k=v==null?void 0:v[g],{newErrorValue:E,newSource:P}=Kt({newFormValidatorError:k,isPreviousErrorFromFormValidator:(b==null?void 0:b[m])==="form",previousErrorValue:M==null?void 0:M[m]});P==="form"&&(a[g]={...a[g],[m]:k}),(M==null?void 0:M[m])!==E&&this.setFieldMeta(g,_=>({..._,errorMap:{..._.errorMap,[m]:E},errorSourceMap:{..._.errorSourceMap,[m]:P}}))}((o=this.state.errorMap)==null?void 0:o[m])!==u&&this.baseStore.setState(g=>({...g,errorMap:{...g.errorMap,[m]:u}})),(u||v)&&(s=!0)}const c=Ae("submit");(l=this.state.errorMap)!=null&&l[c]&&t!=="submit"&&!s&&this.baseStore.setState(d=>({...d,errorMap:{...d.errorMap,[c]:void 0}}))}),{hasErrored:s,fieldsErrorMap:a}},this.validateAsync=async t=>{const r=rt(t,this.options);this.state.isFormValidating||this.baseStore.setState(c=>({...c,isFormValidating:!0}));const s=[];let a;for(const c of r){if(!c.validate)continue;const d=Ae(c.cause),f=this.state.validationMetaMap[d];f==null||f.lastAbortController.abort();const u=new AbortController;this.state.validationMetaMap[d]={lastAbortController:u},s.push(new Promise(async v=>{let m;try{m=await new Promise((b,k)=>{setTimeout(async()=>{if(u.signal.aborted)return b(void 0);try{b(await this.runValidator({validate:c.validate,value:{value:this.state.values,formApi:this,validationSource:"form",signal:u.signal},type:"validateAsync"}))}catch(E){k(E)}},c.debounceMs)})}catch(b){m=b}const{formError:g,fieldErrors:h}=Ke(m);h&&(a=a?{...a,...h}:h);const M=Ae(c.cause);for(const b of Object.keys(this.state.fieldMeta)){const k=this.getFieldMeta(b);if(!k)continue;const{errorMap:E,errorSourceMap:P}=k,_=a==null?void 0:a[b],{newErrorValue:N,newSource:O}=Kt({newFormValidatorError:_,isPreviousErrorFromFormValidator:(P==null?void 0:P[M])==="form",previousErrorValue:E==null?void 0:E[M]});(E==null?void 0:E[M])!==N&&this.setFieldMeta(b,I=>({...I,errorMap:{...I.errorMap,[M]:N},errorSourceMap:{...I.errorSourceMap,[M]:O}}))}this.baseStore.setState(b=>({...b,errorMap:{...b.errorMap,[M]:g}})),v(a?{fieldErrors:a,errorMapKey:M}:void 0)}))}let o=[];const l={};if(s.length){o=await Promise.all(s);for(const c of o)if(c!=null&&c.fieldErrors){const{errorMapKey:d}=c;for(const[f,u]of Object.entries(c.fieldErrors)){const m={...l[f]||{},[d]:u};l[f]=m}}}return this.baseStore.setState(c=>({...c,isFormValidating:!1})),l},this.validate=t=>{const{hasErrored:r,fieldsErrorMap:s}=this.validateSync(t);return r&&!this.options.asyncAlways?s:this.validateAsync(t)},this.getFieldValue=t=>xe(this.state.values,t),this.getFieldMeta=t=>this.state.fieldMeta[t],this.getFieldInfo=t=>{var r;return(r=this.fieldInfo)[t]||(r[t]={instance:null,validationMetaMap:{onChange:void 0,onBlur:void 0,onSubmit:void 0,onMount:void 0,onServer:void 0}})},this.setFieldMeta=(t,r)=>{this.baseStore.setState(s=>({...s,fieldMetaBase:{...s.fieldMetaBase,[t]:ze(r,s.fieldMetaBase[t])}}))},this.resetFieldMeta=t=>Object.keys(t).reduce((r,s)=>{const a=s;return r[a]=Ne,r},{}),this.setFieldValue=(t,r,s)=>{const a=(s==null?void 0:s.dontUpdateMeta)??!1;ee(()=>{a||this.setFieldMeta(t,o=>({...o,isTouched:!0,isDirty:!0,errorMap:{...o==null?void 0:o.errorMap,onMount:void 0}})),this.baseStore.setState(o=>({...o,values:De(o.values,t,r)}))})},this.deleteField=t=>{const s=[...Object.keys(this.fieldInfo).filter(a=>{const o=t.toString();return a!==o&&a.startsWith(o)}),t];this.baseStore.setState(a=>{const o={...a};return s.forEach(l=>{o.values=zn(o.values,l),delete this.fieldInfo[l],delete o.fieldMetaBase[l]}),o})},this.pushFieldValue=(t,r,s)=>{this.setFieldValue(t,a=>[...Array.isArray(a)?a:[],r],s),this.validateField(t,"change")},this.insertFieldValue=async(t,r,s,a)=>{this.setFieldValue(t,o=>[...o.slice(0,r),s,...o.slice(r)],a),await this.validateField(t,"change"),Oe(this).handleArrayFieldMetaShift(t,r,"insert"),await this.validateArrayFieldsStartingFrom(t,r,"change")},this.replaceFieldValue=async(t,r,s,a)=>{this.setFieldValue(t,o=>o.map((l,c)=>c===r?s:l),a),await this.validateField(t,"change"),await this.validateArrayFieldsStartingFrom(t,r,"change")},this.removeFieldValue=async(t,r,s)=>{const a=this.getFieldValue(t),o=Array.isArray(a)?Math.max(a.length-1,0):null;if(this.setFieldValue(t,l=>l.filter((c,d)=>d!==r),s),Oe(this).handleArrayFieldMetaShift(t,r,"remove"),o!==null){const l=`${t}[${o}]`;this.deleteField(l)}await this.validateField(t,"change"),await this.validateArrayFieldsStartingFrom(t,r,"change")},this.swapFieldValues=(t,r,s,a)=>{this.setFieldValue(t,o=>{const l=o[r],c=o[s];return De(De(o,`${r}`,c),`${s}`,l)},a),Oe(this).handleArrayFieldMetaShift(t,r,"swap",s),this.validateField(t,"change"),this.validateField(`${t}[${r}]`,"change"),this.validateField(`${t}[${s}]`,"change")},this.moveFieldValues=(t,r,s,a)=>{this.setFieldValue(t,o=>{const l=[...o];return l.splice(s,0,l.splice(r,1)[0]),l},a),Oe(this).handleArrayFieldMetaShift(t,r,"move",s),this.validateField(t,"change"),this.validateField(`${t}[${r}]`,"change"),this.validateField(`${t}[${s}]`,"change")},this.clearFieldValues=(t,r)=>{const s=this.getFieldValue(t),a=Array.isArray(s)?Math.max(s.length-1,0):null;if(this.setFieldValue(t,[],r),a!==null)for(let o=0;o<=a;o++){const l=`${t}[${o}]`;this.deleteField(l)}this.validateField(t,"change")},this.resetField=t=>{this.baseStore.setState(r=>({...r,fieldMetaBase:{...r.fieldMetaBase,[t]:Ne},values:this.options.defaultValues?De(r.values,t,xe(this.options.defaultValues,t)):r.values}))},this.getAllErrors=()=>({form:{errors:this.state.errors,errorMap:this.state.errorMap},fields:Object.entries(this.state.fieldMeta).reduce((t,[r,s])=>(Object.keys(s).length&&s.errors.length&&(t[r]={errors:s.errors,errorMap:s.errorMap}),t),{})}),this.parseValuesWithSchema=t=>be.validate({value:this.state.values,validationSource:"form"},t),this.parseValuesWithSchemaAsync=t=>be.validateAsync({value:this.state.values,validationSource:"form"},t),this.baseStore=new On(Ye({...n==null?void 0:n.defaultState,values:(n==null?void 0:n.defaultValues)??((i=n==null?void 0:n.defaultState)==null?void 0:i.values)})),this.fieldMetaDerived=new tt({deps:[this.baseStore],fn:({prevDepVals:t,currDepVals:r,prevVal:s})=>{var a,o,l;const c=s,d=t==null?void 0:t[0],f=r[0];let u=0;const v={};for(const m of Object.keys(f.fieldMetaBase)){const g=f.fieldMetaBase[m],h=d==null?void 0:d.fieldMetaBase[m],M=c==null?void 0:c[m],b=xe(f.values,m);let k=M==null?void 0:M.errors;if(!h||g.errorMap!==h.errorMap){k=Object.values(g.errorMap??{}).filter(O=>O!==void 0);const N=(a=this.getFieldInfo(m))==null?void 0:a.instance;N&&!N.options.disableErrorFlat&&(k=k==null?void 0:k.flat(1))}const E=!Qn(k??[]),P=!g.isDirty,_=me(b,xe(this.options.defaultValues,m))||me(b,(l=(o=this.getFieldInfo(m))==null?void 0:o.instance)==null?void 0:l.options.defaultValue);if(M&&M.isPristine===P&&M.isValid===E&&M.isDefaultValue===_&&M.errors===k&&g===h){v[m]=M,u++;continue}v[m]={...g,errors:k,isPristine:P,isValid:E,isDefaultValue:_}}return Object.keys(f.fieldMetaBase).length&&c&&u===Object.keys(f.fieldMetaBase).length?c:v}}),this.store=new tt({deps:[this.baseStore,this.fieldMetaDerived],fn:({prevDepVals:t,currDepVals:r,prevVal:s})=>{var a,o,l,c;const d=s,f=t==null?void 0:t[0],u=r[0],v=r[1],m=Object.values(v).filter(Boolean),g=m.some(D=>D.isValidating),h=m.every(D=>D.isValid),M=m.some(D=>D.isTouched),b=m.some(D=>D.isBlurred),k=m.every(D=>D.isDefaultValue),E=M&&((a=u.errorMap)==null?void 0:a.onMount),P=m.some(D=>D.isDirty),_=!P,N=!!((o=u.errorMap)!=null&&o.onMount||m.some(D=>{var H;return(H=D==null?void 0:D.errorMap)==null?void 0:H.onMount})),O=!!g;let I=(d==null?void 0:d.errors)??[];(!f||u.errorMap!==f.errorMap)&&(I=Object.values(u.errorMap).reduce((D,H)=>H===void 0?D:H&&st(H)?(D.push(H.form),D):(D.push(H),D),[]));const S=I.length===0,w=h&&S,j=this.options.canSubmitWhenInvalid??!1,$=u.submissionAttempts===0&&!M&&!N||!O&&!u.isSubmitting&&w||j;let T=u.errorMap;if(E&&(I=I.filter(D=>D!==u.errorMap.onMount),T=Object.assign(T,{onMount:void 0})),d&&f&&d.errorMap===T&&d.fieldMeta===this.fieldMetaDerived.state&&d.errors===I&&d.isFieldsValidating===g&&d.isFieldsValid===h&&d.isFormValid===S&&d.isValid===w&&d.canSubmit===$&&d.isTouched===M&&d.isBlurred===b&&d.isPristine===_&&d.isDefaultValue===k&&d.isDirty===P&&me(f,u))return d;let z={...u,errorMap:T,fieldMeta:this.fieldMetaDerived.state,errors:I,isFieldsValidating:g,isFieldsValid:h,isFormValid:S,isValid:w,canSubmit:$,isTouched:M,isBlurred:b,isPristine:_,isDefaultValue:k,isDirty:P};const q=((l=this.options.transform)==null?void 0:l.deps)??[];if(q.length!==this.prevTransformArray.length||q.some((D,H)=>D!==this.prevTransformArray[H])){const D=Object.assign({},this,{state:z});(c=this.options.transform)==null||c.fn(D),z=D.state,this.prevTransformArray=q}return z}}),this.handleSubmit=this.handleSubmit.bind(this),this.update(n||{})}get state(){return this.store.state}runValidator(n){return an(n.validate)?be[n.type](n.value,n.validate):n.validate(n.value)}async handleSubmit(n){var i,t,r,s,a,o,l,c;if(this.baseStore.setState(f=>({...f,isSubmitted:!1,submissionAttempts:f.submissionAttempts+1,isSubmitSuccessful:!1})),ee(()=>{Object.values(this.fieldInfo).forEach(f=>{f.instance&&(f.instance.state.meta.isTouched||f.instance.setMeta(u=>({...u,isTouched:!0})))})}),!this.state.canSubmit)return;this.baseStore.setState(f=>({...f,isSubmitting:!0}));const d=()=>{this.baseStore.setState(f=>({...f,isSubmitting:!1}))};if(await this.validateAllFields("submit"),!this.state.isFieldsValid){d(),(t=(i=this.options).onSubmitInvalid)==null||t.call(i,{value:this.state.values,formApi:this});return}if(await this.validate("submit"),!this.state.isValid){d(),(s=(r=this.options).onSubmitInvalid)==null||s.call(r,{value:this.state.values,formApi:this});return}ee(()=>{Object.values(this.fieldInfo).forEach(f=>{var u,v,m;(m=(v=(u=f.instance)==null?void 0:u.options.listeners)==null?void 0:v.onSubmit)==null||m.call(v,{value:f.instance.state.value,fieldApi:f.instance})})}),(o=(a=this.options.listeners)==null?void 0:a.onSubmit)==null||o.call(a,{formApi:this});try{await((c=(l=this.options).onSubmit)==null?void 0:c.call(l,{value:this.state.values,formApi:this,meta:n??this.options.onSubmitMeta})),ee(()=>{this.baseStore.setState(f=>({...f,isSubmitted:!0,isSubmitSuccessful:!0})),d()})}catch(f){throw this.baseStore.setState(u=>({...u,isSubmitSuccessful:!1})),d(),f}}setErrorMap(n){ee(()=>{Object.entries(n).forEach(([i,t])=>{const r=i;if(st(t)){const{formError:s,fieldErrors:a}=Ke(t);for(const o of Object.keys(this.fieldInfo))this.getFieldMeta(o)&&this.setFieldMeta(o,c=>({...c,errorMap:{...c.errorMap,[r]:a==null?void 0:a[o]},errorSourceMap:{...c.errorSourceMap,[r]:"form"}}));this.baseStore.setState(o=>({...o,errorMap:{...o.errorMap,[r]:s}}))}else this.baseStore.setState(s=>({...s,errorMap:{...s.errorMap,[r]:t}}))})})}}function Ke(e){if(e){if(st(e)){const n=Ke(e.form).formError,i=e.fields;return{formError:n,fieldErrors:i}}return{formError:e}}return{formError:void 0}}function Ae(e){switch(e){case"submit":return"onSubmit";case"blur":return"onBlur";case"mount":return"onMount";case"server":return"onServer";case"change":default:return"onChange"}}class tr{constructor(n){this.options={},this.mount=()=>{var i,t;const r=this.store.mount();this.options.defaultValue!==void 0&&this.form.setFieldValue(this.name,this.options.defaultValue,{dontUpdateMeta:!0});const s=this.getInfo();s.instance=this,this.update(this.options);const{onMount:a}=this.options.validators||{};if(a){const o=this.runValidator({validate:a,value:{value:this.state.value,fieldApi:this,validationSource:"field"},type:"validate"});o&&this.setMeta(l=>({...l,errorMap:{...l==null?void 0:l.errorMap,onMount:o},errorSourceMap:{...l==null?void 0:l.errorSourceMap,onMount:"field"}}))}return(t=(i=this.options.listeners)==null?void 0:i.onMount)==null||t.call(i,{value:this.state.value,fieldApi:this}),r},this.update=i=>{this.options=i;const t=this.name!==i.name;if(this.name=i.name,this.state.value===void 0){const r=xe(i.form.options.defaultValues,i.name),s=i.defaultValue??r;t?this.setValue(a=>a||s,{dontUpdateMeta:!0}):s!==void 0&&this.setValue(s,{dontUpdateMeta:!0})}this.form.getFieldMeta(this.name)===void 0&&this.setMeta(this.state.meta)},this.getValue=()=>this.form.getFieldValue(this.name),this.setValue=(i,t)=>{this.form.setFieldValue(this.name,i,t),this.triggerOnChangeListener(),this.validate("change")},this.getMeta=()=>this.store.state.meta,this.setMeta=i=>this.form.setFieldMeta(this.name,i),this.getInfo=()=>this.form.getFieldInfo(this.name),this.pushValue=(i,t)=>{this.form.pushFieldValue(this.name,i,t),this.triggerOnChangeListener()},this.insertValue=(i,t,r)=>{this.form.insertFieldValue(this.name,i,t,r),this.triggerOnChangeListener()},this.replaceValue=(i,t,r)=>{this.form.replaceFieldValue(this.name,i,t,r),this.triggerOnChangeListener()},this.removeValue=(i,t)=>{this.form.removeFieldValue(this.name,i,t),this.triggerOnChangeListener()},this.swapValues=(i,t,r)=>{this.form.swapFieldValues(this.name,i,t,r),this.triggerOnChangeListener()},this.moveValue=(i,t,r)=>{this.form.moveFieldValues(this.name,i,t,r),this.triggerOnChangeListener()},this.clearValues=i=>{this.form.clearFieldValues(this.name,i),this.triggerOnChangeListener()},this.getLinkedFields=i=>{const t=Object.values(this.form.fieldInfo),r=[];for(const s of t){if(!s.instance)continue;const{onChangeListenTo:a,onBlurListenTo:o}=s.instance.options.validators||{};i==="change"&&(a!=null&&a.includes(this.name))&&r.push(s.instance),i==="blur"&&(o!=null&&o.includes(this.name))&&r.push(s.instance)}return r},this.validateSync=(i,t)=>{var r;const s=it(i,this.options),o=this.getLinkedFields(i).reduce((d,f)=>{const u=it(i,f.options);return u.forEach(v=>{v.field=f}),d.concat(u)},[]);let l=!1;ee(()=>{const d=(f,u)=>{var v;const m=je(u.cause),g=u.validate?Nt(f.runValidator({validate:u.validate,value:{value:f.store.state.value,validationSource:"field",fieldApi:f},type:"validate"})):void 0,h=t[m],{newErrorValue:M,newSource:b}=Bt({formLevelError:h,fieldLevelError:g});((v=f.state.meta.errorMap)==null?void 0:v[m])!==M&&f.setMeta(k=>({...k,errorMap:{...k.errorMap,[m]:M},errorSourceMap:{...k.errorSourceMap,[m]:b}})),M&&(l=!0)};for(const f of s)d(this,f);for(const f of o)f.validate&&d(f.field,f)});const c=je("submit");return(r=this.state.meta.errorMap)!=null&&r[c]&&i!=="submit"&&!l&&this.setMeta(d=>({...d,errorMap:{...d.errorMap,[c]:void 0},errorSourceMap:{...d.errorSourceMap,[c]:void 0}})),{hasErrored:l}},this.validateAsync=async(i,t)=>{const r=rt(i,this.options),s=await t,a=this.getLinkedFields(i),o=a.reduce((u,v)=>{const m=rt(i,v.options);return m.forEach(g=>{g.field=v}),u.concat(m)},[]);this.state.meta.isValidating||this.setMeta(u=>({...u,isValidating:!0}));for(const u of a)u.setMeta(v=>({...v,isValidating:!0}));const l=[],c=[],d=(u,v,m)=>{const g=je(v.cause),h=u.getInfo().validationMetaMap[g];h==null||h.lastAbortController.abort();const M=new AbortController;this.getInfo().validationMetaMap[g]={lastAbortController:M},m.push(new Promise(async b=>{var k;let E;try{E=await new Promise((I,S)=>{this.timeoutIds.validations[v.cause]&&clearTimeout(this.timeoutIds.validations[v.cause]),this.timeoutIds.validations[v.cause]=setTimeout(async()=>{if(M.signal.aborted)return I(void 0);try{I(await this.runValidator({validate:v.validate,value:{value:u.store.state.value,fieldApi:u,signal:M.signal,validationSource:"field"},type:"validateAsync"}))}catch(w){S(w)}},v.debounceMs)})}catch(I){E=I}if(M.signal.aborted)return b(void 0);const P=Nt(E),_=(k=s[this.name])==null?void 0:k[g],{newErrorValue:N,newSource:O}=Bt({formLevelError:_,fieldLevelError:P});u.setMeta(I=>({...I,errorMap:{...I==null?void 0:I.errorMap,[g]:N},errorSourceMap:{...I.errorSourceMap,[g]:O}})),b(N)}))};for(const u of r)u.validate&&d(this,u,l);for(const u of o)u.validate&&d(u.field,u,c);let f=[];(l.length||c.length)&&(f=await Promise.all(l),await Promise.all(c)),this.setMeta(u=>({...u,isValidating:!1}));for(const u of a)u.setMeta(v=>({...v,isValidating:!1}));return f.filter(Boolean)},this.validate=(i,t)=>{var r;if(!this.state.meta.isTouched)return[];const{fieldsErrorMap:s}=t!=null&&t.skipFormValidation?{fieldsErrorMap:{}}:this.form.validateSync(i),{hasErrored:a}=this.validateSync(i,s[this.name]??{});if(a&&!this.options.asyncAlways)return(r=this.getInfo().validationMetaMap[je(i)])==null||r.lastAbortController.abort(),this.state.meta.errors;const o=t!=null&&t.skipFormValidation?Promise.resolve({}):this.form.validateAsync(i);return this.validateAsync(i,o)},this.handleChange=i=>{this.setValue(i)},this.handleBlur=()=>{this.state.meta.isTouched||this.setMeta(t=>({...t,isTouched:!0})),this.state.meta.isBlurred||this.setMeta(t=>({...t,isBlurred:!0})),this.validate("blur"),this.triggerOnBlurListener()},this.parseValueWithSchema=i=>be.validate({value:this.state.value,validationSource:"field"},i),this.parseValueWithSchemaAsync=i=>be.validateAsync({value:this.state.value,validationSource:"field"},i),this.form=n.form,this.name=n.name,this.timeoutIds={validations:{},listeners:{},formListeners:{}},this.store=new tt({deps:[this.form.store],fn:()=>{const i=this.form.getFieldValue(this.name),t=this.form.getFieldMeta(this.name)??{...Ne,...n.defaultMeta};return{value:i,meta:t}}}),this.options=n}get state(){return this.store.state}runValidator(n){return an(n.validate)?be[n.type](n.value,n.validate):n.validate(n.value)}setErrorMap(n){this.setMeta(i=>({...i,errorMap:{...i.errorMap,...n}}))}triggerOnBlurListener(){var n,i,t,r,s,a;const o=(n=this.form.options.listeners)==null?void 0:n.onBlurDebounceMs;o&&o>0?(this.timeoutIds.formListeners.blur&&clearTimeout(this.timeoutIds.formListeners.blur),this.timeoutIds.formListeners.blur=setTimeout(()=>{var c,d;(d=(c=this.form.options.listeners)==null?void 0:c.onBlur)==null||d.call(c,{formApi:this.form,fieldApi:this})},o)):(t=(i=this.form.options.listeners)==null?void 0:i.onBlur)==null||t.call(i,{formApi:this.form,fieldApi:this});const l=(r=this.options.listeners)==null?void 0:r.onBlurDebounceMs;l&&l>0?(this.timeoutIds.listeners.blur&&clearTimeout(this.timeoutIds.listeners.blur),this.timeoutIds.listeners.blur=setTimeout(()=>{var c,d;(d=(c=this.options.listeners)==null?void 0:c.onBlur)==null||d.call(c,{value:this.state.value,fieldApi:this})},l)):(a=(s=this.options.listeners)==null?void 0:s.onBlur)==null||a.call(s,{value:this.state.value,fieldApi:this})}triggerOnChangeListener(){var n,i,t,r,s,a;const o=(n=this.form.options.listeners)==null?void 0:n.onChangeDebounceMs;o&&o>0?(this.timeoutIds.formListeners.blur&&clearTimeout(this.timeoutIds.formListeners.blur),this.timeoutIds.formListeners.blur=setTimeout(()=>{var c,d;(d=(c=this.form.options.listeners)==null?void 0:c.onChange)==null||d.call(c,{formApi:this.form,fieldApi:this})},o)):(t=(i=this.form.options.listeners)==null?void 0:i.onChange)==null||t.call(i,{formApi:this.form,fieldApi:this});const l=(r=this.options.listeners)==null?void 0:r.onChangeDebounceMs;l&&l>0?(this.timeoutIds.listeners.change&&clearTimeout(this.timeoutIds.listeners.change),this.timeoutIds.listeners.change=setTimeout(()=>{var c,d;(d=(c=this.options.listeners)==null?void 0:c.onChange)==null||d.call(c,{value:this.state.value,fieldApi:this})},l)):(a=(s=this.options.listeners)==null?void 0:s.onChange)==null||a.call(s,{value:this.state.value,fieldApi:this})}}function Nt(e){if(e)return e}function je(e){switch(e){case"submit":return"onSubmit";case"blur":return"onBlur";case"mount":return"onMount";case"server":return"onServer";case"change":default:return"onChange"}}const Le=typeof window<"u"?p.useLayoutEffect:p.useEffect;function nr(e){const[n]=p.useState(()=>{const t=new tr({...e,form:e.form,name:e.name});return t.Field=on,t});return Le(n.mount,[n]),Le(()=>{n.update(e)}),lt(n.store,e.mode==="array"?i=>[i.meta,Object.keys(i.value??[]).length]:void 0),n}const on=({children:e,...n})=>{const i=nr(n),t=p.useMemo(()=>ze(e,i),[e,i,i.state.value,i.state.meta]);return y.jsx(y.Fragment,{children:t})};function rr({form:e,selector:n,children:i}){const t=lt(e.store,n);return ze(i,t)}function ir(e){const[n]=p.useState(()=>{const i=new er(e),t=i;return t.Field=function(s){return y.jsx(on,{...s,form:i})},t.Subscribe=r=>y.jsx(rr,{form:i,selector:r.selector,children:r.children}),t});return Le(n.mount,[]),lt(n.store,i=>i.isSubmitting),Le(()=>{n.update(e)}),n}function sr(){const e=p.createContext(null);function n(){const r=p.useContext(e);if(!r)throw new Error("`fieldContext` only works when within a `fieldComponent` passed to `createFormHook`");return r}const i=p.createContext(null);function t(){const r=p.useContext(i);if(!r)throw new Error("`formContext` only works when within a `formComponent` passed to `createFormHook`");return r}return{fieldContext:e,useFieldContext:n,useFormContext:t,formContext:i}}function ar({fieldComponents:e,fieldContext:n,formContext:i,formComponents:t}){function r(a){const o=ir(a),l=p.useMemo(()=>({children:f})=>y.jsx(i.Provider,{value:o,children:f}),[o]),c=p.useMemo(()=>({children:f,...u})=>y.jsx(o.Field,{...u,children:v=>y.jsx(n.Provider,{value:v,children:f(Object.assign(v,e))})}),[o]);return p.useMemo(()=>Object.assign(o,{AppField:c,AppForm:l,...t}),[o,c,l])}function s({render:a,props:o}){return l=>a({...o,...l})}return{useAppForm:r,withForm:s}}function ge(e,n){if(e==null)return{};var i={};for(var t in e)if({}.hasOwnProperty.call(e,t)){if(n.indexOf(t)!==-1)continue;i[t]=e[t]}return i}function K(){return K=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var i=arguments[n];for(var t in i)({}).hasOwnProperty.call(i,t)&&(e[t]=i[t])}return e},K.apply(null,arguments)}var Je={exports:{}},Qe,Lt;function or(){if(Lt)return Qe;Lt=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return Qe=e,Qe}var Ze,Ht;function lr(){if(Ht)return Ze;Ht=1;var e=or();function n(){}function i(){}return i.resetWarningCache=n,Ze=function(){function t(a,o,l,c,d,f){if(f!==e){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}t.isRequired=t;function r(){return t}var s={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:r,element:t,elementType:t,instanceOf:r,node:t,objectOf:r,oneOf:r,oneOfType:r,shape:r,exact:r,checkPropTypes:i,resetWarningCache:n};return s.PropTypes=s,s},Ze}var Wt;function ur(){return Wt||(Wt=1,Je.exports=lr()()),Je.exports}var cr=ur();const x=An(cr);var et={exports:{}},R={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qt;function dr(){if(qt)return R;qt=1;var e=Symbol.for("react.element"),n=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),t=Symbol.for("react.strict_mode"),r=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),a=Symbol.for("react.context"),o=Symbol.for("react.server_context"),l=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),f=Symbol.for("react.memo"),u=Symbol.for("react.lazy"),v=Symbol.for("react.offscreen"),m;m=Symbol.for("react.module.reference");function g(h){if(typeof h=="object"&&h!==null){var M=h.$$typeof;switch(M){case e:switch(h=h.type,h){case i:case r:case t:case c:case d:return h;default:switch(h=h&&h.$$typeof,h){case o:case a:case l:case u:case f:case s:return h;default:return M}}case n:return M}}}return R.ContextConsumer=a,R.ContextProvider=s,R.Element=e,R.ForwardRef=l,R.Fragment=i,R.Lazy=u,R.Memo=f,R.Portal=n,R.Profiler=r,R.StrictMode=t,R.Suspense=c,R.SuspenseList=d,R.isAsyncMode=function(){return!1},R.isConcurrentMode=function(){return!1},R.isContextConsumer=function(h){return g(h)===a},R.isContextProvider=function(h){return g(h)===s},R.isElement=function(h){return typeof h=="object"&&h!==null&&h.$$typeof===e},R.isForwardRef=function(h){return g(h)===l},R.isFragment=function(h){return g(h)===i},R.isLazy=function(h){return g(h)===u},R.isMemo=function(h){return g(h)===f},R.isPortal=function(h){return g(h)===n},R.isProfiler=function(h){return g(h)===r},R.isStrictMode=function(h){return g(h)===t},R.isSuspense=function(h){return g(h)===c},R.isSuspenseList=function(h){return g(h)===d},R.isValidElementType=function(h){return typeof h=="string"||typeof h=="function"||h===i||h===r||h===t||h===c||h===d||h===v||typeof h=="object"&&h!==null&&(h.$$typeof===u||h.$$typeof===f||h.$$typeof===s||h.$$typeof===a||h.$$typeof===l||h.$$typeof===m||h.getModuleId!==void 0)},R.typeOf=g,R}var zt;function fr(){return zt||(zt=1,et.exports=dr()),et.exports}fr();const Ut=e=>typeof e=="object"&&e!=null&&e.nodeType===1,Gt=(e,n)=>(!n||e!=="hidden")&&e!=="visible"&&e!=="clip",Re=(e,n)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){const i=getComputedStyle(e,null);return Gt(i.overflowY,n)||Gt(i.overflowX,n)||(t=>{const r=(s=>{if(!s.ownerDocument||!s.ownerDocument.defaultView)return null;try{return s.ownerDocument.defaultView.frameElement}catch{return null}})(t);return!!r&&(r.clientHeight<t.scrollHeight||r.clientWidth<t.scrollWidth)})(e)}return!1},Pe=(e,n,i,t,r,s,a,o)=>s<e&&a>n||s>e&&a<n?0:s<=e&&o<=i||a>=n&&o>=i?s-e-t:a>n&&o<i||s<e&&o>i?a-n+r:0,hr=e=>{const n=e.parentElement;return n??(e.getRootNode().host||null)},mr=(e,n)=>{var i,t,r,s;if(typeof document>"u")return[];const{inline:a,boundary:o,skipOverflowHiddenElements:l}=n,c=typeof o=="function"?o:$=>$!==o;if(!Ut(e))throw new TypeError("Invalid target");const d=document.scrollingElement||document.documentElement,f=[];let u=e;for(;Ut(u)&&c(u);){if(u=hr(u),u===d){f.push(u);break}u!=null&&u===document.body&&Re(u)&&!Re(document.documentElement)||u!=null&&Re(u,l)&&f.push(u)}const v=(t=(i=window.visualViewport)==null?void 0:i.width)!=null?t:innerWidth,m=(s=(r=window.visualViewport)==null?void 0:r.height)!=null?s:innerHeight,{scrollX:g,scrollY:h}=window,{height:M,width:b,top:k,right:E,bottom:P,left:_}=e.getBoundingClientRect(),{top:N,right:O,left:I}=($=>{const T=window.getComputedStyle($);return{top:parseFloat(T.scrollMarginTop)||0,right:parseFloat(T.scrollMarginRight)||0,bottom:parseFloat(T.scrollMarginBottom)||0,left:parseFloat(T.scrollMarginLeft)||0}})(e);let S=k-N,w=a==="center"?_+b/2-I+O:a==="end"?E+O:_-I;const j=[];for(let $=0;$<f.length;$++){const T=f[$],{height:z,width:q,top:te,right:D,bottom:H,left:Y}=T.getBoundingClientRect();if(k>=0&&_>=0&&P<=m&&E<=v&&(T===d&&!Re(T)||k>=te&&P<=H&&_>=Y&&E<=D))return j;const Z=getComputedStyle(T),V=parseInt(Z.borderLeftWidth,10),C=parseInt(Z.borderTopWidth,10),F=parseInt(Z.borderRightWidth,10),A=parseInt(Z.borderBottomWidth,10);let B=0,L=0;const W="offsetWidth"in T?T.offsetWidth-T.clientWidth-V-F:0,J="offsetHeight"in T?T.offsetHeight-T.clientHeight-C-A:0,U="offsetWidth"in T?T.offsetWidth===0?0:q/T.offsetWidth:0,X="offsetHeight"in T?T.offsetHeight===0?0:z/T.offsetHeight:0;if(d===T)B=Pe(h,h+m,m,C,A,h+S,h+S+M,M),L=a==="start"?w:a==="center"?w-v/2:a==="end"?w-v:Pe(g,g+v,v,V,F,g+w,g+w+b,b),B=Math.max(0,B+h),L=Math.max(0,L+g);else{B=Pe(te,H,z,C,A+J,S,S+M,M),L=a==="start"?w-Y-V:a==="center"?w-(Y+q/2)+W/2:a==="end"?w-D+F+W:Pe(Y,D,q,V,F+W,w,w+b,b);const{scrollLeft:Q,scrollTop:de}=T;B=X===0?0:Math.max(0,Math.min(de+B/X,T.scrollHeight-z/X+J)),L=U===0?0:Math.max(0,Math.min(Q+L/U,T.scrollWidth-q/U+W)),S+=de-B,w+=Q-L}j.push({el:T,top:B,left:L})}return j};var Me=function(){return Me=Object.assign||function(n){for(var i,t=1,r=arguments.length;t<r;t++){i=arguments[t];for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(n[s]=i[s])}return n},Me.apply(this,arguments)};var vr=0;function ln(){}function gr(e,n){if(e){var i=mr(e,{boundary:n});i.forEach(function(t){var r=t.el,s=t.top,a=t.left;r.scrollTop=s,r.scrollLeft=a})}}function Xt(e,n,i){var t=e===n||n instanceof i.Node&&e.contains&&e.contains(n);return t}function un(e,n){var i;function t(){i&&clearTimeout(i)}function r(){for(var s=arguments.length,a=new Array(s),o=0;o<s;o++)a[o]=arguments[o];t(),i=setTimeout(function(){i=null,e.apply(void 0,a)},n)}return r.cancel=t,r}function G(){for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];return function(t){for(var r=arguments.length,s=new Array(r>1?r-1:0),a=1;a<r;a++)s[a-1]=arguments[a];return n.some(function(o){return o&&o.apply(void 0,[t].concat(s)),t.preventDownshiftDefault||t.hasOwnProperty("nativeEvent")&&t.nativeEvent.preventDownshiftDefault})}}function pe(){for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];return function(t){n.forEach(function(r){typeof r=="function"?r(t):r&&(r.current=t)})}}function pr(){return String(vr++)}function He(e,n){return!e||!n?e:Object.keys(e).reduce(function(i,t){return i[t]=cn(n,t)?n[t]:e[t],i},{})}function cn(e,n){return e[n]!==void 0}function at(e){var n=e.key,i=e.keyCode;return i>=37&&i<=40&&n.indexOf("Arrow")!==0?"Arrow"+n:n}function _e(e,n,i,t,r){var s=i.length;if(s===0)return-1;var a=s-1;(typeof e!="number"||e<0||e>a)&&(e=n>0?-1:a+1);var o=e+n;o<0?o=a:o>a&&(o=0);var l=We(o,n<0,i,t,r);return l===-1?e>=s?-1:e:l}function We(e,n,i,t,r){r===void 0&&(r=!1);var s=i.length;if(n){for(var a=e;a>=0;a--)if(!t(i[a],a))return a}else for(var o=e;o<s;o++)if(!t(i[o],o))return o;return r?We(n?s-1:0,n,i,t):-1}function Yt(e,n,i,t){return t===void 0&&(t=!0),i&&n.some(function(r){return r&&(Xt(r,e,i)||t&&Xt(r,i.document.activeElement,i))})}var yr=un(function(e){dn(e).textContent=""},500);function dn(e){var n=e.getElementById("a11y-status-message");return n||(n=e.createElement("div"),n.setAttribute("id","a11y-status-message"),n.setAttribute("role","status"),n.setAttribute("aria-live","polite"),n.setAttribute("aria-relevant","additions text"),Object.assign(n.style,{border:"0",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0",position:"absolute",width:"1px"}),e.body.appendChild(n),n)}function br(e,n){if(!(!e||!n)){var i=dn(n);i.textContent=e,yr(n)}}function Ir(e){var n=e==null?void 0:e.getElementById("a11y-status-message");n&&n.remove()}var fn={highlightedIndex:-1,isOpen:!1,selectedItem:null,inputValue:""};function Mr(e,n,i){var t=e.props,r=e.type,s={};Object.keys(n).forEach(function(a){Sr(a,e,n,i),i[a]!==n[a]&&(s[a]=i[a])}),t.onStateChange&&Object.keys(s).length&&t.onStateChange(K({type:r},s))}function Sr(e,n,i,t){var r=n.props,s=n.type,a="on"+ct(e)+"Change";r[a]&&t[e]!==void 0&&t[e]!==i[e]&&r[a](K({type:s},t))}function wr(e,n){return n.changes}var Jt=un(function(e,n){br(e,n)},200),xr=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?p.useLayoutEffect:p.useEffect,Fr="useId"in $e?function(n){var i=n.id,t=n.labelId,r=n.menuId,s=n.getItemId,a=n.toggleButtonId,o=n.inputId,l="downshift-"+$e.useId();i||(i=l);var c=p.useRef({labelId:t||i+"-label",menuId:r||i+"-menu",getItemId:s||function(d){return i+"-item-"+d},toggleButtonId:a||i+"-toggle-button",inputId:o||i+"-input"});return c.current}:function(n){var i=n.id,t=i===void 0?"downshift-"+pr():i,r=n.labelId,s=n.menuId,a=n.getItemId,o=n.toggleButtonId,l=n.inputId,c=p.useRef({labelId:r||t+"-label",menuId:s||t+"-menu",getItemId:a||function(d){return t+"-item-"+d},toggleButtonId:o||t+"-toggle-button",inputId:l||t+"-input"});return c.current};function hn(e,n,i,t){var r,s;if(e===void 0){if(n===void 0)throw new Error(t);r=i[n],s=n}else s=n===void 0?i.indexOf(e):n,r=e;return[r,s]}function ct(e){return""+e.slice(0,1).toUpperCase()+e.slice(1)}function dt(e){var n=p.useRef(e);return n.current=e,n}function mn(e,n,i,t){var r=p.useRef(),s=p.useRef(),a=p.useCallback(function(v,m){s.current=m,v=He(v,m.props);var g=e(v,m),h=m.props.stateReducer(v,K({},m,{changes:g}));return h},[e]),o=p.useReducer(a,n,i),l=o[0],c=o[1],d=dt(n),f=p.useCallback(function(v){return c(K({props:d.current},v))},[d]),u=s.current;return p.useEffect(function(){var v=He(r.current,u==null?void 0:u.props),m=u&&r.current&&!t(v,l);m&&Mr(u,v,l),r.current=l},[l,u,t]),[l,f]}function Cr(e,n,i,t){var r=mn(e,n,i,t),s=r[0],a=r[1];return[He(s,n),a]}var Fe={itemToString:function(n){return n?String(n):""},itemToKey:function(n){return n},stateReducer:wr,scrollIntoView:gr,environment:typeof window>"u"?void 0:window};function ie(e,n,i){i===void 0&&(i=fn);var t=e["default"+ct(n)];return t!==void 0?t:i[n]}function Ie(e,n,i){i===void 0&&(i=fn);var t=e[n];if(t!==void 0)return t;var r=e["initial"+ct(n)];return r!==void 0?r:ie(e,n,i)}function Vr(e){var n=Ie(e,"selectedItem"),i=Ie(e,"isOpen"),t=Tr(e),r=Ie(e,"inputValue");return{highlightedIndex:t<0&&n&&i?e.items.findIndex(function(s){return e.itemToKey(s)===e.itemToKey(n)}):t,isOpen:i,selectedItem:n,inputValue:r}}function Ce(e,n,i){var t=e.items,r=e.initialHighlightedIndex,s=e.defaultHighlightedIndex,a=e.isItemDisabled,o=e.itemToKey,l=n.selectedItem,c=n.highlightedIndex;return t.length===0?-1:r!==void 0&&c===r&&!a(t[r],r)?r:s!==void 0&&!a(t[s],s)?s:l?t.findIndex(function(d){return o(l)===o(d)}):i<0&&!a(t[t.length-1],t.length-1)?t.length-1:i>0&&!a(t[0],0)?0:-1}function Er(e,n,i){var t=p.useRef({isMouseDown:!1,isTouchMove:!1,isTouchEnd:!1});return p.useEffect(function(){if(!e)return ln;var r=i.map(function(d){return d.current});function s(){t.current.isTouchEnd=!1,t.current.isMouseDown=!0}function a(d){t.current.isMouseDown=!1,Yt(d.target,r,e)||n()}function o(){t.current.isTouchEnd=!1,t.current.isTouchMove=!1}function l(){t.current.isTouchMove=!0}function c(d){t.current.isTouchEnd=!0,!t.current.isTouchMove&&!Yt(d.target,r,e,!1)&&n()}return e.addEventListener("mousedown",s),e.addEventListener("mouseup",a),e.addEventListener("touchstart",o),e.addEventListener("touchmove",l),e.addEventListener("touchend",c),function(){e.removeEventListener("mousedown",s),e.removeEventListener("mouseup",a),e.removeEventListener("touchstart",o),e.removeEventListener("touchmove",l),e.removeEventListener("touchend",c)}},[i,e,n]),t.current}var vn=function(){return ln};function gn(e,n,i,t){t===void 0&&(t={});var r=t.document,s=Ue();p.useEffect(function(){if(!(!e||s||!r)){var a=e(n);Jt(a,r)}},i),p.useEffect(function(){return function(){Jt.cancel(),Ir(r)}},[r])}function kr(e){var n=e.highlightedIndex,i=e.isOpen,t=e.itemRefs,r=e.getItemNodeFromIndex,s=e.menuElement,a=e.scrollIntoView,o=p.useRef(!0);return xr(function(){n<0||!i||!Object.keys(t.current).length||(o.current===!1?o.current=!0:a(r(n),s))},[n]),o}function Qt(e,n,i){var t;i===void 0&&(i=!0);var r=((t=e.items)==null?void 0:t.length)&&n>=0;return K({isOpen:!1,highlightedIndex:-1},r&&K({selectedItem:e.items[n],isOpen:ie(e,"isOpen"),highlightedIndex:ie(e,"highlightedIndex")},i&&{inputValue:e.itemToString(e.items[n])}))}function Dr(e,n){return e.isOpen===n.isOpen&&e.inputValue===n.inputValue&&e.highlightedIndex===n.highlightedIndex&&e.selectedItem===n.selectedItem}function Ue(){var e=$e.useRef(!0);return $e.useEffect(function(){return e.current=!1,function(){e.current=!0}},[]),e.current}function ot(e){var n=ie(e,"highlightedIndex");return n>-1&&e.isItemDisabled(e.items[n],n)?-1:n}function Tr(e){var n=Ie(e,"highlightedIndex");return n>-1&&e.isItemDisabled(e.items[n],n)?-1:n}var Be={environment:x.shape({addEventListener:x.func.isRequired,removeEventListener:x.func.isRequired,document:x.shape({createElement:x.func.isRequired,getElementById:x.func.isRequired,activeElement:x.any.isRequired,body:x.any.isRequired}).isRequired,Node:x.func.isRequired}),itemToString:x.func,itemToKey:x.func,stateReducer:x.func},pn=K({},Be,{getA11yStatusMessage:x.func,highlightedIndex:x.number,defaultHighlightedIndex:x.number,initialHighlightedIndex:x.number,isOpen:x.bool,defaultIsOpen:x.bool,initialIsOpen:x.bool,selectedItem:x.any,initialSelectedItem:x.any,defaultSelectedItem:x.any,id:x.string,labelId:x.string,menuId:x.string,getItemId:x.func,toggleButtonId:x.string,onSelectedItemChange:x.func,onHighlightedIndexChange:x.func,onStateChange:x.func,onIsOpenChange:x.func,scrollIntoView:x.func});function Or(e,n,i){var t=n.type,r=n.props,s;switch(t){case i.ItemMouseMove:s={highlightedIndex:n.disabled?-1:n.index};break;case i.MenuMouseLeave:s={highlightedIndex:-1};break;case i.ToggleButtonClick:case i.FunctionToggleMenu:s={isOpen:!e.isOpen,highlightedIndex:e.isOpen?-1:Ce(r,e,0)};break;case i.FunctionOpenMenu:s={isOpen:!0,highlightedIndex:Ce(r,e,0)};break;case i.FunctionCloseMenu:s={isOpen:!1};break;case i.FunctionSetHighlightedIndex:s={highlightedIndex:r.isItemDisabled(r.items[n.highlightedIndex],n.highlightedIndex)?-1:n.highlightedIndex};break;case i.FunctionSetInputValue:s={inputValue:n.inputValue};break;case i.FunctionReset:s={highlightedIndex:ot(r),isOpen:ie(r,"isOpen"),selectedItem:ie(r,"selectedItem"),inputValue:ie(r,"inputValue")};break;default:throw new Error("Reducer called without proper action type.")}return K({},e,s)}Me(Me({},pn),{items:x.array.isRequired,isItemDisabled:x.func});Me(Me({},Fe),{isItemDisabled:function(){return!1}});var ft=0,ht=1,mt=2,vt=3,gt=4,pt=5,yt=6,bt=7,It=8,qe=9,Mt=10,yn=11,bn=12,St=13,In=14,Mn=15,Sn=16,wn=17,xn=18,wt=19,Fn=20,Cn=21,xt=22,Vn=Object.freeze({__proto__:null,ControlledPropUpdatedSelectedItem:xt,FunctionCloseMenu:wn,FunctionOpenMenu:Sn,FunctionReset:Cn,FunctionSelectItem:wt,FunctionSetHighlightedIndex:xn,FunctionSetInputValue:Fn,FunctionToggleMenu:Mn,InputBlur:qe,InputChange:It,InputClick:Mt,InputKeyDownArrowDown:ft,InputKeyDownArrowUp:ht,InputKeyDownEnd:gt,InputKeyDownEnter:bt,InputKeyDownEscape:mt,InputKeyDownHome:vt,InputKeyDownPageDown:yt,InputKeyDownPageUp:pt,ItemClick:St,ItemMouseMove:bn,MenuMouseLeave:yn,ToggleButtonClick:In});function Ar(e){var n=Vr(e),i=n.selectedItem,t=n.inputValue;return t===""&&i&&e.defaultInputValue===void 0&&e.initialInputValue===void 0&&e.inputValue===void 0&&(t=e.itemToString(i)),K({},n,{inputValue:t})}K({},pn,{items:x.array.isRequired,isItemDisabled:x.func,inputValue:x.string,defaultInputValue:x.string,initialInputValue:x.string,inputId:x.string,onInputValueChange:x.func});function jr(e,n,i,t){var r=p.useRef(),s=mn(e,n,i,t),a=s[0],o=s[1],l=Ue();return p.useEffect(function(){if(cn(n,"selectedItem")){if(!l){var c=n.itemToKey(n.selectedItem)!==n.itemToKey(r.current);c&&o({type:xt,inputValue:n.itemToString(n.selectedItem)})}r.current=a.selectedItem===r.current?n.selectedItem:a.selectedItem}},[a.selectedItem,n.selectedItem]),[He(a,n),o]}var Rr=K({},Fe,{isItemDisabled:function(){return!1}});function Pr(e,n){var i,t=n.type,r=n.props,s=n.altKey,a;switch(t){case St:a={isOpen:ie(r,"isOpen"),highlightedIndex:ot(r),selectedItem:r.items[n.index],inputValue:r.itemToString(r.items[n.index])};break;case ft:e.isOpen?a={highlightedIndex:_e(e.highlightedIndex,1,r.items,r.isItemDisabled,!0)}:a={highlightedIndex:s&&e.selectedItem==null?-1:Ce(r,e,1),isOpen:r.items.length>=0};break;case ht:e.isOpen?s?a=Qt(r,e.highlightedIndex):a={highlightedIndex:_e(e.highlightedIndex,-1,r.items,r.isItemDisabled,!0)}:a={highlightedIndex:Ce(r,e,-1),isOpen:r.items.length>=0};break;case bt:a=Qt(r,e.highlightedIndex);break;case mt:a=K({isOpen:!1,highlightedIndex:-1},!e.isOpen&&{selectedItem:null,inputValue:""});break;case pt:a={highlightedIndex:_e(e.highlightedIndex,-10,r.items,r.isItemDisabled,!0)};break;case yt:a={highlightedIndex:_e(e.highlightedIndex,10,r.items,r.isItemDisabled,!0)};break;case vt:a={highlightedIndex:We(0,!1,r.items,r.isItemDisabled)};break;case gt:a={highlightedIndex:We(r.items.length-1,!0,r.items,r.isItemDisabled)};break;case qe:a=K({isOpen:!1,highlightedIndex:-1},e.highlightedIndex>=0&&((i=r.items)==null?void 0:i.length)&&n.selectItem&&{selectedItem:r.items[e.highlightedIndex],inputValue:r.itemToString(r.items[e.highlightedIndex])});break;case It:a={isOpen:!0,highlightedIndex:ot(r),inputValue:n.inputValue};break;case Mt:a={isOpen:!e.isOpen,highlightedIndex:e.isOpen?-1:Ce(r,e,0)};break;case wt:a={selectedItem:n.selectedItem,inputValue:r.itemToString(n.selectedItem)};break;case xt:a={inputValue:n.inputValue};break;default:return Or(e,n,Vn)}return K({},e,a)}var _r=["onMouseLeave","refKey","ref"],Kr=["item","index","refKey","ref","onMouseMove","onMouseDown","onClick","onPress","disabled"],Br=["onClick","onPress","refKey","ref"],$r=["onKeyDown","onChange","onInput","onBlur","onChangeText","onClick","refKey","ref"];oe.stateChangeTypes=Vn;function oe(e){e===void 0&&(e={});var n=K({},Rr,e),i=n.items,t=n.scrollIntoView,r=n.environment,s=n.getA11yStatusMessage,a=jr(Pr,n,Ar,Dr),o=a[0],l=a[1],c=o.isOpen,d=o.highlightedIndex,f=o.selectedItem,u=o.inputValue,v=p.useRef(null),m=p.useRef({}),g=p.useRef(null),h=p.useRef(null),M=Ue(),b=Fr(n),k=p.useRef(),E=dt({state:o,props:n}),P=p.useCallback(function(V){return m.current[b.getItemId(V)]},[b]);gn(s,o,[c,d,f,u],r);var _=kr({menuElement:v.current,highlightedIndex:d,isOpen:c,itemRefs:m,scrollIntoView:t,getItemNodeFromIndex:P});p.useEffect(function(){var V=Ie(n,"isOpen");V&&g.current&&g.current.focus()},[]),p.useEffect(function(){M||(k.current=i.length)});var N=Er(r,p.useCallback(function(){E.current.state.isOpen&&l({type:qe,selectItem:!1})},[l,E]),p.useMemo(function(){return[v,h,g]},[v.current,h.current,g.current])),O=vn();p.useEffect(function(){c||(m.current={})},[c]),p.useEffect(function(){var V;!c||!(r!=null&&r.document)||!(g!=null&&(V=g.current)!=null&&V.focus)||r.document.activeElement!==g.current&&g.current.focus()},[c,r]);var I=p.useMemo(function(){return{ArrowDown:function(C){C.preventDefault(),l({type:ft,altKey:C.altKey})},ArrowUp:function(C){C.preventDefault(),l({type:ht,altKey:C.altKey})},Home:function(C){E.current.state.isOpen&&(C.preventDefault(),l({type:vt}))},End:function(C){E.current.state.isOpen&&(C.preventDefault(),l({type:gt}))},Escape:function(C){var F=E.current.state;(F.isOpen||F.inputValue||F.selectedItem||F.highlightedIndex>-1)&&(C.preventDefault(),l({type:mt}))},Enter:function(C){var F=E.current.state;!F.isOpen||C.which===229||(C.preventDefault(),l({type:bt}))},PageUp:function(C){E.current.state.isOpen&&(C.preventDefault(),l({type:pt}))},PageDown:function(C){E.current.state.isOpen&&(C.preventDefault(),l({type:yt}))}}},[l,E]),S=p.useCallback(function(V){return K({id:b.labelId,htmlFor:b.inputId},V)},[b]),w=p.useCallback(function(V,C){var F,A=V===void 0?{}:V,B=A.onMouseLeave,L=A.refKey,W=L===void 0?"ref":L,J=A.ref,U=ge(A,_r),X=C===void 0?{}:C;return X.suppressRefError,K((F={},F[W]=pe(J,function(Q){v.current=Q}),F.id=b.menuId,F.role="listbox",F["aria-labelledby"]=U&&U["aria-label"]?void 0:""+b.labelId,F.onMouseLeave=G(B,function(){l({type:yn})}),F),U)},[l,O,b]),j=p.useCallback(function(V){var C,F,A=V===void 0?{}:V,B=A.item,L=A.index,W=A.refKey,J=W===void 0?"ref":W,U=A.ref,X=A.onMouseMove,Q=A.onMouseDown,de=A.onClick;A.onPress;var fe=A.disabled,Ge=ge(A,Kr);fe!==void 0&&console.warn('Passing "disabled" as an argument to getItemProps is not supported anymore. Please use the isItemDisabled prop from useCombobox.');var ne=E.current,Ve=ne.props,Ee=ne.state,ke=hn(B,L,Ve.items,"Pass either item or index to getItemProps!"),Xe=ke[0],re=ke[1],he=Ve.isItemDisabled(Xe,re),se="onClick",le=de,ae=function(){N.isTouchEnd||re===Ee.highlightedIndex||(_.current=!1,l({type:bn,index:re,disabled:he}))},ue=function(){l({type:St,index:re})},Dn=function(Tn){return Tn.preventDefault()};return K((C={},C[J]=pe(U,function(we){we&&(m.current[b.getItemId(re)]=we)}),C["aria-disabled"]=he,C["aria-selected"]=re===Ee.highlightedIndex,C.id=b.getItemId(re),C.role="option",C),!he&&(F={},F[se]=G(le,ue),F),{onMouseMove:G(X,ae),onMouseDown:G(Q,Dn)},Ge)},[l,b,E,N,_]),$=p.useCallback(function(V){var C,F=V===void 0?{}:V,A=F.onClick;F.onPress;var B=F.refKey,L=B===void 0?"ref":B,W=F.ref,J=ge(F,Br),U=E.current.state,X=function(){l({type:In})};return K((C={},C[L]=pe(W,function(Q){h.current=Q}),C["aria-controls"]=b.menuId,C["aria-expanded"]=U.isOpen,C.id=b.toggleButtonId,C.tabIndex=-1,C),!J.disabled&&K({},{onClick:G(A,X)}),J)},[l,E,b]),T=p.useCallback(function(V,C){var F,A=V===void 0?{}:V,B=A.onKeyDown,L=A.onChange,W=A.onInput,J=A.onBlur;A.onChangeText;var U=A.onClick,X=A.refKey,Q=X===void 0?"ref":X,de=A.ref,fe=ge(A,$r),Ge=C===void 0?{}:C;Ge.suppressRefError;var ne=E.current.state,Ve=function(ae){var ue=at(ae);ue&&I[ue]&&I[ue](ae)},Ee=function(ae){l({type:It,inputValue:ae.target.value})},ke=function(ae){if(r!=null&&r.document&&ne.isOpen&&!N.isMouseDown){var ue=ae.relatedTarget===null&&r.document.activeElement!==r.document.body;l({type:qe,selectItem:!ue})}},Xe=function(){l({type:Mt})},re="onChange",he={};if(!fe.disabled){var se;he=(se={},se[re]=G(L,W,Ee),se.onKeyDown=G(B,Ve),se.onBlur=G(J,ke),se.onClick=G(U,Xe),se)}return K((F={},F[Q]=pe(de,function(le){g.current=le}),F["aria-activedescendant"]=ne.isOpen&&ne.highlightedIndex>-1?b.getItemId(ne.highlightedIndex):"",F["aria-autocomplete"]="list",F["aria-controls"]=b.menuId,F["aria-expanded"]=ne.isOpen,F["aria-labelledby"]=fe&&fe["aria-label"]?void 0:b.labelId,F.autoComplete="off",F.id=b.inputId,F.role="combobox",F.value=ne.inputValue,F),he,fe)},[l,b,r,I,E,N,O]),z=p.useCallback(function(){l({type:Mn})},[l]),q=p.useCallback(function(){l({type:wn})},[l]),te=p.useCallback(function(){l({type:Sn})},[l]),D=p.useCallback(function(V){l({type:xn,highlightedIndex:V})},[l]),H=p.useCallback(function(V){l({type:wt,selectedItem:V})},[l]),Y=p.useCallback(function(V){l({type:Fn,inputValue:V})},[l]),Z=p.useCallback(function(){l({type:Cn})},[l]);return{getItemProps:j,getLabelProps:S,getMenuProps:w,getInputProps:T,getToggleButtonProps:$,toggleMenu:z,openMenu:te,closeMenu:q,setHighlightedIndex:D,setInputValue:Y,selectItem:H,reset:Z,highlightedIndex:d,isOpen:c,selectedItem:f,inputValue:u}}var En={activeIndex:-1,selectedItems:[]};function Zt(e,n){return Ie(e,n,En)}function en(e,n){return ie(e,n,En)}function Nr(e){var n=Zt(e,"activeIndex"),i=Zt(e,"selectedItems");return{activeIndex:n,selectedItems:i}}function tn(e){if(e.shiftKey||e.metaKey||e.ctrlKey||e.altKey)return!1;var n=e.target;return!(n instanceof HTMLInputElement&&n.value!==""&&(n.selectionStart!==0||n.selectionEnd!==0))}function Lr(e,n){return e.selectedItems===n.selectedItems&&e.activeIndex===n.activeIndex}Be.stateReducer,Be.itemToKey,Be.environment,x.array,x.array,x.array,x.func,x.number,x.number,x.number,x.func,x.func,x.string,x.string;var Hr={itemToKey:Fe.itemToKey,stateReducer:Fe.stateReducer,environment:Fe.environment,keyNavigationNext:"ArrowRight",keyNavigationPrevious:"ArrowLeft"},Ft=0,Ct=1,Vt=2,Et=3,kt=4,Dt=5,Tt=6,Ot=7,At=8,jt=9,Rt=10,Pt=11,_t=12,Wr=Object.freeze({__proto__:null,DropdownClick:Ot,DropdownKeyDownBackspace:Tt,DropdownKeyDownNavigationPrevious:Dt,FunctionAddSelectedItem:At,FunctionRemoveSelectedItem:jt,FunctionReset:_t,FunctionSetActiveIndex:Pt,FunctionSetSelectedItems:Rt,SelectedItemClick:Ft,SelectedItemKeyDownBackspace:Vt,SelectedItemKeyDownDelete:Ct,SelectedItemKeyDownNavigationNext:Et,SelectedItemKeyDownNavigationPrevious:kt});function qr(e,n){var i=n.type,t=n.index,r=n.props,s=n.selectedItem,a=e.activeIndex,o=e.selectedItems,l;switch(i){case Ft:l={activeIndex:t};break;case kt:l={activeIndex:a-1<0?0:a-1};break;case Et:l={activeIndex:a+1>=o.length?-1:a+1};break;case Vt:case Ct:{if(a<0)break;var c=a;o.length===1?c=-1:a===o.length-1&&(c=o.length-2),l=K({selectedItems:[].concat(o.slice(0,a),o.slice(a+1))},{activeIndex:c});break}case Dt:l={activeIndex:o.length-1};break;case Tt:l={selectedItems:o.slice(0,o.length-1)};break;case At:l={selectedItems:[].concat(o,[s])};break;case Ot:l={activeIndex:-1};break;case jt:{var d=a,f=o.findIndex(function(m){return r.itemToKey(m)===r.itemToKey(s)});if(f<0)break;o.length===1?d=-1:f===o.length-1&&(d=o.length-2),l={selectedItems:[].concat(o.slice(0,f),o.slice(f+1)),activeIndex:d};break}case Rt:{var u=n.selectedItems;l={selectedItems:u};break}case Pt:{var v=n.activeIndex;l={activeIndex:v};break}case _t:l={activeIndex:en(r,"activeIndex"),selectedItems:en(r,"selectedItems")};break;default:throw new Error("Reducer called without proper action type.")}return K({},e,l)}var zr=["refKey","ref","onClick","onKeyDown","selectedItem","index"],Ur=["refKey","ref","onKeyDown","onClick","preventKeyAction"];ve.stateChangeTypes=Wr;function ve(e){e===void 0&&(e={});var n=K({},Hr,e),i=n.getA11yStatusMessage,t=n.environment,r=n.keyNavigationNext,s=n.keyNavigationPrevious,a=Cr(qr,n,Nr,Lr),o=a[0],l=a[1],c=o.activeIndex,d=o.selectedItems,f=Ue(),u=p.useRef(null),v=p.useRef();v.current=[];var m=dt({state:o,props:n});gn(i,o,[c,d],t),p.useEffect(function(){f||(c===-1&&u.current?u.current.focus():v.current[c]&&v.current[c].focus())},[c]);var g=vn(),h=p.useMemo(function(){var I;return I={},I[s]=function(){l({type:kt})},I[r]=function(){l({type:Et})},I.Delete=function(){l({type:Ct})},I.Backspace=function(){l({type:Vt})},I},[l,r,s]),M=p.useMemo(function(){var I;return I={},I[s]=function(S){tn(S)&&l({type:Dt})},I.Backspace=function(w){tn(w)&&l({type:Tt})},I},[l,s]),b=p.useCallback(function(I){var S,w=I===void 0?{}:I,j=w.refKey,$=j===void 0?"ref":j,T=w.ref,z=w.onClick,q=w.onKeyDown,te=w.selectedItem,D=w.index,H=ge(w,zr),Y=m.current.state,Z=hn(te,D,Y.selectedItems,"Pass either item or index to getSelectedItemProps!"),V=Z[1],C=V>-1&&V===Y.activeIndex,F=function(){l({type:Ft,index:V})},A=function(L){var W=at(L);W&&h[W]&&h[W](L)};return K((S={},S[$]=pe(T,function(B){B&&v.current.push(B)}),S.tabIndex=C?0:-1,S.onClick=G(z,F),S.onKeyDown=G(q,A),S),H)},[l,m,h]),k=p.useCallback(function(I,S){var w,j=I===void 0?{}:I,$=j.refKey,T=$===void 0?"ref":$,z=j.ref,q=j.onKeyDown,te=j.onClick,D=j.preventKeyAction,H=D===void 0?!1:D,Y=ge(j,Ur),Z=S===void 0?{}:S;Z.suppressRefError;var V=function(A){var B=at(A);B&&M[B]&&M[B](A)},C=function(){l({type:Ot})};return K((w={},w[T]=pe(z,function(F){F&&(u.current=F)}),w),!H&&{onKeyDown:G(q,V),onClick:G(te,C)},Y)},[l,M,g]),E=p.useCallback(function(I){l({type:At,selectedItem:I})},[l]),P=p.useCallback(function(I){l({type:jt,selectedItem:I})},[l]),_=p.useCallback(function(I){l({type:Rt,selectedItems:I})},[l]),N=p.useCallback(function(I){l({type:Pt,activeIndex:I})},[l]),O=p.useCallback(function(){l({type:_t})},[l]);return{getSelectedItemProps:b,getDropdownProps:k,addSelectedItem:E,removeSelectedItem:P,setSelectedItems:_,setActiveIndex:N,reset:O,selectedItems:d,activeIndex:c}}function Gr(e,n){p.useRef({patches:[],inversePatches:[]});const i=p.useRef(0),t=p.useRef(0);let r=i.current;p.useEffect(()=>{i.current=r,t.current=r}),r+=1,t.current+=1;const[s,a]=p.useState(()=>typeof e=="function"?e():e),o=p.useCallback(l=>{a(c=>jn(c,typeof l=="function"?l:()=>l,n))},[]);return p.useEffect(()=>{}),[s,o]}function kn(e){return e.isMultiple?y.jsx(Xr,{...e}):y.jsx(Yr,{...e})}function Xr({value:e=[],isLoading:n=!1,options:i,defaultSelected:t=[],onChange:r,placeholder:s="Seleccionar...",className:a,hideReset:o=!1,size:l="md"}){const[c,d]=p.useState(t);p.useEffect(()=>{d(t)},[t]);const f=p.useMemo(()=>i.filter(S=>!c.some(w=>w.value===S.value)),[c,i]),u=p.useCallback(S=>{d(S),r(S)},[r]),v=p.useCallback(S=>{const w=c.filter(j=>j.value!==S.value);u(w)},[c,u]),m=p.useCallback(()=>{u([])},[u]),{getSelectedItemProps:g,getDropdownProps:h}=ve({selectedItems:c,defaultSelectedItems:t,onStateChange({selectedItems:S,type:w}){switch(w){case ve.stateChangeTypes.SelectedItemKeyDownBackspace:case ve.stateChangeTypes.SelectedItemKeyDownDelete:case ve.stateChangeTypes.DropdownKeyDownBackspace:case ve.stateChangeTypes.FunctionRemoveSelectedItem:S&&u(S);break}}}),{isOpen:M,getToggleButtonProps:b,getMenuProps:k,getInputProps:E,highlightedIndex:P,getItemProps:_,setInputValue:N}=oe({items:f,itemToString:S=>(S==null?void 0:S.label)??"",defaultHighlightedIndex:0,selectedItem:null,stateReducer(S,w){const{changes:j,type:$}=w;switch($){case oe.stateChangeTypes.InputKeyDownEnter:case oe.stateChangeTypes.ItemClick:return{...j,isOpen:!0,highlightedIndex:0};default:return j}},onStateChange({type:S,selectedItem:w}){switch(S){case oe.stateChangeTypes.InputKeyDownEnter:case oe.stateChangeTypes.ItemClick:case oe.stateChangeTypes.InputBlur:if(w){const j=[...c,w];u(j),N("")}break}}}),O=p.useCallback((S,w)=>y.jsxs("div",{className:"badge badge-primary pr-0",children:[y.jsx("div",{className:"rounded-l-md",...g({selectedItem:S,index:w}),children:S.label}),y.jsx("button",{type:"button",className:"btn btn-xs btn-circle btn-error btn-ghost",onClick:j=>{j.stopPropagation(),v(S)},children:y.jsx(qn,{size:16})})]},`selected-item-${S.value}-${w}`),[g,v]),I=p.useCallback((S,w)=>y.jsx("li",{className:ye("flex cursor-pointer flex-col px-3 py-2 shadow-sm",P===w&&"bg-neutral"),..._({item:S,index:w}),children:y.jsx("span",{children:S.label})},`${S.value}-${w}`),[P,_]);return y.jsxs("div",{className:ye("dropdown",a),children:[y.jsxs("div",{className:"join input h-fit w-full items-center rounded-md p-1",children:[n?y.jsx("span",{className:"loading loading-dots join-item loading-sm"}):y.jsxs("div",{className:"inline-flex w-full flex-wrap items-center gap-1",children:[c.map(O),y.jsx("input",{placeholder:s,className:"input input-ghost flex-1 focus:outline-none",...E(h({preventKeyAction:M}))})]}),y.jsx("button",{className:"btn btn-sm join-item btn-ghost btn-circle",type:"button",onClick:m,disabled:c.length===0,children:y.jsx(sn,{})}),y.jsx("button",{"aria-label":"toggle menu",className:"btn btn-sm join-item btn-ghost btn-circle",type:"button",...b(),children:M?y.jsx(rn,{className:"h-6 w-6"}):y.jsx(nn,{className:"h-6 w-6"})})]}),y.jsx("ul",{className:ye("dropdown-content z-10 w-full rounded-box bg-base-200 shadow-sm",(!M||!f.length)&&"hidden"),...k(),children:M&&f.map(I)})]})}function Yr({value:e=null,isLoading:n=!1,options:i,defaultSelected:t=null,onChange:r,placeholder:s="Seleccionar...",className:a,size:o="md",label:l="Seleccionar...",hideReset:c=!1}){const[d,f]=Gr(i),[u,v]=p.useState((t==null?void 0:t.label)||(e==null?void 0:e.label)||""),m=p.useCallback(O=>{f(I=>{for(let S=0;S<i.length;S++)I[S]=i[S];if(I.length=i.length,O){let S=0;const w=O.toLowerCase();for(let j=0;j<I.length;j++){const $=I[j];$!=null&&$.label.toLowerCase().includes(w)&&(I[S]=$,S++)}I.length=S}})},[i,f]),{isOpen:g,getToggleButtonProps:h,getMenuProps:M,getInputProps:b,highlightedIndex:k,getItemProps:E,reset:P,getLabelProps:_}=oe({items:d,defaultSelectedItem:t||e,inputValue:u,itemToString:O=>(O==null?void 0:O.label)??"",onInputValueChange({inputValue:O}){v(O||""),m(O||"")},onSelectedItemChange:({selectedItem:O})=>{r(O)},onIsOpenChange:({isOpen:O,selectedItem:I})=>{O||v((I==null?void 0:I.label)||"")}});p.useEffect(()=>{f(i)},[i,f]),p.useEffect(()=>{e===null&&P()},[e,P]);const N=p.useCallback((O,I)=>y.jsx("li",{className:ye("flex cursor-pointer flex-col px-3 py-2 shadow-sm",k===I&&"bg-neutral",(e==null?void 0:e.value)===O.value&&"font-bold"),...E({item:O,index:I}),children:y.jsx("span",{children:O.label})},`${O.value}-${I}`),[k,e,E]);return y.jsxs("div",{className:ye("dropdown",a),children:[y.jsx("label",{htmlFor:"combo",className:"label",..._(),children:l}),y.jsxs("div",{className:"join h-fit w-full items-center bg-base-100",children:[n?y.jsx("span",{className:"loading loading-dots join-item loading-sm"}):y.jsx("input",{placeholder:s,className:"input input-sm input-bordered join-item w-full",...b()}),!c&&y.jsx("button",{className:"btn btn-sm join-item",type:"button",onClick:P,disabled:!e,children:y.jsx(sn,{})}),y.jsx("button",{"aria-label":"toggle menu",className:"btn btn-sm join-item",type:"button",...h(),children:g?y.jsx(rn,{className:"h-6 w-6"}):y.jsx(nn,{className:"h-6 w-6"})})]}),y.jsx("ul",{className:ye("dropdown-content z-10 w-full rounded-box bg-base-200 shadow-sm",(!g||!d.length)&&"hidden"),...M(),children:g&&d.map(N)})]})}function Jr({label:e,placeholder:n,options:i,isNumber:t=!1,isMultiple:r=!1}){return y.jsxs(y.Fragment,{children:[y.jsx("label",{htmlFor:"combobox",className:"label",children:e}),r?y.jsx(Zr,{placeholder:n,options:i,isNumber:t}):y.jsx(Qr,{placeholder:n,options:i})]})}function Qr({placeholder:e,options:n,isLoading:i=!1}){const t=Se();return y.jsx(kn,{isMultiple:!1,options:n,onChange:r=>t.handleChange((r==null?void 0:r.value)||""),placeholder:e,isLoading:i,defaultSelected:n.find(r=>r.value===t.state.value)||null})}function Zr({placeholder:e,options:n,isLoading:i=!1}){const t=Se();return y.jsx(kn,{isMultiple:!0,options:n,onChange:r=>t.handleChange(r.map(s=>s.value)),placeholder:e,isLoading:i,defaultSelected:n.filter(r=>t.state.value.includes(r.value))})}function ei({label:e,placeholder:n}){const i=Se(),[t,r]=p.useState(!1);return y.jsxs("fieldset",{className:"fieldset",children:[y.jsx("legend",{className:"fieldset-legend",children:e}),y.jsxs("label",{className:"input w-full",children:[y.jsx(Hn,{size:16}),y.jsx("input",{type:t?"text":"password",className:"grow",placeholder:n,value:i.state.value,onChange:s=>i.handleChange(s.target.value)}),y.jsx("button",{type:"button",className:"btn btn-sm btn-circle",onClick:()=>r(!t),children:t?y.jsx(Nn,{size:16}):y.jsx(Bn,{size:16})})]}),i.state.meta.isTouched&&i.state.meta.errors.length?i.state.meta.errors.map(s=>y.jsx("p",{className:"fieldset-label text-error",children:s.message},s.path)):null]})}function ti({label:e,placeholder:n,options:i,isNumber:t=!1}){var s;const r=Se();return y.jsxs("fieldset",{className:"fieldset",children:[y.jsx("legend",{className:"fieldset-legend",children:e}),y.jsxs("select",{className:"select w-full",value:t?r.state.value:(s=r.state.value)==null?void 0:s.toString(),onChange:a=>r.handleChange(t?Number(a.target.value):a.target.value),children:[y.jsx("option",{disabled:!0,selected:!0,children:n||"Seleccione una opción"}),i.map(a=>y.jsx("option",{value:a.value,children:a.label},a.value))]}),r.state.meta.isTouched&&r.state.meta.errors.length?r.state.meta.errors.flatMap(a=>y.jsx("p",{className:"fieldset-label text-error",children:a.message},a.message)):null]})}function ni({label:e,placeholder:n,type:i="text",prefixComponent:t,suffixComponent:r}){var a;const s=Se();return y.jsxs("fieldset",{className:"fieldset",children:[y.jsx("legend",{className:"fieldset-legend",children:e}),y.jsxs("div",{className:"input w-full",children:[t&&t,y.jsx("input",{type:i,className:"grow",placeholder:n,value:i==="number"?(a=s.state.value)==null?void 0:a.toString():s.state.value,onChange:o=>s.handleChange(i==="number"?Number(o.target.value):o.target.value)}),r&&r]}),s.state.meta.isTouched&&s.state.meta.errors.length?s.state.meta.errors.flatMap(o=>y.jsx("p",{className:"fieldset-label text-error",children:o.message},o.message)):null]})}function ri({label:e,trueLabel:n,falseLabel:i}){const t=Se();return y.jsxs("fieldset",{className:"fieldset",children:[y.jsx("legend",{className:"fieldset-legend",children:e}),y.jsxs("label",{className:"fieldset-label",children:[y.jsx("input",{type:"checkbox",className:"toggle",defaultChecked:!0,checked:t.state.value,onChange:()=>t.handleChange(!t.state.value)}),t.state.value?n:i]}),t.state.meta.isTouched&&t.state.meta.errors.length?t.state.meta.errors.flatMap(r=>y.jsx("p",{className:"fieldset-label text-error",children:r.message},r.message)):null]})}function ii({label:e,className:n="btn btn-neutral"}){const{Subscribe:i}=oi();return y.jsx(i,{selector:t=>({isSubmitting:t.isSubmitting,canSubmit:t.canSubmit}),children:({isSubmitting:t,canSubmit:r})=>y.jsxs("button",{disabled:t||!r,className:n,children:[t&&y.jsx("span",{className:"loading loading-spinner"}),e]})})}const{fieldContext:si,useFieldContext:Se,formContext:ai,useFormContext:oi}=sr(),{useAppForm:di,withForm:fi}=ar({fieldComponents:{FSTextField:ni,FSPasswordField:ei,FSSelectField:ti,FSToggleField:ri,FSComboBoxField:Jr},formComponents:{SubscribeButton:ii},fieldContext:si,formContext:ai});export{Bn as E,di as u,fi as w};
