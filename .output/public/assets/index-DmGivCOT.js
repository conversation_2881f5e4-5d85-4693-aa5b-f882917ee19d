import{u as h,b as g,j as e,y as u,r as p,L as b}from"./main-RY8ZkTMc.js";import{u as j}from"./useQuery-D9VtETml.js";import{g as d}from"./effectErrors-D8W8e9uM.js";import{s as x,T as y}from"./schedule-options-BL0O7AQ8.js";import{C as N,c as v,u as S,g as D,B as C}from"./BasicTable-RsYQtHbn.js";import{c as E,a as T}from"./classes-CraQI9Rs.js";import{u as R}from"./useMutation-D8585ZL-.js";import{A as M}from"./runtimes-CTOS42-v.js";import{S as Q}from"./square-pen-KUfj7VU3.js";import"./createLucideIcon-78bvTjT9.js";import"./queryOptions-C9woPjwX.js";function k(){const s=h(),{schedule:r}=s,a=g(),n=x(s).queryKey;return R({mutationKey:["delete-schedule"],mutationFn:t=>M.runPromise(r.delete(t)),onMutate:async t=>{await a.cancelQueries({queryKey:n});const i=a.getQueryData(n);return i&&a.setQueryData(n,E(i,o=>{const l=o.findIndex(f=>f.id===t);l!==-1&&o.splice(l,1)})),{previousSchedules:i}},onError:(t,i,o)=>{a.setQueryData(n,o==null?void 0:o.previousSchedules)},onSettled:()=>{a.invalidateQueries({queryKey:n})}})}function q({isOpen:s,setIsOpen:r,id:a}){const{mutate:n,isPending:t}=k(),i=()=>{n(a,{onSuccess:()=>{u.success("Horario eliminado exitosamente"),r(!1)},onError:o=>{const{error:l}=d(o);u.error(l.message)}})};return e.jsx("div",{className:T("modal",s&&"modal-open"),children:e.jsxs("div",{className:"modal-box",children:[e.jsx(N,{onClose:()=>r(!1)}),e.jsx("h3",{className:"font-bold text-lg",children:"Eliminar Horario"}),e.jsx("p",{className:"mb-4",children:"¿Estás seguro de que deseas eliminar este horario? Esta acción no se puede deshacer."}),e.jsxs("div",{className:"modal-action",children:[e.jsx("button",{type:"button",className:"btn btn-ghost",onClick:()=>r(!1),disabled:t,children:"Cancelar"}),e.jsx("button",{type:"button",className:"btn btn-error",onClick:i,disabled:t,children:t?e.jsx("span",{className:"loading loading-spinner loading-sm"}):"Eliminar"})]})]})})}const c=v(),m=s=>{const r=Math.floor(s/100),a=s%100;return`${r.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},A=s=>s?new Date(s).toLocaleDateString():"N/A",V=[c.accessor("name",{header:"Nombre",cell:s=>e.jsx("div",{className:"font-bold",children:s.getValue()})}),c.accessor("sessionDuration",{header:"Duración Sesión",cell:s=>`${s.getValue()} min`}),c.accessor("breakDuration",{header:"Duración Descanso",cell:s=>`${s.getValue()} min`}),c.accessor("turns",{header:"Turnos",cell:s=>e.jsx("div",{className:"space-y-1",children:s.getValue().map(r=>e.jsxs("div",{className:"badge badge-outline",children:[r.name,": ",m(r.startTime)," -"," ",m(r.endTime)]},r.id))})}),c.accessor("createdAt",{header:"Fecha Creación",cell:s=>A(s.getValue())}),c.display({header:"Acciones",cell:({row:s})=>{const[r,a]=p.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(b,{to:"/admin/schedules/edit/$id",params:{id:s.original.id},className:"btn btn-circle btn-primary",children:e.jsx(Q,{size:16})}),e.jsx("button",{type:"button",className:"btn btn-circle btn-error",onClick:()=>a(!0),children:e.jsx(y,{size:16})})]}),e.jsx(q,{isOpen:r,setIsOpen:a,id:s.original.id})]})}})];function $({schedules:s}){const r=S({data:s,columns:V,getCoreRowModel:D()});return e.jsx(C,{table:r})}function H(){const s=h(),{data:r,isError:a,error:n,isPending:t}=j(x(s));return p.useEffect(()=>{n&&console.log(d(n).error)},[n]),a?e.jsxs("div",{children:["Error: ",d(n).error.message]}):t?e.jsx("div",{children:"Loading..."}):e.jsx($,{schedules:r})}const J=function(){return e.jsx("div",{className:"container mx-auto",children:e.jsx("div",{className:"card bg-base-300",children:e.jsxs("div",{className:"card-body",children:[e.jsx("div",{children:e.jsx(b,{to:"/admin/schedules/create",className:"btn btn-primary",children:"Nuevo horario"})}),e.jsx(H,{})]})})})};export{J as component};
