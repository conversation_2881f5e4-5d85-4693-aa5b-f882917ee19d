import{u,S as p,a as d,y as h,j as e}from"./main-RY8ZkTMc.js";import{u as x}from"./form-BdoD3Q87.js";import{g as b}from"./effectErrors-D8W8e9uM.js";import{u as g}from"./useMutation-D8585ZL-.js";import{A as j,o as f,p as t,m as i,s as c}from"./runtimes-CTOS42-v.js";import{U as S}from"./user-BHfkra8r.js";import"./classes-CraQI9Rs.js";import"./createLucideIcon-78bvTjT9.js";function v(){const{auth:r}=u();return g({mutationKey:["login"],mutationFn:a=>j.runPromise(r.login(a))})}const m=new p(void 0),N={setUser:r=>m.setState(r),clearUser:()=>m.setState(void 0)},w=f({username:t(c("Debe ingresar una cuenta"),i(4,"Debe tener al menos 4 caracteres")),password:t(c("Debe ingresar su contraseña"),i(4,"Debe tener al menos 4 caracteres"))});function A(){const{mutateAsync:r}=v(),a=d(),o=x({defaultValues:{username:"",password:""},onSubmit:async({value:s})=>{r(s,{onSuccess(n){N.setUser(n.user),a({to:"/admin"})},onError(n){const l=b(n);h.error(l.error.message)}})},validators:{onChange:w}});return e.jsx("form",{onSubmit:s=>{s.preventDefault(),o.handleSubmit()},children:e.jsxs(o.AppForm,{children:[e.jsx(o.AppField,{name:"username",children:({FSTextField:s})=>e.jsx(s,{label:"Usuario o Correo",placeholder:"<EMAIL>",prefixComponent:e.jsx(S,{size:16})})}),e.jsx(o.AppField,{name:"password",children:({FSPasswordField:s})=>e.jsx(s,{label:"Contraseña",placeholder:"Contraseña"})}),e.jsx(o.SubscribeButton,{label:"Iniciar sesión",className:"btn btn-neutral mt-4"})]})})}const I=function(){return e.jsx("div",{className:"hero min-h-screen bg-base-200",children:e.jsxs("div",{className:"hero-content flex-col lg:flex-row-reverse",children:[e.jsxs("div",{className:"text-center lg:text-left",children:[e.jsx("h1",{className:"font-bold text-5xl",children:"Ingresa ahora!"}),e.jsx("p",{className:"py-6",children:"Sistema de administración de horarios y sesiones psicológicas"})]}),e.jsx("div",{className:"card w-full max-w-sm shrink-0 bg-base-100 shadow-2xl",children:e.jsx("div",{className:"card-body",children:e.jsx(A,{})})})]})})};export{I as component};
