import{x as vo}from"./main-RY8ZkTMc.js";const w={Remove:"remove",Replace:"replace",Add:"add"},to=Symbol.for("__MUTATIVE_PROXY_DRAFT__"),xo=Symbol("__MUTATIVE_RAW_RETURN_SYMBOL__"),me=Symbol.iterator,D={mutable:"mutable",immutable:"immutable"},Ce={};function ne(e,o){return e instanceof Map?e.has(o):Object.prototype.hasOwnProperty.call(e,o)}function Ye(e,o){if(o in e){let t=Reflect.getPrototypeOf(e);for(;t;){const r=Reflect.getOwnPropertyDescriptor(t,o);if(r)return r;t=Reflect.getPrototypeOf(t)}}}function _e(e){return Object.getPrototypeOf(e)===Set.prototype}function Ie(e){return Object.getPrototypeOf(e)===Map.prototype}function T(e){var o;return(o=e.copy)!==null&&o!==void 0?o:e.original}function q(e){return!!y(e)}function y(e){return typeof e!="object"?null:e==null?void 0:e[to]}function Te(e){var o;const t=y(e);return t?(o=t.copy)!==null&&o!==void 0?o:t.original:e}function G(e,o){if(!e||typeof e!="object")return!1;let t;return Object.getPrototypeOf(e)===Object.prototype||Array.isArray(e)||e instanceof Map||e instanceof Set||!!(o!=null&&o.mark)&&((t=o.mark(e,D))===D.immutable||typeof t=="function")}function ro(e,o=[]){if(Object.hasOwnProperty.call(e,"key")){const t=e.parent.copy,r=y(B(t,e.key));if(r!==null&&(r==null?void 0:r.original)!==e.original)return null;const n=e.parent.type===3,a=n?Array.from(e.parent.setMap.keys()).indexOf(e.key):e.key;if(!(n&&t.size>a||ne(t,a)))return null;o.push(a)}if(e.parent)return ro(e.parent,o);o.reverse();try{ko(e.copy,o)}catch{return null}return o}function X(e){return Array.isArray(e)?1:e instanceof Map?2:e instanceof Set?3:0}function B(e,o){return X(e)===2?e.get(o):e[o]}function ie(e,o,t){X(e)===2?e.set(o,t):e[o]=t}function xe(e,o){const t=y(e);return(t?T(t):e)[o]}function W(e,o){return e===o?e!==0||1/e===1/o:e!==e&&o!==o}function ze(e){if(e)for(;e.finalities.revoke.length>0;)e.finalities.revoke.pop()()}function Y(e,o){return o?e:[""].concat(e).map(t=>{const r=`${t}`;return r.indexOf("/")===-1&&r.indexOf("~")===-1?r:r.replace(/~/g,"~0").replace(/\//g,"~1")}).join("/")}function ko(e,o){for(let t=0;t<o.length-1;t+=1){const r=o[t];if(e=B(X(e)===3?Array.from(e):e,r),typeof e!="object")throw new Error(`Cannot resolve patch at '${o.join("/")}'.`)}return e}function Mo(e){const o=Object.create(Object.getPrototypeOf(e));return Reflect.ownKeys(e).forEach(t=>{let r=Reflect.getOwnPropertyDescriptor(e,t);if(r.enumerable&&r.configurable&&r.writable){o[t]=e[t];return}r.writable||(r.writable=!0,r.configurable=!0),(r.get||r.set)&&(r={configurable:!0,writable:!0,enumerable:r.enumerable,value:e[t]}),Reflect.defineProperty(o,t,r)}),o}const zo=Object.prototype.propertyIsEnumerable;function no(e,o){let t;if(Array.isArray(e))return Array.prototype.concat.call(e);if(e instanceof Set){if(!_e(e)){const r=Object.getPrototypeOf(e).constructor;return new r(e.values())}return Set.prototype.difference?Set.prototype.difference.call(e,new Set):new Set(e.values())}else if(e instanceof Map){if(!Ie(e)){const r=Object.getPrototypeOf(e).constructor;return new r(e)}return new Map(e)}else if(o!=null&&o.mark&&(t=o.mark(e,D),t!==void 0)&&t!==D.mutable){if(t===D.immutable)return Mo(e);if(typeof t=="function"){if(o.enablePatches||o.enableAutoFreeze)throw new Error("You can't use mark and patches or auto freeze together.");return t()}throw new Error(`Unsupported mark result: ${t}`)}else if(typeof e=="object"&&Object.getPrototypeOf(e)===Object.prototype){const r={};return Object.keys(e).forEach(n=>{r[n]=e[n]}),Object.getOwnPropertySymbols(e).forEach(n=>{zo.call(e,n)&&(r[n]=e[n])}),r}else throw new Error("Please check mark() to ensure that it is a stable marker draftable function.")}function E(e){e.copy||(e.copy=no(e.original,e.options))}function re(e){if(!G(e))return Te(e);if(Array.isArray(e))return e.map(re);if(e instanceof Map){const t=Array.from(e.entries()).map(([r,n])=>[r,re(n)]);if(!Ie(e)){const r=Object.getPrototypeOf(e).constructor;return new r(t)}return new Map(t)}if(e instanceof Set){const t=Array.from(e).map(re);if(!_e(e)){const r=Object.getPrototypeOf(e).constructor;return new r(t)}return new Set(t)}const o=Object.create(Object.getPrototypeOf(e));for(const t in e)o[t]=re(e[t]);return o}function be(e){return q(e)?re(e):e}function V(e){var o;e.assignedMap=(o=e.assignedMap)!==null&&o!==void 0?o:new Map,e.operated||(e.operated=!0,e.parent&&V(e.parent))}function qe(){throw new Error("Cannot modify frozen object")}function Z(e,o,t,r,n){{t=t??new WeakMap,r=r??[],n=n??[];const s=t.has(e)?t.get(e):e;if(r.length>0){const l=r.indexOf(s);if(s&&typeof s=="object"&&l!==-1)throw r[0]===s?new Error("Forbids circular reference"):new Error(`Forbids circular reference: ~/${n.slice(0,l).map((i,c)=>{if(typeof i=="symbol")return`[${i.toString()}]`;const d=r[c];return typeof i=="object"&&(d instanceof Map||d instanceof Set)?Array.from(d.keys()).indexOf(i):i}).join("/")}`);r.push(s),n.push(o)}else r.push(s)}if(Object.isFrozen(e)||q(e)){r.pop(),n.pop();return}switch(X(e)){case 2:for(const[l,i]of e)Z(l,l,t,r,n),Z(i,l,t,r,n);e.set=e.clear=e.delete=qe;break;case 3:for(const l of e)Z(l,l,t,r,n);e.add=e.clear=e.delete=qe;break;case 1:Object.freeze(e);let s=0;for(const l of e)Z(l,s,t,r,n),s+=1;break;default:Object.freeze(e),Object.keys(e).forEach(l=>{const i=e[l];Z(i,l,t,r,n)})}r.pop(),n.pop()}function De(e,o){const t=X(e);if(t===0)Reflect.ownKeys(e).forEach(r=>{o(r,e[r],e)});else if(t===1){let r=0;for(const n of e)o(r,n,e),r+=1}else e.forEach((r,n)=>o(n,r,e))}function so(e,o,t){if(q(e)||!G(e,t)||o.has(e)||Object.isFrozen(e))return;const r=e instanceof Set,n=r?new Map:void 0;if(o.add(e),De(e,(a,s)=>{var l;if(q(s)){const i=y(s);E(i);const c=!((l=i.assignedMap)===null||l===void 0)&&l.size||i.operated?i.copy:i.original;ie(r?n:e,a,c)}else so(s,o,t)}),n){const a=e,s=Array.from(a);a.clear(),s.forEach(l=>{a.add(n.has(l)?n.get(l):l)})}}function So(e,o){const t=e.type===3?e.setMap:e.copy;e.finalities.revoke.length>1&&e.assignedMap.get(o)&&t&&so(B(t,o),e.finalities.handledSet,e.options)}function Se(e){e.type===3&&e.copy&&(e.copy.clear(),e.setMap.forEach(o=>{e.copy.add(Te(o))}))}function Re(e,o,t,r){if(e.operated&&e.assignedMap&&e.assignedMap.size>0&&!e.finalized){if(t&&r){const a=ro(e);a&&o(e,a,t,r)}e.finalized=!0}}function Fe(e,o,t,r){const n=y(t);n&&(n.callbacks||(n.callbacks=[]),n.callbacks.push((a,s)=>{var l;const i=e.type===3?e.setMap:e.copy;if(W(B(i,o),t)){let c=n.original;n.copy&&(c=n.copy),Se(e),Re(e,r,a,s),e.options.enableAutoFreeze&&(e.options.updatedValues=(l=e.options.updatedValues)!==null&&l!==void 0?l:new WeakMap,e.options.updatedValues.set(c,n.original)),ie(i,o,c)}}),e.options.enableAutoFreeze&&n.finalities!==e.finalities&&(e.options.enableAutoFreeze=!1)),G(t,e.options)&&e.finalities.draft.push(()=>{const a=e.type===3?e.setMap:e.copy;W(B(a,o),t)&&So(e,o)})}function Ro(e,o,t,r,n){let{original:a,assignedMap:s,options:l}=e,i=e.copy;i.length<a.length&&([a,i]=[i,a],[t,r]=[r,t]);for(let c=0;c<a.length;c+=1)if(s.get(c.toString())&&i[c]!==a[c]){const d=o.concat([c]),b=Y(d,n);t.push({op:w.Replace,path:b,value:be(i[c])}),r.push({op:w.Replace,path:b,value:be(a[c])})}for(let c=a.length;c<i.length;c+=1){const d=o.concat([c]),b=Y(d,n);t.push({op:w.Add,path:b,value:be(i[c])})}if(a.length<i.length){const{arrayLengthAssignment:c=!0}=l.enablePatches;if(c){const d=o.concat(["length"]),b=Y(d,n);r.push({op:w.Replace,path:b,value:a.length})}else for(let d=i.length;a.length<d;d-=1){const b=o.concat([d-1]),O=Y(b,n);r.push({op:w.Remove,path:O})}}}function Oo({original:e,copy:o,assignedMap:t},r,n,a,s){t.forEach((l,i)=>{const c=B(e,i),d=be(B(o,i)),b=l?ne(e,i)?w.Replace:w.Add:w.Remove;if(W(c,d)&&b===w.Replace)return;const O=r.concat(i),S=Y(O,s);n.push(b===w.Remove?{op:b,path:S}:{op:b,path:S,value:d}),a.push(b===w.Add?{op:w.Remove,path:S}:b===w.Remove?{op:w.Add,path:S,value:c}:{op:w.Replace,path:S,value:c})})}function Ao({original:e,copy:o},t,r,n,a){let s=0;e.forEach(l=>{if(!o.has(l)){const i=t.concat([s]),c=Y(i,a);r.push({op:w.Remove,path:c,value:l}),n.unshift({op:w.Add,path:c,value:l})}s+=1}),s=0,o.forEach(l=>{if(!e.has(l)){const i=t.concat([s]),c=Y(i,a);r.push({op:w.Add,path:c,value:l}),n.unshift({op:w.Remove,path:c,value:l})}s+=1})}function se(e,o,t,r){const{pathAsArray:n=!0}=e.options.enablePatches;switch(e.type){case 0:case 2:return Oo(e,o,t,r,n);case 1:return Ro(e,o,t,r,n);case 3:return Ao(e,o,t,r,n)}}const he=(e,o,t=!1)=>{if(typeof e=="object"&&e!==null&&(!G(e,o)||t))throw new Error("Strict mode: Mutable data cannot be accessed directly, please use 'unsafe(callback)' wrap.")},Oe={get size(){return T(y(this)).size},has(e){return T(y(this)).has(e)},set(e,o){const t=y(this),r=T(t);return(!r.has(e)||!W(r.get(e),o))&&(E(t),V(t),t.assignedMap.set(e,!0),t.copy.set(e,o),Fe(t,e,o,se)),this},delete(e){if(!this.has(e))return!1;const o=y(this);return E(o),V(o),o.original.has(e)?o.assignedMap.set(e,!1):o.assignedMap.delete(e),o.copy.delete(e),!0},clear(){const e=y(this);if(this.size){E(e),V(e),e.assignedMap=new Map;for(const[o]of e.original)e.assignedMap.set(o,!1);e.copy.clear()}},forEach(e,o){const t=y(this);T(t).forEach((r,n)=>{e.call(o,this.get(n),n,this)})},get(e){var o,t;const r=y(this),n=T(r).get(e),a=((t=(o=r.options).mark)===null||t===void 0?void 0:t.call(o,n,D))===D.mutable;if(r.options.strict&&he(n,r.options,a),a||r.finalized||!G(n,r.options)||n!==r.original.get(e))return n;const s=Ce.createDraft({original:n,parentDraft:r,key:e,finalities:r.finalities,options:r.options});return E(r),r.copy.set(e,s),s},keys(){return T(y(this)).keys()},values(){const e=this.keys();return{[me]:()=>this.values(),next:()=>{const o=e.next();return o.done?o:{done:!1,value:this.get(o.value)}}}},entries(){const e=this.keys();return{[me]:()=>this.entries(),next:()=>{const o=e.next();if(o.done)return o;const t=this.get(o.value);return{done:!1,value:[o.value,t]}}}},[me](){return this.entries()}},Po=Reflect.ownKeys(Oe),Xe=(e,o,{isValuesIterator:t})=>()=>{var r,n;const a=o.next();if(a.done)return a;const s=a.value;let l=e.setMap.get(s);const i=y(l),c=((n=(r=e.options).mark)===null||n===void 0?void 0:n.call(r,l,D))===D.mutable;if(e.options.strict&&he(s,e.options,c),!c&&!i&&G(s,e.options)&&!e.finalized&&e.original.has(s)){const d=Ce.createDraft({original:s,parentDraft:e,key:s,finalities:e.finalities,options:e.options});e.setMap.set(s,d),l=d}else i&&(l=i.proxy);return{done:!1,value:t?l:[l,l]}},ye={get size(){return y(this).setMap.size},has(e){const o=y(this);if(o.setMap.has(e))return!0;E(o);const t=y(e);return!!(t&&o.setMap.has(t.original))},add(e){const o=y(this);return this.has(e)||(E(o),V(o),o.assignedMap.set(e,!0),o.setMap.set(e,e),Fe(o,e,e,se)),this},delete(e){if(!this.has(e))return!1;const o=y(this);E(o),V(o);const t=y(e);return t&&o.setMap.has(t.original)?(o.assignedMap.set(t.original,!1),o.setMap.delete(t.original)):(!t&&o.setMap.has(e)?o.assignedMap.set(e,!1):o.assignedMap.delete(e),o.setMap.delete(e))},clear(){if(!this.size)return;const e=y(this);E(e),V(e);for(const o of e.original)e.assignedMap.set(o,!1);e.setMap.clear()},values(){const e=y(this);E(e);const o=e.setMap.keys();return{[Symbol.iterator]:()=>this.values(),next:Xe(e,o,{isValuesIterator:!0})}},entries(){const e=y(this);E(e);const o=e.setMap.keys();return{[Symbol.iterator]:()=>this.entries(),next:Xe(e,o,{isValuesIterator:!1})}},keys(){return this.values()},[me](){return this.values()},forEach(e,o){const t=this.values();let r=t.next();for(;!r.done;)e.call(o,r.value,r.value,this),r=t.next()}};Set.prototype.difference&&Object.assign(ye,{intersection(e){return Set.prototype.intersection.call(new Set(this.values()),e)},union(e){return Set.prototype.union.call(new Set(this.values()),e)},difference(e){return Set.prototype.difference.call(new Set(this.values()),e)},symmetricDifference(e){return Set.prototype.symmetricDifference.call(new Set(this.values()),e)},isSubsetOf(e){return Set.prototype.isSubsetOf.call(new Set(this.values()),e)},isSupersetOf(e){return Set.prototype.isSupersetOf.call(new Set(this.values()),e)},isDisjointFrom(e){return Set.prototype.isDisjointFrom.call(new Set(this.values()),e)}});const jo=Reflect.ownKeys(ye),io=new WeakSet,ao={get(e,o,t){var r,n;const a=(r=e.copy)===null||r===void 0?void 0:r[o];if(a&&io.has(a))return a;if(o===to)return e;let s;if(e.options.mark){const c=o==="size"&&(e.original instanceof Map||e.original instanceof Set)?Reflect.get(e.original,o):Reflect.get(e.original,o,t);if(s=e.options.mark(c,D),s===D.mutable)return e.options.strict&&he(c,e.options,!0),c}const l=T(e);if(l instanceof Map&&Po.includes(o)){if(o==="size")return Object.getOwnPropertyDescriptor(Oe,"size").get.call(e.proxy);const c=Oe[o];if(c)return c.bind(e.proxy)}if(l instanceof Set&&jo.includes(o)){if(o==="size")return Object.getOwnPropertyDescriptor(ye,"size").get.call(e.proxy);const c=ye[o];if(c)return c.bind(e.proxy)}if(!ne(l,o)){const c=Ye(l,o);return c?"value"in c?c.value:(n=c.get)===null||n===void 0?void 0:n.call(e.proxy):void 0}const i=l[o];if(e.options.strict&&he(i,e.options),e.finalized||!G(i,e.options))return i;if(i===xe(e.original,o)){if(E(e),e.copy[o]=Ge({original:e.original[o],parentDraft:e,key:e.type===1?Number(o):o,finalities:e.finalities,options:e.options}),typeof s=="function"){const c=y(e.copy[o]);return E(c),V(c),c.copy}return e.copy[o]}return i},set(e,o,t){var r;if(e.type===3||e.type===2)throw new Error("Map/Set draft does not support any property assignment.");let n;if(e.type===1&&o!=="length"&&!(Number.isInteger(n=Number(o))&&n>=0&&(o===0||n===0||String(n)===String(o))))throw new Error("Only supports setting array indices and the 'length' property.");const a=Ye(T(e),o);if(a!=null&&a.set)return a.set.call(e.proxy,t),!0;const s=xe(T(e),o),l=y(s);return l&&W(l.original,t)?(e.copy[o]=t,e.assignedMap=(r=e.assignedMap)!==null&&r!==void 0?r:new Map,e.assignedMap.set(o,!1),!0):(W(t,s)&&(t!==void 0||ne(e.original,o))||(E(e),V(e),ne(e.original,o)&&W(t,e.original[o])?e.assignedMap.delete(o):e.assignedMap.set(o,!0),e.copy[o]=t,Fe(e,o,t,se)),!0)},has(e,o){return o in T(e)},ownKeys(e){return Reflect.ownKeys(T(e))},getOwnPropertyDescriptor(e,o){const t=T(e),r=Reflect.getOwnPropertyDescriptor(t,o);return r&&{writable:!0,configurable:e.type!==1||o!=="length",enumerable:r.enumerable,value:t[o]}},getPrototypeOf(e){return Reflect.getPrototypeOf(e.original)},setPrototypeOf(){throw new Error("Cannot call 'setPrototypeOf()' on drafts")},defineProperty(){throw new Error("Cannot call 'defineProperty()' on drafts")},deleteProperty(e,o){var t;return e.type===1?ao.set.call(this,e,o,void 0,e.proxy):(xe(e.original,o)!==void 0||o in e.original?(E(e),V(e),e.assignedMap.set(o,!1)):(e.assignedMap=(t=e.assignedMap)!==null&&t!==void 0?t:new Map,e.assignedMap.delete(o)),e.copy&&delete e.copy[o],!0)}};function Ge(e){const{original:o,parentDraft:t,key:r,finalities:n,options:a}=e,s=X(o),l={type:s,finalized:!1,parent:t,original:o,copy:null,proxy:null,finalities:n,options:a,setMap:s===3?new Map(o.entries()):void 0};(r||"key"in e)&&(l.key=r);const{proxy:i,revoke:c}=Proxy.revocable(s===1?Object.assign([],l):l,ao);if(n.revoke.push(c),io.add(i),l.proxy=i,t){const d=t;d.finalities.draft.push((b,O)=>{var S,x;const C=y(i);let P=d.type===3?d.setMap:d.copy;const k=B(P,r),M=y(k);if(M){let A=M.original;M.operated&&(A=Te(k)),Se(M),Re(M,se,b,O),d.options.enableAutoFreeze&&(d.options.updatedValues=(S=d.options.updatedValues)!==null&&S!==void 0?S:new WeakMap,d.options.updatedValues.set(A,M.original)),ie(P,r,A)}(x=C.callbacks)===null||x===void 0||x.forEach(A=>{A(b,O)})})}else{const d=y(i);d.finalities.draft.push((b,O)=>{Se(d),Re(d,se,b,O)})}return i}Ce.createDraft=Ge;function Eo(e,o,t,r,n){var a;const s=y(e),l=(a=s==null?void 0:s.original)!==null&&a!==void 0?a:e,i=!!o.length;if(s!=null&&s.operated)for(;s.finalities.draft.length>0;)s.finalities.draft.pop()(t,r);const c=i?o[0]:s?s.operated?s.copy:s.original:e;return s&&ze(s),n&&Z(c,c,s==null?void 0:s.options.updatedValues),[c,t&&i?[{op:w.Replace,path:[],value:o[0]}]:t,r&&i?[{op:w.Replace,path:[],value:l}]:r]}function Co(e,o){var t;const r={draft:[],revoke:[],handledSet:new WeakSet};let n,a;o.enablePatches&&(n=[],a=[]);const l=((t=o.mark)===null||t===void 0?void 0:t.call(o,e,D))===D.mutable||!G(e,o)?e:Ge({original:e,parentDraft:null,finalities:r,options:o});return[l,(i=[])=>{const[c,d,b]=Eo(l,i,n,a,o.enableAutoFreeze);return o.enablePatches?[c,d,b]:c}]}function Ae(e){const{rootDraft:o,value:t,useRawReturn:r=!1,isRoot:n=!0}=e;De(t,(a,s,l)=>{const i=y(s);if(i&&o&&i.finalities===o.finalities){e.isContainDraft=!0;const c=i.original;if(l instanceof Set){const d=Array.from(l);l.clear(),d.forEach(b=>l.add(a===b?c:b))}else ie(l,a,c)}else typeof s=="object"&&s!==null&&(e.value=s,e.isRoot=!1,Ae(e))}),n&&(e.isContainDraft||console.warn("The return value does not contain any draft, please use 'rawReturn()' to wrap the return value to improve performance."),r&&console.warn("The return value contains drafts, please don't use 'rawReturn()' to wrap the return value."))}function lo(e){var o;const t=y(e);if(!G(e,t==null?void 0:t.options))return e;const r=X(e);if(t&&!t.operated)return t.original;let n;function a(){n=r===2?Ie(e)?new Map(e):new(Object.getPrototypeOf(e)).constructor(e):r===3?Array.from(t.setMap.values()):no(e,t==null?void 0:t.options)}if(t){t.finalized=!0;try{a()}finally{t.finalized=!1}}else n=e;if(De(n,(s,l)=>{if(t&&W(B(t.original,s),l))return;const i=lo(l);i!==l&&(n===e&&a(),ie(n,s,i))}),r===3){const s=(o=t==null?void 0:t.original)!==null&&o!==void 0?o:n;return _e(s)?new Set(n):new(Object.getPrototypeOf(s)).constructor(n)}return n}function Je(e){if(!q(e))throw new Error(`current() is only used for Draft, parameter: ${e}`);return lo(e)}const _o=e=>function o(t,r,n){var a,s,l;if(typeof t=="function"&&typeof r!="function")return function(g,...R){return o(g,_=>t.call(this,_,...R),r)};const i=t,c=r;let d=n;if(typeof r!="function"&&(d=r),d!==void 0&&Object.prototype.toString.call(d)!=="[object Object]")throw new Error(`Invalid options: ${d}, 'options' should be an object.`);d=Object.assign(Object.assign({},e),d);const b=q(i)?Je(i):i,O=Array.isArray(d.mark)?(g,R)=>{for(const _ of d.mark){if(typeof _!="function")throw new Error(`Invalid mark: ${_}, 'mark' should be a function.`);const p=_(g,R);if(p)return p}}:d.mark,S=(a=d.enablePatches)!==null&&a!==void 0?a:!1,x=(s=d.strict)!==null&&s!==void 0?s:!1,P={enableAutoFreeze:(l=d.enableAutoFreeze)!==null&&l!==void 0?l:!1,mark:O,strict:x,enablePatches:S};if(!G(b,P)&&typeof b=="object"&&b!==null)throw new Error("Invalid base state: create() only supports plain objects, arrays, Set, Map or using mark() to mark the state as immutable.");const[k,M]=Co(b,P);if(typeof r!="function"){if(!G(b,P))throw new Error("Invalid base state: create() only supports plain objects, arrays, Set, Map or using mark() to mark the state as immutable.");return[k,M]}let A;try{A=c(k)}catch(g){throw ze(y(k)),g}const N=g=>{const R=y(k);if(!q(g)){if(g!==void 0&&!W(g,k)&&(R!=null&&R.operated))throw new Error("Either the value is returned as a new non-draft value, or only the draft is modified without returning any value.");const p=g==null?void 0:g[xo];if(p){const F=p[0];return P.strict&&typeof g=="object"&&g!==null&&Ae({rootDraft:R,value:g,useRawReturn:!0}),M([F])}if(g!==void 0)return typeof g=="object"&&g!==null&&Ae({rootDraft:R,value:g}),M([g])}if(g===k||g===void 0)return M([]);const _=y(g);if(P===_.options){if(_.operated)throw new Error("Cannot return a modified child draft.");return M([Je(g)])}return M([g])};return A instanceof Promise?A.then(N,g=>{throw ze(y(k)),g}):N(A)},bt=_o();Object.prototype.constructor.toString();const Ve="-",Io=e=>{const o=Do(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:s=>{const l=s.split(Ve);return l[0]===""&&l.length!==1&&l.shift(),co(l,o)||To(s)},getConflictingClassGroupIds:(s,l)=>{const i=t[s]||[];return l&&r[s]?[...i,...r[s]]:i}}},co=(e,o)=>{var s;if(e.length===0)return o.classGroupId;const t=e[0],r=o.nextPart.get(t),n=r?co(e.slice(1),r):void 0;if(n)return n;if(o.validators.length===0)return;const a=e.join(Ve);return(s=o.validators.find(({validator:l})=>l(a)))==null?void 0:s.classGroupId},Qe=/^\[(.+)\]$/,To=e=>{if(Qe.test(e)){const o=Qe.exec(e)[1],t=o==null?void 0:o.substring(0,o.indexOf(":"));if(t)return"arbitrary.."+t}},Do=e=>{const{theme:o,classGroups:t}=e,r={nextPart:new Map,validators:[]};for(const n in t)Pe(t[n],r,n,o);return r},Pe=(e,o,t,r)=>{e.forEach(n=>{if(typeof n=="string"){const a=n===""?o:Ze(o,n);a.classGroupId=t;return}if(typeof n=="function"){if(Fo(n)){Pe(n(r),o,t,r);return}o.validators.push({validator:n,classGroupId:t});return}Object.entries(n).forEach(([a,s])=>{Pe(s,Ze(o,a),t,r)})})},Ze=(e,o)=>{let t=e;return o.split(Ve).forEach(r=>{t.nextPart.has(r)||t.nextPart.set(r,{nextPart:new Map,validators:[]}),t=t.nextPart.get(r)}),t},Fo=e=>e.isThemeGetter,Go=e=>{if(e<1)return{get:()=>{},set:()=>{}};let o=0,t=new Map,r=new Map;const n=(a,s)=>{t.set(a,s),o++,o>e&&(o=0,r=t,t=new Map)};return{get(a){let s=t.get(a);if(s!==void 0)return s;if((s=r.get(a))!==void 0)return n(a,s),s},set(a,s){t.has(a)?t.set(a,s):n(a,s)}}},je="!",Ee=":",Vo=Ee.length,No=e=>{const{prefix:o,experimentalParseClassName:t}=e;let r=n=>{const a=[];let s=0,l=0,i=0,c;for(let x=0;x<n.length;x++){let C=n[x];if(s===0&&l===0){if(C===Ee){a.push(n.slice(i,x)),i=x+Vo;continue}if(C==="/"){c=x;continue}}C==="["?s++:C==="]"?s--:C==="("?l++:C===")"&&l--}const d=a.length===0?n:n.substring(i),b=Lo(d),O=b!==d,S=c&&c>i?c-i:void 0;return{modifiers:a,hasImportantModifier:O,baseClassName:b,maybePostfixModifierPosition:S}};if(o){const n=o+Ee,a=r;r=s=>s.startsWith(n)?a(s.substring(n.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:s,maybePostfixModifierPosition:void 0}}if(t){const n=r;r=a=>t({className:a,parseClassName:n})}return r},Lo=e=>e.endsWith(je)?e.substring(0,e.length-1):e.startsWith(je)?e.substring(1):e,$o=e=>{const o=Object.fromEntries(e.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const n=[];let a=[];return r.forEach(s=>{s[0]==="["||o[s]?(n.push(...a.sort(),s),a=[]):a.push(s)}),n.push(...a.sort()),n}},Wo=e=>({cache:Go(e.cacheSize),parseClassName:No(e),sortModifiers:$o(e),...Io(e)}),Bo=/\s+/,Uo=(e,o)=>{const{parseClassName:t,getClassGroupId:r,getConflictingClassGroupIds:n,sortModifiers:a}=o,s=[],l=e.trim().split(Bo);let i="";for(let c=l.length-1;c>=0;c-=1){const d=l[c],{isExternal:b,modifiers:O,hasImportantModifier:S,baseClassName:x,maybePostfixModifierPosition:C}=t(d);if(b){i=d+(i.length>0?" "+i:i);continue}let P=!!C,k=r(P?x.substring(0,C):x);if(!k){if(!P){i=d+(i.length>0?" "+i:i);continue}if(k=r(x),!k){i=d+(i.length>0?" "+i:i);continue}P=!1}const M=a(O).join(":"),A=S?M+je:M,N=A+k;if(s.includes(N))continue;s.push(N);const g=n(k,P);for(let R=0;R<g.length;++R){const _=g[R];s.push(A+_)}i=d+(i.length>0?" "+i:i)}return i};function Ko(){let e=0,o,t,r="";for(;e<arguments.length;)(o=arguments[e++])&&(t=fo(o))&&(r&&(r+=" "),r+=t);return r}const fo=e=>{if(typeof e=="string")return e;let o,t="";for(let r=0;r<e.length;r++)e[r]&&(o=fo(e[r]))&&(t&&(t+=" "),t+=o);return t};function Ho(e,...o){let t,r,n,a=s;function s(i){const c=o.reduce((d,b)=>b(d),e());return t=Wo(c),r=t.cache.get,n=t.cache.set,a=l,l(i)}function l(i){const c=r(i);if(c)return c;const d=Uo(i,t);return n(i,d),d}return function(){return a(Ko.apply(null,arguments))}}const v=e=>{const o=t=>t[e]||[];return o.isThemeGetter=!0,o},uo=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,po=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Yo=/^\d+\/\d+$/,qo=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Xo=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Jo=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,Qo=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Zo=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Q=e=>Yo.test(e),h=e=>!!e&&!Number.isNaN(Number(e)),U=e=>!!e&&Number.isInteger(Number(e)),ke=e=>e.endsWith("%")&&h(e.slice(0,-1)),$=e=>qo.test(e),et=()=>!0,ot=e=>Xo.test(e)&&!Jo.test(e),mo=()=>!1,tt=e=>Qo.test(e),rt=e=>Zo.test(e),nt=e=>!f(e)&&!u(e),st=e=>ee(e,yo,mo),f=e=>uo.test(e),H=e=>ee(e,go,ot),Me=e=>ee(e,dt,h),eo=e=>ee(e,bo,mo),it=e=>ee(e,ho,rt),ue=e=>ee(e,wo,tt),u=e=>po.test(e),te=e=>oe(e,go),at=e=>oe(e,ft),oo=e=>oe(e,bo),lt=e=>oe(e,yo),ct=e=>oe(e,ho),pe=e=>oe(e,wo,!0),ee=(e,o,t)=>{const r=uo.exec(e);return r?r[1]?o(r[1]):t(r[2]):!1},oe=(e,o,t=!1)=>{const r=po.exec(e);return r?r[1]?o(r[1]):t:!1},bo=e=>e==="position"||e==="percentage",ho=e=>e==="image"||e==="url",yo=e=>e==="length"||e==="size"||e==="bg-size",go=e=>e==="length",dt=e=>e==="number",ft=e=>e==="family-name",wo=e=>e==="shadow",ut=()=>{const e=v("color"),o=v("font"),t=v("text"),r=v("font-weight"),n=v("tracking"),a=v("leading"),s=v("breakpoint"),l=v("container"),i=v("spacing"),c=v("radius"),d=v("shadow"),b=v("inset-shadow"),O=v("text-shadow"),S=v("drop-shadow"),x=v("blur"),C=v("perspective"),P=v("aspect"),k=v("ease"),M=v("animate"),A=()=>["auto","avoid","all","avoid-page","page","left","right","column"],N=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],g=()=>[...N(),u,f],R=()=>["auto","hidden","clip","visible","scroll"],_=()=>["auto","contain","none"],p=()=>[u,f,i],F=()=>[Q,"full","auto",...p()],Ne=()=>[U,"none","subgrid",u,f],Le=()=>["auto",{span:["full",U,u,f]},U,u,f],ae=()=>[U,"auto",u,f],$e=()=>["auto","min","max","fr",u,f],ge=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],J=()=>["start","end","center","stretch","center-safe","end-safe"],L=()=>["auto",...p()],K=()=>[Q,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...p()],m=()=>[e,u,f],We=()=>[...N(),oo,eo,{position:[u,f]}],Be=()=>["no-repeat",{repeat:["","x","y","space","round"]}],Ue=()=>["auto","cover","contain",lt,st,{size:[u,f]}],we=()=>[ke,te,H],j=()=>["","none","full",c,u,f],I=()=>["",h,te,H],le=()=>["solid","dashed","dotted","double"],Ke=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],z=()=>[h,ke,oo,eo],He=()=>["","none",x,u,f],ce=()=>["none",h,u,f],de=()=>["none",h,u,f],ve=()=>[h,u,f],fe=()=>[Q,"full",...p()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[$],breakpoint:[$],color:[et],container:[$],"drop-shadow":[$],ease:["in","out","in-out"],font:[nt],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[$],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[$],shadow:[$],spacing:["px",h],text:[$],"text-shadow":[$],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Q,f,u,P]}],container:["container"],columns:[{columns:[h,f,u,l]}],"break-after":[{"break-after":A()}],"break-before":[{"break-before":A()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:g()}],overflow:[{overflow:R()}],"overflow-x":[{"overflow-x":R()}],"overflow-y":[{"overflow-y":R()}],overscroll:[{overscroll:_()}],"overscroll-x":[{"overscroll-x":_()}],"overscroll-y":[{"overscroll-y":_()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:F()}],"inset-x":[{"inset-x":F()}],"inset-y":[{"inset-y":F()}],start:[{start:F()}],end:[{end:F()}],top:[{top:F()}],right:[{right:F()}],bottom:[{bottom:F()}],left:[{left:F()}],visibility:["visible","invisible","collapse"],z:[{z:[U,"auto",u,f]}],basis:[{basis:[Q,"full","auto",l,...p()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[h,Q,"auto","initial","none",f]}],grow:[{grow:["",h,u,f]}],shrink:[{shrink:["",h,u,f]}],order:[{order:[U,"first","last","none",u,f]}],"grid-cols":[{"grid-cols":Ne()}],"col-start-end":[{col:Le()}],"col-start":[{"col-start":ae()}],"col-end":[{"col-end":ae()}],"grid-rows":[{"grid-rows":Ne()}],"row-start-end":[{row:Le()}],"row-start":[{"row-start":ae()}],"row-end":[{"row-end":ae()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":$e()}],"auto-rows":[{"auto-rows":$e()}],gap:[{gap:p()}],"gap-x":[{"gap-x":p()}],"gap-y":[{"gap-y":p()}],"justify-content":[{justify:[...ge(),"normal"]}],"justify-items":[{"justify-items":[...J(),"normal"]}],"justify-self":[{"justify-self":["auto",...J()]}],"align-content":[{content:["normal",...ge()]}],"align-items":[{items:[...J(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...J(),{baseline:["","last"]}]}],"place-content":[{"place-content":ge()}],"place-items":[{"place-items":[...J(),"baseline"]}],"place-self":[{"place-self":["auto",...J()]}],p:[{p:p()}],px:[{px:p()}],py:[{py:p()}],ps:[{ps:p()}],pe:[{pe:p()}],pt:[{pt:p()}],pr:[{pr:p()}],pb:[{pb:p()}],pl:[{pl:p()}],m:[{m:L()}],mx:[{mx:L()}],my:[{my:L()}],ms:[{ms:L()}],me:[{me:L()}],mt:[{mt:L()}],mr:[{mr:L()}],mb:[{mb:L()}],ml:[{ml:L()}],"space-x":[{"space-x":p()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":p()}],"space-y-reverse":["space-y-reverse"],size:[{size:K()}],w:[{w:[l,"screen",...K()]}],"min-w":[{"min-w":[l,"screen","none",...K()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[s]},...K()]}],h:[{h:["screen","lh",...K()]}],"min-h":[{"min-h":["screen","lh","none",...K()]}],"max-h":[{"max-h":["screen","lh",...K()]}],"font-size":[{text:["base",t,te,H]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,u,Me]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ke,f]}],"font-family":[{font:[at,f,o]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,u,f]}],"line-clamp":[{"line-clamp":[h,"none",u,Me]}],leading:[{leading:[a,...p()]}],"list-image":[{"list-image":["none",u,f]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",u,f]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:m()}],"text-color":[{text:m()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...le(),"wavy"]}],"text-decoration-thickness":[{decoration:[h,"from-font","auto",u,H]}],"text-decoration-color":[{decoration:m()}],"underline-offset":[{"underline-offset":[h,"auto",u,f]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:p()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",u,f]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",u,f]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:We()}],"bg-repeat":[{bg:Be()}],"bg-size":[{bg:Ue()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},U,u,f],radial:["",u,f],conic:[U,u,f]},ct,it]}],"bg-color":[{bg:m()}],"gradient-from-pos":[{from:we()}],"gradient-via-pos":[{via:we()}],"gradient-to-pos":[{to:we()}],"gradient-from":[{from:m()}],"gradient-via":[{via:m()}],"gradient-to":[{to:m()}],rounded:[{rounded:j()}],"rounded-s":[{"rounded-s":j()}],"rounded-e":[{"rounded-e":j()}],"rounded-t":[{"rounded-t":j()}],"rounded-r":[{"rounded-r":j()}],"rounded-b":[{"rounded-b":j()}],"rounded-l":[{"rounded-l":j()}],"rounded-ss":[{"rounded-ss":j()}],"rounded-se":[{"rounded-se":j()}],"rounded-ee":[{"rounded-ee":j()}],"rounded-es":[{"rounded-es":j()}],"rounded-tl":[{"rounded-tl":j()}],"rounded-tr":[{"rounded-tr":j()}],"rounded-br":[{"rounded-br":j()}],"rounded-bl":[{"rounded-bl":j()}],"border-w":[{border:I()}],"border-w-x":[{"border-x":I()}],"border-w-y":[{"border-y":I()}],"border-w-s":[{"border-s":I()}],"border-w-e":[{"border-e":I()}],"border-w-t":[{"border-t":I()}],"border-w-r":[{"border-r":I()}],"border-w-b":[{"border-b":I()}],"border-w-l":[{"border-l":I()}],"divide-x":[{"divide-x":I()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":I()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...le(),"hidden","none"]}],"divide-style":[{divide:[...le(),"hidden","none"]}],"border-color":[{border:m()}],"border-color-x":[{"border-x":m()}],"border-color-y":[{"border-y":m()}],"border-color-s":[{"border-s":m()}],"border-color-e":[{"border-e":m()}],"border-color-t":[{"border-t":m()}],"border-color-r":[{"border-r":m()}],"border-color-b":[{"border-b":m()}],"border-color-l":[{"border-l":m()}],"divide-color":[{divide:m()}],"outline-style":[{outline:[...le(),"none","hidden"]}],"outline-offset":[{"outline-offset":[h,u,f]}],"outline-w":[{outline:["",h,te,H]}],"outline-color":[{outline:m()}],shadow:[{shadow:["","none",d,pe,ue]}],"shadow-color":[{shadow:m()}],"inset-shadow":[{"inset-shadow":["none",b,pe,ue]}],"inset-shadow-color":[{"inset-shadow":m()}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:m()}],"ring-offset-w":[{"ring-offset":[h,H]}],"ring-offset-color":[{"ring-offset":m()}],"inset-ring-w":[{"inset-ring":I()}],"inset-ring-color":[{"inset-ring":m()}],"text-shadow":[{"text-shadow":["none",O,pe,ue]}],"text-shadow-color":[{"text-shadow":m()}],opacity:[{opacity:[h,u,f]}],"mix-blend":[{"mix-blend":[...Ke(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Ke()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[h]}],"mask-image-linear-from-pos":[{"mask-linear-from":z()}],"mask-image-linear-to-pos":[{"mask-linear-to":z()}],"mask-image-linear-from-color":[{"mask-linear-from":m()}],"mask-image-linear-to-color":[{"mask-linear-to":m()}],"mask-image-t-from-pos":[{"mask-t-from":z()}],"mask-image-t-to-pos":[{"mask-t-to":z()}],"mask-image-t-from-color":[{"mask-t-from":m()}],"mask-image-t-to-color":[{"mask-t-to":m()}],"mask-image-r-from-pos":[{"mask-r-from":z()}],"mask-image-r-to-pos":[{"mask-r-to":z()}],"mask-image-r-from-color":[{"mask-r-from":m()}],"mask-image-r-to-color":[{"mask-r-to":m()}],"mask-image-b-from-pos":[{"mask-b-from":z()}],"mask-image-b-to-pos":[{"mask-b-to":z()}],"mask-image-b-from-color":[{"mask-b-from":m()}],"mask-image-b-to-color":[{"mask-b-to":m()}],"mask-image-l-from-pos":[{"mask-l-from":z()}],"mask-image-l-to-pos":[{"mask-l-to":z()}],"mask-image-l-from-color":[{"mask-l-from":m()}],"mask-image-l-to-color":[{"mask-l-to":m()}],"mask-image-x-from-pos":[{"mask-x-from":z()}],"mask-image-x-to-pos":[{"mask-x-to":z()}],"mask-image-x-from-color":[{"mask-x-from":m()}],"mask-image-x-to-color":[{"mask-x-to":m()}],"mask-image-y-from-pos":[{"mask-y-from":z()}],"mask-image-y-to-pos":[{"mask-y-to":z()}],"mask-image-y-from-color":[{"mask-y-from":m()}],"mask-image-y-to-color":[{"mask-y-to":m()}],"mask-image-radial":[{"mask-radial":[u,f]}],"mask-image-radial-from-pos":[{"mask-radial-from":z()}],"mask-image-radial-to-pos":[{"mask-radial-to":z()}],"mask-image-radial-from-color":[{"mask-radial-from":m()}],"mask-image-radial-to-color":[{"mask-radial-to":m()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":N()}],"mask-image-conic-pos":[{"mask-conic":[h]}],"mask-image-conic-from-pos":[{"mask-conic-from":z()}],"mask-image-conic-to-pos":[{"mask-conic-to":z()}],"mask-image-conic-from-color":[{"mask-conic-from":m()}],"mask-image-conic-to-color":[{"mask-conic-to":m()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:We()}],"mask-repeat":[{mask:Be()}],"mask-size":[{mask:Ue()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",u,f]}],filter:[{filter:["","none",u,f]}],blur:[{blur:He()}],brightness:[{brightness:[h,u,f]}],contrast:[{contrast:[h,u,f]}],"drop-shadow":[{"drop-shadow":["","none",S,pe,ue]}],"drop-shadow-color":[{"drop-shadow":m()}],grayscale:[{grayscale:["",h,u,f]}],"hue-rotate":[{"hue-rotate":[h,u,f]}],invert:[{invert:["",h,u,f]}],saturate:[{saturate:[h,u,f]}],sepia:[{sepia:["",h,u,f]}],"backdrop-filter":[{"backdrop-filter":["","none",u,f]}],"backdrop-blur":[{"backdrop-blur":He()}],"backdrop-brightness":[{"backdrop-brightness":[h,u,f]}],"backdrop-contrast":[{"backdrop-contrast":[h,u,f]}],"backdrop-grayscale":[{"backdrop-grayscale":["",h,u,f]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[h,u,f]}],"backdrop-invert":[{"backdrop-invert":["",h,u,f]}],"backdrop-opacity":[{"backdrop-opacity":[h,u,f]}],"backdrop-saturate":[{"backdrop-saturate":[h,u,f]}],"backdrop-sepia":[{"backdrop-sepia":["",h,u,f]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":p()}],"border-spacing-x":[{"border-spacing-x":p()}],"border-spacing-y":[{"border-spacing-y":p()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",u,f]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[h,"initial",u,f]}],ease:[{ease:["linear","initial",k,u,f]}],delay:[{delay:[h,u,f]}],animate:[{animate:["none",M,u,f]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[C,u,f]}],"perspective-origin":[{"perspective-origin":g()}],rotate:[{rotate:ce()}],"rotate-x":[{"rotate-x":ce()}],"rotate-y":[{"rotate-y":ce()}],"rotate-z":[{"rotate-z":ce()}],scale:[{scale:de()}],"scale-x":[{"scale-x":de()}],"scale-y":[{"scale-y":de()}],"scale-z":[{"scale-z":de()}],"scale-3d":["scale-3d"],skew:[{skew:ve()}],"skew-x":[{"skew-x":ve()}],"skew-y":[{"skew-y":ve()}],transform:[{transform:[u,f,"","none","gpu","cpu"]}],"transform-origin":[{origin:g()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:fe()}],"translate-x":[{"translate-x":fe()}],"translate-y":[{"translate-y":fe()}],"translate-z":[{"translate-z":fe()}],"translate-none":["translate-none"],accent:[{accent:m()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:m()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",u,f]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":p()}],"scroll-mx":[{"scroll-mx":p()}],"scroll-my":[{"scroll-my":p()}],"scroll-ms":[{"scroll-ms":p()}],"scroll-me":[{"scroll-me":p()}],"scroll-mt":[{"scroll-mt":p()}],"scroll-mr":[{"scroll-mr":p()}],"scroll-mb":[{"scroll-mb":p()}],"scroll-ml":[{"scroll-ml":p()}],"scroll-p":[{"scroll-p":p()}],"scroll-px":[{"scroll-px":p()}],"scroll-py":[{"scroll-py":p()}],"scroll-ps":[{"scroll-ps":p()}],"scroll-pe":[{"scroll-pe":p()}],"scroll-pt":[{"scroll-pt":p()}],"scroll-pr":[{"scroll-pr":p()}],"scroll-pb":[{"scroll-pb":p()}],"scroll-pl":[{"scroll-pl":p()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",u,f]}],fill:[{fill:["none",...m()]}],"stroke-w":[{stroke:[h,te,H,Me]}],stroke:[{stroke:["none",...m()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},pt=Ho(ut),ht=(...e)=>pt(vo(e));export{ht as a,bt as c};
