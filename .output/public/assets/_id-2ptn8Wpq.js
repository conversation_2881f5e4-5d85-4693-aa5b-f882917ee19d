import{u as j,b as f,r as v,y as x,j as e,L as b,c as g,R as N}from"./main-RY8ZkTMc.js";import{u as S}from"./useQuery-D9VtETml.js";import{g as h}from"./effectErrors-D8W8e9uM.js";import{s as D,a as y}from"./schedule-options-BL0O7AQ8.js";import{u as T}from"./form-BdoD3Q87.js";import{u as A}from"./useMutation-D8585ZL-.js";import{c as C}from"./classes-CraQI9Rs.js";import{A as k}from"./runtimes-CTOS42-v.js";import{C as E,a as p,T as F,S as w,A as H}from"./index-C7U4vq_b.js";import{F as z}from"./file-text-DYqz-Dos.js";import"./createLucideIcon-78bvTjT9.js";import"./queryOptions-C9woPjwX.js";function I(){const o=j(),{schedule:i}=o,m=f(),l=D(o).queryKey;return A({mutationFn:c=>k.runPromise(i.update(c)),onSuccess:(c,r)=>{m.setQueryData(l,s=>C(s??[],a=>{const t=a.findIndex(n=>n.id===r.id);t!==-1&&(a[t]={...a[t],name:r.name,sessionDuration:r.sessionDuration,breakDuration:r.breakDuration,turns:r.turns.map(n=>{var d;return{id:n.id,name:n.name,startTime:n.startTime,endTime:n.endTime,createdAt:((d=a[t].turns.find(u=>u.id===n.id))==null?void 0:d.createdAt)||null,updatedAt:new Date().toISOString(),deletedAt:null}}),updatedAt:new Date().toISOString()})}))}})}function R({id:o,schedule:i}){const{mutate:m}=I(),[l,c]=v.useState(null),r=T({defaultValues:{...i},validators:{onChange:E},onSubmit:({value:s})=>{m({id:o,name:s.name,sessionDuration:s.sessionDuration,breakDuration:s.breakDuration,turns:s.turns.map((a,t)=>{var n;return{id:((n=i==null?void 0:i.turns[t])==null?void 0:n.id)||"",name:a.name,startTime:a.startTime,endTime:a.endTime}})},{onSuccess:()=>{x.success("Horario actualizado exitosamente"),window.history.back()},onError:a=>{console.log(a);const{error:t}=h(a);x.error(t.message)}})}});return e.jsx("form",{onSubmit:s=>{s.preventDefault(),r.handleSubmit()},children:e.jsx(r.AppForm,{children:e.jsxs("div",{className:"grid grid-cols-1 gap-8",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("fieldset",{className:"fieldset",children:e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsx(r.AppField,{name:"name",children:({FSTextField:s})=>e.jsx(s,{label:"Nombre del Horario",placeholder:"Nombre del Horario",prefixComponent:e.jsx(z,{size:16})})}),e.jsx(r.AppField,{name:"sessionDuration",children:({FSTextField:s})=>e.jsx(s,{label:"Duración de Sesión (minutos)",placeholder:"60",type:"number",prefixComponent:e.jsx(p,{size:16})})}),e.jsx(r.AppField,{name:"breakDuration",children:({FSTextField:s})=>e.jsx(s,{label:"Duración de Descanso (minutos)",placeholder:"15",type:"number",prefixComponent:e.jsx(p,{size:16})})})]})}),e.jsx(F,{form:r,selectedTurnIndex:l,onTurnSelect:c}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(r.SubscribeButton,{label:"Actualizar Horario",className:"btn btn-primary"}),e.jsx(b,{to:"/admin/schedules",className:"btn btn-outline",children:"Cancelar"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"font-semibold text-xl",children:"Vista Previa del Horario"}),e.jsx(r.Subscribe,{selector:s=>[s.values.sessionDuration,s.values.breakDuration],children:([s,a,t])=>{const n=g(r.store,u=>u.values.turns),d=l!==null?n[l]:null;return d?e.jsx(w,{turn:d,sessionDuration:s||60,breakDuration:a||15}):e.jsx("div",{className:"card bg-base-200",children:e.jsx("div",{className:"card-body text-center",children:e.jsx("p",{className:"text-base-content/70",children:"Selecciona un turno para ver la vista previa del horario"})})})}})]})]})})})}function L({id:o}){const i=j(),{data:m,isLoading:l,error:c,isSuccess:r}=S(y(i,o));if(l)return e.jsx("div",{className:"flex h-64 items-center justify-center",children:e.jsx("span",{className:"loading loading-spinner loading-lg"})});if(c)return e.jsx("div",{className:"alert alert-error",children:e.jsxs("span",{children:["Error: ",h(c).error.message]})});if(r)return e.jsx(R,{id:o,schedule:m})}const W=function(){const{id:i}=N.useParams();return e.jsxs("div",{className:"container mx-auto max-w-6xl",children:[e.jsx("div",{className:"mb-6",children:e.jsxs(b,{to:"/admin/schedules",className:"btn btn-ghost",children:[e.jsx(H,{size:16}),"Volver a Horarios"]})}),e.jsx("div",{className:"card bg-base-300",children:e.jsxs("div",{className:"card-body",children:[e.jsx("h2",{className:"card-title mb-6 text-2xl",children:"Editar Horario"}),e.jsx(L,{id:i})]})})]})};export{W as component};
