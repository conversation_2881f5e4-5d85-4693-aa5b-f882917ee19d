import{u as f,b as g,j as e,y as u,r as b}from"./main-RY8ZkTMc.js";import{u as P}from"./useQuery-D9VtETml.js";import{g as j}from"./effectErrors-D8W8e9uM.js";import{q as z}from"./queryOptions-C9woPjwX.js";import{A as C,o as k,n as w,p as h,b as Q,c as U,d as S,u as B,m as y,s as d,l as G,e as H,D as T}from"./runtimes-CTOS42-v.js";import{C as D,c as $,u as J,g as W,B as X}from"./BasicTable-RsYQtHbn.js";import{c as A,a as F}from"./classes-CraQI9Rs.js";import{u as v}from"./useMutation-D8585ZL-.js";import{M as L,a as q,P as I,T as M,b as Z}from"./TextModal-DBwVHEpd.js";import{u as O}from"./form-BdoD3Q87.js";import{U as p}from"./user-BHfkra8r.js";import{C as R}from"./calendar-C7hPlekH.js";import{F as V}from"./file-text-DYqz-Dos.js";import{S as _}from"./square-pen-KUfj7VU3.js";import"./createLucideIcon-78bvTjT9.js";const N=({client:n})=>z({queryKey:["clients"],queryFn:()=>C.runPromise(n.getAll())}),ee=({client:n},a)=>z({queryKey:["clients",a],queryFn:()=>C.runPromise(n.getById(a))});function ne(){const n=f(),{client:a}=n,o=g(),t=N(n).queryKey;return v({mutationFn:s=>C.runPromise(a.delete(s)),onSuccess:(s,r)=>{o.setQueryData(t,l=>A(l??[],i=>{const c=i.findIndex(x=>x.id===r);c!==-1&&i.splice(c,1)}))}})}function se({isOpen:n,setIsOpen:a,client:o}){const{mutate:t}=ne();return e.jsx("div",{className:F("modal",n&&"modal-open"),children:e.jsxs("div",{className:"modal-box",children:[e.jsx(D,{onClose:()=>a(!1)}),e.jsx("h3",{className:"font-bold text-lg",children:"Eliminar cliente"}),e.jsx("p",{children:"¿Estás seguro de que quieres eliminar este cliente?"}),e.jsxs("p",{className:"mt-2 text-gray-600 text-sm",children:["Cliente: ",o.person.name," ",o.person.fatherLastName," ",o.person.motherLastName]}),e.jsxs("div",{className:"modal-action",children:[e.jsx("button",{type:"button",className:"btn btn-primary",onClick:()=>a(!1),children:"Cancelar"}),e.jsx("button",{type:"button",className:"btn btn-error",onClick:()=>{t(o.id,{onSuccess:()=>{u.success("Cliente eliminado"),a(!1)},onError:s=>{console.log(s),u.error("Error al eliminar cliente")}})},children:"Eliminar"})]})]})})}function oe(){const n=f(),{client:a}=n,o=g(),t=N(n).queryKey;return v({mutationFn:s=>C.runPromise(a.update(s)),onSuccess:(s,r)=>{o.setQueryData(t,l=>A(l??[],i=>{const c=i.findIndex(x=>x.id===r.id);c!==-1&&(i[c]={...i[c],person:r.person,updatedAt:new Date().toISOString()})}))}})}const K=k({name:h(d("Debe ingresar un nombre"),y(1,"Debe tener al menos un caracter")),fatherLastName:h(d("Debe ingresar un apellido"),y(1,"Debe tener al menos un caracter")),motherLastName:h(d("Debe ingresar un apellido"),y(1,"Debe tener al menos un caracter")),email:B([h(d("Debe ingresar un email"),H("Debe ingresar un email valido")),G("")]),address:S(d()),phone:S(d()),birthDate:U(d()),gender:Q(),document:h(d("Debe ingresar un documento"),y(8,"Debe tener al menos 8 caracteres")),documentType:w()});function re({setIsOpen:n,client:a}){const{mutate:o}=oe(),{id:t,person:{id:s,...r},...l}=a,i=O({defaultValues:{...r},validators:{onChange:K},onSubmit:({value:x})=>{o({id:t,person:{...x,id:s}},{onSuccess:()=>{u.success("Cliente actualizado"),c()},onError:E=>{console.log(E);const{error:Y}=j(E);u.error(Y.message)}})}});function c(){i.reset(),n(!1)}return{form:i,handleClose:c}}function te({isOpen:n,setIsOpen:a,client:o}){const{form:t,handleClose:s}=re({setIsOpen:a,client:o});return e.jsx("div",{className:F("modal",n&&"modal-open"),children:e.jsxs("div",{className:"modal-box",children:[e.jsx(D,{onClose:s}),e.jsx("h3",{className:"font-bold text-lg",children:"Editar Cliente"}),e.jsx("form",{onSubmit:r=>{r.preventDefault(),t.handleSubmit()},children:e.jsxs(t.AppForm,{children:[e.jsxs("fieldset",{className:"fieldset",children:[e.jsx(t.AppField,{name:"name",children:({FSTextField:r})=>e.jsx(r,{label:"Nombre",placeholder:"Nombre",prefixComponent:e.jsx(p,{size:16})})}),e.jsx(t.AppField,{name:"fatherLastName",children:({FSTextField:r})=>e.jsx(r,{label:"Apellido Paterno",placeholder:"Apellido Paterno",prefixComponent:e.jsx(p,{size:16})})}),e.jsx(t.AppField,{name:"motherLastName",children:({FSTextField:r})=>e.jsx(r,{label:"Apellido Materno",placeholder:"Apellido Materno",prefixComponent:e.jsx(p,{size:16})})}),e.jsx(t.AppField,{name:"email",children:({FSTextField:r})=>e.jsx(r,{label:"Correo Electrónico",placeholder:"<EMAIL>",prefixComponent:e.jsx(L,{size:16})})}),e.jsx(t.AppField,{name:"address",children:({FSTextField:r})=>e.jsx(r,{label:"Dirección",placeholder:"Dirección",prefixComponent:e.jsx(q,{size:16})})}),e.jsx(t.AppField,{name:"phone",children:({FSTextField:r})=>e.jsx(r,{label:"Teléfono",placeholder:"Teléfono",prefixComponent:e.jsx(I,{size:16})})}),e.jsx(t.AppField,{name:"birthDate",children:({FSTextField:r})=>e.jsx(r,{label:"Fecha de Nacimiento",placeholder:"YYYY-MM-DD",type:"date",prefixComponent:e.jsx(R,{size:16})})}),e.jsx(t.AppField,{name:"document",children:({FSTextField:r})=>e.jsx(r,{label:"Documento",placeholder:"Número de Documento",prefixComponent:e.jsx(V,{size:16})})}),e.jsx(t.AppField,{name:"documentType",children:({FSSelectField:r})=>e.jsx(r,{label:"Tipo de Documento",placeholder:"Tipo de Documento",isNumber:!0,options:[{value:0,label:"DNI"},{value:1,label:"Pasaporte"},{value:2,label:"RUC"}]})}),e.jsx(t.AppField,{name:"gender",children:({FSToggleField:r})=>e.jsx(r,{label:"Género",trueLabel:"Masculino",falseLabel:"Femenino"})})]}),e.jsx("div",{className:"modal-action",children:e.jsx("button",{type:"submit",className:"btn btn-primary",children:"Actualizar"})})]})})]})})}function ae({isOpen:n,setIsOpen:a,id:o}){const t=f(),{data:s,isError:r,error:l,isPending:i}=P({...ee(t,o),enabled:n});return b.useEffect(()=>{l&&console.log(l)},[l]),i?e.jsx(M,{text:"Cargando..."}):r?e.jsx(M,{text:"No se pudo cargar el cliente",title:j(l).error.code.toString()}):e.jsx(te,{isOpen:n,setIsOpen:a,client:s})}const m=$(),le=[m.accessor("person.name",{header:"Nombre",cell:n=>n.getValue()}),m.accessor("person.fatherLastName",{header:"Apellido Paterno",cell:n=>n.getValue()}),m.accessor("person.motherLastName",{header:"Apellido Materno",cell:n=>n.getValue()}),m.accessor("person.email",{header:"Email",cell:n=>n.getValue()||"-"}),m.accessor("person.phone",{header:"Teléfono",cell:n=>n.getValue()||"-"}),m.accessor("person.birthDate",{header:"Fecha de nacimiento",cell:n=>n.getValue()||"-"}),m.accessor("person.document",{header:"Documento",cell:n=>{const a=n.row.original.person.documentType,o=n.getValue();return`${a===T.DNI?"DNI":a===T.PASAPORTE?"Pasaporte":"RUC"}: ${o}`}}),m.display({id:"actions",header:"Acciones",cell:({row:n})=>{const[a,o]=b.useState(!1),[t,s]=b.useState(!1),r=n.original;return e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{type:"button",className:"btn btn-sm btn-primary",onClick:()=>o(!0),children:e.jsx(_,{size:16})}),e.jsx("button",{type:"button",className:"btn btn-sm btn-error",onClick:()=>s(!0),children:e.jsx(Z,{size:16})}),e.jsx(ae,{isOpen:a,setIsOpen:o,id:r.id}),e.jsx(se,{isOpen:t,setIsOpen:s,client:r})]})}})];function ie({clients:n}){const a=J({data:n,columns:le,getCoreRowModel:W()});return e.jsx(X,{table:a})}function ce(){const n=f(),{data:a,isError:o,error:t,isPending:s}=P(N(n));return b.useEffect(()=>{t&&console.log(j(t).error)},[t]),o?e.jsxs("div",{children:["Error: ",j(t).error.message]}):s?e.jsx("div",{children:"Loading..."}):e.jsx(ie,{clients:a})}function de(){const n=f(),{client:a}=n,o=g(),t=N(n).queryKey;return v({mutationFn:s=>C.runPromise(a.create(s)),onSuccess:(s,r)=>{o.setQueryData(t,l=>A(l??[],i=>{i.push({id:s,person:r.person,createdAt:new Date().toISOString(),updatedAt:null,deletedAt:null})}))}})}const me={name:"",fatherLastName:"",motherLastName:"",email:"",address:"",phone:"",birthDate:"",gender:!1,document:"",documentType:0};function pe({setIsOpen:n}){const{mutate:a}=de(),o=O({defaultValues:me,validators:{onChange:K},onSubmit:({value:s})=>{a({person:s},{onSuccess:()=>{u.success("Cliente creado"),t()},onError:r=>{console.log(r);const{error:l}=j(r);u.error(l.message)}})}});function t(){o.reset(),n(!1)}return{form:o,handleClose:t}}function ue({isOpen:n,setIsOpen:a}){const{form:o,handleClose:t}=pe({setIsOpen:a});return e.jsx("div",{className:F("modal",n&&"modal-open"),children:e.jsxs("div",{className:"modal-box",children:[e.jsx(D,{onClose:t}),e.jsx("h3",{className:"font-bold text-lg",children:"Crear Cliente"}),e.jsx("form",{onSubmit:s=>{s.preventDefault(),o.handleSubmit()},children:e.jsxs(o.AppForm,{children:[e.jsxs("fieldset",{className:"fieldset",children:[e.jsx(o.AppField,{name:"name",children:({FSTextField:s})=>e.jsx(s,{label:"Nombre",placeholder:"Nombre",prefixComponent:e.jsx(p,{size:16})})}),e.jsx(o.AppField,{name:"fatherLastName",children:({FSTextField:s})=>e.jsx(s,{label:"Apellido Paterno",placeholder:"Apellido Paterno",prefixComponent:e.jsx(p,{size:16})})}),e.jsx(o.AppField,{name:"motherLastName",children:({FSTextField:s})=>e.jsx(s,{label:"Apellido Materno",placeholder:"Apellido Materno",prefixComponent:e.jsx(p,{size:16})})}),e.jsx(o.AppField,{name:"email",children:({FSTextField:s})=>e.jsx(s,{label:"Correo Electrónico",placeholder:"<EMAIL>",prefixComponent:e.jsx(L,{size:16})})}),e.jsx(o.AppField,{name:"address",children:({FSTextField:s})=>e.jsx(s,{label:"Dirección",placeholder:"Dirección",prefixComponent:e.jsx(q,{size:16})})}),e.jsx(o.AppField,{name:"phone",children:({FSTextField:s})=>e.jsx(s,{label:"Teléfono",placeholder:"Teléfono",prefixComponent:e.jsx(I,{size:16})})}),e.jsx(o.AppField,{name:"birthDate",children:({FSTextField:s})=>e.jsx(s,{label:"Fecha de Nacimiento",placeholder:"YYYY-MM-DD",type:"date",prefixComponent:e.jsx(R,{size:16})})}),e.jsx(o.AppField,{name:"document",children:({FSTextField:s})=>e.jsx(s,{label:"Documento",placeholder:"Número de Documento",prefixComponent:e.jsx(V,{size:16})})}),e.jsx(o.AppField,{name:"documentType",children:({FSSelectField:s})=>e.jsx(s,{label:"Tipo de Documento",placeholder:"Tipo de Documento",isNumber:!0,options:[{value:0,label:"DNI"},{value:1,label:"Pasaporte"},{value:2,label:"RUC"}]})}),e.jsx(o.AppField,{name:"gender",children:({FSToggleField:s})=>e.jsx(s,{label:"Género",trueLabel:"Masculino",falseLabel:"Femenino"})})]}),e.jsx("div",{className:"modal-action",children:e.jsx("button",{type:"submit",className:"btn btn-primary",children:"Crear"})})]})})]})})}const Te=function(){const[a,o]=b.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"container mx-auto",children:e.jsx("div",{className:"card bg-base-300",children:e.jsxs("div",{className:"card-body",children:[e.jsx("div",{children:e.jsx("button",{type:"button",className:"btn btn-primary",onClick:()=>o(!0),children:"Nuevo cliente"})}),e.jsx(ce,{})]})})}),e.jsx(ue,{isOpen:a,setIsOpen:o})]})};export{Te as component};
