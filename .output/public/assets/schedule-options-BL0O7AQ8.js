import{c as o}from"./createLucideIcon-78bvTjT9.js";import{q as r}from"./queryOptions-C9woPjwX.js";import{A as t}from"./runtimes-CTOS42-v.js";/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],a=o("trash-2",y),d=({schedule:e})=>r({queryKey:["schedules"],queryFn:()=>t.runPromise(e.getAll())}),i=({schedule:e},s)=>r({queryKey:["schedules",s],queryFn:()=>t.runPromise(e.getById(s))});export{a as T,i as a,d as s};
