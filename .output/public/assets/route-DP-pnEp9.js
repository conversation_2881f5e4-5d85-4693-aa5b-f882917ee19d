import{u as m,r as i,y as n,j as e,O as p,N as f}from"./main-RY8ZkTMc.js";import{u as g}from"./useQuery-D9VtETml.js";import{q as x}from"./queryOptions-C9woPjwX.js";import{A as d}from"./runtimes-CTOS42-v.js";import{g as l}from"./effectErrors-D8W8e9uM.js";const y=({auth:t})=>x({queryKey:["authenticated"],queryFn:()=>d.runPromise(t.getSession())}),O=function(){const u=m(),{isLoading:a,isSuccess:r,isError:s,error:o}=g({...y(u)});return i.useEffect(()=>{if(s){const c=l(o);n.error(c.error.message)}},[s,o]),i.useEffect(()=>{r&&n.success("Usuario autenticado")},[r]),a?e.jsx("div",{children:"Verificando autenticación..."}):r?e.jsx(p,{}):e.jsx(f,{to:"/login"})};export{O as component};
