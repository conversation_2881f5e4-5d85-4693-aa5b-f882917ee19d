var dt=e=>{throw TypeError(e)};var Y=(e,t,s)=>t.has(e)||dt("Cannot "+s);var i=(e,t,s)=>(Y(e,t,"read from private field"),s?s.call(e):t.get(e)),b=(e,t,s)=>t.has(e)?dt("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s),c=(e,t,s,r)=>(Y(e,t,"write to private field"),r?r.call(e,s):t.set(e,s),s),d=(e,t,s)=>(Y(e,t,"access private method"),s);import{d as St,p as ft,e as O,s as Z,f as j,n as $,i as q,g as pt,t as Et,h as It,k as Qt,l as bt,m as vt,r as S,o as wt,b as xt}from"./main-RY8ZkTMc.js";var y,n,z,g,x,D,E,I,V,M,P,T,F,Q,_,a,H,tt,et,st,it,rt,nt,at,Ct,mt,Tt=(mt=class extends St{constructor(t,s){super();b(this,a);b(this,y);b(this,n);b(this,z);b(this,g);b(this,x);b(this,D);b(this,E);b(this,I);b(this,V);b(this,M);b(this,P);b(this,T);b(this,F);b(this,Q);b(this,_,new Set);this.options=s,c(this,y,t),c(this,I,null),c(this,E,ft()),this.options.experimental_prefetchInRender||i(this,E).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(i(this,n).addObserver(this),gt(i(this,n),this.options)?d(this,a,H).call(this):this.updateResult(),d(this,a,it).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return ht(i(this,n),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return ht(i(this,n),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,d(this,a,rt).call(this),d(this,a,nt).call(this),i(this,n).removeObserver(this)}setOptions(t){const s=this.options,r=i(this,n);if(this.options=i(this,y).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof O(this.options.enabled,i(this,n))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");d(this,a,at).call(this),i(this,n).setOptions(this.options),s._defaulted&&!Z(this.options,s)&&i(this,y).getQueryCache().notify({type:"observerOptionsUpdated",query:i(this,n),observer:this});const u=this.hasListeners();u&&yt(i(this,n),r,this.options,s)&&d(this,a,H).call(this),this.updateResult(),u&&(i(this,n)!==r||O(this.options.enabled,i(this,n))!==O(s.enabled,i(this,n))||j(this.options.staleTime,i(this,n))!==j(s.staleTime,i(this,n)))&&d(this,a,tt).call(this);const h=d(this,a,et).call(this);u&&(i(this,n)!==r||O(this.options.enabled,i(this,n))!==O(s.enabled,i(this,n))||h!==i(this,Q))&&d(this,a,st).call(this,h)}getOptimisticResult(t){const s=i(this,y).getQueryCache().build(i(this,y),t),r=this.createResult(s,t);return Ut(this,r)&&(c(this,g,r),c(this,D,this.options),c(this,x,i(this,n).state)),r}getCurrentResult(){return i(this,g)}trackResult(t,s){return new Proxy(t,{get:(r,u)=>(this.trackProp(u),s==null||s(u),Reflect.get(r,u))})}trackProp(t){i(this,_).add(t)}getCurrentQuery(){return i(this,n)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const s=i(this,y).defaultQueryOptions(t),r=i(this,y).getQueryCache().build(i(this,y),s);return r.fetch().then(()=>this.createResult(r,s))}fetch(t){return d(this,a,H).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),i(this,g)))}createResult(t,s){var lt;const r=i(this,n),u=this.options,h=i(this,g),o=i(this,x),w=i(this,D),v=t!==r?t.state:i(this,z),{state:L}=t;let l={...L},k=!1,f;if(s._optimisticResults){const m=this.hasListeners(),N=!m&&gt(t,s),U=m&&yt(t,r,s,u);(N||U)&&(l={...l,...Qt(L.data,t.options)}),s._optimisticResults==="isRestoring"&&(l.fetchStatus="idle")}let{error:B,errorUpdatedAt:A,status:R}=l;f=l.data;let W=!1;if(s.placeholderData!==void 0&&f===void 0&&R==="pending"){let m;h!=null&&h.isPlaceholderData&&s.placeholderData===(w==null?void 0:w.placeholderData)?(m=h.data,W=!0):m=typeof s.placeholderData=="function"?s.placeholderData((lt=i(this,P))==null?void 0:lt.state.data,i(this,P)):s.placeholderData,m!==void 0&&(R="success",f=bt(h==null?void 0:h.data,m,s),k=!0)}if(s.select&&f!==void 0&&!W)if(h&&f===(o==null?void 0:o.data)&&s.select===i(this,V))f=i(this,M);else try{c(this,V,s.select),f=s.select(f),f=bt(h==null?void 0:h.data,f,s),c(this,M,f),c(this,I,null)}catch(m){c(this,I,m)}i(this,I)&&(B=i(this,I),f=i(this,M),A=Date.now(),R="error");const G=l.fetchStatus==="fetching",J=R==="pending",X=R==="error",ct=J&&G,ut=f!==void 0,C={status:R,fetchStatus:l.fetchStatus,isPending:J,isSuccess:R==="success",isError:X,isInitialLoading:ct,isLoading:ct,data:f,dataUpdatedAt:l.dataUpdatedAt,error:B,errorUpdatedAt:A,failureCount:l.fetchFailureCount,failureReason:l.fetchFailureReason,errorUpdateCount:l.errorUpdateCount,isFetched:l.dataUpdateCount>0||l.errorUpdateCount>0,isFetchedAfterMount:l.dataUpdateCount>v.dataUpdateCount||l.errorUpdateCount>v.errorUpdateCount,isFetching:G,isRefetching:G&&!J,isLoadingError:X&&!ut,isPaused:l.fetchStatus==="paused",isPlaceholderData:k,isRefetchError:X&&ut,isStale:ot(t,s),refetch:this.refetch,promise:i(this,E)};if(this.options.experimental_prefetchInRender){const m=K=>{C.status==="error"?K.reject(C.error):C.data!==void 0&&K.resolve(C.data)},N=()=>{const K=c(this,E,C.promise=ft());m(K)},U=i(this,E);switch(U.status){case"pending":t.queryHash===r.queryHash&&m(U);break;case"fulfilled":(C.status==="error"||C.data!==U.value)&&N();break;case"rejected":(C.status!=="error"||C.error!==U.reason)&&N();break}}return C}updateResult(){const t=i(this,g),s=this.createResult(i(this,n),this.options);if(c(this,x,i(this,n).state),c(this,D,this.options),i(this,x).data!==void 0&&c(this,P,i(this,n)),Z(s,t))return;c(this,g,s);const r=()=>{if(!t)return!0;const{notifyOnChangeProps:u}=this.options,h=typeof u=="function"?u():u;if(h==="all"||!h&&!i(this,_).size)return!0;const o=new Set(h??i(this,_));return this.options.throwOnError&&o.add("error"),Object.keys(i(this,g)).some(w=>{const p=w;return i(this,g)[p]!==t[p]&&o.has(p)})};d(this,a,Ct).call(this,{listeners:r()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&d(this,a,it).call(this)}},y=new WeakMap,n=new WeakMap,z=new WeakMap,g=new WeakMap,x=new WeakMap,D=new WeakMap,E=new WeakMap,I=new WeakMap,V=new WeakMap,M=new WeakMap,P=new WeakMap,T=new WeakMap,F=new WeakMap,Q=new WeakMap,_=new WeakMap,a=new WeakSet,H=function(t){d(this,a,at).call(this);let s=i(this,n).fetch(this.options,t);return t!=null&&t.throwOnError||(s=s.catch($)),s},tt=function(){d(this,a,rt).call(this);const t=j(this.options.staleTime,i(this,n));if(q||i(this,g).isStale||!pt(t))return;const r=Et(i(this,g).dataUpdatedAt,t)+1;c(this,T,setTimeout(()=>{i(this,g).isStale||this.updateResult()},r))},et=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(i(this,n)):this.options.refetchInterval)??!1},st=function(t){d(this,a,nt).call(this),c(this,Q,t),!(q||O(this.options.enabled,i(this,n))===!1||!pt(i(this,Q))||i(this,Q)===0)&&c(this,F,setInterval(()=>{(this.options.refetchIntervalInBackground||It.isFocused())&&d(this,a,H).call(this)},i(this,Q)))},it=function(){d(this,a,tt).call(this),d(this,a,st).call(this,d(this,a,et).call(this))},rt=function(){i(this,T)&&(clearTimeout(i(this,T)),c(this,T,void 0))},nt=function(){i(this,F)&&(clearInterval(i(this,F)),c(this,F,void 0))},at=function(){const t=i(this,y).getQueryCache().build(i(this,y),this.options);if(t===i(this,n))return;const s=i(this,n);c(this,n,t),c(this,z,t.state),this.hasListeners()&&(s==null||s.removeObserver(this),t.addObserver(this))},Ct=function(t){vt.batch(()=>{t.listeners&&this.listeners.forEach(s=>{s(i(this,g))}),i(this,y).getQueryCache().notify({query:i(this,n),type:"observerResultsUpdated"})})},mt);function Ft(e,t){return O(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function gt(e,t){return Ft(e,t)||e.state.data!==void 0&&ht(e,t,t.refetchOnMount)}function ht(e,t,s){if(O(t.enabled,e)!==!1&&j(t.staleTime,e)!=="static"){const r=typeof s=="function"?s(e):s;return r==="always"||r!==!1&&ot(e,t)}return!1}function yt(e,t,s,r){return(e!==t||O(r.enabled,e)===!1)&&(!s.suspense||e.state.status!=="error")&&ot(e,s)}function ot(e,t){return O(t.enabled,e)!==!1&&e.isStaleByTime(j(t.staleTime,e))}function Ut(e,t){return!Z(e.getCurrentResult(),t)}var Ot=S.createContext(!1),Dt=()=>S.useContext(Ot);Ot.Provider;function Mt(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var Pt=S.createContext(Mt()),_t=()=>S.useContext(Pt),Lt=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},kt=e=>{S.useEffect(()=>{e.clearReset()},[e])},Bt=({result:e,errorResetBoundary:t,throwOnError:s,query:r,suspense:u})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&(u&&e.data===void 0||wt(s,[e.error,r])),At=e=>{if(e.suspense){const t=r=>r==="static"?r:Math.max(r??1e3,1e3),s=e.staleTime;e.staleTime=typeof s=="function"?(...r)=>t(s(...r)):t(s),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3))}},Ht=(e,t)=>e.isLoading&&e.isFetching&&!t,jt=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,Rt=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function zt(e,t,s){var l,k,f,B,A;const r=Dt(),u=_t(),h=xt(),o=h.defaultQueryOptions(e);(k=(l=h.getDefaultOptions().queries)==null?void 0:l._experimental_beforeQuery)==null||k.call(l,o),o._optimisticResults=r?"isRestoring":"optimistic",At(o),Lt(o,u),kt(u);const w=!h.getQueryCache().get(o.queryHash),[p]=S.useState(()=>new t(h,o)),v=p.getOptimisticResult(o),L=!r&&e.subscribed!==!1;if(S.useSyncExternalStore(S.useCallback(R=>{const W=L?p.subscribe(vt.batchCalls(R)):$;return p.updateResult(),W},[p,L]),()=>p.getCurrentResult(),()=>p.getCurrentResult()),S.useEffect(()=>{p.setOptions(o)},[o,p]),jt(o,v))throw Rt(o,p,u);if(Bt({result:v,errorResetBoundary:u,throwOnError:o.throwOnError,query:h.getQueryCache().get(o.queryHash),suspense:o.suspense}))throw v.error;if((B=(f=h.getDefaultOptions().queries)==null?void 0:f._experimental_afterQuery)==null||B.call(f,o,v),o.experimental_prefetchInRender&&!q&&Ht(v,r)){const R=w?Rt(o,p,u):(A=h.getQueryCache().get(o.queryHash))==null?void 0:A.promise;R==null||R.catch($).finally(()=>{p.updateResult()})}return o.notifyOnChangeProps?v:p.trackResult(v)}function Kt(e,t){return zt(e,Tt)}export{Kt as u};
