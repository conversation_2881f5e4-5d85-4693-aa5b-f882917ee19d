import{j as P,r as U}from"./main-RY8ZkTMc.js";function ut({onClose:e}){return P.jsx("button",{type:"button",className:"btn btn-sm btn-circle btn-ghost absolute top-2 right-2",onClick:e,children:"✕"})}/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function st(){return{accessor:(e,o)=>typeof e=="function"?{...o,accessorFn:e}:{...o,accessorKey:e},display:e=>e,group:e=>e}}function x(e,o){return typeof e=="function"?e(o):e}function F(e,o){return t=>{o.setState(n=>({...n,[e]:x(t,n[e])}))}}function z(e){return e instanceof Function}function Ce(e){return Array.isArray(e)&&e.every(o=>typeof o=="number")}function Re(e,o){const t=[],n=i=>{i.forEach(r=>{t.push(r);const l=o(r);l!=null&&l.length&&n(l)})};return n(e),t}function S(e,o,t){let n=[],i;return r=>{let l;t.key&&t.debug&&(l=Date.now());const u=e(r);if(!(u.length!==n.length||u.some((c,C)=>n[C]!==c)))return i;n=u;let g;if(t.key&&t.debug&&(g=Date.now()),i=o(...u),t==null||t.onChange==null||t.onChange(i),t.key&&t.debug&&t!=null&&t.debug()){const c=Math.round((Date.now()-l)*100)/100,C=Math.round((Date.now()-g)*100)/100,d=C/16,a=(f,p)=>{for(f=String(f);f.length<p;)f=" "+f;return f};console.info(`%c⏱ ${a(C,5)} /${a(c,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*d,120))}deg 100% 31%);`,t==null?void 0:t.key)}return i}}function m(e,o,t,n){return{debug:()=>{var i;return(i=e==null?void 0:e.debugAll)!=null?i:e[o]},key:!1,onChange:n}}function we(e,o,t,n){const i=()=>{var l;return(l=r.getValue())!=null?l:e.options.renderFallbackValue},r={id:`${o.id}_${t.id}`,row:o,column:t,getValue:()=>o.getValue(n),renderValue:i,getContext:S(()=>[e,t,o,r],(l,u,s,g)=>({table:l,column:u,row:s,cell:g,getValue:g.getValue,renderValue:g.renderValue}),m(e.options,"debugCells"))};return e._features.forEach(l=>{l.createCell==null||l.createCell(r,t,o,e)},{}),r}function ve(e,o,t,n){var i,r;const u={...e._getDefaultColumnDef(),...o},s=u.accessorKey;let g=(i=(r=u.id)!=null?r:s?typeof String.prototype.replaceAll=="function"?s.replaceAll(".","_"):s.replace(/\./g,"_"):void 0)!=null?i:typeof u.header=="string"?u.header:void 0,c;if(u.accessorFn?c=u.accessorFn:s&&(s.includes(".")?c=d=>{let a=d;for(const p of s.split(".")){var f;a=(f=a)==null?void 0:f[p]}return a}:c=d=>d[u.accessorKey]),!g)throw new Error;let C={id:`${String(g)}`,accessorFn:c,parent:n,depth:t,columnDef:u,columns:[],getFlatColumns:S(()=>[!0],()=>{var d;return[C,...(d=C.columns)==null?void 0:d.flatMap(a=>a.getFlatColumns())]},m(e.options,"debugColumns")),getLeafColumns:S(()=>[e._getOrderColumnsFn()],d=>{var a;if((a=C.columns)!=null&&a.length){let f=C.columns.flatMap(p=>p.getLeafColumns());return d(f)}return[C]},m(e.options,"debugColumns"))};for(const d of e._features)d.createColumn==null||d.createColumn(C,e);return C}const h="debugHeaders";function oe(e,o,t){var n;let r={id:(n=t.id)!=null?n:o.id,column:o,index:t.index,isPlaceholder:!!t.isPlaceholder,placeholderId:t.placeholderId,depth:t.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const l=[],u=s=>{s.subHeaders&&s.subHeaders.length&&s.subHeaders.map(u),l.push(s)};return u(r),l},getContext:()=>({table:e,header:r,column:o})};return e._features.forEach(l=>{l.createHeader==null||l.createHeader(r,e)}),r}const he={createTable:e=>{e.getHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n,i)=>{var r,l;const u=(r=n==null?void 0:n.map(C=>t.find(d=>d.id===C)).filter(Boolean))!=null?r:[],s=(l=i==null?void 0:i.map(C=>t.find(d=>d.id===C)).filter(Boolean))!=null?l:[],g=t.filter(C=>!(n!=null&&n.includes(C.id))&&!(i!=null&&i.includes(C.id)));return H(o,[...u,...g,...s],e)},m(e.options,h)),e.getCenterHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n,i)=>(t=t.filter(r=>!(n!=null&&n.includes(r.id))&&!(i!=null&&i.includes(r.id))),H(o,t,e,"center")),m(e.options,h)),e.getLeftHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(o,t,n)=>{var i;const r=(i=n==null?void 0:n.map(l=>t.find(u=>u.id===l)).filter(Boolean))!=null?i:[];return H(o,r,e,"left")},m(e.options,h)),e.getRightHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(o,t,n)=>{var i;const r=(i=n==null?void 0:n.map(l=>t.find(u=>u.id===l)).filter(Boolean))!=null?i:[];return H(o,r,e,"right")},m(e.options,h)),e.getFooterGroups=S(()=>[e.getHeaderGroups()],o=>[...o].reverse(),m(e.options,h)),e.getLeftFooterGroups=S(()=>[e.getLeftHeaderGroups()],o=>[...o].reverse(),m(e.options,h)),e.getCenterFooterGroups=S(()=>[e.getCenterHeaderGroups()],o=>[...o].reverse(),m(e.options,h)),e.getRightFooterGroups=S(()=>[e.getRightHeaderGroups()],o=>[...o].reverse(),m(e.options,h)),e.getFlatHeaders=S(()=>[e.getHeaderGroups()],o=>o.map(t=>t.headers).flat(),m(e.options,h)),e.getLeftFlatHeaders=S(()=>[e.getLeftHeaderGroups()],o=>o.map(t=>t.headers).flat(),m(e.options,h)),e.getCenterFlatHeaders=S(()=>[e.getCenterHeaderGroups()],o=>o.map(t=>t.headers).flat(),m(e.options,h)),e.getRightFlatHeaders=S(()=>[e.getRightHeaderGroups()],o=>o.map(t=>t.headers).flat(),m(e.options,h)),e.getCenterLeafHeaders=S(()=>[e.getCenterFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),m(e.options,h)),e.getLeftLeafHeaders=S(()=>[e.getLeftFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),m(e.options,h)),e.getRightLeafHeaders=S(()=>[e.getRightFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),m(e.options,h)),e.getLeafHeaders=S(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(o,t,n)=>{var i,r,l,u,s,g;return[...(i=(r=o[0])==null?void 0:r.headers)!=null?i:[],...(l=(u=t[0])==null?void 0:u.headers)!=null?l:[],...(s=(g=n[0])==null?void 0:g.headers)!=null?s:[]].map(c=>c.getLeafHeaders()).flat()},m(e.options,h))}};function H(e,o,t,n){var i,r;let l=0;const u=function(d,a){a===void 0&&(a=1),l=Math.max(l,a),d.filter(f=>f.getIsVisible()).forEach(f=>{var p;(p=f.columns)!=null&&p.length&&u(f.columns,a+1)},0)};u(e);let s=[];const g=(d,a)=>{const f={depth:a,id:[n,`${a}`].filter(Boolean).join("_"),headers:[]},p=[];d.forEach(w=>{const R=[...p].reverse()[0],_=w.column.depth===f.depth;let v,V=!1;if(_&&w.column.parent?v=w.column.parent:(v=w.column,V=!0),R&&(R==null?void 0:R.column)===v)R.subHeaders.push(w);else{const y=oe(t,v,{id:[n,a,v.id,w==null?void 0:w.id].filter(Boolean).join("_"),isPlaceholder:V,placeholderId:V?`${p.filter(L=>L.column===v).length}`:void 0,depth:a,index:p.length});y.subHeaders.push(w),p.push(y)}f.headers.push(w),w.headerGroup=f}),s.push(f),a>0&&g(p,a-1)},c=o.map((d,a)=>oe(t,d,{depth:l,index:a}));g(c,l-1),s.reverse();const C=d=>d.filter(f=>f.column.getIsVisible()).map(f=>{let p=0,w=0,R=[0];f.subHeaders&&f.subHeaders.length?(R=[],C(f.subHeaders).forEach(v=>{let{colSpan:V,rowSpan:y}=v;p+=V,R.push(y)})):p=1;const _=Math.min(...R);return w=w+_,f.colSpan=p,f.rowSpan=w,{colSpan:p,rowSpan:w}});return C((i=(r=s[0])==null?void 0:r.headers)!=null?i:[]),s}const _e=(e,o,t,n,i,r,l)=>{let u={id:o,index:n,original:t,depth:i,parentId:l,_valuesCache:{},_uniqueValuesCache:{},getValue:s=>{if(u._valuesCache.hasOwnProperty(s))return u._valuesCache[s];const g=e.getColumn(s);if(g!=null&&g.accessorFn)return u._valuesCache[s]=g.accessorFn(u.original,n),u._valuesCache[s]},getUniqueValues:s=>{if(u._uniqueValuesCache.hasOwnProperty(s))return u._uniqueValuesCache[s];const g=e.getColumn(s);if(g!=null&&g.accessorFn)return g.columnDef.getUniqueValues?(u._uniqueValuesCache[s]=g.columnDef.getUniqueValues(u.original,n),u._uniqueValuesCache[s]):(u._uniqueValuesCache[s]=[u.getValue(s)],u._uniqueValuesCache[s])},renderValue:s=>{var g;return(g=u.getValue(s))!=null?g:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>Re(u.subRows,s=>s.subRows),getParentRow:()=>u.parentId?e.getRow(u.parentId,!0):void 0,getParentRows:()=>{let s=[],g=u;for(;;){const c=g.getParentRow();if(!c)break;s.push(c),g=c}return s.reverse()},getAllCells:S(()=>[e.getAllLeafColumns()],s=>s.map(g=>we(e,u,g,g.id)),m(e.options,"debugRows")),_getAllCellsByColumnId:S(()=>[u.getAllCells()],s=>s.reduce((g,c)=>(g[c.column.id]=c,g),{}),m(e.options,"debugRows"))};for(let s=0;s<e._features.length;s++){const g=e._features[s];g==null||g.createRow==null||g.createRow(u,e)}return u},Fe={createColumn:(e,o)=>{e._getFacetedRowModel=o.options.getFacetedRowModel&&o.options.getFacetedRowModel(o,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():o.getPreFilteredRowModel(),e._getFacetedUniqueValues=o.options.getFacetedUniqueValues&&o.options.getFacetedUniqueValues(o,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=o.options.getFacetedMinMaxValues&&o.options.getFacetedMinMaxValues(o,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},le=(e,o,t)=>{var n,i;const r=t==null||(n=t.toString())==null?void 0:n.toLowerCase();return!!(!((i=e.getValue(o))==null||(i=i.toString())==null||(i=i.toLowerCase())==null)&&i.includes(r))};le.autoRemove=e=>$(e);const ue=(e,o,t)=>{var n;return!!(!((n=e.getValue(o))==null||(n=n.toString())==null)&&n.includes(t))};ue.autoRemove=e=>$(e);const se=(e,o,t)=>{var n;return((n=e.getValue(o))==null||(n=n.toString())==null?void 0:n.toLowerCase())===(t==null?void 0:t.toLowerCase())};se.autoRemove=e=>$(e);const ae=(e,o,t)=>{var n;return(n=e.getValue(o))==null?void 0:n.includes(t)};ae.autoRemove=e=>$(e);const ge=(e,o,t)=>!t.some(n=>{var i;return!((i=e.getValue(o))!=null&&i.includes(n))});ge.autoRemove=e=>$(e)||!(e!=null&&e.length);const de=(e,o,t)=>t.some(n=>{var i;return(i=e.getValue(o))==null?void 0:i.includes(n)});de.autoRemove=e=>$(e)||!(e!=null&&e.length);const ce=(e,o,t)=>e.getValue(o)===t;ce.autoRemove=e=>$(e);const fe=(e,o,t)=>e.getValue(o)==t;fe.autoRemove=e=>$(e);const Y=(e,o,t)=>{let[n,i]=t;const r=e.getValue(o);return r>=n&&r<=i};Y.resolveFilterValue=e=>{let[o,t]=e,n=typeof o!="number"?parseFloat(o):o,i=typeof t!="number"?parseFloat(t):t,r=o===null||Number.isNaN(n)?-1/0:n,l=t===null||Number.isNaN(i)?1/0:i;if(r>l){const u=r;r=l,l=u}return[r,l]};Y.autoRemove=e=>$(e)||$(e[0])&&$(e[1]);const M={includesString:le,includesStringSensitive:ue,equalsString:se,arrIncludes:ae,arrIncludesAll:ge,arrIncludesSome:de,equals:ce,weakEquals:fe,inNumberRange:Y};function $(e){return e==null||e===""}const $e={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:F("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,o)=>{e.getAutoFilterFn=()=>{const t=o.getCoreRowModel().flatRows[0],n=t==null?void 0:t.getValue(e.id);return typeof n=="string"?M.includesString:typeof n=="number"?M.inNumberRange:typeof n=="boolean"||n!==null&&typeof n=="object"?M.equals:Array.isArray(n)?M.arrIncludes:M.weakEquals},e.getFilterFn=()=>{var t,n;return z(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(t=(n=o.options.filterFns)==null?void 0:n[e.columnDef.filterFn])!=null?t:M[e.columnDef.filterFn]},e.getCanFilter=()=>{var t,n,i;return((t=e.columnDef.enableColumnFilter)!=null?t:!0)&&((n=o.options.enableColumnFilters)!=null?n:!0)&&((i=o.options.enableFilters)!=null?i:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var t;return(t=o.getState().columnFilters)==null||(t=t.find(n=>n.id===e.id))==null?void 0:t.value},e.getFilterIndex=()=>{var t,n;return(t=(n=o.getState().columnFilters)==null?void 0:n.findIndex(i=>i.id===e.id))!=null?t:-1},e.setFilterValue=t=>{o.setColumnFilters(n=>{const i=e.getFilterFn(),r=n==null?void 0:n.find(c=>c.id===e.id),l=x(t,r?r.value:void 0);if(ie(i,l,e)){var u;return(u=n==null?void 0:n.filter(c=>c.id!==e.id))!=null?u:[]}const s={id:e.id,value:l};if(r){var g;return(g=n==null?void 0:n.map(c=>c.id===e.id?s:c))!=null?g:[]}return n!=null&&n.length?[...n,s]:[s]})}},createRow:(e,o)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=o=>{const t=e.getAllLeafColumns(),n=i=>{var r;return(r=x(o,i))==null?void 0:r.filter(l=>{const u=t.find(s=>s.id===l.id);if(u){const s=u.getFilterFn();if(ie(s,l.value,u))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(n)},e.resetColumnFilters=o=>{var t,n;e.setColumnFilters(o?[]:(t=(n=e.initialState)==null?void 0:n.columnFilters)!=null?t:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function ie(e,o,t){return(e&&e.autoRemove?e.autoRemove(o,t):!1)||typeof o>"u"||typeof o=="string"&&!o}const Me=(e,o,t)=>t.reduce((n,i)=>{const r=i.getValue(e);return n+(typeof r=="number"?r:0)},0),Ve=(e,o,t)=>{let n;return t.forEach(i=>{const r=i.getValue(e);r!=null&&(n>r||n===void 0&&r>=r)&&(n=r)}),n},Pe=(e,o,t)=>{let n;return t.forEach(i=>{const r=i.getValue(e);r!=null&&(n<r||n===void 0&&r>=r)&&(n=r)}),n},xe=(e,o,t)=>{let n,i;return t.forEach(r=>{const l=r.getValue(e);l!=null&&(n===void 0?l>=l&&(n=i=l):(n>l&&(n=l),i<l&&(i=l)))}),[n,i]},Ie=(e,o)=>{let t=0,n=0;if(o.forEach(i=>{let r=i.getValue(e);r!=null&&(r=+r)>=r&&(++t,n+=r)}),t)return n/t},ye=(e,o)=>{if(!o.length)return;const t=o.map(r=>r.getValue(e));if(!Ce(t))return;if(t.length===1)return t[0];const n=Math.floor(t.length/2),i=t.sort((r,l)=>r-l);return t.length%2!==0?i[n]:(i[n-1]+i[n])/2},De=(e,o)=>Array.from(new Set(o.map(t=>t.getValue(e))).values()),Ee=(e,o)=>new Set(o.map(t=>t.getValue(e))).size,He=(e,o)=>o.length,O={sum:Me,min:Ve,max:Pe,extent:xe,mean:Ie,median:ye,unique:De,uniqueCount:Ee,count:He},Ge={getDefaultColumnDef:()=>({aggregatedCell:e=>{var o,t;return(o=(t=e.getValue())==null||t.toString==null?void 0:t.toString())!=null?o:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:F("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,o)=>{e.toggleGrouping=()=>{o.setGrouping(t=>t!=null&&t.includes(e.id)?t.filter(n=>n!==e.id):[...t??[],e.id])},e.getCanGroup=()=>{var t,n;return((t=e.columnDef.enableGrouping)!=null?t:!0)&&((n=o.options.enableGrouping)!=null?n:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var t;return(t=o.getState().grouping)==null?void 0:t.includes(e.id)},e.getGroupedIndex=()=>{var t;return(t=o.getState().grouping)==null?void 0:t.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const t=o.getCoreRowModel().flatRows[0],n=t==null?void 0:t.getValue(e.id);if(typeof n=="number")return O.sum;if(Object.prototype.toString.call(n)==="[object Date]")return O.extent},e.getAggregationFn=()=>{var t,n;if(!e)throw new Error;return z(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(t=(n=o.options.aggregationFns)==null?void 0:n[e.columnDef.aggregationFn])!=null?t:O[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=o=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(o),e.resetGrouping=o=>{var t,n;e.setGrouping(o?[]:(t=(n=e.initialState)==null?void 0:n.grouping)!=null?t:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,o)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=t=>{if(e._groupingValuesCache.hasOwnProperty(t))return e._groupingValuesCache[t];const n=o.getColumn(t);return n!=null&&n.columnDef.getGroupingValue?(e._groupingValuesCache[t]=n.columnDef.getGroupingValue(e.original),e._groupingValuesCache[t]):e.getValue(t)},e._groupingValuesCache={}},createCell:(e,o,t,n)=>{e.getIsGrouped=()=>o.getIsGrouped()&&o.id===t.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&o.getIsGrouped(),e.getIsAggregated=()=>{var i;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((i=t.subRows)!=null&&i.length)}}};function Ae(e,o,t){if(!(o!=null&&o.length)||!t)return e;const n=e.filter(r=>!o.includes(r.id));return t==="remove"?n:[...o.map(r=>e.find(l=>l.id===r)).filter(Boolean),...n]}const ze={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:F("columnOrder",e)}),createColumn:(e,o)=>{e.getIndex=S(t=>[E(o,t)],t=>t.findIndex(n=>n.id===e.id),m(o.options,"debugColumns")),e.getIsFirstColumn=t=>{var n;return((n=E(o,t)[0])==null?void 0:n.id)===e.id},e.getIsLastColumn=t=>{var n;const i=E(o,t);return((n=i[i.length-1])==null?void 0:n.id)===e.id}},createTable:e=>{e.setColumnOrder=o=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(o),e.resetColumnOrder=o=>{var t;e.setColumnOrder(o?[]:(t=e.initialState.columnOrder)!=null?t:[])},e._getOrderColumnsFn=S(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(o,t,n)=>i=>{let r=[];if(!(o!=null&&o.length))r=i;else{const l=[...o],u=[...i];for(;u.length&&l.length;){const s=l.shift(),g=u.findIndex(c=>c.id===s);g>-1&&r.push(u.splice(g,1)[0])}r=[...r,...u]}return Ae(r,t,n)},m(e.options,"debugTable"))}},B=()=>({left:[],right:[]}),Le={getInitialState:e=>({columnPinning:B(),...e}),getDefaultOptions:e=>({onColumnPinningChange:F("columnPinning",e)}),createColumn:(e,o)=>{e.pin=t=>{const n=e.getLeafColumns().map(i=>i.id).filter(Boolean);o.setColumnPinning(i=>{var r,l;if(t==="right"){var u,s;return{left:((u=i==null?void 0:i.left)!=null?u:[]).filter(C=>!(n!=null&&n.includes(C))),right:[...((s=i==null?void 0:i.right)!=null?s:[]).filter(C=>!(n!=null&&n.includes(C))),...n]}}if(t==="left"){var g,c;return{left:[...((g=i==null?void 0:i.left)!=null?g:[]).filter(C=>!(n!=null&&n.includes(C))),...n],right:((c=i==null?void 0:i.right)!=null?c:[]).filter(C=>!(n!=null&&n.includes(C)))}}return{left:((r=i==null?void 0:i.left)!=null?r:[]).filter(C=>!(n!=null&&n.includes(C))),right:((l=i==null?void 0:i.right)!=null?l:[]).filter(C=>!(n!=null&&n.includes(C)))}})},e.getCanPin=()=>e.getLeafColumns().some(n=>{var i,r,l;return((i=n.columnDef.enablePinning)!=null?i:!0)&&((r=(l=o.options.enableColumnPinning)!=null?l:o.options.enablePinning)!=null?r:!0)}),e.getIsPinned=()=>{const t=e.getLeafColumns().map(u=>u.id),{left:n,right:i}=o.getState().columnPinning,r=t.some(u=>n==null?void 0:n.includes(u)),l=t.some(u=>i==null?void 0:i.includes(u));return r?"left":l?"right":!1},e.getPinnedIndex=()=>{var t,n;const i=e.getIsPinned();return i?(t=(n=o.getState().columnPinning)==null||(n=n[i])==null?void 0:n.indexOf(e.id))!=null?t:-1:0}},createRow:(e,o)=>{e.getCenterVisibleCells=S(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left,o.getState().columnPinning.right],(t,n,i)=>{const r=[...n??[],...i??[]];return t.filter(l=>!r.includes(l.column.id))},m(o.options,"debugRows")),e.getLeftVisibleCells=S(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left],(t,n)=>(n??[]).map(r=>t.find(l=>l.column.id===r)).filter(Boolean).map(r=>({...r,position:"left"})),m(o.options,"debugRows")),e.getRightVisibleCells=S(()=>[e._getAllVisibleCells(),o.getState().columnPinning.right],(t,n)=>(n??[]).map(r=>t.find(l=>l.column.id===r)).filter(Boolean).map(r=>({...r,position:"right"})),m(o.options,"debugRows"))},createTable:e=>{e.setColumnPinning=o=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(o),e.resetColumnPinning=o=>{var t,n;return e.setColumnPinning(o?B():(t=(n=e.initialState)==null?void 0:n.columnPinning)!=null?t:B())},e.getIsSomeColumnsPinned=o=>{var t;const n=e.getState().columnPinning;if(!o){var i,r;return!!((i=n.left)!=null&&i.length||(r=n.right)!=null&&r.length)}return!!((t=n[o])!=null&&t.length)},e.getLeftLeafColumns=S(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(o,t)=>(t??[]).map(n=>o.find(i=>i.id===n)).filter(Boolean),m(e.options,"debugColumns")),e.getRightLeafColumns=S(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(o,t)=>(t??[]).map(n=>o.find(i=>i.id===n)).filter(Boolean),m(e.options,"debugColumns")),e.getCenterLeafColumns=S(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n)=>{const i=[...t??[],...n??[]];return o.filter(r=>!i.includes(r.id))},m(e.options,"debugColumns"))}};function Oe(e){return e||(typeof document<"u"?document:null)}const G={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},T=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),Be={getDefaultColumnDef:()=>G,getInitialState:e=>({columnSizing:{},columnSizingInfo:T(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:F("columnSizing",e),onColumnSizingInfoChange:F("columnSizingInfo",e)}),createColumn:(e,o)=>{e.getSize=()=>{var t,n,i;const r=o.getState().columnSizing[e.id];return Math.min(Math.max((t=e.columnDef.minSize)!=null?t:G.minSize,(n=r??e.columnDef.size)!=null?n:G.size),(i=e.columnDef.maxSize)!=null?i:G.maxSize)},e.getStart=S(t=>[t,E(o,t),o.getState().columnSizing],(t,n)=>n.slice(0,e.getIndex(t)).reduce((i,r)=>i+r.getSize(),0),m(o.options,"debugColumns")),e.getAfter=S(t=>[t,E(o,t),o.getState().columnSizing],(t,n)=>n.slice(e.getIndex(t)+1).reduce((i,r)=>i+r.getSize(),0),m(o.options,"debugColumns")),e.resetSize=()=>{o.setColumnSizing(t=>{let{[e.id]:n,...i}=t;return i})},e.getCanResize=()=>{var t,n;return((t=e.columnDef.enableResizing)!=null?t:!0)&&((n=o.options.enableColumnResizing)!=null?n:!0)},e.getIsResizing=()=>o.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,o)=>{e.getSize=()=>{let t=0;const n=i=>{if(i.subHeaders.length)i.subHeaders.forEach(n);else{var r;t+=(r=i.column.getSize())!=null?r:0}};return n(e),t},e.getStart=()=>{if(e.index>0){const t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=t=>{const n=o.getColumn(e.column.id),i=n==null?void 0:n.getCanResize();return r=>{if(!n||!i||(r.persist==null||r.persist(),q(r)&&r.touches&&r.touches.length>1))return;const l=e.getSize(),u=e?e.getLeafHeaders().map(R=>[R.column.id,R.column.getSize()]):[[n.id,n.getSize()]],s=q(r)?Math.round(r.touches[0].clientX):r.clientX,g={},c=(R,_)=>{typeof _=="number"&&(o.setColumnSizingInfo(v=>{var V,y;const L=o.options.columnResizeDirection==="rtl"?-1:1,ee=(_-((V=v==null?void 0:v.startOffset)!=null?V:0))*L,te=Math.max(ee/((y=v==null?void 0:v.startSize)!=null?y:0),-.999999);return v.columnSizingStart.forEach(Se=>{let[me,ne]=Se;g[me]=Math.round(Math.max(ne+ne*te,0)*100)/100}),{...v,deltaOffset:ee,deltaPercentage:te}}),(o.options.columnResizeMode==="onChange"||R==="end")&&o.setColumnSizing(v=>({...v,...g})))},C=R=>c("move",R),d=R=>{c("end",R),o.setColumnSizingInfo(_=>({..._,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},a=Oe(t),f={moveHandler:R=>C(R.clientX),upHandler:R=>{a==null||a.removeEventListener("mousemove",f.moveHandler),a==null||a.removeEventListener("mouseup",f.upHandler),d(R.clientX)}},p={moveHandler:R=>(R.cancelable&&(R.preventDefault(),R.stopPropagation()),C(R.touches[0].clientX),!1),upHandler:R=>{var _;a==null||a.removeEventListener("touchmove",p.moveHandler),a==null||a.removeEventListener("touchend",p.upHandler),R.cancelable&&(R.preventDefault(),R.stopPropagation()),d((_=R.touches[0])==null?void 0:_.clientX)}},w=Te()?{passive:!1}:!1;q(r)?(a==null||a.addEventListener("touchmove",p.moveHandler,w),a==null||a.addEventListener("touchend",p.upHandler,w)):(a==null||a.addEventListener("mousemove",f.moveHandler,w),a==null||a.addEventListener("mouseup",f.upHandler,w)),o.setColumnSizingInfo(R=>({...R,startOffset:s,startSize:l,deltaOffset:0,deltaPercentage:0,columnSizingStart:u,isResizingColumn:n.id}))}}},createTable:e=>{e.setColumnSizing=o=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(o),e.setColumnSizingInfo=o=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(o),e.resetColumnSizing=o=>{var t;e.setColumnSizing(o?{}:(t=e.initialState.columnSizing)!=null?t:{})},e.resetHeaderSizeInfo=o=>{var t;e.setColumnSizingInfo(o?T():(t=e.initialState.columnSizingInfo)!=null?t:T())},e.getTotalSize=()=>{var o,t;return(o=(t=e.getHeaderGroups()[0])==null?void 0:t.headers.reduce((n,i)=>n+i.getSize(),0))!=null?o:0},e.getLeftTotalSize=()=>{var o,t;return(o=(t=e.getLeftHeaderGroups()[0])==null?void 0:t.headers.reduce((n,i)=>n+i.getSize(),0))!=null?o:0},e.getCenterTotalSize=()=>{var o,t;return(o=(t=e.getCenterHeaderGroups()[0])==null?void 0:t.headers.reduce((n,i)=>n+i.getSize(),0))!=null?o:0},e.getRightTotalSize=()=>{var o,t;return(o=(t=e.getRightHeaderGroups()[0])==null?void 0:t.headers.reduce((n,i)=>n+i.getSize(),0))!=null?o:0}}};let A=null;function Te(){if(typeof A=="boolean")return A;let e=!1;try{const o={get passive(){return e=!0,!1}},t=()=>{};window.addEventListener("test",t,o),window.removeEventListener("test",t)}catch{e=!1}return A=e,A}function q(e){return e.type==="touchstart"}const qe={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:F("columnVisibility",e)}),createColumn:(e,o)=>{e.toggleVisibility=t=>{e.getCanHide()&&o.setColumnVisibility(n=>({...n,[e.id]:t??!e.getIsVisible()}))},e.getIsVisible=()=>{var t,n;const i=e.columns;return(t=i.length?i.some(r=>r.getIsVisible()):(n=o.getState().columnVisibility)==null?void 0:n[e.id])!=null?t:!0},e.getCanHide=()=>{var t,n;return((t=e.columnDef.enableHiding)!=null?t:!0)&&((n=o.options.enableHiding)!=null?n:!0)},e.getToggleVisibilityHandler=()=>t=>{e.toggleVisibility==null||e.toggleVisibility(t.target.checked)}},createRow:(e,o)=>{e._getAllVisibleCells=S(()=>[e.getAllCells(),o.getState().columnVisibility],t=>t.filter(n=>n.column.getIsVisible()),m(o.options,"debugRows")),e.getVisibleCells=S(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(t,n,i)=>[...t,...n,...i],m(o.options,"debugRows"))},createTable:e=>{const o=(t,n)=>S(()=>[n(),n().filter(i=>i.getIsVisible()).map(i=>i.id).join("_")],i=>i.filter(r=>r.getIsVisible==null?void 0:r.getIsVisible()),m(e.options,"debugColumns"));e.getVisibleFlatColumns=o("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=o("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=o("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=o("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=o("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var n;e.setColumnVisibility(t?{}:(n=e.initialState.columnVisibility)!=null?n:{})},e.toggleAllColumnsVisible=t=>{var n;t=(n=t)!=null?n:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((i,r)=>({...i,[r.id]:t||!(r.getCanHide!=null&&r.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(t=>!(t.getIsVisible!=null&&t.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(t=>t.getIsVisible==null?void 0:t.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var n;e.toggleAllColumnsVisible((n=t.target)==null?void 0:n.checked)}}};function E(e,o){return o?o==="center"?e.getCenterVisibleLeafColumns():o==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const je={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},ke={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:F("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:o=>{var t;const n=(t=e.getCoreRowModel().flatRows[0])==null||(t=t._getAllCellsByColumnId()[o.id])==null?void 0:t.getValue();return typeof n=="string"||typeof n=="number"}}),createColumn:(e,o)=>{e.getCanGlobalFilter=()=>{var t,n,i,r;return((t=e.columnDef.enableGlobalFilter)!=null?t:!0)&&((n=o.options.enableGlobalFilter)!=null?n:!0)&&((i=o.options.enableFilters)!=null?i:!0)&&((r=o.options.getColumnCanGlobalFilter==null?void 0:o.options.getColumnCanGlobalFilter(e))!=null?r:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>M.includesString,e.getGlobalFilterFn=()=>{var o,t;const{globalFilterFn:n}=e.options;return z(n)?n:n==="auto"?e.getGlobalAutoFilterFn():(o=(t=e.options.filterFns)==null?void 0:t[n])!=null?o:M[n]},e.setGlobalFilter=o=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(o)},e.resetGlobalFilter=o=>{e.setGlobalFilter(o?void 0:e.initialState.globalFilter)}}},Ne={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:F("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let o=!1,t=!1;e._autoResetExpanded=()=>{var n,i;if(!o){e._queue(()=>{o=!0});return}if((n=(i=e.options.autoResetAll)!=null?i:e.options.autoResetExpanded)!=null?n:!e.options.manualExpanding){if(t)return;t=!0,e._queue(()=>{e.resetExpanded(),t=!1})}},e.setExpanded=n=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(n),e.toggleAllRowsExpanded=n=>{n??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=n=>{var i,r;e.setExpanded(n?{}:(i=(r=e.initialState)==null?void 0:r.expanded)!=null?i:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(n=>n.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>n=>{n.persist==null||n.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const n=e.getState().expanded;return n===!0||Object.values(n).some(Boolean)},e.getIsAllRowsExpanded=()=>{const n=e.getState().expanded;return typeof n=="boolean"?n===!0:!(!Object.keys(n).length||e.getRowModel().flatRows.some(i=>!i.getIsExpanded()))},e.getExpandedDepth=()=>{let n=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(r=>{const l=r.split(".");n=Math.max(n,l.length)}),n},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,o)=>{e.toggleExpanded=t=>{o.setExpanded(n=>{var i;const r=n===!0?!0:!!(n!=null&&n[e.id]);let l={};if(n===!0?Object.keys(o.getRowModel().rowsById).forEach(u=>{l[u]=!0}):l=n,t=(i=t)!=null?i:!r,!r&&t)return{...l,[e.id]:!0};if(r&&!t){const{[e.id]:u,...s}=l;return s}return n})},e.getIsExpanded=()=>{var t;const n=o.getState().expanded;return!!((t=o.options.getIsRowExpanded==null?void 0:o.options.getIsRowExpanded(e))!=null?t:n===!0||n!=null&&n[e.id])},e.getCanExpand=()=>{var t,n,i;return(t=o.options.getRowCanExpand==null?void 0:o.options.getRowCanExpand(e))!=null?t:((n=o.options.enableExpanding)!=null?n:!0)&&!!((i=e.subRows)!=null&&i.length)},e.getIsAllParentsExpanded=()=>{let t=!0,n=e;for(;t&&n.parentId;)n=o.getRow(n.parentId,!0),t=n.getIsExpanded();return t},e.getToggleExpandedHandler=()=>{const t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},X=0,K=10,j=()=>({pageIndex:X,pageSize:K}),Ue={getInitialState:e=>({...e,pagination:{...j(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:F("pagination",e)}),createTable:e=>{let o=!1,t=!1;e._autoResetPageIndex=()=>{var n,i;if(!o){e._queue(()=>{o=!0});return}if((n=(i=e.options.autoResetAll)!=null?i:e.options.autoResetPageIndex)!=null?n:!e.options.manualPagination){if(t)return;t=!0,e._queue(()=>{e.resetPageIndex(),t=!1})}},e.setPagination=n=>{const i=r=>x(n,r);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(i)},e.resetPagination=n=>{var i;e.setPagination(n?j():(i=e.initialState.pagination)!=null?i:j())},e.setPageIndex=n=>{e.setPagination(i=>{let r=x(n,i.pageIndex);const l=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return r=Math.max(0,Math.min(r,l)),{...i,pageIndex:r}})},e.resetPageIndex=n=>{var i,r;e.setPageIndex(n?X:(i=(r=e.initialState)==null||(r=r.pagination)==null?void 0:r.pageIndex)!=null?i:X)},e.resetPageSize=n=>{var i,r;e.setPageSize(n?K:(i=(r=e.initialState)==null||(r=r.pagination)==null?void 0:r.pageSize)!=null?i:K)},e.setPageSize=n=>{e.setPagination(i=>{const r=Math.max(1,x(n,i.pageSize)),l=i.pageSize*i.pageIndex,u=Math.floor(l/r);return{...i,pageIndex:u,pageSize:r}})},e.setPageCount=n=>e.setPagination(i=>{var r;let l=x(n,(r=e.options.pageCount)!=null?r:-1);return typeof l=="number"&&(l=Math.max(-1,l)),{...i,pageCount:l}}),e.getPageOptions=S(()=>[e.getPageCount()],n=>{let i=[];return n&&n>0&&(i=[...new Array(n)].fill(null).map((r,l)=>l)),i},m(e.options,"debugTable")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:n}=e.getState().pagination,i=e.getPageCount();return i===-1?!0:i===0?!1:n<i-1},e.previousPage=()=>e.setPageIndex(n=>n-1),e.nextPage=()=>e.setPageIndex(n=>n+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var n;return(n=e.options.pageCount)!=null?n:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var n;return(n=e.options.rowCount)!=null?n:e.getPrePaginationRowModel().rows.length}}},k=()=>({top:[],bottom:[]}),Xe={getInitialState:e=>({rowPinning:k(),...e}),getDefaultOptions:e=>({onRowPinningChange:F("rowPinning",e)}),createRow:(e,o)=>{e.pin=(t,n,i)=>{const r=n?e.getLeafRows().map(s=>{let{id:g}=s;return g}):[],l=i?e.getParentRows().map(s=>{let{id:g}=s;return g}):[],u=new Set([...l,e.id,...r]);o.setRowPinning(s=>{var g,c;if(t==="bottom"){var C,d;return{top:((C=s==null?void 0:s.top)!=null?C:[]).filter(p=>!(u!=null&&u.has(p))),bottom:[...((d=s==null?void 0:s.bottom)!=null?d:[]).filter(p=>!(u!=null&&u.has(p))),...Array.from(u)]}}if(t==="top"){var a,f;return{top:[...((a=s==null?void 0:s.top)!=null?a:[]).filter(p=>!(u!=null&&u.has(p))),...Array.from(u)],bottom:((f=s==null?void 0:s.bottom)!=null?f:[]).filter(p=>!(u!=null&&u.has(p)))}}return{top:((g=s==null?void 0:s.top)!=null?g:[]).filter(p=>!(u!=null&&u.has(p))),bottom:((c=s==null?void 0:s.bottom)!=null?c:[]).filter(p=>!(u!=null&&u.has(p)))}})},e.getCanPin=()=>{var t;const{enableRowPinning:n,enablePinning:i}=o.options;return typeof n=="function"?n(e):(t=n??i)!=null?t:!0},e.getIsPinned=()=>{const t=[e.id],{top:n,bottom:i}=o.getState().rowPinning,r=t.some(u=>n==null?void 0:n.includes(u)),l=t.some(u=>i==null?void 0:i.includes(u));return r?"top":l?"bottom":!1},e.getPinnedIndex=()=>{var t,n;const i=e.getIsPinned();if(!i)return-1;const r=(t=i==="top"?o.getTopRows():o.getBottomRows())==null?void 0:t.map(l=>{let{id:u}=l;return u});return(n=r==null?void 0:r.indexOf(e.id))!=null?n:-1}},createTable:e=>{e.setRowPinning=o=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(o),e.resetRowPinning=o=>{var t,n;return e.setRowPinning(o?k():(t=(n=e.initialState)==null?void 0:n.rowPinning)!=null?t:k())},e.getIsSomeRowsPinned=o=>{var t;const n=e.getState().rowPinning;if(!o){var i,r;return!!((i=n.top)!=null&&i.length||(r=n.bottom)!=null&&r.length)}return!!((t=n[o])!=null&&t.length)},e._getPinnedRows=(o,t,n)=>{var i;return((i=e.options.keepPinnedRows)==null||i?(t??[]).map(l=>{const u=e.getRow(l,!0);return u.getIsAllParentsExpanded()?u:null}):(t??[]).map(l=>o.find(u=>u.id===l))).filter(Boolean).map(l=>({...l,position:n}))},e.getTopRows=S(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(o,t)=>e._getPinnedRows(o,t,"top"),m(e.options,"debugRows")),e.getBottomRows=S(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(o,t)=>e._getPinnedRows(o,t,"bottom"),m(e.options,"debugRows")),e.getCenterRows=S(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(o,t,n)=>{const i=new Set([...t??[],...n??[]]);return o.filter(r=>!i.has(r.id))},m(e.options,"debugRows"))}},Ke={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:F("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=o=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(o),e.resetRowSelection=o=>{var t;return e.setRowSelection(o?{}:(t=e.initialState.rowSelection)!=null?t:{})},e.toggleAllRowsSelected=o=>{e.setRowSelection(t=>{o=typeof o<"u"?o:!e.getIsAllRowsSelected();const n={...t},i=e.getPreGroupedRowModel().flatRows;return o?i.forEach(r=>{r.getCanSelect()&&(n[r.id]=!0)}):i.forEach(r=>{delete n[r.id]}),n})},e.toggleAllPageRowsSelected=o=>e.setRowSelection(t=>{const n=typeof o<"u"?o:!e.getIsAllPageRowsSelected(),i={...t};return e.getRowModel().rows.forEach(r=>{J(i,r.id,n,!0,e)}),i}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=S(()=>[e.getState().rowSelection,e.getCoreRowModel()],(o,t)=>Object.keys(o).length?N(e,t):{rows:[],flatRows:[],rowsById:{}},m(e.options,"debugTable")),e.getFilteredSelectedRowModel=S(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(o,t)=>Object.keys(o).length?N(e,t):{rows:[],flatRows:[],rowsById:{}},m(e.options,"debugTable")),e.getGroupedSelectedRowModel=S(()=>[e.getState().rowSelection,e.getSortedRowModel()],(o,t)=>Object.keys(o).length?N(e,t):{rows:[],flatRows:[],rowsById:{}},m(e.options,"debugTable")),e.getIsAllRowsSelected=()=>{const o=e.getFilteredRowModel().flatRows,{rowSelection:t}=e.getState();let n=!!(o.length&&Object.keys(t).length);return n&&o.some(i=>i.getCanSelect()&&!t[i.id])&&(n=!1),n},e.getIsAllPageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows.filter(i=>i.getCanSelect()),{rowSelection:t}=e.getState();let n=!!o.length;return n&&o.some(i=>!t[i.id])&&(n=!1),n},e.getIsSomeRowsSelected=()=>{var o;const t=Object.keys((o=e.getState().rowSelection)!=null?o:{}).length;return t>0&&t<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:o.filter(t=>t.getCanSelect()).some(t=>t.getIsSelected()||t.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>o=>{e.toggleAllRowsSelected(o.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>o=>{e.toggleAllPageRowsSelected(o.target.checked)}},createRow:(e,o)=>{e.toggleSelected=(t,n)=>{const i=e.getIsSelected();o.setRowSelection(r=>{var l;if(t=typeof t<"u"?t:!i,e.getCanSelect()&&i===t)return r;const u={...r};return J(u,e.id,t,(l=n==null?void 0:n.selectChildren)!=null?l:!0,o),u})},e.getIsSelected=()=>{const{rowSelection:t}=o.getState();return Z(e,t)},e.getIsSomeSelected=()=>{const{rowSelection:t}=o.getState();return Q(e,t)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:t}=o.getState();return Q(e,t)==="all"},e.getCanSelect=()=>{var t;return typeof o.options.enableRowSelection=="function"?o.options.enableRowSelection(e):(t=o.options.enableRowSelection)!=null?t:!0},e.getCanSelectSubRows=()=>{var t;return typeof o.options.enableSubRowSelection=="function"?o.options.enableSubRowSelection(e):(t=o.options.enableSubRowSelection)!=null?t:!0},e.getCanMultiSelect=()=>{var t;return typeof o.options.enableMultiRowSelection=="function"?o.options.enableMultiRowSelection(e):(t=o.options.enableMultiRowSelection)!=null?t:!0},e.getToggleSelectedHandler=()=>{const t=e.getCanSelect();return n=>{var i;t&&e.toggleSelected((i=n.target)==null?void 0:i.checked)}}}},J=(e,o,t,n,i)=>{var r;const l=i.getRow(o,!0);t?(l.getCanMultiSelect()||Object.keys(e).forEach(u=>delete e[u]),l.getCanSelect()&&(e[o]=!0)):delete e[o],n&&(r=l.subRows)!=null&&r.length&&l.getCanSelectSubRows()&&l.subRows.forEach(u=>J(e,u.id,t,n,i))};function N(e,o){const t=e.getState().rowSelection,n=[],i={},r=function(l,u){return l.map(s=>{var g;const c=Z(s,t);if(c&&(n.push(s),i[s.id]=s),(g=s.subRows)!=null&&g.length&&(s={...s,subRows:r(s.subRows)}),c)return s}).filter(Boolean)};return{rows:r(o.rows),flatRows:n,rowsById:i}}function Z(e,o){var t;return(t=o[e.id])!=null?t:!1}function Q(e,o,t){var n;if(!((n=e.subRows)!=null&&n.length))return!1;let i=!0,r=!1;return e.subRows.forEach(l=>{if(!(r&&!i)&&(l.getCanSelect()&&(Z(l,o)?r=!0:i=!1),l.subRows&&l.subRows.length)){const u=Q(l,o);u==="all"?r=!0:(u==="some"&&(r=!0),i=!1)}}),i?"all":r?"some":!1}const W=/([0-9]+)/gm,Je=(e,o,t)=>pe(I(e.getValue(t)).toLowerCase(),I(o.getValue(t)).toLowerCase()),Qe=(e,o,t)=>pe(I(e.getValue(t)),I(o.getValue(t))),We=(e,o,t)=>b(I(e.getValue(t)).toLowerCase(),I(o.getValue(t)).toLowerCase()),Ye=(e,o,t)=>b(I(e.getValue(t)),I(o.getValue(t))),Ze=(e,o,t)=>{const n=e.getValue(t),i=o.getValue(t);return n>i?1:n<i?-1:0},be=(e,o,t)=>b(e.getValue(t),o.getValue(t));function b(e,o){return e===o?0:e>o?1:-1}function I(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function pe(e,o){const t=e.split(W).filter(Boolean),n=o.split(W).filter(Boolean);for(;t.length&&n.length;){const i=t.shift(),r=n.shift(),l=parseInt(i,10),u=parseInt(r,10),s=[l,u].sort();if(isNaN(s[0])){if(i>r)return 1;if(r>i)return-1;continue}if(isNaN(s[1]))return isNaN(l)?-1:1;if(l>u)return 1;if(u>l)return-1}return t.length-n.length}const D={alphanumeric:Je,alphanumericCaseSensitive:Qe,text:We,textCaseSensitive:Ye,datetime:Ze,basic:be},et={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:F("sorting",e),isMultiSortEvent:o=>o.shiftKey}),createColumn:(e,o)=>{e.getAutoSortingFn=()=>{const t=o.getFilteredRowModel().flatRows.slice(10);let n=!1;for(const i of t){const r=i==null?void 0:i.getValue(e.id);if(Object.prototype.toString.call(r)==="[object Date]")return D.datetime;if(typeof r=="string"&&(n=!0,r.split(W).length>1))return D.alphanumeric}return n?D.text:D.basic},e.getAutoSortDir=()=>{const t=o.getFilteredRowModel().flatRows[0];return typeof(t==null?void 0:t.getValue(e.id))=="string"?"asc":"desc"},e.getSortingFn=()=>{var t,n;if(!e)throw new Error;return z(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(t=(n=o.options.sortingFns)==null?void 0:n[e.columnDef.sortingFn])!=null?t:D[e.columnDef.sortingFn]},e.toggleSorting=(t,n)=>{const i=e.getNextSortingOrder(),r=typeof t<"u"&&t!==null;o.setSorting(l=>{const u=l==null?void 0:l.find(a=>a.id===e.id),s=l==null?void 0:l.findIndex(a=>a.id===e.id);let g=[],c,C=r?t:i==="desc";if(l!=null&&l.length&&e.getCanMultiSort()&&n?u?c="toggle":c="add":l!=null&&l.length&&s!==l.length-1?c="replace":u?c="toggle":c="replace",c==="toggle"&&(r||i||(c="remove")),c==="add"){var d;g=[...l,{id:e.id,desc:C}],g.splice(0,g.length-((d=o.options.maxMultiSortColCount)!=null?d:Number.MAX_SAFE_INTEGER))}else c==="toggle"?g=l.map(a=>a.id===e.id?{...a,desc:C}:a):c==="remove"?g=l.filter(a=>a.id!==e.id):g=[{id:e.id,desc:C}];return g})},e.getFirstSortDir=()=>{var t,n;return((t=(n=e.columnDef.sortDescFirst)!=null?n:o.options.sortDescFirst)!=null?t:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=t=>{var n,i;const r=e.getFirstSortDir(),l=e.getIsSorted();return l?l!==r&&((n=o.options.enableSortingRemoval)==null||n)&&(!(t&&(i=o.options.enableMultiRemove)!=null)||i)?!1:l==="desc"?"asc":"desc":r},e.getCanSort=()=>{var t,n;return((t=e.columnDef.enableSorting)!=null?t:!0)&&((n=o.options.enableSorting)!=null?n:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var t,n;return(t=(n=e.columnDef.enableMultiSort)!=null?n:o.options.enableMultiSort)!=null?t:!!e.accessorFn},e.getIsSorted=()=>{var t;const n=(t=o.getState().sorting)==null?void 0:t.find(i=>i.id===e.id);return n?n.desc?"desc":"asc":!1},e.getSortIndex=()=>{var t,n;return(t=(n=o.getState().sorting)==null?void 0:n.findIndex(i=>i.id===e.id))!=null?t:-1},e.clearSorting=()=>{o.setSorting(t=>t!=null&&t.length?t.filter(n=>n.id!==e.id):[])},e.getToggleSortingHandler=()=>{const t=e.getCanSort();return n=>{t&&(n.persist==null||n.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?o.options.isMultiSortEvent==null?void 0:o.options.isMultiSortEvent(n):!1))}}},createTable:e=>{e.setSorting=o=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(o),e.resetSorting=o=>{var t,n;e.setSorting(o?[]:(t=(n=e.initialState)==null?void 0:n.sorting)!=null?t:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},tt=[he,qe,ze,Le,Fe,$e,je,ke,et,Ge,Ne,Ue,Xe,Ke,Be];function nt(e){var o,t;const n=[...tt,...(o=e._features)!=null?o:[]];let i={_features:n};const r=i._features.reduce((d,a)=>Object.assign(d,a.getDefaultOptions==null?void 0:a.getDefaultOptions(i)),{}),l=d=>i.options.mergeOptions?i.options.mergeOptions(r,d):{...r,...d};let s={...{},...(t=e.initialState)!=null?t:{}};i._features.forEach(d=>{var a;s=(a=d.getInitialState==null?void 0:d.getInitialState(s))!=null?a:s});const g=[];let c=!1;const C={_features:n,options:{...r,...e},initialState:s,_queue:d=>{g.push(d),c||(c=!0,Promise.resolve().then(()=>{for(;g.length;)g.shift()();c=!1}).catch(a=>setTimeout(()=>{throw a})))},reset:()=>{i.setState(i.initialState)},setOptions:d=>{const a=x(d,i.options);i.options=l(a)},getState:()=>i.options.state,setState:d=>{i.options.onStateChange==null||i.options.onStateChange(d)},_getRowId:(d,a,f)=>{var p;return(p=i.options.getRowId==null?void 0:i.options.getRowId(d,a,f))!=null?p:`${f?[f.id,a].join("."):a}`},getCoreRowModel:()=>(i._getCoreRowModel||(i._getCoreRowModel=i.options.getCoreRowModel(i)),i._getCoreRowModel()),getRowModel:()=>i.getPaginationRowModel(),getRow:(d,a)=>{let f=(a?i.getPrePaginationRowModel():i.getRowModel()).rowsById[d];if(!f&&(f=i.getCoreRowModel().rowsById[d],!f))throw new Error;return f},_getDefaultColumnDef:S(()=>[i.options.defaultColumn],d=>{var a;return d=(a=d)!=null?a:{},{header:f=>{const p=f.header.column.columnDef;return p.accessorKey?p.accessorKey:p.accessorFn?p.id:null},cell:f=>{var p,w;return(p=(w=f.renderValue())==null||w.toString==null?void 0:w.toString())!=null?p:null},...i._features.reduce((f,p)=>Object.assign(f,p.getDefaultColumnDef==null?void 0:p.getDefaultColumnDef()),{}),...d}},m(e,"debugColumns")),_getColumnDefs:()=>i.options.columns,getAllColumns:S(()=>[i._getColumnDefs()],d=>{const a=function(f,p,w){return w===void 0&&(w=0),f.map(R=>{const _=ve(i,R,w,p),v=R;return _.columns=v.columns?a(v.columns,_,w+1):[],_})};return a(d)},m(e,"debugColumns")),getAllFlatColumns:S(()=>[i.getAllColumns()],d=>d.flatMap(a=>a.getFlatColumns()),m(e,"debugColumns")),_getAllFlatColumnsById:S(()=>[i.getAllFlatColumns()],d=>d.reduce((a,f)=>(a[f.id]=f,a),{}),m(e,"debugColumns")),getAllLeafColumns:S(()=>[i.getAllColumns(),i._getOrderColumnsFn()],(d,a)=>{let f=d.flatMap(p=>p.getLeafColumns());return a(f)},m(e,"debugColumns")),getColumn:d=>i._getAllFlatColumnsById()[d]};Object.assign(i,C);for(let d=0;d<i._features.length;d++){const a=i._features[d];a==null||a.createTable==null||a.createTable(i)}return i}function at(){return e=>S(()=>[e.options.data],o=>{const t={rows:[],flatRows:[],rowsById:{}},n=function(i,r,l){r===void 0&&(r=0);const u=[];for(let g=0;g<i.length;g++){const c=_e(e,e._getRowId(i[g],g,l),i[g],g,r,void 0,l==null?void 0:l.id);if(t.flatRows.push(c),t.rowsById[c.id]=c,u.push(c),e.options.getSubRows){var s;c.originalSubRows=e.options.getSubRows(i[g],g),(s=c.originalSubRows)!=null&&s.length&&(c.subRows=n(c.originalSubRows,r+1,c))}}return u};return t.rows=n(o),t},m(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function re(e,o){return e?ot(e)?U.createElement(e,o):e:null}function ot(e){return it(e)||typeof e=="function"||rt(e)}function it(e){return typeof e=="function"&&(()=>{const o=Object.getPrototypeOf(e);return o.prototype&&o.prototype.isReactComponent})()}function rt(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function gt(e){const o={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[t]=U.useState(()=>({current:nt(o)})),[n,i]=U.useState(()=>t.current.initialState);return t.current.setOptions(r=>({...r,...e,state:{...n,...e.state},onStateChange:l=>{i(l),e.onStateChange==null||e.onStateChange(l)}})),t.current}function dt({table:e}){return P.jsxs("table",{className:"table",children:[P.jsx("thead",{children:e.getHeaderGroups().map(o=>P.jsx("tr",{children:o.headers.map(t=>P.jsx("th",{children:t.isPlaceholder?null:re(t.column.columnDef.header,t.getContext())},t.id))},o.id))}),P.jsx("tbody",{children:e.getRowModel().rows.map(o=>P.jsx("tr",{children:o.getVisibleCells().map(t=>P.jsx("td",{children:re(t.column.columnDef.cell,t.getContext())},t.id))},o.id))})]})}export{dt as B,ut as C,st as c,at as g,gt as u};
