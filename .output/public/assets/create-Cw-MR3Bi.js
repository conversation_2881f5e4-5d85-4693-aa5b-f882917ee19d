import{u as x,b,r as h,y as u,j as e,L as m,c as j}from"./main-RY8ZkTMc.js";import{u as f}from"./form-BdoD3Q87.js";import{g as v}from"./effectErrors-D8W8e9uM.js";import{u as S}from"./useMutation-D8585ZL-.js";import{c as D}from"./classes-CraQI9Rs.js";import{A as N}from"./runtimes-CTOS42-v.js";import{s as g}from"./schedule-options-BL0O7AQ8.js";import{C as y,d as C,a as d,T as A,S as T,A as k}from"./index-C7U4vq_b.js";import{F}from"./file-text-DYqz-Dos.js";import"./createLucideIcon-78bvTjT9.js";import"./queryOptions-C9woPjwX.js";function H(){const n=x(),{schedule:i}=n,l=b(),r=g(n).queryKey;return S({mutationFn:s=>N.runPromise(i.create(s)),onSuccess:(s,a)=>{l.setQueryData(r,o=>D(o??[],c=>{c.push({id:s,name:a.name,sessionDuration:a.sessionDuration,breakDuration:a.breakDuration,turns:a.turns.map(t=>({id:"",name:t.name,startTime:t.startTime,endTime:t.endTime,createdAt:new Date().toISOString(),updatedAt:null,deletedAt:null})),createdAt:new Date().toISOString(),updatedAt:null,deletedAt:null})}))}})}function w(){const[n,i]=h.useState(null),{mutate:l}=H(),r=f({defaultValues:C,validators:{onChange:y},onSubmit:({value:s})=>{l({name:s.name,sessionDuration:s.sessionDuration,breakDuration:s.breakDuration,turns:s.turns},{onSuccess:()=>{u.success("Horario creado exitosamente"),window.history.back()},onError:a=>{console.log(a);const{error:o}=v(a);u.error(o.message)}})}});return e.jsx("form",{onSubmit:s=>{s.preventDefault(),r.handleSubmit()},children:e.jsx(r.AppForm,{children:e.jsxs("div",{className:"grid grid-cols-1 gap-8",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("fieldset",{className:"fieldset",children:e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsx(r.AppField,{name:"name",children:({FSTextField:s})=>e.jsx(s,{label:"Nombre del Horario",placeholder:"Nombre del Horario",prefixComponent:e.jsx(F,{size:16})})}),e.jsx(r.AppField,{name:"sessionDuration",children:({FSTextField:s})=>e.jsx(s,{label:"Duración de Sesión (minutos)",placeholder:"60",type:"number",prefixComponent:e.jsx(d,{size:16})})}),e.jsx(r.AppField,{name:"breakDuration",children:({FSTextField:s})=>e.jsx(s,{label:"Duración de Descanso (minutos)",placeholder:"15",type:"number",prefixComponent:e.jsx(d,{size:16})})})]})}),e.jsx(A,{form:r,selectedTurnIndex:n,onTurnSelect:i}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(r.SubscribeButton,{label:"Crear Horario",className:"btn btn-primary"}),e.jsx(m,{to:"/admin/schedules",className:"btn btn-outline",children:"Cancelar"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"font-semibold text-xl",children:"Vista Previa del Horario"}),e.jsx(r.Subscribe,{selector:s=>[s.values.sessionDuration,s.values.breakDuration],children:([s,a,o])=>{const c=j(r.store,p=>p.values.turns),t=n!==null?c[n]:null;return t?e.jsx(T,{turn:t,sessionDuration:s||60,breakDuration:a||15}):e.jsx("div",{className:"card bg-base-200",children:e.jsx("div",{className:"card-body text-center",children:e.jsx("p",{className:"text-base-content/70",children:"Selecciona un turno para ver la vista previa del horario"})})})}})]})]})})})}const B=function(){return e.jsxs("div",{className:"container mx-auto max-w-6xl",children:[e.jsx("div",{className:"mb-6",children:e.jsxs(m,{to:"/admin/schedules",className:"btn btn-ghost",children:[e.jsx(k,{size:16}),"Volver a Horarios"]})}),e.jsx("div",{className:"card bg-base-300",children:e.jsxs("div",{className:"card-body",children:[e.jsx("h2",{className:"card-title mb-6 text-2xl",children:"Crear Nuevo Horario"}),e.jsx(w,{})]})})]})};export{B as component};
