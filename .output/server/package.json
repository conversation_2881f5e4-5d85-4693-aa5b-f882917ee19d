{"name": "schedhold-frontend-prod", "version": "0.0.0", "type": "module", "private": true, "dependencies": {"@babel/runtime": "7.27.4", "@effect/platform": "0.84.11", "@tanstack/form-core": "1.12.3", "@tanstack/history": "1.120.17", "@tanstack/query-core": "5.80.7", "@tanstack/query-devtools": "5.80.0", "@tanstack/react-form": "1.12.3", "@tanstack/react-query": "5.80.7", "@tanstack/react-query-devtools": "5.80.7", "@tanstack/react-router": "1.121.2", "@tanstack/react-router-devtools": "1.121.8", "@tanstack/react-router-with-query": "1.121.2", "@tanstack/react-store": "0.7.1", "@tanstack/react-table": "8.21.3", "@tanstack/router-core": "1.121.2", "@tanstack/router-devtools-core": "1.121.8", "@tanstack/store": "0.7.1", "@tanstack/table-core": "8.21.3", "clsx": "2.1.1", "compute-scroll-into-view": "3.1.1", "downshift": "9.0.9", "effect": "3.16.7", "fast-check": "3.23.2", "find-my-way-ts": "0.1.5", "goober": "2.1.16", "isbot": "5.1.28", "jsesc": "3.1.0", "lucide-react": "0.515.0", "msgpackr": "1.11.4", "multipasta": "0.2.5", "mutative": "1.2.0", "object-assign": "4.1.1", "prop-types": "15.8.1", "pure-rand": "6.1.0", "react": "19.1.0", "react-dom": "19.1.0", "react-is": "18.2.0", "react-toastify": "11.0.5", "seroval": "1.3.2", "seroval-plugins": "1.3.2", "solid-js": "1.9.7", "tailwind-merge": "3.3.1", "tiny-invariant": "1.3.3", "tiny-warning": "1.0.3", "tslib": "2.8.1", "use-mutative": "1.3.0", "use-sync-external-store": "1.5.0", "valibot": "1.1.0"}}