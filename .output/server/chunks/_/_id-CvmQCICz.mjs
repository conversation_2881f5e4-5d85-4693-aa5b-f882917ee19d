import { jsxs, jsx } from 'react/jsx-runtime';
import { Link } from '@tanstack/react-router';
import { ArrowLeft, FileText, Clock } from 'lucide-react';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { R as Route, u as useService } from './ssr.mjs';
import { g as getErrorResult } from './effectErrors-CR6URVJK.mjs';
import { a as scheduleOptionsById, s as scheduleOptions } from './schedule-options-hwpkl8x6.mjs';
import { useStore } from '@tanstack/react-store';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { u as useAppForm } from './form-B3Cqm05b.mjs';
import { create } from 'mutative';
import { A as AppRuntime } from './runtimes-CvAMo_9p.mjs';
import { C as CreateScheduleSchema, T as TurnsTable, S as SchedulePreview } from './index-CJ4nBaQx.mjs';
import '@tanstack/react-query-devtools';
import '@tanstack/react-router-devtools';
import 'valibot';
import 'tiny-invariant';
import 'tiny-warning';
import '@tanstack/router-core';
import 'node:async_hooks';
import 'effect';
import '@tanstack/react-router-with-query';
import '@tanstack/history';
import 'jsesc';
import 'node:stream';
import 'isbot';
import 'react-dom/server';
import 'node:stream/web';
import 'effect/Runtime';
import '@tanstack/react-form';
import 'downshift';
import 'use-mutative';
import './classes-DMlJhLpb.mjs';
import 'clsx';
import 'tailwind-merge';
import '@effect/platform';

function useEditSchedule() {
  const service = useService();
  const { schedule } = service;
  const queryClient = useQueryClient();
  const queryKey = scheduleOptions(service).queryKey;
  return useMutation({
    mutationFn: (updatedSchedule) => AppRuntime.runPromise(schedule.update(updatedSchedule)),
    onSuccess: (_, updatedSchedule) => {
      queryClient.setQueryData(
        queryKey,
        (old) => create(old != null ? old : [], (draft) => {
          const index = draft.findIndex((s) => s.id === updatedSchedule.id);
          if (index !== -1) {
            draft[index] = {
              ...draft[index],
              name: updatedSchedule.name,
              sessionDuration: updatedSchedule.sessionDuration,
              breakDuration: updatedSchedule.breakDuration,
              turns: updatedSchedule.turns.map((turn) => {
                var _a;
                return {
                  id: turn.id,
                  name: turn.name,
                  startTime: turn.startTime,
                  endTime: turn.endTime,
                  createdAt: ((_a = draft[index].turns.find((t) => t.id === turn.id)) == null ? void 0 : _a.createdAt) || null,
                  updatedAt: (/* @__PURE__ */ new Date()).toISOString(),
                  deletedAt: null
                };
              }),
              updatedAt: (/* @__PURE__ */ new Date()).toISOString()
            };
          }
        })
      );
    }
  });
}
function EditSchedule({ id, schedule }) {
  const { mutate } = useEditSchedule();
  const [selectedTurnIndex, setSelectedTurnIndex] = useState(
    null
  );
  const form = useAppForm({
    defaultValues: {
      ...schedule
    },
    validators: {
      onChange: CreateScheduleSchema
    },
    onSubmit: ({ value }) => {
      mutate(
        {
          id,
          name: value.name,
          sessionDuration: value.sessionDuration,
          breakDuration: value.breakDuration,
          turns: value.turns.map((turn, index) => {
            var _a;
            return {
              id: ((_a = schedule == null ? void 0 : schedule.turns[index]) == null ? void 0 : _a.id) || "",
              name: turn.name,
              startTime: turn.startTime,
              endTime: turn.endTime
            };
          })
        },
        {
          onSuccess: () => {
            toast.success("Horario actualizado exitosamente");
            window.history.back();
          },
          onError: (_error) => {
            console.log(_error);
            const { error } = getErrorResult(_error);
            toast.error(error.message);
          }
        }
      );
    }
  });
  return /* @__PURE__ */ jsx(
    "form",
    {
      onSubmit: (e) => {
        e.preventDefault();
        form.handleSubmit();
      },
      children: /* @__PURE__ */ jsx(form.AppForm, { children: /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 gap-8", children: [
        /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
          /* @__PURE__ */ jsx("fieldset", { className: "fieldset", children: /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-3 gap-4", children: [
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "name",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Nombre del Horario",
                    placeholder: "Nombre del Horario",
                    prefixComponent: /* @__PURE__ */ jsx(FileText, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "sessionDuration",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Duraci\xF3n de Sesi\xF3n (minutos)",
                    placeholder: "60",
                    type: "number",
                    prefixComponent: /* @__PURE__ */ jsx(Clock, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "breakDuration",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Duraci\xF3n de Descanso (minutos)",
                    placeholder: "15",
                    type: "number",
                    prefixComponent: /* @__PURE__ */ jsx(Clock, { size: 16 })
                  }
                )
              }
            )
          ] }) }),
          /* @__PURE__ */ jsx(
            TurnsTable,
            {
              form,
              selectedTurnIndex,
              onTurnSelect: setSelectedTurnIndex
            }
          ),
          /* @__PURE__ */ jsxs("div", { className: "flex gap-4", children: [
            /* @__PURE__ */ jsx(
              form.SubscribeButton,
              {
                label: "Actualizar Horario",
                className: "btn btn-primary"
              }
            ),
            /* @__PURE__ */ jsx(Link, { to: "/admin/schedules", className: "btn btn-outline", children: "Cancelar" })
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
          /* @__PURE__ */ jsx("h3", { className: "font-semibold text-xl", children: "Vista Previa del Horario" }),
          /* @__PURE__ */ jsx(
            form.Subscribe,
            {
              selector: (state) => [
                state.values.sessionDuration,
                state.values.breakDuration
              ],
              children: ([sessionDuration, breakDuration, _]) => {
                const turns = useStore(
                  form.store,
                  (state) => state.values.turns
                );
                const selectedTurn = selectedTurnIndex !== null ? turns[selectedTurnIndex] : null;
                if (selectedTurn)
                  return /* @__PURE__ */ jsx(
                    SchedulePreview,
                    {
                      turn: selectedTurn,
                      sessionDuration: sessionDuration || 60,
                      breakDuration: breakDuration || 15
                    }
                  );
                return /* @__PURE__ */ jsx("div", { className: "card bg-base-200", children: /* @__PURE__ */ jsx("div", { className: "card-body text-center", children: /* @__PURE__ */ jsx("p", { className: "text-base-content/70", children: "Selecciona un turno para ver la vista previa del horario" }) }) });
              }
            }
          )
        ] })
      ] }) })
    }
  );
}
function EditScheduleForm({ id }) {
  const svc = useService();
  const {
    data: schedule,
    isLoading,
    error,
    isSuccess
  } = useQuery(scheduleOptionsById(svc, id));
  if (isLoading) {
    return /* @__PURE__ */ jsx("div", { className: "flex h-64 items-center justify-center", children: /* @__PURE__ */ jsx("span", { className: "loading loading-spinner loading-lg" }) });
  }
  if (error) {
    return /* @__PURE__ */ jsx("div", { className: "alert alert-error", children: /* @__PURE__ */ jsxs("span", { children: [
      "Error: ",
      getErrorResult(error).error.message
    ] }) });
  }
  if (isSuccess) return /* @__PURE__ */ jsx(EditSchedule, { id, schedule });
}
const SplitComponent = function RouteComponent() {
  const {
    id
  } = Route.useParams();
  return /* @__PURE__ */ jsxs("div", { className: "container mx-auto max-w-6xl", children: [
    /* @__PURE__ */ jsx("div", { className: "mb-6", children: /* @__PURE__ */ jsxs(Link, { to: "/admin/schedules", className: "btn btn-ghost", children: [
      /* @__PURE__ */ jsx(ArrowLeft, { size: 16 }),
      "Volver a Horarios"
    ] }) }),
    /* @__PURE__ */ jsx("div", { className: "card bg-base-300", children: /* @__PURE__ */ jsxs("div", { className: "card-body", children: [
      /* @__PURE__ */ jsx("h2", { className: "card-title mb-6 text-2xl", children: "Editar Horario" }),
      /* @__PURE__ */ jsx(EditScheduleForm, { id })
    ] }) })
  ] });
};

export { SplitComponent as component };
//# sourceMappingURL=_id-CvmQCICz.mjs.map
