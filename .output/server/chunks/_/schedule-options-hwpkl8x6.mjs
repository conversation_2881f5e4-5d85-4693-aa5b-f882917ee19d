import { queryOptions } from '@tanstack/react-query';
import { A as AppRuntime } from './runtimes-CvAMo_9p.mjs';

const scheduleOptions = ({ schedule }) => queryOptions({
  queryKey: ["schedules"],
  queryFn: () => AppRuntime.runPromise(schedule.getAll())
});
const scheduleOptionsById = ({ schedule }, id) => queryOptions({
  queryKey: ["schedules", id],
  queryFn: () => AppRuntime.runPromise(schedule.getById(id))
});

export { scheduleOptionsById as a, scheduleOptions as s };
//# sourceMappingURL=schedule-options-hwpkl8x6.mjs.map
