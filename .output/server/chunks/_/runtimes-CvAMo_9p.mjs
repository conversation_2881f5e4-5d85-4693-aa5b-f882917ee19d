import { ManagedRuntime, Schema, Layer, Effect, Context, Data, Either } from 'effect';
import { HttpBody, FetchHttpClient, HttpClient, HttpClientRequest } from '@effect/platform';
import { A as AuthUsecase, C as ClientUsecase, W as WorkerUsecase, S as ScheduleUsecase, c as constants } from './ssr.mjs';

var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, key + "" , value);
const ErrorSchema = Schema.Struct({
  message: Schema.String,
  details: Schema.optional(Schema.Unknown),
  code: Schema.Number
});
const ErrorResult = Schema.Struct({
  error: ErrorSchema,
  correlationId: Schema.String
});
class AppError extends Data.TaggedError("AppError") {
}
var ErrorCode = /* @__PURE__ */ ((ErrorCode2) => {
  ErrorCode2[ErrorCode2["ClientParseError"] = -3] = "ClientParseError";
  ErrorCode2[ErrorCode2["RequestError"] = -2] = "RequestError";
  ErrorCode2[ErrorCode2["ResponseError"] = -1] = "ResponseError";
  ErrorCode2[ErrorCode2["InternalErrorCode"] = 0] = "InternalErrorCode";
  ErrorCode2[ErrorCode2["MultiErrorCode"] = 1] = "MultiErrorCode";
  ErrorCode2[ErrorCode2["ParseErrorCode"] = 2] = "ParseErrorCode";
  ErrorCode2[ErrorCode2["BadRequestCode"] = 3] = "BadRequestCode";
  ErrorCode2[ErrorCode2["UnauthorizedCode"] = 4] = "UnauthorizedCode";
  ErrorCode2[ErrorCode2["NotFoundCode"] = 5] = "NotFoundCode";
  ErrorCode2[ErrorCode2["ConflictCode"] = 6] = "ConflictCode";
  return ErrorCode2;
})(ErrorCode || {});
const CustomFetchLive = FetchHttpClient.layer.pipe(
  Layer.provide(
    Layer.succeed(FetchHttpClient.RequestInit, {
      credentials: "include"
    })
  )
);
const makeApiHttpClient = Effect.gen(function* () {
  const httpClient = (yield* HttpClient.HttpClient).pipe(
    HttpClient.mapRequest(
      HttpClientRequest.prependUrl(`${constants.API_URL}/api`)
    ),
    HttpClient.filterStatusOk,
    HttpClient.transformResponse(
      Effect.catchTags({
        RequestError: (error) => Effect.fail(
          new AppError({
            correlationId: ErrorCode.RequestError.toString(),
            error: {
              code: ErrorCode.RequestError,
              details: error,
              message: "Request error"
            }
          })
        ),
        ResponseError: (error) => error.response.json.pipe(
          Effect.flatMap(
            (response) => Schema.decodeUnknownEither(ErrorSchema)(response).pipe(
              Either.match({
                onLeft: (decodeError) => {
                  var _a;
                  return Effect.fail(
                    new AppError({
                      correlationId: (_a = error.response.headers[constants.CORRELATION_ID_HEADER]) != null ? _a : ErrorCode.ResponseError.toString(),
                      error: {
                        code: ErrorCode.ResponseError,
                        details: decodeError,
                        message: "Response error"
                      }
                    })
                  );
                },
                onRight: (errorResult) => {
                  var _a;
                  return Effect.fail(
                    new AppError({
                      correlationId: (_a = error.response.headers[constants.CORRELATION_ID_HEADER]) != null ? _a : ErrorCode.ResponseError.toString(),
                      error: errorResult
                    })
                  );
                }
              })
            )
          )
        )
      })
    ),
    HttpClient.transformResponse(
      Effect.catchTags({
        ResponseError: (error) => error.response.json.pipe(
          Schema.decodeUnknownEither(ErrorSchema),
          Either.match({
            onLeft: (decodeError) => {
              var _a;
              return Effect.fail(
                new AppError({
                  correlationId: (_a = error.response.headers[constants.CORRELATION_ID_HEADER]) != null ? _a : ErrorCode.ResponseError.toString(),
                  error: {
                    code: ErrorCode.ClientParseError,
                    details: decodeError,
                    message: "Response error"
                  }
                })
              );
            },
            onRight: (errorResult) => {
              var _a;
              return Effect.fail(
                new AppError({
                  correlationId: (_a = error.response.headers[constants.CORRELATION_ID_HEADER]) != null ? _a : ErrorCode.ResponseError.toString(),
                  error: errorResult
                })
              );
            }
          })
        )
      })
    )
  );
  return { httpClient };
}).pipe(Effect.provide(CustomFetchLive));
const _ApiHttpClient = class _ApiHttpClient2 extends Context.Tag("ApiHttpClient")() {
};
__publicField(_ApiHttpClient, "Live", Layer.effect(_ApiHttpClient, makeApiHttpClient));
let ApiHttpClient = _ApiHttpClient;
const handleDResponse = (schema) => (response) => response.json.pipe(
  Effect.flatMap(Schema.decodeUnknown(schema)),
  Effect.catchTags({
    ParseError: (error) => Effect.fail(
      new AppError({
        correlationId: ErrorCode.ClientParseError.toString(),
        error: {
          code: ErrorCode.ClientParseError,
          details: error,
          message: "Parse error"
        }
      })
    ),
    ResponseError: (error) => {
      var _a;
      return Effect.fail(
        new AppError({
          correlationId: (_a = response.headers[constants.CORRELATION_ID_HEADER]) != null ? _a : ErrorCode.ResponseError.toString(),
          error: {
            code: ErrorCode.ResponseError,
            details: error,
            message: "Response error"
          }
        })
      );
    }
  })
);
const handleResponse = (response) => response.json.pipe(
  Effect.catchTags({
    ResponseError: (error) => {
      var _a;
      return Effect.fail(
        new AppError({
          correlationId: (_a = response.headers[constants.CORRELATION_ID_HEADER]) != null ? _a : ErrorCode.ResponseError.toString(),
          error: {
            code: ErrorCode.ResponseError,
            details: error,
            message: "Response error"
          }
        })
      );
    }
  }),
  Effect.andThen(Effect.succeed(Effect.void))
);
class AuthRepository extends Effect.Tag("AuthRepository")() {
}
const User = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  email: Schema.String,
  createdAt: Schema.NullOr(Schema.String),
  updatedAt: Schema.NullOr(Schema.String),
  deletedAt: Schema.NullOr(Schema.String)
});
const UserApiStruct = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  email: Schema.String,
  created_at: Schema.NullOr(Schema.String),
  updated_at: Schema.NullOr(Schema.String),
  deleted_at: Schema.NullOr(Schema.String)
});
const UserApi = Schema.transform(UserApiStruct, User, {
  strict: true,
  decode: (userApi) => ({
    ...userApi,
    createdAt: userApi.created_at,
    updatedAt: userApi.updated_at,
    deletedAt: userApi.deleted_at
  }),
  encode: (user) => ({
    ...user,
    created_at: user.createdAt,
    updated_at: user.updatedAt,
    deleted_at: user.deletedAt
  })
});
const LoginCredentials = Schema.Struct({
  username: Schema.String,
  password: Schema.String
});
Schema.Struct({
  user: User
});
const SessionApi = Schema.Struct({
  user: UserApi
});
const LoginCredentialsApi = LoginCredentials;
const baseUrl$4 = "/v1/auth";
const makeAuthApiRepo = ApiHttpClient.pipe(
  Effect.andThen(({ httpClient }) => httpClient),
  Effect.andThen((httpClient) => ({
    login: (credentials) => httpClient.post(`${baseUrl$4}/login`, {
      body: HttpBody.unsafeJson(
        Schema.decodeUnknownSync(LoginCredentialsApi)(credentials)
      )
    }).pipe(Effect.flatMap(handleDResponse(SessionApi))),
    logout: () => httpClient.post(`${baseUrl$4}/logout`).pipe(Effect.flatMap(handleResponse)),
    getSession: () => httpClient.get(`${baseUrl$4}/is_logged_in`).pipe(Effect.flatMap(handleDResponse(SessionApi)))
  })),
  Effect.provide(ApiHttpClient.Live)
);
const authApiRepoLive = Layer.effect(AuthRepository, makeAuthApiRepo);
const authRepositoryLive = Layer.effect(AuthRepository, AuthRepository);
const authUsecaseLive = Layer.effect(AuthUsecase, AuthRepository);
class ClientRepository extends Effect.Tag("ClientRepository")() {
}
var DocumentType = /* @__PURE__ */ ((DocumentType2) => {
  DocumentType2[DocumentType2["DNI"] = 0] = "DNI";
  DocumentType2[DocumentType2["PASAPORTE"] = 1] = "PASAPORTE";
  DocumentType2[DocumentType2["RUC"] = 2] = "RUC";
  return DocumentType2;
})(DocumentType || {});
const Person = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  fatherLastName: Schema.String,
  motherLastName: Schema.String,
  email: Schema.optional(Schema.String),
  address: Schema.optional(Schema.String),
  phone: Schema.optional(Schema.String),
  birthDate: Schema.NullOr(Schema.String),
  gender: Schema.Boolean,
  document: Schema.String,
  documentType: Schema.Number,
  createdAt: Schema.NullOr(Schema.String),
  updatedAt: Schema.NullOr(Schema.String),
  deletedAt: Schema.NullOr(Schema.String)
});
const CreatePerson = Person.omit(
  "id",
  "createdAt",
  "updatedAt",
  "deletedAt"
);
const UpdatePerson = Person.omit("createdAt", "updatedAt", "deletedAt");
const PersonApi = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  father_last_name: Schema.String,
  mother_last_name: Schema.String,
  email: Schema.optional(Schema.String),
  address: Schema.optional(Schema.String),
  phone: Schema.optional(Schema.String),
  birth_date: Schema.NullOr(Schema.String),
  gender: Schema.Boolean,
  document: Schema.String,
  document_type: Schema.Number,
  created_at: Schema.NullOr(Schema.String),
  updated_at: Schema.NullOr(Schema.String),
  deleted_at: Schema.NullOr(Schema.String)
});
const PersonFromApi = Schema.transform(PersonApi, Person, {
  strict: true,
  decode: (personApi) => ({
    ...personApi,
    fatherLastName: personApi.father_last_name,
    motherLastName: personApi.mother_last_name,
    birthDate: personApi.birth_date,
    documentType: personApi.document_type,
    createdAt: personApi.created_at,
    updatedAt: personApi.updated_at,
    deletedAt: personApi.deleted_at
  }),
  encode: (person) => ({
    ...person,
    father_last_name: person.fatherLastName,
    mother_last_name: person.motherLastName,
    birth_date: person.birthDate,
    document_type: person.documentType,
    created_at: person.createdAt,
    updated_at: person.updatedAt,
    deleted_at: person.deletedAt
  })
});
const PersonListFromApi = Schema.transform(
  Schema.mutable(Schema.NullishOr(Schema.Array(PersonFromApi))),
  Schema.mutable(Schema.Array(Person)),
  {
    strict: true,
    decode: (personApiList) => personApiList ? personApiList : [],
    encode: (personList) => personList
  }
);
const CreatePersonApi = PersonApi.omit(
  "id",
  "created_at",
  "updated_at",
  "deleted_at"
);
const CreatePersonApiFromCreatePerson = Schema.transform(
  CreatePerson,
  CreatePersonApi,
  {
    strict: true,
    decode: (person) => ({
      ...person,
      father_last_name: person.fatherLastName,
      mother_last_name: person.motherLastName,
      birth_date: person.birthDate,
      document_type: person.documentType
    }),
    encode: (personApi) => ({
      ...personApi,
      fatherLastName: personApi.father_last_name,
      motherLastName: personApi.mother_last_name,
      birthDate: personApi.birth_date,
      documentType: personApi.document_type
    })
  }
);
const CreatePersonApiResponse = Schema.String;
const UpdatePersonApi = PersonApi.omit(
  "created_at",
  "updated_at",
  "deleted_at"
);
const UpdatePersonApiFromUpdatePerson = Schema.transform(
  UpdatePerson,
  UpdatePersonApi,
  {
    strict: true,
    decode: (person) => ({
      ...person,
      father_last_name: person.fatherLastName,
      mother_last_name: person.motherLastName,
      birth_date: person.birthDate,
      document_type: person.documentType
    }),
    encode: (personApi) => ({
      ...personApi,
      fatherLastName: personApi.father_last_name,
      motherLastName: personApi.mother_last_name,
      birthDate: personApi.birth_date,
      documentType: personApi.document_type
    })
  }
);
const Client = Schema.Struct({
  id: Schema.String,
  person: Person,
  createdAt: Schema.NullOr(Schema.String),
  updatedAt: Schema.NullOr(Schema.String),
  deletedAt: Schema.NullOr(Schema.String)
});
const CreateClient = Schema.Struct({
  person: CreatePerson
});
const UpdateClient = Schema.Struct({
  id: Schema.String,
  person: UpdatePerson
});
const ClientApi = Schema.Struct({
  id: Schema.String,
  person: PersonApi,
  created_at: Schema.NullOr(Schema.String),
  updated_at: Schema.NullOr(Schema.String),
  deleted_at: Schema.NullOr(Schema.String)
});
const ClientFromApi = Schema.transform(ClientApi, Client, {
  strict: true,
  decode: (clientApi) => ({
    ...clientApi,
    person: Schema.decodeUnknownSync(PersonFromApi)(clientApi.person),
    createdAt: clientApi.created_at,
    updatedAt: clientApi.updated_at,
    deletedAt: clientApi.deleted_at
  }),
  encode: (client) => ({
    ...client,
    person: Schema.encodeUnknownSync(PersonFromApi)(client.person),
    created_at: client.createdAt,
    updated_at: client.updatedAt,
    deleted_at: client.deletedAt
  })
});
const ClientListFromApi = Schema.transform(
  Schema.mutable(Schema.NullishOr(Schema.Array(ClientFromApi))),
  Schema.mutable(Schema.Array(Client)),
  {
    strict: true,
    decode: (clientApiList) => clientApiList ? clientApiList : [],
    encode: (clientList) => clientList
  }
);
const CreateClientApi = Schema.Struct({
  person: CreatePersonApi
});
const CreateClientApiFromCreateClient = Schema.transform(
  CreateClient,
  CreateClientApi,
  {
    strict: true,
    encode: (clientApi) => ({
      ...clientApi,
      person: Schema.encodeUnknownSync(CreatePersonApiFromCreatePerson)(
        clientApi.person
      )
    }),
    decode: (client) => ({
      ...client,
      person: Schema.decodeUnknownSync(CreatePersonApiFromCreatePerson)(
        client.person
      )
    })
  }
);
const CreateClientApiResponse = Schema.String;
const UpdateClientApi = Schema.Struct({
  id: Schema.String,
  person: UpdatePersonApi
});
const UpdateClientApiFromUpdateClient = Schema.transform(
  UpdateClient,
  UpdateClientApi,
  {
    strict: true,
    encode: (clientApi) => ({
      ...clientApi,
      person: Schema.encodeUnknownSync(UpdatePersonApiFromUpdatePerson)(
        clientApi.person
      )
    }),
    decode: (client) => ({
      ...client,
      person: Schema.decodeUnknownSync(UpdatePersonApiFromUpdatePerson)(
        client.person
      )
    })
  }
);
const baseUrl$3 = "/v1/clients";
const makeClientApiRepo = ApiHttpClient.pipe(
  Effect.andThen(({ httpClient }) => httpClient),
  Effect.andThen((httpClient) => ({
    getAll: () => httpClient.get(baseUrl$3).pipe(Effect.flatMap(handleDResponse(ClientListFromApi))),
    getById: (id) => httpClient.get(`${baseUrl$3}/${id}`).pipe(Effect.flatMap(handleDResponse(ClientFromApi))),
    create: (client) => httpClient.post(baseUrl$3, {
      body: HttpBody.unsafeJson(
        Schema.decodeUnknownSync(CreateClientApiFromCreateClient)(client)
      )
    }).pipe(Effect.flatMap(handleDResponse(CreateClientApiResponse))),
    update: (client) => httpClient.put(`${baseUrl$3}/${client.id}`, {
      body: HttpBody.unsafeJson(
        Schema.decodeUnknownSync(UpdateClientApiFromUpdateClient)(client)
      )
    }).pipe(Effect.flatMap(handleResponse)),
    delete: (id) => httpClient.del(`${baseUrl$3}/${id}`).pipe(Effect.flatMap(handleResponse))
  })),
  Effect.provide(ApiHttpClient.Live)
);
const clientApiRepoLive = Layer.effect(
  ClientRepository,
  makeClientApiRepo
);
const ClientRepositoryLive = Layer.effect(
  ClientRepository,
  ClientRepository
);
const clientUsecaseLive = Layer.effect(ClientUsecase, ClientRepository);
class PersonRepository extends Effect.Tag("PersonRepository")() {
}
const baseUrl$2 = "/v1/person";
const makePersonApiRepo = ApiHttpClient.pipe(
  Effect.andThen(({ httpClient }) => httpClient),
  Effect.andThen((httpClient) => ({
    getAll: () => httpClient.get(baseUrl$2).pipe(Effect.flatMap(handleDResponse(PersonListFromApi))),
    getById: (id) => httpClient.get(`${baseUrl$2}/${id}`).pipe(Effect.flatMap(handleDResponse(PersonFromApi))),
    create: (person) => httpClient.post(baseUrl$2, {
      body: HttpBody.unsafeJson(
        Schema.decodeUnknownSync(CreatePersonApiFromCreatePerson)(person)
      )
    }).pipe(Effect.flatMap(handleDResponse(CreatePersonApiResponse))),
    update: (person) => httpClient.post(baseUrl$2, {
      body: HttpBody.unsafeJson(
        Schema.encodeUnknownSync(PersonFromApi)(person)
      )
    }).pipe(Effect.flatMap(handleResponse)),
    delete: (id) => httpClient.post(baseUrl$2, {
      body: HttpBody.unsafeJson({ id })
    }).pipe(Effect.flatMap(handleResponse))
  })),
  Effect.provide(ApiHttpClient.Live)
);
const personApiRepoLive = Layer.effect(
  PersonRepository,
  makePersonApiRepo
);
const PersonRepositoryLive = Layer.effect(
  PersonRepository,
  PersonRepository
);
class PersonUsecase extends Effect.Tag("PersonUsecase")() {
}
const personUsecaseLive = Layer.effect(PersonUsecase, PersonRepository);
class ScheduleRepository extends Effect.Tag("ScheduleRepository")() {
}
const Turn = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  startTime: Schema.Number,
  endTime: Schema.Number,
  createdAt: Schema.NullOr(Schema.String),
  updatedAt: Schema.NullOr(Schema.String),
  deletedAt: Schema.NullOr(Schema.String)
});
const Schedule = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  sessionDuration: Schema.Number,
  breakDuration: Schema.Number,
  turns: Schema.mutable(Schema.Array(Turn)),
  createdAt: Schema.NullOr(Schema.String),
  updatedAt: Schema.NullOr(Schema.String),
  deletedAt: Schema.NullOr(Schema.String)
});
const TurnCreate = Schema.Struct({
  name: Schema.String,
  startTime: Schema.Number,
  endTime: Schema.Number
});
const TurnUpdate = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  startTime: Schema.Number,
  endTime: Schema.Number
});
const CreateSchedule = Schema.Struct({
  name: Schema.String,
  sessionDuration: Schema.Number,
  breakDuration: Schema.Number,
  turns: Schema.mutable(Schema.Array(TurnCreate))
});
const UpdateSchedule = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  sessionDuration: Schema.Number,
  breakDuration: Schema.Number,
  turns: Schema.mutable(Schema.Array(TurnUpdate))
});
const TurnApi = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  start_time: Schema.Number,
  end_time: Schema.Number,
  created_at: Schema.NullOr(Schema.String),
  updated_at: Schema.NullOr(Schema.String),
  deleted_at: Schema.NullOr(Schema.String)
});
const TurnFromApi = Schema.transform(TurnApi, Turn, {
  strict: true,
  encode: (turn) => ({
    id: turn.id,
    name: turn.name,
    start_time: turn.startTime,
    end_time: turn.endTime,
    created_at: turn.createdAt,
    updated_at: turn.updatedAt,
    deleted_at: turn.deletedAt
  }),
  decode: (turnApi) => ({
    id: turnApi.id,
    name: turnApi.name,
    startTime: turnApi.start_time,
    endTime: turnApi.end_time,
    createdAt: turnApi.created_at,
    updatedAt: turnApi.updated_at,
    deletedAt: turnApi.deleted_at
  })
});
const ScheduleApi = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  session_duration: Schema.Number,
  break_duration: Schema.Number,
  turns: Schema.mutable(Schema.Array(TurnApi)),
  created_at: Schema.NullOr(Schema.String),
  updated_at: Schema.NullOr(Schema.String),
  deleted_at: Schema.NullOr(Schema.String)
});
const ScheduleFromApi = Schema.transform(ScheduleApi, Schedule, {
  strict: true,
  encode: (schedule) => ({
    id: schedule.id,
    name: schedule.name,
    session_duration: schedule.sessionDuration,
    break_duration: schedule.breakDuration,
    turns: schedule.turns.map(
      (turn) => Schema.encodeUnknownSync(TurnFromApi)(turn)
    ),
    created_at: schedule.createdAt,
    updated_at: schedule.updatedAt,
    deleted_at: schedule.deletedAt
  }),
  decode: (scheduleApi) => ({
    id: scheduleApi.id,
    name: scheduleApi.name,
    sessionDuration: scheduleApi.session_duration,
    breakDuration: scheduleApi.break_duration,
    turns: scheduleApi.turns.map(
      (turnApi) => Schema.decodeUnknownSync(TurnFromApi)(turnApi)
    ),
    createdAt: scheduleApi.created_at,
    updatedAt: scheduleApi.updated_at,
    deletedAt: scheduleApi.deleted_at
  })
});
const ScheduleListFromApi = Schema.mutable(
  Schema.Array(ScheduleFromApi)
);
const TurnCreateApi = Schema.Struct({
  name: Schema.String,
  start_time: Schema.Number,
  end_time: Schema.Number
});
const TurnCreateApiFromTurnCreate = Schema.transform(
  TurnCreate,
  TurnCreateApi,
  {
    strict: true,
    decode: (turnCreate) => ({
      name: turnCreate.name,
      start_time: turnCreate.startTime,
      end_time: turnCreate.endTime
    }),
    encode: (turnCreateApi) => ({
      name: turnCreateApi.name,
      startTime: turnCreateApi.start_time,
      endTime: turnCreateApi.end_time
    })
  }
);
const CreateScheduleApi = Schema.Struct({
  name: Schema.String,
  session_duration: Schema.Number,
  break_duration: Schema.Number,
  turns: Schema.mutable(Schema.Array(TurnCreateApi))
});
const CreateScheduleApiFromCreateSchedule = Schema.transform(
  CreateSchedule,
  CreateScheduleApi,
  {
    strict: true,
    decode: (createSchedule) => ({
      name: createSchedule.name,
      session_duration: createSchedule.sessionDuration,
      break_duration: createSchedule.breakDuration,
      turns: createSchedule.turns.map(
        (turn) => Schema.decodeUnknownSync(TurnCreateApiFromTurnCreate)(turn)
      )
    }),
    encode: (createScheduleApi) => ({
      name: createScheduleApi.name,
      sessionDuration: createScheduleApi.session_duration,
      breakDuration: createScheduleApi.break_duration,
      turns: createScheduleApi.turns.map(
        (turnApi) => Schema.encodeUnknownSync(TurnCreateApiFromTurnCreate)(turnApi)
      )
    })
  }
);
const CreateScheduleApiResponse = Schema.String;
const TurnUpdateApi = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  start_time: Schema.Number,
  end_time: Schema.Number
});
const TurnUpdateApiFromTurnUpdate = Schema.transform(
  TurnUpdate,
  TurnUpdateApi,
  {
    strict: true,
    decode: (turnUpdate) => ({
      id: turnUpdate.id,
      name: turnUpdate.name,
      start_time: turnUpdate.startTime,
      end_time: turnUpdate.endTime
    }),
    encode: (turnUpdateApi) => ({
      id: turnUpdateApi.id,
      name: turnUpdateApi.name,
      startTime: turnUpdateApi.start_time,
      endTime: turnUpdateApi.end_time
    })
  }
);
const UpdateScheduleApi = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  session_duration: Schema.Number,
  break_duration: Schema.Number,
  turns: Schema.mutable(Schema.Array(TurnUpdateApi))
});
const UpdateScheduleApiFromUpdateSchedule = Schema.transform(
  UpdateSchedule,
  UpdateScheduleApi,
  {
    strict: true,
    decode: (updateSchedule) => ({
      id: updateSchedule.id,
      name: updateSchedule.name,
      session_duration: updateSchedule.sessionDuration,
      break_duration: updateSchedule.breakDuration,
      turns: updateSchedule.turns.map(
        (turn) => Schema.decodeUnknownSync(TurnUpdateApiFromTurnUpdate)(turn)
      )
    }),
    encode: (updateScheduleApi) => ({
      id: updateScheduleApi.id,
      name: updateScheduleApi.name,
      sessionDuration: updateScheduleApi.session_duration,
      breakDuration: updateScheduleApi.break_duration,
      turns: updateScheduleApi.turns.map(
        (turnApi) => Schema.encodeUnknownSync(TurnUpdateApiFromTurnUpdate)(turnApi)
      )
    })
  }
);
const baseUrl$1 = "/v1/schedules";
const makeScheduleApiRepo = ApiHttpClient.pipe(
  Effect.andThen(({ httpClient }) => httpClient),
  Effect.andThen((httpClient) => ({
    getAll: () => httpClient.get(baseUrl$1).pipe(Effect.flatMap(handleDResponse(ScheduleListFromApi))),
    getById: (id) => httpClient.get(`${baseUrl$1}/${id}`).pipe(Effect.flatMap(handleDResponse(ScheduleFromApi))),
    create: (schedule) => httpClient.post(baseUrl$1, {
      body: HttpBody.unsafeJson(
        Schema.decodeUnknownSync(CreateScheduleApiFromCreateSchedule)(
          schedule
        )
      )
    }).pipe(Effect.flatMap(handleDResponse(CreateScheduleApiResponse))),
    update: (schedule) => httpClient.put(`${baseUrl$1}/${schedule.id}`, {
      body: HttpBody.unsafeJson(
        Schema.decodeUnknownSync(UpdateScheduleApiFromUpdateSchedule)(
          schedule
        )
      )
    }).pipe(Effect.flatMap(handleResponse)),
    delete: (id) => httpClient.del(`${baseUrl$1}/${id}`).pipe(Effect.flatMap(handleResponse))
  })),
  Effect.provide(ApiHttpClient.Live)
);
const scheduleApiRepoLive = Layer.effect(
  ScheduleRepository,
  makeScheduleApiRepo
);
const ScheduleRepositoryLive = Layer.effect(
  ScheduleRepository,
  ScheduleRepository
);
const scheduleUsecaseLive = Layer.effect(ScheduleUsecase, ScheduleRepository);
class WorkerRepository extends Effect.Tag("WorkerRepository")() {
}
var WorkerPosition = /* @__PURE__ */ ((WorkerPosition2) => {
  WorkerPosition2[WorkerPosition2["MANAGER"] = 1] = "MANAGER";
  WorkerPosition2[WorkerPosition2["ADMINISTRATOR"] = 2] = "ADMINISTRATOR";
  WorkerPosition2[WorkerPosition2["INTERN"] = 3] = "INTERN";
  WorkerPosition2[WorkerPosition2["PSYCHOLOGIST"] = 4] = "PSYCHOLOGIST";
  WorkerPosition2[WorkerPosition2["THERAPIST"] = 5] = "THERAPIST";
  return WorkerPosition2;
})(WorkerPosition || {});
const Worker = Schema.Struct({
  id: Schema.String,
  person: Person,
  positions: Schema.mutable(Schema.Array(Schema.Number)),
  createdAt: Schema.NullOr(Schema.String),
  updatedAt: Schema.NullOr(Schema.String),
  deletedAt: Schema.NullOr(Schema.String)
});
const CreateWorker = Schema.Struct({
  person: CreatePerson,
  positions: Schema.mutable(Schema.Array(Schema.Number))
});
const UpdateWorker = Schema.Struct({
  id: Schema.String,
  person: UpdatePerson,
  positions: Schema.mutable(Schema.Array(Schema.Number))
});
const WorkerApi = Schema.Struct({
  id: Schema.String,
  person: PersonApi,
  positions: Schema.mutable(Schema.Array(Schema.Number)),
  created_at: Schema.NullOr(Schema.String),
  updated_at: Schema.NullOr(Schema.String),
  deleted_at: Schema.NullOr(Schema.String)
});
const WorkerFromApi = Schema.transform(WorkerApi, Worker, {
  strict: true,
  decode: (workerApi) => ({
    ...workerApi,
    person: Schema.decodeUnknownSync(PersonFromApi)(workerApi.person),
    createdAt: workerApi.created_at,
    updatedAt: workerApi.updated_at,
    deletedAt: workerApi.deleted_at
  }),
  encode: (worker) => ({
    ...worker,
    person: Schema.encodeUnknownSync(PersonFromApi)(worker.person),
    created_at: worker.createdAt,
    updated_at: worker.updatedAt,
    deleted_at: worker.deletedAt
  })
});
const WorkerListFromApi = Schema.transform(
  Schema.mutable(Schema.NullishOr(Schema.Array(WorkerFromApi))),
  Schema.mutable(Schema.Array(Worker)),
  {
    strict: true,
    decode: (workerApiList) => workerApiList ? workerApiList : [],
    encode: (workerList) => workerList
  }
);
const CreateWorkerApi = Schema.Struct({
  person: CreatePersonApi,
  positions: Schema.mutable(Schema.Array(Schema.Number))
});
const CreateWorkerApiFromCreateWorker = Schema.transform(
  CreateWorker,
  CreateWorkerApi,
  {
    strict: true,
    encode: (workerApi) => ({
      ...workerApi,
      person: Schema.encodeUnknownSync(CreatePersonApiFromCreatePerson)(
        workerApi.person
      )
    }),
    decode: (worker) => ({
      ...worker,
      person: Schema.decodeUnknownSync(CreatePersonApiFromCreatePerson)(
        worker.person
      )
    })
  }
);
const CreateWorkerApiResponse = Schema.String;
const UpdateWorkerApi = Schema.Struct({
  id: Schema.String,
  person: UpdatePersonApi,
  positions: Schema.mutable(Schema.Array(Schema.Number))
});
const UpdateWorkerApiFromUpdateWorker = Schema.transform(
  UpdateWorker,
  UpdateWorkerApi,
  {
    strict: true,
    encode: (workerApi) => ({
      ...workerApi,
      person: Schema.encodeUnknownSync(UpdatePersonApiFromUpdatePerson)(
        workerApi.person
      )
    }),
    decode: (worker) => ({
      ...worker,
      person: Schema.decodeUnknownSync(UpdatePersonApiFromUpdatePerson)(
        worker.person
      )
    })
  }
);
const baseUrl = "/v1/workers";
const makeWorkerApiRepo = ApiHttpClient.pipe(
  Effect.andThen(({ httpClient }) => httpClient),
  Effect.andThen((httpClient) => ({
    getAll: () => httpClient.get(baseUrl).pipe(Effect.flatMap(handleDResponse(WorkerListFromApi))),
    getById: (id) => httpClient.get(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleDResponse(WorkerFromApi))),
    create: (worker) => httpClient.post(baseUrl, {
      body: HttpBody.unsafeJson(
        Schema.decodeUnknownSync(CreateWorkerApiFromCreateWorker)(worker)
      )
    }).pipe(Effect.flatMap(handleDResponse(CreateWorkerApiResponse))),
    update: (worker) => httpClient.put(baseUrl, {
      body: HttpBody.unsafeJson(
        Schema.decodeUnknownSync(UpdateWorkerApiFromUpdateWorker)(worker)
      )
    }).pipe(Effect.flatMap(handleResponse)),
    delete: (id) => httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse))
  })),
  Effect.provide(ApiHttpClient.Live)
);
const workerApiRepoLive = Layer.effect(
  WorkerRepository,
  makeWorkerApiRepo
);
const WorkerRepositoryLive = Layer.effect(
  WorkerRepository,
  WorkerRepository
);
const workerUsecaseLive = Layer.effect(WorkerUsecase, WorkerRepository);
const makeAuthUsecaseLive = authUsecaseLive.pipe(
  Layer.provide(authRepositoryLive),
  Layer.provide(authApiRepoLive)
);
const makeClientUsecaseLive = clientUsecaseLive.pipe(
  Layer.provide(ClientRepositoryLive),
  Layer.provide(clientApiRepoLive)
);
const makePersonUsecaseLive = personUsecaseLive.pipe(
  Layer.provide(PersonRepositoryLive),
  Layer.provide(personApiRepoLive)
);
const makeWorkerUsecaseLive = workerUsecaseLive.pipe(
  Layer.provide(WorkerRepositoryLive),
  Layer.provide(workerApiRepoLive)
);
const makeScheduleUsecaseLive = scheduleUsecaseLive.pipe(
  Layer.provide(ScheduleRepositoryLive),
  Layer.provide(scheduleApiRepoLive)
);
const MainLayer = Layer.mergeAll(
  makeAuthUsecaseLive,
  makeClientUsecaseLive,
  makePersonUsecaseLive,
  makeWorkerUsecaseLive,
  makeScheduleUsecaseLive
);
const AppRuntime = ManagedRuntime.make(MainLayer);

export { AppRuntime as A, DocumentType as D, ErrorResult as E, WorkerPosition as W, Worker as a };
//# sourceMappingURL=runtimes-CvAMo_9p.mjs.map
