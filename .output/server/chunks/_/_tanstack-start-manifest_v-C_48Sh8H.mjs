const tsrStartManifest = () => ({ "routes": { "__root__": { "filePath": "__root.tsx", "children": ["/", "/_authed", "/login"], "preloads": ["/assets/main-RY8ZkTMc.js"], "assets": [{ "tag": "script", "attrs": { "src": "/assets/main-RY8ZkTMc.js", "type": "module" } }] }, "/": { "filePath": "index.tsx", "assets": [], "preloads": ["/assets/index-CFxSTSbc.js"] }, "/_authed": { "filePath": "_authed/route.tsx", "children": ["/_authed/admin"], "assets": [], "preloads": ["/assets/route-DP-pnEp9.js", "/assets/useQuery-D9VtETml.js", "/assets/queryOptions-C9woPjwX.js", "/assets/runtimes-CTOS42-v.js", "/assets/effectErrors-D8W8e9uM.js"] }, "/login": { "filePath": "login.tsx", "assets": [], "preloads": ["/assets/login-Ckz1Puxy.js", "/assets/form-BdoD3Q87.js", "/assets/effectErrors-D8W8e9uM.js", "/assets/useMutation-D8585ZL-.js", "/assets/runtimes-CTOS42-v.js", "/assets/user-BHfkra8r.js", "/assets/classes-CraQI9Rs.js", "/assets/createLucideIcon-78bvTjT9.js"] }, "/_authed/admin": { "filePath": "_authed/admin/route.tsx", "parent": "/_authed", "children": ["/_authed/admin/", "/_authed/admin/schedules/create", "/_authed/admin/clients/", "/_authed/admin/schedules/", "/_authed/admin/sessions/", "/_authed/admin/workers/", "/_authed/admin/schedules/edit/$id"], "assets": [], "preloads": ["/assets/route-DvpekX38.js", "/assets/createLucideIcon-78bvTjT9.js", "/assets/users-DptPd1yw.js", "/assets/calendar-C7hPlekH.js", "/assets/useMutation-D8585ZL-.js", "/assets/user-BHfkra8r.js"] }, "/_authed/admin/": { "filePath": "_authed/admin/index.tsx", "parent": "/_authed/admin", "assets": [], "preloads": ["/assets/index-BbWl0DHf.js"] }, "/_authed/admin/schedules/create": { "filePath": "_authed/admin/schedules/create.tsx", "parent": "/_authed/admin", "assets": [], "preloads": ["/assets/create-Cw-MR3Bi.js", "/assets/form-BdoD3Q87.js", "/assets/classes-CraQI9Rs.js", "/assets/schedule-options-BL0O7AQ8.js", "/assets/index-C7U4vq_b.js", "/assets/file-text-DYqz-Dos.js"] }, "/_authed/admin/clients/": { "filePath": "_authed/admin/clients/index.tsx", "parent": "/_authed/admin", "assets": [], "preloads": ["/assets/index-BZf7DOmf.js", "/assets/BasicTable-RsYQtHbn.js", "/assets/classes-CraQI9Rs.js", "/assets/TextModal-DBwVHEpd.js", "/assets/form-BdoD3Q87.js", "/assets/file-text-DYqz-Dos.js", "/assets/square-pen-KUfj7VU3.js"] }, "/_authed/admin/schedules/": { "filePath": "_authed/admin/schedules/index.tsx", "parent": "/_authed/admin", "assets": [], "preloads": ["/assets/index-DmGivCOT.js", "/assets/schedule-options-BL0O7AQ8.js", "/assets/BasicTable-RsYQtHbn.js", "/assets/classes-CraQI9Rs.js", "/assets/square-pen-KUfj7VU3.js"] }, "/_authed/admin/sessions/": { "filePath": "_authed/admin/sessions/index.tsx", "parent": "/_authed/admin", "assets": [], "preloads": ["/assets/index-Eltu2spq.js"] }, "/_authed/admin/workers/": { "filePath": "_authed/admin/workers/index.tsx", "parent": "/_authed/admin", "assets": [], "preloads": ["/assets/index-Q8CEboXZ.js", "/assets/BasicTable-RsYQtHbn.js", "/assets/classes-CraQI9Rs.js", "/assets/form-BdoD3Q87.js", "/assets/TextModal-DBwVHEpd.js", "/assets/file-text-DYqz-Dos.js"] }, "/_authed/admin/schedules/edit/$id": { "filePath": "_authed/admin/schedules/edit/$id.tsx", "parent": "/_authed/admin", "assets": [], "preloads": ["/assets/_id-2ptn8Wpq.js", "/assets/schedule-options-BL0O7AQ8.js", "/assets/form-BdoD3Q87.js", "/assets/classes-CraQI9Rs.js", "/assets/index-C7U4vq_b.js", "/assets/file-text-DYqz-Dos.js"] } } });

export { tsrStartManifest };
//# sourceMappingURL=_tanstack-start-manifest_v-C_48Sh8H.mjs.map
