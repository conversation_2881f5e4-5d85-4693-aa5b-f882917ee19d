import { jsxs, Fragment, jsx } from 'react/jsx-runtime';
import { useState, useEffect } from 'react';
import { User, Mail, MapPin, Phone, Calendar, FileText, Edit2, Trash } from 'lucide-react';
import { C as CloseModal, B as BasicTable } from './BasicTable-WMVaHEYn.mjs';
import { c as cn } from './classes-DMlJhLpb.mjs';
import { W as WorkerPosition, A as AppRuntime, D as DocumentType, a as Worker } from './runtimes-CvAMo_9p.mjs';
import { toast } from 'react-toastify';
import { u as useAppForm } from './form-B3Cqm05b.mjs';
import { g as getErrorResult } from './effectErrors-CR6URVJK.mjs';
import { useQuery, queryOptions, useQueryClient, useMutation } from '@tanstack/react-query';
import { create } from 'mutative';
import { u as useService } from './ssr.mjs';
import * as v from 'valibot';
import { useReactTable, getCoreRowModel, createColumnHelper } from '@tanstack/react-table';
import { T as TextModal } from './TextModal-CtXTWDme.mjs';
import 'clsx';
import 'tailwind-merge';
import 'effect';
import '@effect/platform';
import '@tanstack/react-form';
import 'downshift';
import 'use-mutative';
import 'effect/Runtime';
import '@tanstack/react-router';
import '@tanstack/react-query-devtools';
import '@tanstack/react-router-devtools';
import 'tiny-invariant';
import 'tiny-warning';
import '@tanstack/router-core';
import 'node:async_hooks';
import '@tanstack/react-router-with-query';
import '@tanstack/history';
import 'jsesc';
import 'node:stream';
import 'isbot';
import 'react-dom/server';
import 'node:stream/web';

const workerOptions = ({ worker }) => queryOptions({
  queryKey: ["workers"],
  queryFn: () => AppRuntime.runPromise(worker.getAll())
});
const workerOptionsById = ({ worker }, id) => queryOptions({
  queryKey: ["workers", id],
  queryFn: () => AppRuntime.runPromise(worker.getById(id))
});
function useCreateWorker() {
  const service = useService();
  const { worker } = service;
  const queryClient = useQueryClient();
  const queryKey = workerOptions(service).queryKey;
  return useMutation({
    mutationKey: ["create-worker"],
    mutationFn: (newWorker) => AppRuntime.runPromise(worker.create(newWorker)),
    onMutate: async (newWorker) => {
      await queryClient.cancelQueries({ queryKey });
      const previousWorkers = queryClient.getQueryData(queryKey);
      if (previousWorkers) {
        queryClient.setQueryData(
          queryKey,
          create(previousWorkers, (draft) => {
            draft.push(
              Worker.make({
                id: "new",
                ...newWorker,
                person: {
                  id: "new",
                  ...newWorker.person,
                  createdAt: null,
                  updatedAt: null,
                  deletedAt: null
                },
                createdAt: null,
                updatedAt: null,
                deletedAt: null
              })
            );
          })
        );
      } else {
        queryClient.setQueryData(queryKey, [
          Worker.make({
            id: "new",
            ...newWorker,
            person: {
              id: "new",
              ...newWorker.person,
              createdAt: null,
              updatedAt: null,
              deletedAt: null
            },
            createdAt: null,
            updatedAt: null,
            deletedAt: null
          })
        ]);
      }
      return { previousWorkers };
    },
    onError: (_, __, context) => {
      queryClient.setQueryData(queryKey, context == null ? void 0 : context.previousWorkers);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey });
    }
  });
}
const CreateWorkerSchema = v.object({
  name: v.pipe(
    v.string("Debe ingresar una cuenta"),
    v.minLength(1, "Debe tener al menos un caracter")
  ),
  fatherLastName: v.pipe(
    v.string("Debe ingresar un apellido"),
    v.minLength(1, "Debe tener al menos un caracter")
  ),
  motherLastName: v.pipe(
    v.string("Debe ingresar un apellido"),
    v.minLength(1, "Debe tener al menos un caracter")
  ),
  email: v.union([
    v.pipe(
      v.string("Debe ingresar un email"),
      v.email("Debe ingresar un email valido")
    ),
    v.literal("")
  ]),
  address: v.optional(v.string()),
  phone: v.optional(v.string()),
  birthDate: v.nullable(v.string()),
  gender: v.boolean(),
  document: v.pipe(
    v.string("Debe ingresar un documento"),
    v.minLength(8, "Debe tener al menos 8 caracteres")
  ),
  documentType: v.number(),
  positions: v.array(v.number())
});
const defaultValues = {
  name: "",
  fatherLastName: "",
  motherLastName: "",
  email: "",
  address: "",
  phone: "",
  birthDate: "",
  gender: false,
  document: "",
  documentType: 0,
  positions: []
};
function useCreateWorkerModal({
  setIsOpen
}) {
  const { mutate } = useCreateWorker();
  const form = useAppForm({
    defaultValues,
    validators: {
      onChange: CreateWorkerSchema
    },
    onSubmit: ({ value }) => {
      var _a;
      const { positions, ...person } = value;
      mutate(
        {
          person,
          positions: (_a = value.positions) != null ? _a : []
        },
        {
          onSuccess: () => {
            toast.success("Trabajador creado");
            handleClose();
          },
          onError: (_error) => {
            console.log(_error);
            const { error } = getErrorResult(_error);
            toast.error(error.message);
          }
        }
      );
    }
  });
  function handleClose() {
    form.reset();
    setIsOpen(false);
  }
  return {
    form,
    handleClose
  };
}
function CreateWorkerModal({
  isOpen,
  setIsOpen
}) {
  const { form, handleClose } = useCreateWorkerModal({ setIsOpen });
  return /* @__PURE__ */ jsx("div", { className: cn("modal", isOpen && "modal-open"), children: /* @__PURE__ */ jsxs("div", { className: "modal-box", children: [
    /* @__PURE__ */ jsx(CloseModal, { onClose: handleClose }),
    /* @__PURE__ */ jsx("h3", { className: "font-bold text-lg", children: "Crear Trabajador" }),
    /* @__PURE__ */ jsx(
      "form",
      {
        onSubmit: (e) => {
          e.preventDefault();
          form.handleSubmit();
        },
        children: /* @__PURE__ */ jsxs(form.AppForm, { children: [
          /* @__PURE__ */ jsxs("fieldset", { className: "fieldset", children: [
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "positions",
                children: ({ FSComboBoxField }) => /* @__PURE__ */ jsx(
                  FSComboBoxField,
                  {
                    label: "Posici\xF3n",
                    placeholder: "Posici\xF3n",
                    isMultiple: true,
                    options: [
                      { value: WorkerPosition.MANAGER, label: "Gerente" },
                      {
                        value: WorkerPosition.ADMINISTRATOR,
                        label: "Administrador"
                      },
                      { value: WorkerPosition.INTERN, label: "Interno" },
                      {
                        value: WorkerPosition.PSYCHOLOGIST,
                        label: "Psic\xF3logo"
                      },
                      {
                        value: WorkerPosition.THERAPIST,
                        label: "Terapeuta"
                      }
                    ]
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "name",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Nombre",
                    placeholder: "Nombre",
                    prefixComponent: /* @__PURE__ */ jsx(User, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "fatherLastName",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Apellido Paterno",
                    placeholder: "Apellido Paterno",
                    prefixComponent: /* @__PURE__ */ jsx(User, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "motherLastName",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Apellido Materno",
                    placeholder: "Apellido Materno",
                    prefixComponent: /* @__PURE__ */ jsx(User, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "email",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Correo Electr\xF3nico",
                    placeholder: "<EMAIL>",
                    prefixComponent: /* @__PURE__ */ jsx(Mail, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "address",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Direcci\xF3n",
                    placeholder: "Direcci\xF3n",
                    prefixComponent: /* @__PURE__ */ jsx(MapPin, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "phone",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Tel\xE9fono",
                    placeholder: "Tel\xE9fono",
                    prefixComponent: /* @__PURE__ */ jsx(Phone, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "birthDate",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Fecha de Nacimiento",
                    placeholder: "YYYY-MM-DD",
                    type: "date",
                    prefixComponent: /* @__PURE__ */ jsx(Calendar, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "document",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Documento",
                    placeholder: "N\xFAmero de Documento",
                    prefixComponent: /* @__PURE__ */ jsx(FileText, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "documentType",
                children: ({ FSSelectField }) => /* @__PURE__ */ jsx(
                  FSSelectField,
                  {
                    label: "Tipo de Documento",
                    placeholder: "Tipo de Documento",
                    isNumber: true,
                    options: [
                      {
                        value: 0,
                        label: "DNI"
                      },
                      {
                        value: 1,
                        label: "Pasaporte"
                      },
                      {
                        value: 2,
                        label: "RUC"
                      }
                    ]
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "gender",
                children: ({ FSToggleField }) => /* @__PURE__ */ jsx(
                  FSToggleField,
                  {
                    label: "G\xE9nero",
                    trueLabel: "Masculino",
                    falseLabel: "Femenino"
                  }
                )
              }
            )
          ] }),
          /* @__PURE__ */ jsx("div", { className: "modal-action", children: /* @__PURE__ */ jsx("button", { type: "submit", className: "btn btn-primary", children: "Crear" }) })
        ] })
      }
    )
  ] }) });
}
function useDeleteWorker() {
  const service = useService();
  const { worker } = service;
  const queryClient = useQueryClient();
  const queryKey = workerOptions(service).queryKey;
  return useMutation({
    mutationKey: ["delete-worker"],
    mutationFn: (workerId) => AppRuntime.runPromise(worker.delete(workerId)),
    onMutate: async (workerId) => {
      await queryClient.cancelQueries({ queryKey });
      const previousWorkers = queryClient.getQueryData(queryKey);
      if (previousWorkers) {
        queryClient.setQueryData(
          queryKey,
          create(previousWorkers, (draft) => {
            const index = draft.findIndex((w) => w.id === workerId);
            if (index !== -1) {
              draft.splice(index, 1);
            }
          })
        );
      }
      return { previousWorkers };
    },
    onError: (_, __, context) => {
      queryClient.setQueryData(queryKey, context == null ? void 0 : context.previousWorkers);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey });
    }
  });
}
function DeleteWorkerModal({ isOpen, setIsOpen, id }) {
  const { mutate } = useDeleteWorker();
  return /* @__PURE__ */ jsx("div", { className: cn("modal", isOpen && "modal-open"), children: /* @__PURE__ */ jsxs("div", { className: "modal-box", children: [
    /* @__PURE__ */ jsx(CloseModal, { onClose: () => setIsOpen(false) }),
    /* @__PURE__ */ jsx("h3", { className: "font-bold text-lg", children: "Eliminar trabajador" }),
    /* @__PURE__ */ jsx("p", { children: "\xBFEst\xE1s seguro de que quieres eliminar este trabajador?" }),
    /* @__PURE__ */ jsxs("div", { className: "modal-action", children: [
      /* @__PURE__ */ jsx(
        "button",
        {
          type: "button",
          className: "btn btn-primary",
          onClick: () => setIsOpen(false),
          children: "Cancelar"
        }
      ),
      /* @__PURE__ */ jsx(
        "button",
        {
          type: "button",
          className: "btn btn-error",
          onClick: () => {
            mutate(id, {
              onSuccess: () => {
                toast.success("Trabajador eliminado");
                setIsOpen(false);
              },
              onError: (error) => {
                console.log(error);
              }
            });
          },
          children: "Eliminar"
        }
      )
    ] })
  ] }) });
}
function useEditeWorker() {
  const service = useService();
  const { worker } = service;
  const queryClient = useQueryClient();
  const queryKey = workerOptions(service).queryKey;
  return useMutation({
    mutationKey: ["update-worker"],
    mutationFn: (updatedWorker) => AppRuntime.runPromise(worker.update(updatedWorker)),
    onMutate: async (updatedWorker) => {
      await queryClient.cancelQueries({ queryKey });
      const previousWorkers = queryClient.getQueryData(queryKey);
      if (previousWorkers) {
        queryClient.setQueryData(
          queryKey,
          create(previousWorkers, (draft) => {
            const index = draft.findIndex((w) => w.id === updatedWorker.id);
            if (index !== -1) {
              draft[index] = Worker.make({
                ...updatedWorker,
                person: {
                  ...updatedWorker.person,
                  createdAt: null,
                  updatedAt: null,
                  deletedAt: null
                },
                createdAt: null,
                updatedAt: null,
                deletedAt: null
              });
            }
          })
        );
      }
      return { previousWorkers };
    },
    onError: (_, __, context) => {
      queryClient.setQueryData(queryKey, context == null ? void 0 : context.previousWorkers);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey });
    }
  });
}
function useEditeWorkerModal({
  setIsOpen,
  worker
}) {
  const { mutate } = useEditeWorker();
  const { id, createdAt, updatedAt, deletedAt, ...rest } = worker;
  const {
    id: personId,
    createdAt: personCreatedAt,
    updatedAt: personUpdatedAt,
    deletedAt: personDeletedAt,
    ...personRest
  } = rest.person;
  const form = useAppForm({
    defaultValues: {
      ...personRest,
      positions: rest.positions
    },
    validators: {
      onChange: CreateWorkerSchema
    },
    onSubmit: ({ value }) => {
      var _a;
      const { positions, ...person } = value;
      mutate(
        {
          id,
          person: {
            ...person,
            id: personId
          },
          positions: (_a = value.positions) != null ? _a : []
        },
        {
          onSuccess: () => {
            toast.success("Trabajador actualizado");
            handleClose();
          },
          onError: (_error) => {
            console.log(_error);
            const { error } = getErrorResult(_error);
            toast.error(error.message);
          }
        }
      );
    }
  });
  function handleClose() {
    form.reset();
    setIsOpen(false);
  }
  return {
    form,
    handleClose
  };
}
function EditeWorkerForm({
  isOpen,
  setIsOpen,
  worker
}) {
  const { form, handleClose } = useEditeWorkerModal({
    setIsOpen,
    worker
  });
  return /* @__PURE__ */ jsx("div", { className: cn("modal", isOpen && "modal-open"), children: /* @__PURE__ */ jsxs("div", { className: "modal-box", children: [
    /* @__PURE__ */ jsx(CloseModal, { onClose: handleClose }),
    /* @__PURE__ */ jsx("h3", { className: "font-bold text-lg", children: "Crear Trabajador" }),
    /* @__PURE__ */ jsx(
      "form",
      {
        onSubmit: (e) => {
          e.preventDefault();
          form.handleSubmit();
        },
        children: /* @__PURE__ */ jsxs(form.AppForm, { children: [
          /* @__PURE__ */ jsxs("fieldset", { className: "fieldset", children: [
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "positions",
                children: ({ FSComboBoxField }) => /* @__PURE__ */ jsx(
                  FSComboBoxField,
                  {
                    label: "Posici\xF3n",
                    placeholder: "Posici\xF3n",
                    isMultiple: true,
                    options: [
                      { value: WorkerPosition.MANAGER, label: "Gerente" },
                      {
                        value: WorkerPosition.ADMINISTRATOR,
                        label: "Administrador"
                      },
                      { value: WorkerPosition.INTERN, label: "Interno" },
                      {
                        value: WorkerPosition.PSYCHOLOGIST,
                        label: "Psic\xF3logo"
                      },
                      {
                        value: WorkerPosition.THERAPIST,
                        label: "Terapeuta"
                      }
                    ]
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "name",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Nombre",
                    placeholder: "Nombre",
                    prefixComponent: /* @__PURE__ */ jsx(User, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "fatherLastName",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Apellido Paterno",
                    placeholder: "Apellido Paterno",
                    prefixComponent: /* @__PURE__ */ jsx(User, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "motherLastName",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Apellido Materno",
                    placeholder: "Apellido Materno",
                    prefixComponent: /* @__PURE__ */ jsx(User, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "email",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Correo Electr\xF3nico",
                    placeholder: "<EMAIL>",
                    prefixComponent: /* @__PURE__ */ jsx(Mail, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "address",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Direcci\xF3n",
                    placeholder: "Direcci\xF3n",
                    prefixComponent: /* @__PURE__ */ jsx(MapPin, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "phone",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Tel\xE9fono",
                    placeholder: "Tel\xE9fono",
                    prefixComponent: /* @__PURE__ */ jsx(Phone, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "birthDate",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Fecha de Nacimiento",
                    placeholder: "YYYY-MM-DD",
                    type: "date",
                    prefixComponent: /* @__PURE__ */ jsx(Calendar, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "document",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Documento",
                    placeholder: "N\xFAmero de Documento",
                    prefixComponent: /* @__PURE__ */ jsx(FileText, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "documentType",
                children: ({ FSSelectField }) => /* @__PURE__ */ jsx(
                  FSSelectField,
                  {
                    label: "Tipo de Documento",
                    placeholder: "Tipo de Documento",
                    isNumber: true,
                    options: [
                      {
                        value: 0,
                        label: "DNI"
                      },
                      {
                        value: 1,
                        label: "Pasaporte"
                      },
                      {
                        value: 2,
                        label: "RUC"
                      }
                    ]
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "gender",
                children: ({ FSToggleField }) => /* @__PURE__ */ jsx(
                  FSToggleField,
                  {
                    label: "G\xE9nero",
                    trueLabel: "Masculino",
                    falseLabel: "Femenino"
                  }
                )
              }
            )
          ] }),
          /* @__PURE__ */ jsx("div", { className: "modal-action", children: /* @__PURE__ */ jsx("button", { type: "submit", className: "btn btn-primary", children: "Crear" }) })
        ] })
      }
    )
  ] }) });
}
function EditeWorkerModal({ isOpen, setIsOpen, id }) {
  const svc = useService();
  const { data, isError, error, isPending } = useQuery({
    ...workerOptionsById(svc, id),
    enabled: isOpen
  });
  useEffect(() => {
    if (error) {
      console.log(error);
    }
  }, [error]);
  if (isPending) return /* @__PURE__ */ jsx(TextModal, { text: "Cargando..." });
  if (isError)
    return /* @__PURE__ */ jsx(
      TextModal,
      {
        text: "No se pudo cargar el trabajador",
        title: getErrorResult(error).error.code.toString()
      }
    );
  return /* @__PURE__ */ jsx(EditeWorkerForm, { isOpen, setIsOpen, worker: data });
}
const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor("person.name", {
    header: "Name",
    cell: (info) => {
      var _a;
      return (_a = info.getValue()) != null ? _a : "-";
    }
  }),
  columnHelper.display({
    header: "Apellidos",
    cell: ({ row }) => `${row.original.person.fatherLastName} ${row.original.person.motherLastName}`
  }),
  columnHelper.accessor("person.email", {
    header: "Email",
    cell: (info) => {
      var _a;
      return (_a = info.getValue()) != null ? _a : "-";
    }
  }),
  columnHelper.accessor("person.address", {
    header: "Direcci\xF3n",
    cell: (info) => {
      var _a;
      return (_a = info.getValue()) != null ? _a : "-";
    }
  }),
  columnHelper.accessor("person.phone", {
    header: "Tel\xE9fono",
    cell: (info) => {
      var _a;
      return (_a = info.getValue()) != null ? _a : "-";
    }
  }),
  columnHelper.accessor("person.birthDate", {
    header: "Fecha de Nacimiento",
    cell: (info) => {
      var _a;
      return (_a = info.getValue()) != null ? _a : "-";
    }
  }),
  columnHelper.accessor("person.document", {
    header: "Documento",
    cell: (info) => {
      var _a;
      return (_a = info.getValue()) != null ? _a : "-";
    }
  }),
  columnHelper.accessor("person.documentType", {
    header: "Tipo de Documento",
    cell: (info) => {
      var _a;
      const documentTypeText = {
        [DocumentType.DNI]: "DNI",
        [DocumentType.PASAPORTE]: "Pasaporte",
        [DocumentType.RUC]: "RUC"
      };
      return (_a = documentTypeText[info.getValue()]) != null ? _a : "-";
    }
  }),
  columnHelper.accessor("positions", {
    header: "Positions",
    cell: (info) => {
      const positionsText = {
        [WorkerPosition.MANAGER]: "Gerente",
        [WorkerPosition.ADMINISTRATOR]: "Administrador",
        [WorkerPosition.INTERN]: "Interno",
        [WorkerPosition.PSYCHOLOGIST]: "Psic\xF3logo",
        [WorkerPosition.THERAPIST]: "Terapeuta"
      };
      return info.getValue().map((position) => positionsText[position]).join(", ");
    }
  }),
  columnHelper.display({
    header: "Acciones",
    cell: ({ row }) => {
      const [isOpen, setIsOpen] = useState(false);
      const [isDeleteOpen, setIsDeleteOpen] = useState(false);
      return /* @__PURE__ */ jsxs(Fragment, { children: [
        /* @__PURE__ */ jsxs("div", { className: "flex gap-2", children: [
          /* @__PURE__ */ jsx(
            "button",
            {
              type: "button",
              className: "btn btn-circle btn-primary",
              onClick: () => setIsOpen(true),
              children: /* @__PURE__ */ jsx(Edit2, { size: 16 })
            }
          ),
          /* @__PURE__ */ jsx(
            "button",
            {
              type: "button",
              className: "btn btn-circle btn-error",
              onClick: () => setIsDeleteOpen(true),
              children: /* @__PURE__ */ jsx(Trash, { size: 16 })
            }
          )
        ] }),
        /* @__PURE__ */ jsx(
          EditeWorkerModal,
          {
            isOpen,
            setIsOpen,
            id: row.original.id
          }
        ),
        /* @__PURE__ */ jsx(
          DeleteWorkerModal,
          {
            isOpen: isDeleteOpen,
            setIsOpen: setIsDeleteOpen,
            id: row.original.id
          }
        )
      ] });
    }
  })
];
function Table({ workers }) {
  const table = useReactTable({
    data: workers,
    columns,
    getCoreRowModel: getCoreRowModel()
  });
  return /* @__PURE__ */ jsx(BasicTable, { table });
}
function WorkerTable() {
  const svc = useService();
  const { data, isError, error, isPending } = useQuery(workerOptions(svc));
  useEffect(() => {
    if (error) {
      console.log(getErrorResult(error).error);
    }
  }, [error]);
  if (isError) return /* @__PURE__ */ jsxs("div", { children: [
    "Error: ",
    getErrorResult(error).error.message
  ] });
  if (isPending) return /* @__PURE__ */ jsx("div", { children: "Loading..." });
  return /* @__PURE__ */ jsx(Table, { workers: data });
}
const SplitComponent = function RouteComponent() {
  const [isOpen, setIsOpen] = useState(false);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx("div", { className: "container mx-auto", children: /* @__PURE__ */ jsx("div", { className: "card bg-base-300", children: /* @__PURE__ */ jsxs("div", { className: "card-body", children: [
      /* @__PURE__ */ jsx("div", { children: /* @__PURE__ */ jsx("button", { type: "button", className: "btn btn-primary", onClick: () => setIsOpen(true), children: "Nuevo trabajador" }) }),
      /* @__PURE__ */ jsx(WorkerTable, {})
    ] }) }) }),
    /* @__PURE__ */ jsx(CreateWorkerModal, { isOpen, setIsOpen })
  ] });
};

export { SplitComponent as component };
//# sourceMappingURL=index-v1viJmSR.mjs.map
