import { jsx, jsxs } from 'react/jsx-runtime';
import { useNavigate } from '@tanstack/react-router';
import { User } from 'lucide-react';
import { toast } from 'react-toastify';
import { u as useAppForm } from './form-B3Cqm05b.mjs';
import { g as getErrorResult } from './effectErrors-CR6URVJK.mjs';
import { useMutation } from '@tanstack/react-query';
import { u as useService } from './ssr.mjs';
import { A as AppRuntime } from './runtimes-CvAMo_9p.mjs';
import { Store } from '@tanstack/react-store';
import * as v from 'valibot';
import '@tanstack/react-form';
import 'downshift';
import 'react';
import 'use-mutative';
import './classes-DMlJhLpb.mjs';
import 'clsx';
import 'tailwind-merge';
import 'effect/Runtime';
import '@tanstack/react-query-devtools';
import '@tanstack/react-router-devtools';
import 'tiny-invariant';
import 'tiny-warning';
import '@tanstack/router-core';
import 'node:async_hooks';
import 'effect';
import '@tanstack/react-router-with-query';
import '@tanstack/history';
import 'jsesc';
import 'node:stream';
import 'isbot';
import 'react-dom/server';
import 'node:stream/web';
import '@effect/platform';

function useLogin() {
  const { auth } = useService();
  return useMutation({
    mutationKey: ["login"],
    mutationFn: (credentials) => AppRuntime.runPromise(auth.login(credentials))
  });
}
const authStore = new Store(void 0);
const authActions = {
  setUser: (user) => authStore.setState(user),
  clearUser: () => authStore.setState(void 0)
};
const LoginSchema = v.object({
  username: v.pipe(
    v.string("Debe ingresar una cuenta"),
    v.minLength(4, "Debe tener al menos 4 caracteres")
  ),
  password: v.pipe(
    v.string("Debe ingresar su contrase\xF1a"),
    v.minLength(4, "Debe tener al menos 4 caracteres")
  )
});
function LoginForm() {
  const { mutateAsync } = useLogin();
  const navigate = useNavigate();
  const form = useAppForm({
    defaultValues: {
      username: "",
      password: ""
    },
    onSubmit: async ({ value }) => {
      mutateAsync(value, {
        onSuccess(result) {
          authActions.setUser(result.user);
          navigate({ to: "/admin" });
        },
        onError(error) {
          const errorResult = getErrorResult(error);
          toast.error(errorResult.error.message);
        }
      });
    },
    validators: {
      onChange: LoginSchema
    }
  });
  return /* @__PURE__ */ jsx(
    "form",
    {
      onSubmit: (e) => {
        e.preventDefault();
        form.handleSubmit();
      },
      children: /* @__PURE__ */ jsxs(form.AppForm, { children: [
        /* @__PURE__ */ jsx(
          form.AppField,
          {
            name: "username",
            children: ({ FSTextField }) => /* @__PURE__ */ jsx(
              FSTextField,
              {
                label: "Usuario o Correo",
                placeholder: "<EMAIL>",
                prefixComponent: /* @__PURE__ */ jsx(User, { size: 16 })
              }
            )
          }
        ),
        /* @__PURE__ */ jsx(
          form.AppField,
          {
            name: "password",
            children: ({ FSPasswordField }) => /* @__PURE__ */ jsx(FSPasswordField, { label: "Contrase\xF1a", placeholder: "Contrase\xF1a" })
          }
        ),
        /* @__PURE__ */ jsx(
          form.SubscribeButton,
          {
            label: "Iniciar sesi\xF3n",
            className: "btn btn-neutral mt-4"
          }
        )
      ] })
    }
  );
}
const SplitComponent = function RouteComponent() {
  return /* @__PURE__ */ jsx("div", { className: "hero min-h-screen bg-base-200", children: /* @__PURE__ */ jsxs("div", { className: "hero-content flex-col lg:flex-row-reverse", children: [
    /* @__PURE__ */ jsxs("div", { className: "text-center lg:text-left", children: [
      /* @__PURE__ */ jsx("h1", { className: "font-bold text-5xl", children: "Ingresa ahora!" }),
      /* @__PURE__ */ jsx("p", { className: "py-6", children: "Sistema de administraci\xF3n de horarios y sesiones psicol\xF3gicas" })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "card w-full max-w-sm shrink-0 bg-base-100 shadow-2xl", children: /* @__PURE__ */ jsx("div", { className: "card-body", children: /* @__PURE__ */ jsx(LoginForm, {}) }) })
  ] }) });
};

export { SplitComponent as component };
//# sourceMappingURL=login-rq3VgO6g.mjs.map
