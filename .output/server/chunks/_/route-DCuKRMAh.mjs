import { jsxs, jsx } from 'react/jsx-runtime';
import { Outlet, Link, useNavigate } from '@tanstack/react-router';
import { Home, Users, UserCog, Calendar, User } from 'lucide-react';
import { useMutation } from '@tanstack/react-query';
import { u as useService } from './ssr.mjs';
import { A as AppRuntime } from './runtimes-CvAMo_9p.mjs';
import '@tanstack/react-query-devtools';
import '@tanstack/react-router-devtools';
import 'react-toastify';
import 'react';
import 'valibot';
import 'tiny-invariant';
import 'tiny-warning';
import '@tanstack/router-core';
import 'node:async_hooks';
import 'effect';
import '@tanstack/react-router-with-query';
import '@tanstack/history';
import 'jsesc';
import 'node:stream';
import 'isbot';
import 'react-dom/server';
import 'node:stream/web';
import '@effect/platform';

function Sidebar() {
  return /* @__PURE__ */ jsx("div", { className: "h-full w-64 bg-base-300 shadow-lg", children: /* @__PURE__ */ jsxs("div", { className: "p-4", children: [
    /* @__PURE__ */ jsx("h2", { className: "mb-6 font-bold text-2xl text-primary", children: "Schedhold" }),
    /* @__PURE__ */ jsxs("ul", { className: "menu menu-lg w-full rounded-box bg-base-200", children: [
      /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsxs(
        Link,
        {
          to: "/admin",
          className: "flex items-center gap-3 hover:bg-base-300",
          children: [
            /* @__PURE__ */ jsx(Home, { className: "h-5 w-5" }),
            "Home"
          ]
        }
      ) }),
      /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsxs(
        Link,
        {
          to: "/admin/clients",
          className: "flex items-center gap-3 hover:bg-base-300",
          children: [
            /* @__PURE__ */ jsx(Users, { className: "h-5 w-5" }),
            "Clientes"
          ]
        }
      ) }),
      /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsxs(
        Link,
        {
          to: "/admin/workers",
          className: "flex items-center gap-3 hover:bg-base-300",
          children: [
            /* @__PURE__ */ jsx(UserCog, { className: "h-5 w-5" }),
            "Trabajadores"
          ]
        }
      ) }),
      /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsxs(
        Link,
        {
          to: "/admin/schedules",
          className: "flex items-center gap-3 hover:bg-base-300",
          children: [
            /* @__PURE__ */ jsx(Calendar, { className: "h-5 w-5" }),
            "Horarios"
          ]
        }
      ) })
    ] })
  ] }) });
}
const useLogout = () => {
  const { auth } = useService();
  return useMutation({
    mutationKey: ["logout"],
    mutationFn: () => AppRuntime.runPromise(auth.logout())
  });
};
function Navbar() {
  const { mutate } = useLogout();
  const navigate = useNavigate();
  const handleLogout = () => {
    mutate(void 0, {
      onSettled: () => {
        navigate({
          to: "/login"
        });
      }
    });
  };
  return /* @__PURE__ */ jsxs("div", { className: "navbar w-full bg-neutral text-neutral-content", children: [
    /* @__PURE__ */ jsx("div", { className: "flex-1", children: /* @__PURE__ */ jsx("button", { type: "button", className: "btn btn-ghost text-xl", children: "Schedhold" }) }),
    /* @__PURE__ */ jsx("div", { className: "flex-none", children: /* @__PURE__ */ jsxs("details", { className: "dropdown dropdown-end", children: [
      /* @__PURE__ */ jsx("summary", { className: "btn btn-circle avatar", children: /* @__PURE__ */ jsx(User, { size: 26 }) }),
      /* @__PURE__ */ jsxs("ul", { className: "menu menu-sm dropdown-content z-[1] mt-3 w-52 rounded-box bg-neutral p-2 shadow", children: [
        /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx("span", { children: "Profile" }) }),
        /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx("span", { children: "Settings" }) }),
        /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx("button", { type: "button", onClick: handleLogout, children: "Logout" }) })
      ] })
    ] }) })
  ] });
}
const SplitComponent = function RouteComponent() {
  return /* @__PURE__ */ jsxs("div", { className: "flex h-screen w-full ", children: [
    /* @__PURE__ */ jsx(Sidebar, {}),
    /* @__PURE__ */ jsxs("div", { className: "flex w-[calc(100%-16rem)] min-w-0 flex-col", children: [
      /* @__PURE__ */ jsx(Navbar, {}),
      /* @__PURE__ */ jsx("div", { className: "flex-1 overflow-y-auto bg-base-200", children: /* @__PURE__ */ jsx("div", { className: "container mx-auto px-4 py-8", children: /* @__PURE__ */ jsx(Outlet, {}) }) })
    ] })
  ] });
};

export { SplitComponent as component };
//# sourceMappingURL=route-DCuKRMAh.mjs.map
