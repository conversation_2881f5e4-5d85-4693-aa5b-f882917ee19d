{"version": 3, "file": "route-ntImKvzZ.mjs", "sources": ["../../../../../src/modules/auth/hooks/auth-options.tsx", "../../../../../src/routes/_authed/route.tsx?tsr-split=component"], "sourcesContent": null, "names": ["SplitComponent", "RouteComponent", "svc", "useService", "isLoading", "isSuccess", "isError", "error", "useQuery", "authOptions", "useEffect", "errorResult", "getErrorResult", "message", "toast", "success"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAIO,MAAM,WAAc,GAAA,CAAC,EAAE,IAAA,OAC7B,YAAa,CAAA;AAAA,EACZ,QAAA,EAAU,CAAC,eAAe,CAAA;AAAA,EAC1B,SAAS,MAAM,UAAA,CAAW,UAAW,CAAA,IAAA,CAAK,YAAY;AACvD,CAAC,CAAA;ACFyDA,MAAAA,cAAAA,GAAA,SAMlDC,cAAiB,GAAA;AACzB,EAAA,MAAMC,MAAMC,UAAW,EAAA;AACjB,EAAA,MAAA;AAAA,IAAEC,SAAAA;AAAAA,IAAWC,SAAAA;AAAAA,IAAWC,OAAAA;AAAAA,IAASC;AAAAA,MAAUC,QAAS,CAAA;AAAA,IACzD,GAAGC,YAAYP,GAAG;AAAA,GAClB,CAAA;AAEDQ,EAAAA,SAAAA,CAAU,MAAM;AACf,IAAA,IAAIJ,OAAS,EAAA;AACNK,MAAAA,MAAAA,WAAAA,GAAcC,eAAeL,KAAK,CAAA;AAClCA,MAAAA,KAAAA,CAAAA,KAAAA,CAAMI,WAAYJ,CAAAA,KAAAA,CAAMM,OAAO,CAAA;AAAA;AAAA,GAEpC,EAAA,CAACP,OAASC,EAAAA,KAAK,CAAC,CAAA;AAEnBG,EAAAA,SAAAA,CAAU,MAAM;AACf,IAAA,IAAIL,SAAW,EAAA;AACdS,MAAAA,KAAAA,CAAMC,QAAQ,qBAAqB,CAAA;AAAA;AAAA,GACpC,EACE,CAACV,SAAS,CAAC,CAAA;AAEd,EAAA,IAAID,WAAmB,uBAAA,GAAA,CAAA,OAAA,EAAI,QAAA,EAA4B,mCAAA,CAAA;AAEnDC,EAAAA,IAAAA,SAAkB,EAAA,uBAAC,GAAA,CAAA,MAAA,EAAS,EAAA,CAAA;AACzB,EAAA,uBAAC,GAAA,CAAA,QAAA,EAAS,EAAA,EAAA,EAAG,UAAW,CAAA;AAChC;;;;"}