import { jsx, jsxs } from 'react/jsx-runtime';
import { Link } from '@tanstack/react-router';
import { Users, Briefcase, Calendar } from 'lucide-react';

const SplitComponent = function RouteComponent() {
  return /* @__PURE__ */ jsx("div", { className: "container mx-auto p-8", children: /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3", children: [
    /* @__PURE__ */ jsxs(Link, { to: "/admin/workers", className: "group hover:-translate-y-1 relative overflow-hidden rounded-lg bg-base-100 p-6 shadow-lg transition-all hover:shadow-xl", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-4", children: [
        /* @__PURE__ */ jsx("div", { className: "rounded-full bg-primary/10 p-3", children: /* @__PURE__ */ jsx(Users, { className: "h-6 w-6 text-primary" }) }),
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("h2", { className: "font-semibold text-base-content text-xl", children: "Workers" }),
          /* @__PURE__ */ jsx("p", { className: "text-base-content/70", children: "Manage your workforce" })
        ] })
      ] }),
      /* @__PURE__ */ jsx("div", { className: "absolute bottom-0 left-0 h-1 w-full scale-x-0 transform bg-primary transition-transform duration-300 group-hover:scale-x-100" })
    ] }),
    /* @__PURE__ */ jsxs(Link, { to: "/admin/clients", className: "group hover:-translate-y-1 relative overflow-hidden rounded-lg bg-base-100 p-6 shadow-lg transition-all hover:shadow-xl", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-4", children: [
        /* @__PURE__ */ jsx("div", { className: "rounded-full bg-secondary/10 p-3", children: /* @__PURE__ */ jsx(Briefcase, { className: "h-6 w-6 text-secondary" }) }),
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("h2", { className: "font-semibold text-base-content text-xl", children: "Clients" }),
          /* @__PURE__ */ jsx("p", { className: "text-base-content/70", children: "Manage your clients" })
        ] })
      ] }),
      /* @__PURE__ */ jsx("div", { className: "absolute bottom-0 left-0 h-1 w-full scale-x-0 transform bg-secondary transition-transform duration-300 group-hover:scale-x-100" })
    ] }),
    /* @__PURE__ */ jsxs(Link, { to: "/admin/schedules", className: "group hover:-translate-y-1 relative overflow-hidden rounded-lg bg-base-100 p-6 shadow-lg transition-all hover:shadow-xl", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-4", children: [
        /* @__PURE__ */ jsx("div", { className: "rounded-full bg-accent/10 p-3", children: /* @__PURE__ */ jsx(Calendar, { className: "h-6 w-6 text-accent" }) }),
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("h2", { className: "font-semibold text-base-content text-xl", children: "Schedules" }),
          /* @__PURE__ */ jsx("p", { className: "text-base-content/70", children: "Manage work schedules" })
        ] })
      ] }),
      /* @__PURE__ */ jsx("div", { className: "absolute bottom-0 left-0 h-1 w-full scale-x-0 transform bg-accent transition-transform duration-300 group-hover:scale-x-100" })
    ] })
  ] }) });
};

export { SplitComponent as component };
//# sourceMappingURL=index-CRUcNw9S.mjs.map
