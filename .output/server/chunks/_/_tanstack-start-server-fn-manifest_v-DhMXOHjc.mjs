const _tanstackStartServerFnManifest_v = {
  "src_modules_auth_server_theme_ts--getThemeServerFn_createServerFn_handler": {
    functionName: "getThemeServerFn_createServerFn_handler",
    importer: () => import('./theme-CfcYOZYo.mjs')
  },
  "src_modules_auth_server_theme_ts--setThemeServerFn_createServerFn_handler": {
    functionName: "setThemeServerFn_createServerFn_handler",
    importer: () => import('./theme-CfcYOZYo.mjs')
  }
};

export { _tanstackStartServerFnManifest_v as default };
//# sourceMappingURL=_tanstack-start-server-fn-manifest_v-DhMXOHjc.mjs.map
