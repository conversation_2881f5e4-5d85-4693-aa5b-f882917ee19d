import { jsx } from 'react/jsx-runtime';
import { useQuery, queryOptions } from '@tanstack/react-query';
import { Outlet, Navigate } from '@tanstack/react-router';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import { A as AppRuntime } from './runtimes-CvAMo_9p.mjs';
import { u as useService } from './ssr.mjs';
import { g as getErrorResult } from './effectErrors-CR6URVJK.mjs';
import 'effect';
import '@effect/platform';
import '@tanstack/react-query-devtools';
import '@tanstack/react-router-devtools';
import 'valibot';
import 'tiny-invariant';
import 'tiny-warning';
import '@tanstack/router-core';
import 'node:async_hooks';
import '@tanstack/react-router-with-query';
import '@tanstack/history';
import 'jsesc';
import 'node:stream';
import 'isbot';
import 'react-dom/server';
import 'node:stream/web';
import 'effect/Runtime';

const authOptions = ({ auth }) => queryOptions({
  queryKey: ["authenticated"],
  queryFn: () => AppRuntime.runPromise(auth.getSession())
});
const SplitComponent = function RouteComponent() {
  const svc = useService();
  const {
    isLoading,
    isSuccess,
    isError,
    error
  } = useQuery({
    ...authOptions(svc)
  });
  useEffect(() => {
    if (isError) {
      const errorResult = getErrorResult(error);
      toast.error(errorResult.error.message);
    }
  }, [isError, error]);
  useEffect(() => {
    if (isSuccess) {
      toast.success("Usuario autenticado");
    }
  }, [isSuccess]);
  if (isLoading) return /* @__PURE__ */ jsx("div", { children: "Verificando autenticaci\xF3n..." });
  if (isSuccess) return /* @__PURE__ */ jsx(Outlet, {});
  return /* @__PURE__ */ jsx(Navigate, { to: "/login" });
};

export { SplitComponent as component };
//# sourceMappingURL=route-ntImKvzZ.mjs.map
