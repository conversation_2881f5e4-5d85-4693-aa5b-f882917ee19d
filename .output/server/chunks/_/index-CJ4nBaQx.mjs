import * as v from 'valibot';
import { jsx, jsxs } from 'react/jsx-runtime';
import React, { useEffect } from 'react';
import { useStore } from '@tanstack/react-store';
import { Plus, Eye, Trash2 } from 'lucide-react';
import { w as withForm } from './form-B3Cqm05b.mjs';

const defaultValues = {
  name: "",
  sessionDuration: 60,
  breakDuration: 15,
  turns: []
};
const TurnCreateSchema = v.object({
  name: v.pipe(
    v.string("Debe ingresar un nombre para el turno"),
    v.minLength(1, "Debe tener al menos un caracter")
  ),
  startTime: v.pipe(
    v.number("Debe ingresar una hora de inicio"),
    v.minValue(0, "La hora debe ser mayor o igual a 0"),
    v.maxValue(2359, "La hora debe ser menor o igual a 23:59")
  ),
  endTime: v.pipe(
    v.number("Debe ingresar una hora de fin"),
    v.minValue(0, "La hora debe ser mayor o igual a 0"),
    v.maxValue(2359, "La hora debe ser menor o igual a 23:59")
  )
});
const CreateScheduleSchema = v.object({
  name: v.pipe(
    v.string("Debe ingresar un nombre"),
    v.minLength(1, "Debe tener al menos un caracter")
  ),
  sessionDuration: v.pipe(
    v.number("Debe ingresar la duraci\xF3n de la sesi\xF3n"),
    v.minValue(1, "La duraci\xF3n debe ser mayor a 0")
  ),
  breakDuration: v.pipe(
    v.number("Debe ingresar la duraci\xF3n del descanso"),
    v.minValue(0, "La duraci\xF3n debe ser mayor o igual a 0")
  ),
  turns: v.array(TurnCreateSchema)
});
function SchedulePreview({
  turn,
  sessionDuration,
  breakDuration
}) {
  const formatTime2 = (time) => {
    const hours = Math.floor(time / 100);
    const minutes = time % 100;
    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
  };
  const generateTimeSlots = () => {
    const startHour = Math.floor(turn.startTime / 100);
    const startMinute = turn.startTime % 100;
    const endHour = Math.floor(turn.endTime / 100);
    const endMinute = turn.endTime % 100;
    const startTimeInMinutes = startHour * 60 + startMinute;
    const endTimeInMinutes = endHour * 60 + endMinute;
    const slots = [];
    let currentTime = startTimeInMinutes;
    while (currentTime < endTimeInMinutes) {
      const sessionEnd = currentTime + sessionDuration;
      if (sessionEnd > endTimeInMinutes) {
        break;
      }
      slots.push({
        start: currentTime,
        end: sessionEnd,
        label: "Sesi\xF3n"
      });
      currentTime = sessionEnd + breakDuration;
    }
    return slots;
  };
  const timeSlots = generateTimeSlots();
  const days = [
    "Domingo",
    "Lunes",
    "Martes",
    "Mi\xE9rcoles",
    "Jueves",
    "Viernes",
    "S\xE1bado"
  ];
  const minutesToTimeString = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, "0")}:${mins.toString().padStart(2, "0")}`;
  };
  return /* @__PURE__ */ jsx("div", { className: "card bg-base-100", children: /* @__PURE__ */ jsxs("div", { className: "card-body", children: [
    /* @__PURE__ */ jsxs("h4", { className: "card-title text-lg", children: [
      turn.name || "Turno sin nombre",
      " - ",
      formatTime2(turn.startTime),
      " a",
      " ",
      formatTime2(turn.endTime)
    ] }),
    /* @__PURE__ */ jsx("div", { className: "overflow-x-auto", children: /* @__PURE__ */ jsxs(
      "div",
      {
        className: "grid gap-1 text-xs",
        style: {
          gridTemplateColumns: `120px repeat(${days.length}, 1fr)`,
          gridTemplateRows: `auto repeat(${timeSlots.length}, minmax(64px, auto))`
        },
        children: [
          /* @__PURE__ */ jsx("div", { className: "rounded bg-base-200 p-2 font-medium", children: "Hora" }),
          days.map((day) => /* @__PURE__ */ jsx(
            "div",
            {
              className: "rounded bg-base-200 p-2 text-center font-medium",
              children: day
            },
            day
          )),
          timeSlots.map((slot, index) => /* @__PURE__ */ jsxs(
            React.Fragment,
            {
              children: [
                /* @__PURE__ */ jsxs("div", { className: "flex items-center rounded bg-base-100 p-2 font-mono text-xs", children: [
                  minutesToTimeString(slot.start),
                  " -",
                  " ",
                  minutesToTimeString(slot.end)
                ] }),
                days.map((day) => /* @__PURE__ */ jsx(
                  "div",
                  {
                    className: "flex items-center justify-center p-1",
                    children: /* @__PURE__ */ jsx("div", { className: "flex h-16 w-full items-center justify-center rounded bg-primary px-2 py-1 font-medium text-primary-content text-xs", children: slot.label })
                  },
                  day
                ))
              ]
            },
            `${slot}-${// biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
            index}`
          ))
        ]
      }
    ) }),
    /* @__PURE__ */ jsxs("div", { className: "mt-4 text-sm", children: [
      /* @__PURE__ */ jsx("div", { className: "flex gap-4", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
        /* @__PURE__ */ jsx("div", { className: "h-4 w-4 rounded bg-primary" }),
        /* @__PURE__ */ jsxs("span", { children: [
          "Sesi\xF3n (",
          sessionDuration,
          " min)"
        ] })
      ] }) }),
      /* @__PURE__ */ jsxs("div", { className: "mt-2 text-base-content/70 text-xs", children: [
        "Total de sesiones: ",
        timeSlots.length
      ] })
    ] })
  ] }) });
}
const formatTime = (time) => {
  const hours = Math.floor(time / 100);
  const minutes = time % 100;
  return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
};
const parseTime = (timeString) => {
  const [hours, minutes] = timeString.split(":").map(Number);
  return (hours || 0) * 100 + (minutes || 0);
};
const formatTimeWithAmPm = (time) => {
  const hours = Math.floor(time / 100);
  const minutes = time % 100;
  const period = hours >= 12 ? "PM" : "AM";
  const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
  return `${displayHours}:${minutes.toString().padStart(2, "0")} ${period}`;
};
const generateEndTimeOptions = (startTime, sessionDuration, breakDuration) => {
  const options = [];
  const totalDuration = sessionDuration + breakDuration;
  const startHours = Math.floor(startTime / 100);
  const startMinutes = startTime % 100;
  let currentInMinutes = startHours * 60 + startMinutes + totalDuration;
  while (currentInMinutes < 24 * 60) {
    const currentHours = Math.floor(currentInMinutes / 60);
    const currentMinutes = currentInMinutes % 60;
    if (currentHours >= 24) break;
    const currentTime = currentHours * 100 + currentMinutes;
    options.push({
      value: currentTime,
      label: formatTimeWithAmPm(currentTime)
    });
    currentInMinutes += totalDuration;
  }
  return options;
};
const getTotalSessions = (startTime, endTime, sessionDuration, breakDuration) => {
  const startHours = Math.floor(startTime / 100);
  const startMinutes = startTime % 100;
  const startInMinutes = startHours * 60 + startMinutes;
  const endHours = Math.floor(endTime / 100);
  const endMinutes = endTime % 100;
  const endInMinutes = endHours * 60 + endMinutes;
  const totalAvailableTime = endInMinutes - startInMinutes;
  const totalDuration = sessionDuration + breakDuration;
  return Math.floor(totalAvailableTime / totalDuration);
};
const TurnsTable = withForm({
  defaultValues,
  props: {},
  render: ({ form, selectedTurnIndex, onTurnSelect }) => {
    const [sessionDuration, breakDuration] = useStore(form.store, (state) => [
      state.values.sessionDuration,
      state.values.breakDuration
    ]);
    return /* @__PURE__ */ jsx("div", { children: /* @__PURE__ */ jsx(form.Field, { name: "turns", mode: "array", children: (field) => {
      const turns = field.state.value;
      return /* @__PURE__ */ jsxs("div", { className: "space-y-4", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between gap-4", children: [
          /* @__PURE__ */ jsx("span", { className: "font-semibold", children: "Turnos" }),
          /* @__PURE__ */ jsx("div", { className: "flex justify-end", children: /* @__PURE__ */ jsxs(
            "button",
            {
              type: "button",
              className: "btn btn-primary btn-sm",
              onClick: () => field.pushValue({
                name: "",
                startTime: 800,
                endTime: 1700
              }),
              children: [
                /* @__PURE__ */ jsx(Plus, { size: 16 }),
                "Agregar Turno"
              ]
            }
          ) })
        ] }),
        turns.length > 0 && /* @__PURE__ */ jsx("div", { className: "overflow-x-auto", children: /* @__PURE__ */ jsxs("table", { className: "table", children: [
          /* @__PURE__ */ jsx("thead", { children: /* @__PURE__ */ jsxs("tr", { children: [
            /* @__PURE__ */ jsx("th", { children: "Nombre" }),
            /* @__PURE__ */ jsx("th", { children: "Hora Inicio" }),
            /* @__PURE__ */ jsx("th", { children: "Hora Fin" }),
            /* @__PURE__ */ jsx("th", { children: "Total de sesiones" }),
            /* @__PURE__ */ jsx("th", { children: "Acciones" })
          ] }) }),
          /* @__PURE__ */ jsx("tbody", { children: turns.map((_, index) => {
            return /* @__PURE__ */ jsxs(
              "tr",
              {
                className: selectedTurnIndex === index ? "bg-primary/20" : "",
                children: [
                  /* @__PURE__ */ jsx("td", { children: /* @__PURE__ */ jsx(form.Field, { name: `turns[${index}].name`, children: (subField) => /* @__PURE__ */ jsx(
                    "input",
                    {
                      type: "text",
                      className: "input input-sm w-full",
                      value: subField.state.value || "",
                      onChange: (e) => subField.handleChange(e.target.value),
                      placeholder: "Nombre del turno"
                    }
                  ) }) }),
                  /* @__PURE__ */ jsx("td", { children: /* @__PURE__ */ jsx(form.Field, { name: `turns[${index}].startTime`, children: (subField) => /* @__PURE__ */ jsx(
                    "input",
                    {
                      type: "time",
                      className: "input input-sm w-full",
                      value: formatTime(
                        subField.state.value || 800
                      ),
                      onChange: (e) => subField.handleChange(
                        parseTime(e.target.value)
                      )
                    }
                  ) }) }),
                  /* @__PURE__ */ jsx("td", { children: /* @__PURE__ */ jsx(
                    form.Subscribe,
                    {
                      selector: (state) => {
                        var _a;
                        return (_a = state.values.turns[index]) == null ? void 0 : _a.startTime;
                      },
                      children: (startTime) => {
                        const endTimeOptions = generateEndTimeOptions(
                          startTime || 800,
                          sessionDuration,
                          breakDuration
                        );
                        useEffect(() => {
                          var _a;
                          form.setFieldValue(
                            `turns[${index}].endTime`,
                            ((_a = endTimeOptions[0]) == null ? void 0 : _a.value) || 1700
                          );
                        }, [startTime]);
                        return /* @__PURE__ */ jsx(
                          form.Field,
                          {
                            name: `turns[${index}].endTime`,
                            children: (subField) => {
                              var _a;
                              return /* @__PURE__ */ jsx(
                                "select",
                                {
                                  className: "select select-sm w-full",
                                  value: subField.state.value || ((_a = endTimeOptions[0]) == null ? void 0 : _a.value) || 1700,
                                  onChange: (e) => subField.handleChange(
                                    Number(e.target.value)
                                  ),
                                  children: endTimeOptions.map((option) => /* @__PURE__ */ jsx(
                                    "option",
                                    {
                                      value: option.value,
                                      children: option.label
                                    },
                                    option.value
                                  ))
                                }
                              );
                            }
                          }
                        );
                      }
                    }
                  ) }),
                  /* @__PURE__ */ jsx("td", { children: /* @__PURE__ */ jsx(
                    form.Subscribe,
                    {
                      selector: (state) => {
                        var _a, _b;
                        return [
                          (_a = state.values.turns[index]) == null ? void 0 : _a.startTime,
                          (_b = state.values.turns[index]) == null ? void 0 : _b.endTime
                        ];
                      },
                      children: ([startTime, endTime]) => {
                        const totalSessions = getTotalSessions(
                          startTime || 800,
                          endTime || 1700,
                          sessionDuration,
                          breakDuration
                        );
                        return /* @__PURE__ */ jsxs("div", { className: "text-base-content/70 text-xs", children: [
                          totalSessions,
                          " sesiones"
                        ] });
                      }
                    }
                  ) }),
                  /* @__PURE__ */ jsx("td", { children: /* @__PURE__ */ jsxs("div", { className: "flex gap-2", children: [
                    /* @__PURE__ */ jsx(
                      "button",
                      {
                        type: "button",
                        className: `btn btn-sm ${selectedTurnIndex === index ? "btn-primary" : "btn-outline"}`,
                        onClick: () => onTurnSelect(
                          selectedTurnIndex === index ? null : index
                        ),
                        children: /* @__PURE__ */ jsx(Eye, { size: 16 })
                      }
                    ),
                    /* @__PURE__ */ jsx(
                      "button",
                      {
                        type: "button",
                        className: "btn btn-error btn-sm",
                        onClick: () => {
                          field.removeValue(index);
                          if (selectedTurnIndex === index) {
                            onTurnSelect(null);
                          } else if (selectedTurnIndex !== null && selectedTurnIndex > index) {
                            onTurnSelect(selectedTurnIndex - 1);
                          }
                        },
                        children: /* @__PURE__ */ jsx(Trash2, { size: 16 })
                      }
                    )
                  ] }) })
                ]
              },
              `turn-${// biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
              index}`
            );
          }) })
        ] }) }),
        turns.length === 0 && /* @__PURE__ */ jsx("div", { className: "card bg-base-200", children: /* @__PURE__ */ jsx("div", { className: "card-body text-center", children: /* @__PURE__ */ jsx("p", { className: "text-base-content/70", children: 'No hay turnos agregados. Haz clic en "Agregar Turno" para comenzar.' }) }) })
      ] });
    } }) });
  }
});

export { CreateScheduleSchema as C, SchedulePreview as S, TurnsTable as T, defaultValues as d };
//# sourceMappingURL=index-CJ4nBaQx.mjs.map
