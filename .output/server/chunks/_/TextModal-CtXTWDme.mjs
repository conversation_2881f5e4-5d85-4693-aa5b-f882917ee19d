import { jsx, jsxs } from 'react/jsx-runtime';
import { useState } from 'react';
import { c as cn } from './classes-DMlJhLpb.mjs';
import { C as CloseModal } from './BasicTable-WMVaHEYn.mjs';

function TextModal({ open = false, title, text }) {
  const [_open, setOpen] = useState(open);
  return /* @__PURE__ */ jsx("div", { className: cn("modal", open && "modal-open"), children: /* @__PURE__ */ jsxs("div", { className: "modal-box", children: [
    /* @__PURE__ */ jsx(CloseModal, { onClose: () => setOpen(false) }),
    /* @__PURE__ */ jsx("h3", { className: "font-bold text-lg", children: title }),
    /* @__PURE__ */ jsx("p", { children: text })
  ] }) });
}

export { TextModal as T };
//# sourceMappingURL=TextModal-CtXTWDme.mjs.map
