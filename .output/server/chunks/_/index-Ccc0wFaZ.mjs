import { jsxs, Fragment, jsx } from 'react/jsx-runtime';
import { useState, useEffect } from 'react';
import { useQuery, queryOptions, useQueryClient, useMutation } from '@tanstack/react-query';
import { u as useService } from './ssr.mjs';
import { g as getErrorResult } from './effectErrors-CR6URVJK.mjs';
import { A as AppRuntime, D as DocumentType } from './runtimes-CvAMo_9p.mjs';
import { useReactTable, getCoreRowModel, createColumnHelper } from '@tanstack/react-table';
import { C as CloseModal, B as BasicTable } from './BasicTable-WMVaHEYn.mjs';
import { User, Mail, MapPin, Phone, Calendar, FileText, Edit, Trash } from 'lucide-react';
import { toast } from 'react-toastify';
import { c as cn } from './classes-DMlJhLpb.mjs';
import { create } from 'mutative';
import { T as TextModal } from './TextModal-CtXTWDme.mjs';
import { u as useAppForm } from './form-B3Cqm05b.mjs';
import * as v from 'valibot';
import '@tanstack/react-router';
import '@tanstack/react-query-devtools';
import '@tanstack/react-router-devtools';
import 'tiny-invariant';
import 'tiny-warning';
import '@tanstack/router-core';
import 'node:async_hooks';
import 'effect';
import '@tanstack/react-router-with-query';
import '@tanstack/history';
import 'jsesc';
import 'node:stream';
import 'isbot';
import 'react-dom/server';
import 'node:stream/web';
import 'effect/Runtime';
import '@effect/platform';
import 'clsx';
import 'tailwind-merge';
import '@tanstack/react-form';
import 'downshift';
import 'use-mutative';

const clientOptions = ({ client }) => queryOptions({
  queryKey: ["clients"],
  queryFn: () => AppRuntime.runPromise(client.getAll())
});
const clientOptionsById = ({ client }, id) => queryOptions({
  queryKey: ["clients", id],
  queryFn: () => AppRuntime.runPromise(client.getById(id))
});
function useDeleteClient() {
  const service = useService();
  const { client } = service;
  const queryClient = useQueryClient();
  const queryKey = clientOptions(service).queryKey;
  return useMutation({
    mutationFn: (id) => AppRuntime.runPromise(client.delete(id)),
    onSuccess: (_, id) => {
      queryClient.setQueryData(
        queryKey,
        (old) => create(old != null ? old : [], (draft) => {
          const index = draft.findIndex((c) => c.id === id);
          if (index !== -1) {
            draft.splice(index, 1);
          }
        })
      );
    }
  });
}
function DeleteClientModal({
  isOpen,
  setIsOpen,
  client
}) {
  const { mutate } = useDeleteClient();
  return /* @__PURE__ */ jsx("div", { className: cn("modal", isOpen && "modal-open"), children: /* @__PURE__ */ jsxs("div", { className: "modal-box", children: [
    /* @__PURE__ */ jsx(CloseModal, { onClose: () => setIsOpen(false) }),
    /* @__PURE__ */ jsx("h3", { className: "font-bold text-lg", children: "Eliminar cliente" }),
    /* @__PURE__ */ jsx("p", { children: "\xBFEst\xE1s seguro de que quieres eliminar este cliente?" }),
    /* @__PURE__ */ jsxs("p", { className: "mt-2 text-gray-600 text-sm", children: [
      "Cliente: ",
      client.person.name,
      " ",
      client.person.fatherLastName,
      " ",
      client.person.motherLastName
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "modal-action", children: [
      /* @__PURE__ */ jsx(
        "button",
        {
          type: "button",
          className: "btn btn-primary",
          onClick: () => setIsOpen(false),
          children: "Cancelar"
        }
      ),
      /* @__PURE__ */ jsx(
        "button",
        {
          type: "button",
          className: "btn btn-error",
          onClick: () => {
            mutate(client.id, {
              onSuccess: () => {
                toast.success("Cliente eliminado");
                setIsOpen(false);
              },
              onError: (error) => {
                console.log(error);
                toast.error("Error al eliminar cliente");
              }
            });
          },
          children: "Eliminar"
        }
      )
    ] })
  ] }) });
}
function useEditeClient() {
  const service = useService();
  const { client } = service;
  const queryClient = useQueryClient();
  const queryKey = clientOptions(service).queryKey;
  return useMutation({
    mutationFn: (updatedClient) => AppRuntime.runPromise(client.update(updatedClient)),
    onSuccess: (_, updatedClient) => {
      queryClient.setQueryData(
        queryKey,
        (old) => create(old != null ? old : [], (draft) => {
          const index = draft.findIndex((c) => c.id === updatedClient.id);
          if (index !== -1) {
            draft[index] = {
              ...draft[index],
              person: updatedClient.person,
              updatedAt: (/* @__PURE__ */ new Date()).toISOString()
            };
          }
        })
      );
    }
  });
}
const CreateClientSchema = v.object({
  name: v.pipe(
    v.string("Debe ingresar un nombre"),
    v.minLength(1, "Debe tener al menos un caracter")
  ),
  fatherLastName: v.pipe(
    v.string("Debe ingresar un apellido"),
    v.minLength(1, "Debe tener al menos un caracter")
  ),
  motherLastName: v.pipe(
    v.string("Debe ingresar un apellido"),
    v.minLength(1, "Debe tener al menos un caracter")
  ),
  email: v.union([
    v.pipe(
      v.string("Debe ingresar un email"),
      v.email("Debe ingresar un email valido")
    ),
    v.literal("")
  ]),
  address: v.optional(v.string()),
  phone: v.optional(v.string()),
  birthDate: v.nullable(v.string()),
  gender: v.boolean(),
  document: v.pipe(
    v.string("Debe ingresar un documento"),
    v.minLength(8, "Debe tener al menos 8 caracteres")
  ),
  documentType: v.number()
});
function useEditeClientModal({
  setIsOpen,
  client
}) {
  const { mutate } = useEditeClient();
  const {
    id,
    person: { id: personId, ...personRest },
    ...rest
  } = client;
  const form = useAppForm({
    defaultValues: {
      ...personRest
    },
    validators: {
      onChange: CreateClientSchema
    },
    onSubmit: ({ value }) => {
      mutate(
        {
          id,
          person: {
            ...value,
            id: personId
          }
        },
        {
          onSuccess: () => {
            toast.success("Cliente actualizado");
            handleClose();
          },
          onError: (_error) => {
            console.log(_error);
            const { error } = getErrorResult(_error);
            toast.error(error.message);
          }
        }
      );
    }
  });
  function handleClose() {
    form.reset();
    setIsOpen(false);
  }
  return {
    form,
    handleClose
  };
}
function EditeClientForm({
  isOpen,
  setIsOpen,
  client
}) {
  const { form, handleClose } = useEditeClientModal({
    setIsOpen,
    client
  });
  return /* @__PURE__ */ jsx("div", { className: cn("modal", isOpen && "modal-open"), children: /* @__PURE__ */ jsxs("div", { className: "modal-box", children: [
    /* @__PURE__ */ jsx(CloseModal, { onClose: handleClose }),
    /* @__PURE__ */ jsx("h3", { className: "font-bold text-lg", children: "Editar Cliente" }),
    /* @__PURE__ */ jsx(
      "form",
      {
        onSubmit: (e) => {
          e.preventDefault();
          form.handleSubmit();
        },
        children: /* @__PURE__ */ jsxs(form.AppForm, { children: [
          /* @__PURE__ */ jsxs("fieldset", { className: "fieldset", children: [
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "name",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Nombre",
                    placeholder: "Nombre",
                    prefixComponent: /* @__PURE__ */ jsx(User, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "fatherLastName",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Apellido Paterno",
                    placeholder: "Apellido Paterno",
                    prefixComponent: /* @__PURE__ */ jsx(User, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "motherLastName",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Apellido Materno",
                    placeholder: "Apellido Materno",
                    prefixComponent: /* @__PURE__ */ jsx(User, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "email",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Correo Electr\xF3nico",
                    placeholder: "<EMAIL>",
                    prefixComponent: /* @__PURE__ */ jsx(Mail, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "address",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Direcci\xF3n",
                    placeholder: "Direcci\xF3n",
                    prefixComponent: /* @__PURE__ */ jsx(MapPin, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "phone",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Tel\xE9fono",
                    placeholder: "Tel\xE9fono",
                    prefixComponent: /* @__PURE__ */ jsx(Phone, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "birthDate",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Fecha de Nacimiento",
                    placeholder: "YYYY-MM-DD",
                    type: "date",
                    prefixComponent: /* @__PURE__ */ jsx(Calendar, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "document",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Documento",
                    placeholder: "N\xFAmero de Documento",
                    prefixComponent: /* @__PURE__ */ jsx(FileText, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "documentType",
                children: ({ FSSelectField }) => /* @__PURE__ */ jsx(
                  FSSelectField,
                  {
                    label: "Tipo de Documento",
                    placeholder: "Tipo de Documento",
                    isNumber: true,
                    options: [
                      {
                        value: 0,
                        label: "DNI"
                      },
                      {
                        value: 1,
                        label: "Pasaporte"
                      },
                      {
                        value: 2,
                        label: "RUC"
                      }
                    ]
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "gender",
                children: ({ FSToggleField }) => /* @__PURE__ */ jsx(
                  FSToggleField,
                  {
                    label: "G\xE9nero",
                    trueLabel: "Masculino",
                    falseLabel: "Femenino"
                  }
                )
              }
            )
          ] }),
          /* @__PURE__ */ jsx("div", { className: "modal-action", children: /* @__PURE__ */ jsx("button", { type: "submit", className: "btn btn-primary", children: "Actualizar" }) })
        ] })
      }
    )
  ] }) });
}
function EditeClientModal({ isOpen, setIsOpen, id }) {
  const svc = useService();
  const { data, isError, error, isPending } = useQuery({
    ...clientOptionsById(svc, id),
    enabled: isOpen
  });
  useEffect(() => {
    if (error) {
      console.log(error);
    }
  }, [error]);
  if (isPending) return /* @__PURE__ */ jsx(TextModal, { text: "Cargando..." });
  if (isError)
    return /* @__PURE__ */ jsx(
      TextModal,
      {
        text: "No se pudo cargar el cliente",
        title: getErrorResult(error).error.code.toString()
      }
    );
  return /* @__PURE__ */ jsx(EditeClientForm, { isOpen, setIsOpen, client: data });
}
const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor("person.name", {
    header: "Nombre",
    cell: (info) => info.getValue()
  }),
  columnHelper.accessor("person.fatherLastName", {
    header: "Apellido Paterno",
    cell: (info) => info.getValue()
  }),
  columnHelper.accessor("person.motherLastName", {
    header: "Apellido Materno",
    cell: (info) => info.getValue()
  }),
  columnHelper.accessor("person.email", {
    header: "Email",
    cell: (info) => info.getValue() || "-"
  }),
  columnHelper.accessor("person.phone", {
    header: "Tel\xE9fono",
    cell: (info) => info.getValue() || "-"
  }),
  columnHelper.accessor("person.birthDate", {
    header: "Fecha de nacimiento",
    cell: (info) => info.getValue() || "-"
  }),
  columnHelper.accessor("person.document", {
    header: "Documento",
    cell: (info) => {
      const documentType = info.row.original.person.documentType;
      const document = info.getValue();
      const typeLabel = documentType === DocumentType.DNI ? "DNI" : documentType === DocumentType.PASAPORTE ? "Pasaporte" : "RUC";
      return `${typeLabel}: ${document}`;
    }
  }),
  columnHelper.display({
    id: "actions",
    header: "Acciones",
    cell: ({ row }) => {
      const [isEditOpen, setIsEditOpen] = useState(false);
      const [isDeleteOpen, setIsDeleteOpen] = useState(false);
      const client = row.original;
      return /* @__PURE__ */ jsxs("div", { className: "flex gap-2", children: [
        /* @__PURE__ */ jsx(
          "button",
          {
            type: "button",
            className: "btn btn-sm btn-primary",
            onClick: () => setIsEditOpen(true),
            children: /* @__PURE__ */ jsx(Edit, { size: 16 })
          }
        ),
        /* @__PURE__ */ jsx(
          "button",
          {
            type: "button",
            className: "btn btn-sm btn-error",
            onClick: () => setIsDeleteOpen(true),
            children: /* @__PURE__ */ jsx(Trash, { size: 16 })
          }
        ),
        /* @__PURE__ */ jsx(
          EditeClientModal,
          {
            isOpen: isEditOpen,
            setIsOpen: setIsEditOpen,
            id: client.id
          }
        ),
        /* @__PURE__ */ jsx(
          DeleteClientModal,
          {
            isOpen: isDeleteOpen,
            setIsOpen: setIsDeleteOpen,
            client
          }
        )
      ] });
    }
  })
];
function Table({ clients }) {
  const table = useReactTable({
    data: clients,
    columns,
    getCoreRowModel: getCoreRowModel()
  });
  return /* @__PURE__ */ jsx(BasicTable, { table });
}
function ClientTable() {
  const svc = useService();
  const { data, isError, error, isPending } = useQuery(clientOptions(svc));
  useEffect(() => {
    if (error) {
      console.log(getErrorResult(error).error);
    }
  }, [error]);
  if (isError) return /* @__PURE__ */ jsxs("div", { children: [
    "Error: ",
    getErrorResult(error).error.message
  ] });
  if (isPending) return /* @__PURE__ */ jsx("div", { children: "Loading..." });
  return /* @__PURE__ */ jsx(Table, { clients: data });
}
function useCreateClient() {
  const service = useService();
  const { client } = service;
  const queryClient = useQueryClient();
  const queryKey = clientOptions(service).queryKey;
  return useMutation({
    mutationFn: (newClient) => AppRuntime.runPromise(client.create(newClient)),
    onSuccess: (id, newClient) => {
      queryClient.setQueryData(
        queryKey,
        (old) => create(old != null ? old : [], (draft) => {
          draft.push({
            id,
            person: newClient.person,
            createdAt: (/* @__PURE__ */ new Date()).toISOString(),
            updatedAt: null,
            deletedAt: null
          });
        })
      );
    }
  });
}
const defaultValues = {
  name: "",
  fatherLastName: "",
  motherLastName: "",
  email: "",
  address: "",
  phone: "",
  birthDate: "",
  gender: false,
  document: "",
  documentType: 0
};
function useCreateClientModal({
  setIsOpen
}) {
  const { mutate } = useCreateClient();
  const form = useAppForm({
    defaultValues,
    validators: {
      onChange: CreateClientSchema
    },
    onSubmit: ({ value }) => {
      mutate(
        {
          person: value
        },
        {
          onSuccess: () => {
            toast.success("Cliente creado");
            handleClose();
          },
          onError: (_error) => {
            console.log(_error);
            const { error } = getErrorResult(_error);
            toast.error(error.message);
          }
        }
      );
    }
  });
  function handleClose() {
    form.reset();
    setIsOpen(false);
  }
  return {
    form,
    handleClose
  };
}
function CreateClientModal({
  isOpen,
  setIsOpen
}) {
  const { form, handleClose } = useCreateClientModal({ setIsOpen });
  return /* @__PURE__ */ jsx("div", { className: cn("modal", isOpen && "modal-open"), children: /* @__PURE__ */ jsxs("div", { className: "modal-box", children: [
    /* @__PURE__ */ jsx(CloseModal, { onClose: handleClose }),
    /* @__PURE__ */ jsx("h3", { className: "font-bold text-lg", children: "Crear Cliente" }),
    /* @__PURE__ */ jsx(
      "form",
      {
        onSubmit: (e) => {
          e.preventDefault();
          form.handleSubmit();
        },
        children: /* @__PURE__ */ jsxs(form.AppForm, { children: [
          /* @__PURE__ */ jsxs("fieldset", { className: "fieldset", children: [
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "name",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Nombre",
                    placeholder: "Nombre",
                    prefixComponent: /* @__PURE__ */ jsx(User, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "fatherLastName",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Apellido Paterno",
                    placeholder: "Apellido Paterno",
                    prefixComponent: /* @__PURE__ */ jsx(User, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "motherLastName",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Apellido Materno",
                    placeholder: "Apellido Materno",
                    prefixComponent: /* @__PURE__ */ jsx(User, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "email",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Correo Electr\xF3nico",
                    placeholder: "<EMAIL>",
                    prefixComponent: /* @__PURE__ */ jsx(Mail, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "address",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Direcci\xF3n",
                    placeholder: "Direcci\xF3n",
                    prefixComponent: /* @__PURE__ */ jsx(MapPin, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "phone",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Tel\xE9fono",
                    placeholder: "Tel\xE9fono",
                    prefixComponent: /* @__PURE__ */ jsx(Phone, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "birthDate",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Fecha de Nacimiento",
                    placeholder: "YYYY-MM-DD",
                    type: "date",
                    prefixComponent: /* @__PURE__ */ jsx(Calendar, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "document",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Documento",
                    placeholder: "N\xFAmero de Documento",
                    prefixComponent: /* @__PURE__ */ jsx(FileText, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "documentType",
                children: ({ FSSelectField }) => /* @__PURE__ */ jsx(
                  FSSelectField,
                  {
                    label: "Tipo de Documento",
                    placeholder: "Tipo de Documento",
                    isNumber: true,
                    options: [
                      {
                        value: 0,
                        label: "DNI"
                      },
                      {
                        value: 1,
                        label: "Pasaporte"
                      },
                      {
                        value: 2,
                        label: "RUC"
                      }
                    ]
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "gender",
                children: ({ FSToggleField }) => /* @__PURE__ */ jsx(
                  FSToggleField,
                  {
                    label: "G\xE9nero",
                    trueLabel: "Masculino",
                    falseLabel: "Femenino"
                  }
                )
              }
            )
          ] }),
          /* @__PURE__ */ jsx("div", { className: "modal-action", children: /* @__PURE__ */ jsx("button", { type: "submit", className: "btn btn-primary", children: "Crear" }) })
        ] })
      }
    )
  ] }) });
}
const SplitComponent = function RouteComponent() {
  const [isOpen, setIsOpen] = useState(false);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx("div", { className: "container mx-auto", children: /* @__PURE__ */ jsx("div", { className: "card bg-base-300", children: /* @__PURE__ */ jsxs("div", { className: "card-body", children: [
      /* @__PURE__ */ jsx("div", { children: /* @__PURE__ */ jsx("button", { type: "button", className: "btn btn-primary", onClick: () => setIsOpen(true), children: "Nuevo cliente" }) }),
      /* @__PURE__ */ jsx(ClientTable, {})
    ] }) }) }),
    /* @__PURE__ */ jsx(CreateClientModal, { isOpen, setIsOpen })
  ] });
};

export { SplitComponent as component };
//# sourceMappingURL=index-Ccc0wFaZ.mjs.map
