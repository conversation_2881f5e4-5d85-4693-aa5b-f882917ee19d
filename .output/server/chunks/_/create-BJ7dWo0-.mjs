import { jsxs, jsx } from 'react/jsx-runtime';
import { Link } from '@tanstack/react-router';
import { ArrowLeft, FileText, Clock } from 'lucide-react';
import { useStore } from '@tanstack/react-store';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { u as useAppForm } from './form-B3Cqm05b.mjs';
import { g as getErrorResult } from './effectErrors-CR6URVJK.mjs';
import { useQueryClient, useMutation } from '@tanstack/react-query';
import { create } from 'mutative';
import { u as useService } from './ssr.mjs';
import { A as AppRuntime } from './runtimes-CvAMo_9p.mjs';
import { s as scheduleOptions } from './schedule-options-hwpkl8x6.mjs';
import { C as CreateScheduleSchema, d as defaultValues, T as TurnsTable, S as SchedulePreview } from './index-CJ4nBaQx.mjs';
import '@tanstack/react-form';
import 'downshift';
import 'use-mutative';
import './classes-DMlJhLpb.mjs';
import 'clsx';
import 'tailwind-merge';
import 'effect/Runtime';
import '@tanstack/react-query-devtools';
import '@tanstack/react-router-devtools';
import 'valibot';
import 'tiny-invariant';
import 'tiny-warning';
import '@tanstack/router-core';
import 'node:async_hooks';
import 'effect';
import '@tanstack/react-router-with-query';
import '@tanstack/history';
import 'jsesc';
import 'node:stream';
import 'isbot';
import 'react-dom/server';
import 'node:stream/web';
import '@effect/platform';

function useCreateSchedule() {
  const service = useService();
  const { schedule } = service;
  const queryClient = useQueryClient();
  const queryKey = scheduleOptions(service).queryKey;
  return useMutation({
    mutationFn: (newSchedule) => AppRuntime.runPromise(schedule.create(newSchedule)),
    onSuccess: (id, newSchedule) => {
      queryClient.setQueryData(
        queryKey,
        (old) => create(old != null ? old : [], (draft) => {
          draft.push({
            id,
            name: newSchedule.name,
            sessionDuration: newSchedule.sessionDuration,
            breakDuration: newSchedule.breakDuration,
            turns: newSchedule.turns.map((turn) => ({
              id: "",
              // Will be set by backend
              name: turn.name,
              startTime: turn.startTime,
              endTime: turn.endTime,
              createdAt: (/* @__PURE__ */ new Date()).toISOString(),
              updatedAt: null,
              deletedAt: null
            })),
            createdAt: (/* @__PURE__ */ new Date()).toISOString(),
            updatedAt: null,
            deletedAt: null
          });
        })
      );
    }
  });
}
function CreateScheduleForm() {
  const [selectedTurnIndex, setSelectedTurnIndex] = useState(
    null
  );
  const { mutate } = useCreateSchedule();
  const form = useAppForm({
    defaultValues,
    validators: {
      onChange: CreateScheduleSchema
    },
    onSubmit: ({ value }) => {
      mutate(
        {
          name: value.name,
          sessionDuration: value.sessionDuration,
          breakDuration: value.breakDuration,
          turns: value.turns
        },
        {
          onSuccess: () => {
            toast.success("Horario creado exitosamente");
            window.history.back();
          },
          onError: (_error) => {
            console.log(_error);
            const { error } = getErrorResult(_error);
            toast.error(error.message);
          }
        }
      );
    }
  });
  return /* @__PURE__ */ jsx(
    "form",
    {
      onSubmit: (e) => {
        e.preventDefault();
        form.handleSubmit();
      },
      children: /* @__PURE__ */ jsx(form.AppForm, { children: /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 gap-8", children: [
        /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
          /* @__PURE__ */ jsx("fieldset", { className: "fieldset", children: /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-3 gap-4", children: [
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "name",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Nombre del Horario",
                    placeholder: "Nombre del Horario",
                    prefixComponent: /* @__PURE__ */ jsx(FileText, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "sessionDuration",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Duraci\xF3n de Sesi\xF3n (minutos)",
                    placeholder: "60",
                    type: "number",
                    prefixComponent: /* @__PURE__ */ jsx(Clock, { size: 16 })
                  }
                )
              }
            ),
            /* @__PURE__ */ jsx(
              form.AppField,
              {
                name: "breakDuration",
                children: ({ FSTextField }) => /* @__PURE__ */ jsx(
                  FSTextField,
                  {
                    label: "Duraci\xF3n de Descanso (minutos)",
                    placeholder: "15",
                    type: "number",
                    prefixComponent: /* @__PURE__ */ jsx(Clock, { size: 16 })
                  }
                )
              }
            )
          ] }) }),
          /* @__PURE__ */ jsx(
            TurnsTable,
            {
              form,
              selectedTurnIndex,
              onTurnSelect: setSelectedTurnIndex
            }
          ),
          /* @__PURE__ */ jsxs("div", { className: "flex gap-4", children: [
            /* @__PURE__ */ jsx(
              form.SubscribeButton,
              {
                label: "Crear Horario",
                className: "btn btn-primary"
              }
            ),
            /* @__PURE__ */ jsx(Link, { to: "/admin/schedules", className: "btn btn-outline", children: "Cancelar" })
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
          /* @__PURE__ */ jsx("h3", { className: "font-semibold text-xl", children: "Vista Previa del Horario" }),
          /* @__PURE__ */ jsx(
            form.Subscribe,
            {
              selector: (state) => [
                state.values.sessionDuration,
                state.values.breakDuration
              ],
              children: ([sessionDuration, breakDuration, _]) => {
                const turns = useStore(
                  form.store,
                  (state) => state.values.turns
                );
                const selectedTurn = selectedTurnIndex !== null ? turns[selectedTurnIndex] : null;
                if (selectedTurn)
                  return /* @__PURE__ */ jsx(
                    SchedulePreview,
                    {
                      turn: selectedTurn,
                      sessionDuration: sessionDuration || 60,
                      breakDuration: breakDuration || 15
                    }
                  );
                return /* @__PURE__ */ jsx("div", { className: "card bg-base-200", children: /* @__PURE__ */ jsx("div", { className: "card-body text-center", children: /* @__PURE__ */ jsx("p", { className: "text-base-content/70", children: "Selecciona un turno para ver la vista previa del horario" }) }) });
              }
            }
          )
        ] })
      ] }) })
    }
  );
}
const SplitComponent = function RouteComponent() {
  return /* @__PURE__ */ jsxs("div", { className: "container mx-auto max-w-6xl", children: [
    /* @__PURE__ */ jsx("div", { className: "mb-6", children: /* @__PURE__ */ jsxs(Link, { to: "/admin/schedules", className: "btn btn-ghost", children: [
      /* @__PURE__ */ jsx(ArrowLeft, { size: 16 }),
      "Volver a Horarios"
    ] }) }),
    /* @__PURE__ */ jsx("div", { className: "card bg-base-300", children: /* @__PURE__ */ jsxs("div", { className: "card-body", children: [
      /* @__PURE__ */ jsx("h2", { className: "card-title mb-6 text-2xl", children: "Crear Nuevo Horario" }),
      /* @__PURE__ */ jsx(CreateScheduleForm, {})
    ] }) })
  ] });
};

export { SplitComponent as component };
//# sourceMappingURL=create-BJ7dWo0-.mjs.map
