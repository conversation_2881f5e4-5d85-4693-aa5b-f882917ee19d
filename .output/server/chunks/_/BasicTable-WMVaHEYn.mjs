import { jsx, jsxs } from 'react/jsx-runtime';
import { flexRender } from '@tanstack/react-table';

function CloseModal({ onClose }) {
  return /* @__PURE__ */ jsx(
    "button",
    {
      type: "button",
      className: "btn btn-sm btn-circle btn-ghost absolute top-2 right-2",
      onClick: onClose,
      children: "\u2715"
    }
  );
}
function BasicTable({ table }) {
  return /* @__PURE__ */ jsxs("table", { className: "table", children: [
    /* @__PURE__ */ jsx("thead", { children: table.getHeaderGroups().map((headerGroup) => /* @__PURE__ */ jsx("tr", { children: headerGroup.headers.map((header) => /* @__PURE__ */ jsx("th", { children: header.isPlaceholder ? null : flexRender(
      header.column.columnDef.header,
      header.getContext()
    ) }, header.id)) }, headerGroup.id)) }),
    /* @__PURE__ */ jsx("tbody", { children: table.getRowModel().rows.map((row) => /* @__PURE__ */ jsx("tr", { children: row.getVisibleCells().map((cell) => /* @__PURE__ */ jsx("td", { children: flexRender(cell.column.columnDef.cell, cell.getContext()) }, cell.id)) }, row.id)) })
  ] });
}

export { BasicTable as B, CloseModal as C };
//# sourceMappingURL=BasicTable-WMVaHEYn.mjs.map
