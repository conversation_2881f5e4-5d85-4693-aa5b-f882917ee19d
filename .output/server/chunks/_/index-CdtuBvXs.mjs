import { jsx, jsxs, Fragment } from 'react/jsx-runtime';
import { Link } from '@tanstack/react-router';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { u as useService } from './ssr.mjs';
import { g as getErrorResult } from './effectErrors-CR6URVJK.mjs';
import { s as scheduleOptions } from './schedule-options-hwpkl8x6.mjs';
import { useReactTable, getCoreRowModel, createColumnHelper } from '@tanstack/react-table';
import { B as BasicTable, C as CloseModal } from './BasicTable-WMVaHEYn.mjs';
import { Edit, Trash2 } from 'lucide-react';
import { toast } from 'react-toastify';
import { c as cn } from './classes-DMlJhLpb.mjs';
import { create } from 'mutative';
import { A as AppRuntime } from './runtimes-CvAMo_9p.mjs';
import '@tanstack/react-query-devtools';
import '@tanstack/react-router-devtools';
import 'valibot';
import 'tiny-invariant';
import 'tiny-warning';
import '@tanstack/router-core';
import 'node:async_hooks';
import 'effect';
import '@tanstack/react-router-with-query';
import '@tanstack/history';
import 'jsesc';
import 'node:stream';
import 'isbot';
import 'react-dom/server';
import 'node:stream/web';
import 'effect/Runtime';
import 'clsx';
import 'tailwind-merge';
import '@effect/platform';

function useDeleteSchedule() {
  const service = useService();
  const { schedule } = service;
  const queryClient = useQueryClient();
  const queryKey = scheduleOptions(service).queryKey;
  return useMutation({
    mutationKey: ["delete-schedule"],
    mutationFn: (scheduleId) => AppRuntime.runPromise(schedule.delete(scheduleId)),
    onMutate: async (scheduleId) => {
      await queryClient.cancelQueries({ queryKey });
      const previousSchedules = queryClient.getQueryData(queryKey);
      if (previousSchedules) {
        queryClient.setQueryData(
          queryKey,
          create(previousSchedules, (draft) => {
            const index = draft.findIndex((s) => s.id === scheduleId);
            if (index !== -1) {
              draft.splice(index, 1);
            }
          })
        );
      }
      return { previousSchedules };
    },
    onError: (_, __, context) => {
      queryClient.setQueryData(queryKey, context == null ? void 0 : context.previousSchedules);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey });
    }
  });
}
function DeleteScheduleModal({
  isOpen,
  setIsOpen,
  id
}) {
  const { mutate, isPending } = useDeleteSchedule();
  const handleDelete = () => {
    mutate(id, {
      onSuccess: () => {
        toast.success("Horario eliminado exitosamente");
        setIsOpen(false);
      },
      onError: (error) => {
        const { error: errorResult } = getErrorResult(error);
        toast.error(errorResult.message);
      }
    });
  };
  return /* @__PURE__ */ jsx("div", { className: cn("modal", isOpen && "modal-open"), children: /* @__PURE__ */ jsxs("div", { className: "modal-box", children: [
    /* @__PURE__ */ jsx(CloseModal, { onClose: () => setIsOpen(false) }),
    /* @__PURE__ */ jsx("h3", { className: "font-bold text-lg", children: "Eliminar Horario" }),
    /* @__PURE__ */ jsx("p", { className: "mb-4", children: "\xBFEst\xE1s seguro de que deseas eliminar este horario? Esta acci\xF3n no se puede deshacer." }),
    /* @__PURE__ */ jsxs("div", { className: "modal-action", children: [
      /* @__PURE__ */ jsx(
        "button",
        {
          type: "button",
          className: "btn btn-ghost",
          onClick: () => setIsOpen(false),
          disabled: isPending,
          children: "Cancelar"
        }
      ),
      /* @__PURE__ */ jsx(
        "button",
        {
          type: "button",
          className: "btn btn-error",
          onClick: handleDelete,
          disabled: isPending,
          children: isPending ? /* @__PURE__ */ jsx("span", { className: "loading loading-spinner loading-sm" }) : "Eliminar"
        }
      )
    ] })
  ] }) });
}
const columnHelper = createColumnHelper();
const formatTime = (time) => {
  const hours = Math.floor(time / 100);
  const minutes = time % 100;
  return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
};
const formatDate = (dateString) => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleDateString();
};
const columns = [
  columnHelper.accessor("name", {
    header: "Nombre",
    cell: (info) => /* @__PURE__ */ jsx("div", { className: "font-bold", children: info.getValue() })
  }),
  columnHelper.accessor("sessionDuration", {
    header: "Duraci\xF3n Sesi\xF3n",
    cell: (info) => `${info.getValue()} min`
  }),
  columnHelper.accessor("breakDuration", {
    header: "Duraci\xF3n Descanso",
    cell: (info) => `${info.getValue()} min`
  }),
  columnHelper.accessor("turns", {
    header: "Turnos",
    cell: (info) => /* @__PURE__ */ jsx("div", { className: "space-y-1", children: info.getValue().map((turn) => /* @__PURE__ */ jsxs("div", { className: "badge badge-outline", children: [
      turn.name,
      ": ",
      formatTime(turn.startTime),
      " -",
      " ",
      formatTime(turn.endTime)
    ] }, turn.id)) })
  }),
  columnHelper.accessor("createdAt", {
    header: "Fecha Creaci\xF3n",
    cell: (info) => formatDate(info.getValue())
  }),
  columnHelper.display({
    header: "Acciones",
    cell: ({ row }) => {
      const [isDeleteOpen, setIsDeleteOpen] = useState(false);
      return /* @__PURE__ */ jsxs(Fragment, { children: [
        /* @__PURE__ */ jsxs("div", { className: "flex gap-2", children: [
          /* @__PURE__ */ jsx(
            Link,
            {
              to: "/admin/schedules/edit/$id",
              params: { id: row.original.id },
              className: "btn btn-circle btn-primary",
              children: /* @__PURE__ */ jsx(Edit, { size: 16 })
            }
          ),
          /* @__PURE__ */ jsx(
            "button",
            {
              type: "button",
              className: "btn btn-circle btn-error",
              onClick: () => setIsDeleteOpen(true),
              children: /* @__PURE__ */ jsx(Trash2, { size: 16 })
            }
          )
        ] }),
        /* @__PURE__ */ jsx(
          DeleteScheduleModal,
          {
            isOpen: isDeleteOpen,
            setIsOpen: setIsDeleteOpen,
            id: row.original.id
          }
        )
      ] });
    }
  })
];
function Table({ schedules }) {
  const table = useReactTable({
    data: schedules,
    columns,
    getCoreRowModel: getCoreRowModel()
  });
  return /* @__PURE__ */ jsx(BasicTable, { table });
}
function ScheduleTable() {
  const svc = useService();
  const { data, isError, error, isPending } = useQuery(scheduleOptions(svc));
  useEffect(() => {
    if (error) {
      console.log(getErrorResult(error).error);
    }
  }, [error]);
  if (isError) return /* @__PURE__ */ jsxs("div", { children: [
    "Error: ",
    getErrorResult(error).error.message
  ] });
  if (isPending) return /* @__PURE__ */ jsx("div", { children: "Loading..." });
  return /* @__PURE__ */ jsx(Table, { schedules: data });
}
const SplitComponent = function RouteComponent() {
  return /* @__PURE__ */ jsx("div", { className: "container mx-auto", children: /* @__PURE__ */ jsx("div", { className: "card bg-base-300", children: /* @__PURE__ */ jsxs("div", { className: "card-body", children: [
    /* @__PURE__ */ jsx("div", { children: /* @__PURE__ */ jsx(Link, { to: "/admin/schedules/create", className: "btn btn-primary", children: "Nuevo horario" }) }),
    /* @__PURE__ */ jsx(ScheduleTable, {})
  ] }) }) });
};

export { SplitComponent as component };
//# sourceMappingURL=index-CdtuBvXs.mjs.map
