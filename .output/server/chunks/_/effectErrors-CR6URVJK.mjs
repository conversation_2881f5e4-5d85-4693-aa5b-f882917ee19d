import { isFiberFailure, FiberFailureCauseId } from 'effect/Runtime';
import { E as ErrorResult } from './runtimes-CvAMo_9p.mjs';

const getErrorResult = (error) => {
  const errorZero = ErrorResult.make({
    error: {
      message: "Error desconocido",
      code: 0,
      details: error
    },
    correlationId: "0"
  });
  if (!isFiberFailure(error)) return errorZero;
  const squashed = error[FiberFailureCauseId];
  if (squashed._tag !== "Fail") return errorZero;
  return squashed.error;
};

export { getErrorResult as g };
//# sourceMappingURL=effectErrors-CR6URVJK.mjs.map
