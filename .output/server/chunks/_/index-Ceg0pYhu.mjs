import { jsx, Fragment, jsxs } from 'react/jsx-runtime';
import { Link } from '@tanstack/react-router';

const SplitComponent = function Home() {
  return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsxs("div", { className: "hero min-h-screen", style: {
    backgroundImage: "url(https://img.daisyui.com/images/stock/photo-1507358522600-9f71e620c44e.webp)"
  }, children: [
    /* @__PURE__ */ jsx("div", { className: "hero-overlay" }),
    /* @__PURE__ */ jsx("div", { className: "hero-content text-center text-neutral-content", children: /* @__PURE__ */ jsxs("div", { className: "max-w-md", children: [
      /* @__PURE__ */ jsx("h1", { className: "mb-5 font-bold text-5xl", children: "Hello there" }),
      /* @__PURE__ */ jsxs("p", { className: "mb-5", children: [
        "Gestiona tus citas psicol\xF3gicas de manera f\xE1cil y eficiente. Organiza horarios, controla sesiones y reduce ausencias con recordatorios autom\xE1ticos. \xA1Optimiza tu pr\xE1ctica hoy!",
        " "
      ] }),
      /* @__PURE__ */ jsx(Link, { to: "/login", className: "btn btn-primary", children: "Iniciar sesi\xF3n" })
    ] }) })
  ] }) });
};

export { SplitComponent as component };
//# sourceMappingURL=index-Ceg0pYhu.mjs.map
