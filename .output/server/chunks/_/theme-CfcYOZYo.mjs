import { a as createServerRpc, b as createServerFn, g as getCookie, c as constants, s as setCookie } from './ssr.mjs';
import 'react/jsx-runtime';
import '@tanstack/react-router';
import '@tanstack/react-query-devtools';
import '@tanstack/react-router-devtools';
import 'react-toastify';
import 'react';
import 'valibot';
import 'tiny-invariant';
import 'tiny-warning';
import '@tanstack/router-core';
import 'node:async_hooks';
import 'effect';
import '@tanstack/react-query';
import '@tanstack/react-router-with-query';
import '@tanstack/history';
import 'jsesc';
import 'node:stream';
import 'isbot';
import 'react-dom/server';
import 'node:stream/web';

const getThemeServerFn_createServerFn_handler = createServerRpc("src_modules_auth_server_theme_ts--getThemeServerFn_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return getThemeServerFn.__executeServer(opts, signal);
});
const setThemeServerFn_createServerFn_handler = createServerRpc("src_modules_auth_server_theme_ts--setThemeServerFn_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return setThemeServerFn.__executeServer(opts, signal);
});
const getThemeServerFn = createServerFn().handler(getThemeServerFn_createServerFn_handler, async () => {
  return getCookie(constants.UI_THEME_KEY) || "light";
});
const setThemeServerFn = createServerFn({
  method: "POST"
}).validator((data) => {
  return data;
}).handler(setThemeServerFn_createServerFn_handler, async ({
  data
}) => {
  setCookie(constants.UI_THEME_KEY, data);
});

export { getThemeServerFn_createServerFn_handler, setThemeServerFn_createServerFn_handler };
//# sourceMappingURL=theme-CfcYOZYo.mjs.map
