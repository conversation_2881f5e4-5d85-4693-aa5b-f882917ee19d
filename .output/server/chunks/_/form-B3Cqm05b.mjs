import { createFormHook, createFormHookContexts } from '@tanstack/react-form';
import { jsx, jsxs, Fragment } from 'react/jsx-runtime';
import { useMultipleSelection, useCombobox } from 'downshift';
import { KeyRound, EyeOff, Eye, X, Eraser, ArrowUp, ArrowDown } from 'lucide-react';
import { useState, useEffect, useMemo, useCallback } from 'react';
import { useMutative } from 'use-mutative';
import { c as cn } from './classes-DMlJhLpb.mjs';

function ComboBox(props) {
  if (props.isMultiple) {
    return /* @__PURE__ */ jsx(MultipleComboBox, { ...props });
  }
  return /* @__PURE__ */ jsx(SingleComboBox, { ...props });
}
function MultipleComboBox({
  value = [],
  isLoading = false,
  options,
  defaultSelected = [],
  onChange,
  placeholder = "Seleccionar...",
  className,
  hideReset = false,
  size = "md"
}) {
  const [selectedItems, setSelectedItems] = useState(defaultSelected);
  useEffect(() => {
    setSelectedItems(defaultSelected);
  }, [defaultSelected]);
  const availableItems = useMemo(
    () => options.filter(
      (option) => !selectedItems.some((selected) => selected.value === option.value)
    ),
    [selectedItems, options]
  );
  const handleSelectionChange = useCallback(
    (newSelectedItems) => {
      setSelectedItems(newSelectedItems);
      onChange(newSelectedItems);
    },
    [onChange]
  );
  const handleRemoveItem = useCallback(
    (itemToRemove) => {
      const updatedItems = selectedItems.filter(
        (item) => item.value !== itemToRemove.value
      );
      handleSelectionChange(updatedItems);
    },
    [selectedItems, handleSelectionChange]
  );
  const handleClearAll = useCallback(() => {
    handleSelectionChange([]);
  }, [handleSelectionChange]);
  const { getSelectedItemProps, getDropdownProps, removeSelectedItem } = useMultipleSelection({
    selectedItems,
    defaultSelectedItems: defaultSelected,
    onStateChange({ selectedItems: newSelectedItems, type }) {
      switch (type) {
        case useMultipleSelection.stateChangeTypes.SelectedItemKeyDownBackspace:
        case useMultipleSelection.stateChangeTypes.SelectedItemKeyDownDelete:
        case useMultipleSelection.stateChangeTypes.DropdownKeyDownBackspace:
        case useMultipleSelection.stateChangeTypes.FunctionRemoveSelectedItem:
          if (newSelectedItems) {
            handleSelectionChange(newSelectedItems);
          }
          break;
      }
    }
  });
  const {
    isOpen,
    getToggleButtonProps,
    getMenuProps,
    getInputProps,
    highlightedIndex,
    getItemProps,
    setInputValue
  } = useCombobox({
    items: availableItems,
    itemToString: (item) => {
      var _a;
      return (_a = item == null ? void 0 : item.label) != null ? _a : "";
    },
    defaultHighlightedIndex: 0,
    selectedItem: null,
    stateReducer(_, actionAndChanges) {
      const { changes, type } = actionAndChanges;
      switch (type) {
        case useCombobox.stateChangeTypes.InputKeyDownEnter:
        case useCombobox.stateChangeTypes.ItemClick:
          return {
            ...changes,
            isOpen: true,
            highlightedIndex: 0
          };
        default:
          return changes;
      }
    },
    onStateChange({ type, selectedItem: newSelectedItem }) {
      switch (type) {
        case useCombobox.stateChangeTypes.InputKeyDownEnter:
        case useCombobox.stateChangeTypes.ItemClick:
        case useCombobox.stateChangeTypes.InputBlur:
          if (newSelectedItem) {
            const updatedSelectedItems = [...selectedItems, newSelectedItem];
            handleSelectionChange(updatedSelectedItems);
            setInputValue("");
          }
          break;
      }
    }
  });
  const renderSelectedItem = useCallback(
    (selectedItem, index) => /* @__PURE__ */ jsxs(
      "div",
      {
        className: "badge badge-primary pr-0",
        children: [
          /* @__PURE__ */ jsx(
            "div",
            {
              className: "rounded-l-md",
              ...getSelectedItemProps({ selectedItem, index }),
              children: selectedItem.label
            }
          ),
          /* @__PURE__ */ jsx(
            "button",
            {
              type: "button",
              className: "btn btn-xs btn-circle btn-error btn-ghost",
              onClick: (e) => {
                e.stopPropagation();
                handleRemoveItem(selectedItem);
              },
              children: /* @__PURE__ */ jsx(X, { size: 16 })
            }
          )
        ]
      },
      `selected-item-${selectedItem.value}-${index}`
    ),
    [getSelectedItemProps, handleRemoveItem]
  );
  const renderMenuItem = useCallback(
    (item, index) => /* @__PURE__ */ jsx(
      "li",
      {
        className: cn(
          "flex cursor-pointer flex-col px-3 py-2 shadow-sm",
          highlightedIndex === index && "bg-neutral"
        ),
        ...getItemProps({ item, index }),
        children: /* @__PURE__ */ jsx("span", { children: item.label })
      },
      `${item.value}-${index}`
    ),
    [highlightedIndex, getItemProps]
  );
  return /* @__PURE__ */ jsxs("div", { className: cn("dropdown", className), children: [
    /* @__PURE__ */ jsxs("div", { className: "join input h-fit w-full items-center rounded-md p-1", children: [
      isLoading ? /* @__PURE__ */ jsx("span", { className: "loading loading-dots join-item loading-sm" }) : /* @__PURE__ */ jsxs("div", { className: "inline-flex w-full flex-wrap items-center gap-1", children: [
        selectedItems.map(renderSelectedItem),
        /* @__PURE__ */ jsx(
          "input",
          {
            placeholder,
            className: "input input-ghost flex-1 focus:outline-none",
            ...getInputProps(getDropdownProps({ preventKeyAction: isOpen }))
          }
        )
      ] }),
      /* @__PURE__ */ jsx(
        "button",
        {
          className: "btn btn-sm join-item btn-ghost btn-circle",
          type: "button",
          onClick: handleClearAll,
          disabled: selectedItems.length === 0,
          children: /* @__PURE__ */ jsx(Eraser, {})
        }
      ),
      /* @__PURE__ */ jsx(
        "button",
        {
          "aria-label": "toggle menu",
          className: "btn btn-sm join-item btn-ghost btn-circle",
          type: "button",
          ...getToggleButtonProps(),
          children: isOpen ? /* @__PURE__ */ jsx(ArrowUp, { className: "h-6 w-6" }) : /* @__PURE__ */ jsx(ArrowDown, { className: "h-6 w-6" })
        }
      )
    ] }),
    /* @__PURE__ */ jsx(
      "ul",
      {
        className: cn(
          "dropdown-content z-10 w-full rounded-box bg-base-200 shadow-sm",
          (!isOpen || !availableItems.length) && "hidden"
        ),
        ...getMenuProps(),
        children: isOpen && availableItems.map(renderMenuItem)
      }
    )
  ] });
}
function SingleComboBox({
  value = null,
  isLoading = false,
  options,
  defaultSelected = null,
  onChange,
  placeholder = "Seleccionar...",
  className,
  size = "md",
  label = "Seleccionar...",
  hideReset = false
}) {
  const [items, setItems] = useMutative(options);
  const [inputValue, setInputValue] = useState(
    (defaultSelected == null ? void 0 : defaultSelected.label) || (value == null ? void 0 : value.label) || ""
  );
  const filterItems = useCallback(
    (searchValue) => {
      setItems((draft) => {
        for (let i = 0; i < options.length; i++) {
          draft[i] = options[i];
        }
        draft.length = options.length;
        if (searchValue) {
          let keepIndex = 0;
          const lowerSearchValue = searchValue.toLowerCase();
          for (let i = 0; i < draft.length; i++) {
            const item = draft[i];
            if (item == null ? void 0 : item.label.toLowerCase().includes(lowerSearchValue)) {
              draft[keepIndex] = item;
              keepIndex++;
            }
          }
          draft.length = keepIndex;
        }
      });
    },
    [options, setItems]
  );
  const {
    isOpen,
    getToggleButtonProps,
    getMenuProps,
    getInputProps,
    highlightedIndex,
    getItemProps,
    reset,
    getLabelProps
  } = useCombobox({
    items,
    defaultSelectedItem: defaultSelected || value,
    inputValue,
    itemToString: (item) => {
      var _a;
      return (_a = item == null ? void 0 : item.label) != null ? _a : "";
    },
    onInputValueChange({ inputValue: newInputValue }) {
      setInputValue(newInputValue || "");
      filterItems(newInputValue || "");
    },
    onSelectedItemChange: ({ selectedItem }) => {
      onChange(selectedItem);
    },
    onIsOpenChange: ({ isOpen: newIsOpen, selectedItem }) => {
      if (!newIsOpen) {
        setInputValue((selectedItem == null ? void 0 : selectedItem.label) || "");
      }
    }
  });
  useEffect(() => {
    setItems(options);
  }, [options, setItems]);
  useEffect(() => {
    if (value === null) {
      reset();
    }
  }, [value, reset]);
  const renderMenuItem = useCallback(
    (item, index) => /* @__PURE__ */ jsx(
      "li",
      {
        className: cn(
          "flex cursor-pointer flex-col px-3 py-2 shadow-sm",
          highlightedIndex === index && "bg-neutral",
          (value == null ? void 0 : value.value) === item.value && "font-bold"
        ),
        ...getItemProps({ item, index }),
        children: /* @__PURE__ */ jsx("span", { children: item.label })
      },
      `${item.value}-${index}`
    ),
    [highlightedIndex, value, getItemProps]
  );
  return /* @__PURE__ */ jsxs("div", { className: cn("dropdown", className), children: [
    /* @__PURE__ */ jsx("label", { htmlFor: "combo", className: "label", ...getLabelProps(), children: label }),
    /* @__PURE__ */ jsxs("div", { className: "join h-fit w-full items-center bg-base-100", children: [
      isLoading ? /* @__PURE__ */ jsx("span", { className: "loading loading-dots join-item loading-sm" }) : /* @__PURE__ */ jsx(
        "input",
        {
          placeholder,
          className: "input input-sm input-bordered join-item w-full",
          ...getInputProps()
        }
      ),
      !hideReset && /* @__PURE__ */ jsx(
        "button",
        {
          className: "btn btn-sm join-item",
          type: "button",
          onClick: reset,
          disabled: !value,
          children: /* @__PURE__ */ jsx(Eraser, {})
        }
      ),
      /* @__PURE__ */ jsx(
        "button",
        {
          "aria-label": "toggle menu",
          className: "btn btn-sm join-item",
          type: "button",
          ...getToggleButtonProps(),
          children: isOpen ? /* @__PURE__ */ jsx(ArrowUp, { className: "h-6 w-6" }) : /* @__PURE__ */ jsx(ArrowDown, { className: "h-6 w-6" })
        }
      )
    ] }),
    /* @__PURE__ */ jsx(
      "ul",
      {
        className: cn(
          "dropdown-content z-10 w-full rounded-box bg-base-200 shadow-sm",
          (!isOpen || !items.length) && "hidden"
        ),
        ...getMenuProps(),
        children: isOpen && items.map(renderMenuItem)
      }
    )
  ] });
}
function FSComboBoxField({
  label,
  placeholder,
  options,
  isNumber = false,
  isMultiple = false
}) {
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx("label", { htmlFor: "combobox", className: "label", children: label }),
    isMultiple ? /* @__PURE__ */ jsx(
      MultipleComboBoxField,
      {
        placeholder,
        options,
        isNumber
      }
    ) : /* @__PURE__ */ jsx(SingleComboBoxField, { placeholder, options })
  ] });
}
function SingleComboBoxField({
  placeholder,
  options,
  isLoading = false
}) {
  const field = useFieldContext();
  return /* @__PURE__ */ jsx(
    ComboBox,
    {
      isMultiple: false,
      options,
      onChange: (selectedItem) => field.handleChange((selectedItem == null ? void 0 : selectedItem.value) || ""),
      placeholder,
      isLoading,
      defaultSelected: options.find((option) => option.value === field.state.value) || null
    }
  );
}
function MultipleComboBoxField({
  placeholder,
  options,
  isLoading = false
}) {
  const field = useFieldContext();
  return /* @__PURE__ */ jsx(
    ComboBox,
    {
      isMultiple: true,
      options,
      onChange: (selectedItems) => field.handleChange(selectedItems.map((item) => item.value)),
      placeholder,
      isLoading,
      defaultSelected: options.filter(
        (o) => field.state.value.includes(o.value)
      )
    }
  );
}
function FSPasswordField({
  label,
  placeholder
}) {
  const field = useFieldContext();
  const [showPassword, setShowPassword] = useState(false);
  return /* @__PURE__ */ jsxs("fieldset", { className: "fieldset", children: [
    /* @__PURE__ */ jsx("legend", { className: "fieldset-legend", children: label }),
    /* @__PURE__ */ jsxs("label", { className: "input w-full", children: [
      /* @__PURE__ */ jsx(KeyRound, { size: 16 }),
      /* @__PURE__ */ jsx(
        "input",
        {
          type: showPassword ? "text" : "password",
          className: "grow",
          placeholder,
          value: field.state.value,
          onChange: (e) => field.handleChange(e.target.value)
        }
      ),
      /* @__PURE__ */ jsx(
        "button",
        {
          type: "button",
          className: "btn btn-sm btn-circle",
          onClick: () => setShowPassword(!showPassword),
          children: showPassword ? /* @__PURE__ */ jsx(EyeOff, { size: 16 }) : /* @__PURE__ */ jsx(Eye, { size: 16 })
        }
      )
    ] }),
    field.state.meta.isTouched && field.state.meta.errors.length ? field.state.meta.errors.map((error) => /* @__PURE__ */ jsx("p", { className: "fieldset-label text-error", children: error.message }, error.path)) : null
  ] });
}
function FSSelectField({
  label,
  placeholder,
  options,
  isNumber = false
}) {
  var _a;
  const field = useFieldContext();
  return /* @__PURE__ */ jsxs("fieldset", { className: "fieldset", children: [
    /* @__PURE__ */ jsx("legend", { className: "fieldset-legend", children: label }),
    /* @__PURE__ */ jsxs(
      "select",
      {
        className: "select w-full",
        value: isNumber ? field.state.value : (_a = field.state.value) == null ? void 0 : _a.toString(),
        onChange: (e) => field.handleChange(
          // @ts-ignore
          isNumber ? Number(e.target.value) : e.target.value
        ),
        children: [
          /* @__PURE__ */ jsx("option", { disabled: true, selected: true, children: placeholder || "Seleccione una opci\xF3n" }),
          options.map((option) => /* @__PURE__ */ jsx("option", { value: option.value, children: option.label }, option.value))
        ]
      }
    ),
    field.state.meta.isTouched && field.state.meta.errors.length ? field.state.meta.errors.flatMap((error) => /* @__PURE__ */ jsx("p", { className: "fieldset-label text-error", children: error.message }, error.message)) : null
  ] });
}
function FSTextField({
  label,
  placeholder,
  type = "text",
  prefixComponent,
  suffixComponent
}) {
  var _a;
  const field = useFieldContext();
  return /* @__PURE__ */ jsxs("fieldset", { className: "fieldset", children: [
    /* @__PURE__ */ jsx("legend", { className: "fieldset-legend", children: label }),
    /* @__PURE__ */ jsxs("div", { className: "input w-full", children: [
      prefixComponent && prefixComponent,
      /* @__PURE__ */ jsx(
        "input",
        {
          type,
          className: "grow",
          placeholder,
          value: type === "number" ? (_a = field.state.value) == null ? void 0 : _a.toString() : field.state.value,
          onChange: (e) => field.handleChange(
            // @ts-ignore
            type === "number" ? Number(e.target.value) : e.target.value
          )
        }
      ),
      suffixComponent && suffixComponent
    ] }),
    field.state.meta.isTouched && field.state.meta.errors.length ? field.state.meta.errors.flatMap((error) => /* @__PURE__ */ jsx("p", { className: "fieldset-label text-error", children: error.message }, error.message)) : null
  ] });
}
function FSToggleField({
  label,
  trueLabel,
  falseLabel
}) {
  const field = useFieldContext();
  return /* @__PURE__ */ jsxs("fieldset", { className: "fieldset", children: [
    /* @__PURE__ */ jsx("legend", { className: "fieldset-legend", children: label }),
    /* @__PURE__ */ jsxs("label", { className: "fieldset-label", children: [
      /* @__PURE__ */ jsx(
        "input",
        {
          type: "checkbox",
          className: "toggle",
          defaultChecked: true,
          checked: field.state.value,
          onChange: () => field.handleChange(!field.state.value)
        }
      ),
      field.state.value ? trueLabel : falseLabel
    ] }),
    field.state.meta.isTouched && field.state.meta.errors.length ? field.state.meta.errors.flatMap((error) => /* @__PURE__ */ jsx("p", { className: "fieldset-label text-error", children: error.message }, error.message)) : null
  ] });
}
function SubscribeButton({
  label,
  className = "btn btn-neutral"
}) {
  const { Subscribe } = useFormContext();
  return /* @__PURE__ */ jsx(
    Subscribe,
    {
      selector: (state) => ({
        isSubmitting: state.isSubmitting,
        canSubmit: state.canSubmit
      }),
      children: ({ isSubmitting, canSubmit }) => /* @__PURE__ */ jsxs("button", { disabled: isSubmitting || !canSubmit, className, children: [
        isSubmitting && /* @__PURE__ */ jsx("span", { className: "loading loading-spinner" }),
        label
      ] })
    }
  );
}
const { fieldContext, useFieldContext, formContext, useFormContext } = createFormHookContexts();
const { useAppForm, withForm } = createFormHook({
  fieldComponents: {
    FSTextField,
    FSPasswordField,
    FSSelectField,
    FSToggleField,
    FSComboBoxField
  },
  formComponents: {
    SubscribeButton
  },
  fieldContext,
  formContext
});

export { useAppForm as u, withForm as w };
//# sourceMappingURL=form-B3Cqm05b.mjs.map
