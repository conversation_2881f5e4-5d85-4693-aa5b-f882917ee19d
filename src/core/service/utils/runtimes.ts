import { Layer, ManagedRuntime } from "effect";
import { authApiRepoLive } from "~/auth/service/repo/api/auth-api";
import { authRepositoryLive } from "~/auth/service/repository";
import { authUsecaseLive } from "~/auth/service/usecase";
import { clientApiRepoLive } from "~/client/service/repo/api/client-api";
import { ClientRepositoryLive } from "~/client/service/repository";
import { clientUsecaseLive } from "~/client/service/usecase";
import { personApiRepoLive } from "~/person/service/repo/api/person-api";
import { PersonRepositoryLive } from "~/person/service/repository";
import { personUsecaseLive } from "~/person/service/usecase";
import { scheduleApiRepoLive } from "~/schedule/service/repo/api/schedule-api";
import { ScheduleRepositoryLive } from "~/schedule/service/repository";
import { scheduleUsecaseLive } from "~/schedule/service/usecase";
import { workerApiRepoLive } from "~/worker/service/repo/api/worker-api";
import { WorkerRepositoryLive } from "~/worker/service/repository";
import { workerUsecaseLive } from "~/worker/service/usecase";

const makeAuthUsecaseLive = authUsecaseLive.pipe(
	Layer.provide(authRepositoryLive),
	Layer.provide(authApiRepoLive),
);

const makeClientUsecaseLive = clientUsecaseLive.pipe(
	Layer.provide(ClientRepositoryLive),
	Layer.provide(clientApiRepoLive),
);

const makePersonUsecaseLive = personUsecaseLive.pipe(
	Layer.provide(PersonRepositoryLive),
	Layer.provide(personApiRepoLive),
);

const makeWorkerUsecaseLive = workerUsecaseLive.pipe(
	Layer.provide(WorkerRepositoryLive),
	Layer.provide(workerApiRepoLive),
);

const makeScheduleUsecaseLive = scheduleUsecaseLive.pipe(
	Layer.provide(ScheduleRepositoryLive),
	Layer.provide(scheduleApiRepoLive),
);

const MainLayer = Layer.mergeAll(
	makeAuthUsecaseLive,
	makeClientUsecaseLive,
	makePersonUsecaseLive,
	makeWorkerUsecaseLive,
	makeScheduleUsecaseLive,
);

export const AppRuntime = ManagedRuntime.make(MainLayer);
