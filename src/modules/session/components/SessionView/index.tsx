import { useStore } from "@tanstack/react-store";
import { sessionStore } from "../../store/session";
import ScheduleSelect from "./ScheduleSelect";
import TurnSelect from "./TurnSelect";
import ClientWorkerSelects from "./ClientWorkerSelects";
import SessionScheduleGrid from "./SessionScheduleGrid";

export default function SessionView() {
	const { selectedSchedule, selectedTurn, selectedClient, selectedWorker } = useStore(sessionStore);

	return (
		<div className="space-y-6">
			{/* Schedule Selection */}
			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<h2 className="card-title">Seleccionar Horario</h2>
					<ScheduleSelect />
				</div>
			</div>

			{/* Turn Selection */}
			{selectedSchedule && (
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<h2 className="card-title">Selecci<PERSON><PERSON></h2>
						<TurnSelect schedule={selectedSchedule} />
					</div>
				</div>
			)}

			{/* Client and Worker Selection */}
			{selectedTurn && (
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<h2 className="card-title">Seleccionar Cliente o Trabajador</h2>
						<p className="mb-4 text-base-content/70 text-sm">
							Selecciona un cliente o un trabajador para ver el horario de sesiones
						</p>
						<ClientWorkerSelects />
					</div>
				</div>
			)}

			{/* Schedule Grid */}
			{selectedTurn && selectedSchedule && (selectedClient || selectedWorker) && (
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<h2 className="card-title">Horario de Sesiones</h2>
						<p className="mb-4 text-base-content/70 text-sm">
							{selectedClient
								? `Asignando sesiones para el cliente: ${selectedClient.person.name} ${selectedClient.person.fatherLastName}`
								: `Asignando sesiones para el trabajador: ${selectedWorker?.person.name} ${selectedWorker?.person.fatherLastName}`
							}
						</p>
						<SessionScheduleGrid
							schedule={selectedSchedule}
							turn={selectedTurn}
						/>
					</div>
				</div>
			)}
		</div>
	);
}
