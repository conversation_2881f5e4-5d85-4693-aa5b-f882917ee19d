import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { CreateSession, Session } from "../service/model/session";
import { sessionOptions } from "./session-options";

export default function useCreateSession() {
	const service = useService();
	const { session } = service;
	const queryClient = useQueryClient();
	const queryKey = sessionOptions(service).queryKey;

	return useMutation({
		mutationFn: (newSession: CreateSession) =>
			AppRuntime.runPromise(session.create(newSession)),
		onSuccess: (sessionId) => {
			queryClient.setQueryData(queryKey, (old: Session[] | undefined) =>
				create(old ?? [], (draft) => {
					// Note: We would need the full session object to add it to the cache
					// For now, we'll just invalidate the query to refetch
				}),
			);
			// Invalidate to refetch the updated list
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
