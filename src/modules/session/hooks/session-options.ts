import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const sessionOptions = ({ session }: serviceRegistry) =>
	queryOptions({
		queryKey: ["sessions"],
		queryFn: () => AppRuntime.runPromise(session.getAll()),
	});

export const sessionOptionsById = ({ session }: serviceRegistry, id: string) =>
	queryOptions({
		queryKey: ["sessions", id],
		queryFn: () => AppRuntime.runPromise(session.getById(id)),
	});
