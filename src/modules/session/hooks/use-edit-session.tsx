import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { UpdateSession, Session } from "../service/model/session";
import { sessionOptions } from "./session-options";

export default function useEditSession() {
	const service = useService();
	const { session } = service;
	const queryClient = useQueryClient();
	const queryKey = sessionOptions(service).queryKey;

	return useMutation({
		mutationFn: (updatedSession: UpdateSession) =>
			AppRuntime.runPromise(session.update(updatedSession)),
		onSuccess: (_, updatedSession) => {
			queryClient.setQueryData(queryKey, (old: Session[] | undefined) =>
				create(old ?? [], (draft) => {
					const index = draft.findIndex((s) => s.id === updatedSession.id);
					if (index !== -1) {
						// Update the session in the cache
						// Note: We would need to merge the updated fields with the existing session
						// For now, we'll just invalidate the query to refetch
					}
				}),
			);
			// Invalidate to refetch the updated list
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
