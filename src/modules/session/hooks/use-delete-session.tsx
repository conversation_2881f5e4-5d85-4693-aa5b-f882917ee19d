import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { Session } from "../service/model/session";
import { sessionOptions } from "./session-options";

export default function useDeleteSession() {
	const service = useService();
	const { session } = service;
	const queryClient = useQueryClient();
	const queryKey = sessionOptions(service).queryKey;

	return useMutation({
		mutationFn: (id: string) => AppRuntime.runPromise(session.delete(id)),
		onSuccess: (_, deletedId) => {
			queryClient.setQueryData(queryKey, (old: Session[] | undefined) =>
				create(old ?? [], (draft) => {
					const index = draft.findIndex((s) => s.id === deletedId);
					if (index !== -1) {
						draft.splice(index, 1);
					}
				}),
			);
		},
	});
}
